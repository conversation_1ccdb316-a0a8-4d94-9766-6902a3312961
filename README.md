# 🏢 保呀保险业务管理系统

> 专业的保险业务管理平台，为保险机构提供全方位的业务管理解决方案

## 📋 目录

- [项目概述](#项目概述)
- [系统架构](#系统架构)
- [开发环境搭建](#开发环境搭建)
- [核心功能模块](#核心功能模块)
- [技术规范](#技术规范)
- [开发工具](#开发工具)
- [代码规范](#代码规范)
- [部署指南](#部署指南)
- [团队协作](#团队协作)
- [故障排除](#故障排除)

## 🎯 项目概述

### 项目简介
保呀保险业务管理系统是一个基于 Vue.js 2.x 构建的现代化保险业务管理平台，为保险机构提供全面的业务管理功能，包括保单管理、理赔处理、财务管理、用户管理等核心业务模块。

### 目标用户
- 保险机构管理人员
- 保险业务员
- 系统管理员
- 财务人员

### 核心价值
- 🚀 **高效管理**: 提供完整的保险业务流程管理
- 🛡️ **安全可靠**: 基于 RBAC 权限控制，确保数据安全
- 📊 **数据驱动**: 丰富的图表和报表功能
- 🔧 **灵活配置**: 支持多种保险产品类型和业务模式

## 🏗️ 系统架构

### 技术栈

#### 前端框架
- **Vue.js 2.7.0** - 渐进式 JavaScript 框架
- **Vue Router 3.4.9** - 官方路由管理器
- **Vuex 3.6.0** - 状态管理模式
- **Element UI 2.15.12** - 基于 Vue 的组件库

#### 构建工具
- **Vue CLI 4.5.10** - Vue.js 开发工具
- **Babel** - JavaScript 编译器
- **ESLint** - 代码质量检查
- **Prettier** - 代码格式化

#### 图表和工具库
- **Chart.js 4.4.2** - 图表库
- **Vue Chart.js 5.3.1** - Vue 图表组件
- **Day.js 1.10.4** - 日期处理库
- **Axios 1.6.8** - HTTP 客户端

#### 样式和资源
- **Sass/SCSS** - CSS 预处理器
- **FontAwesome 5.15.1** - 图标库
- **WangEditor 4.6.17** - 富文本编辑器

### 项目目录结构

```
baoya-dash-fe/
├── public/                 # 静态资源
│   ├── favicon.ico
│   └── index.html
├── src/                    # 源代码
│   ├── apis/              # API 接口层
│   │   ├── admin.js       # 管理员相关接口
│   │   ├── auth.js        # 认证相关接口
│   │   ├── claim.js       # 理赔相关接口
│   │   ├── finance.js     # 财务相关接口
│   │   ├── policy.js      # 保单相关接口
│   │   └── ...
│   ├── assets/            # 静态资源
│   │   ├── images/        # 图片资源
│   │   └── svg/          # SVG 图标
│   ├── components/        # 公共组件
│   │   ├── globals/      # 全局组件
│   │   ├── data/         # 数据展示组件
│   │   ├── policy/       # 保单相关组件
│   │   ├── product/      # 产品相关组件
│   │   └── ...
│   ├── config/           # 配置文件
│   │   └── index.js      # 全局配置
│   ├── directives/       # 自定义指令
│   ├── layouts/          # 布局组件
│   ├── mixins/          # 混入
│   ├── router/          # 路由配置
│   │   ├── index.js     # 路由主文件
│   │   ├── routes.js    # 路由定义
│   │   └── modules/     # 模块路由
│   ├── store/           # Vuex 状态管理
│   │   ├── index.js     # Store 主文件
│   │   ├── actions.js   # 全局 Actions
│   │   ├── mutations.js # 全局 Mutations
│   │   └── modules/     # 模块 Store
│   ├── styles/          # 样式文件
│   ├── utils/           # 工具函数
│   │   ├── axios.js     # HTTP 请求封装
│   │   └── global.js    # 全局工具函数
│   ├── views/           # 页面组件
│   │   ├── Home.vue     # 首页
│   │   ├── claim/       # 理赔模块
│   │   ├── data/        # 数据统计
│   │   ├── finance/     # 财务管理
│   │   ├── policies/    # 保单管理
│   │   ├── product/     # 产品管理
│   │   ├── system/      # 系统管理
│   │   └── users/       # 用户管理
│   ├── App.vue          # 根组件
│   └── main.js          # 入口文件
├── package.json          # 项目配置
├── babel.config.cjs      # Babel 配置
└── README.md            # 项目文档
```

### 核心依赖包说明

| 包名 | 版本 | 用途 |
|------|------|------|
| vue | ^2.7.0 | 核心框架 |
| vue-router | ^3.4.9 | 路由管理 |
| vuex | ^3.6.0 | 状态管理 |
| element-ui | ^2.15.12 | UI 组件库 |
| axios | ^1.6.8 | HTTP 请求 |
| chart.js | ^4.4.2 | 图表库 |
| dayjs | ^1.10.4 | 日期处理 |
| wangeditor | ^4.6.17 | 富文本编辑器 |

## 🚀 开发环境搭建

### 环境要求

#### 系统要求
- **Node.js**: >= 14.0.0
- **npm**: >= 6.0.0 或 **yarn**: >= 1.22.0
- **操作系统**: Windows 10+, macOS 10.14+, Ubuntu 18.04+

#### 推荐开发工具
- **IDE**: Visual Studio Code
- **浏览器**: Chrome 80+, Firefox 75+, Safari 13+
- **Git**: >= 2.20.0

### 本地开发环境配置

#### 1. 克隆项目
```bash
git clone <repository-url>
cd baoya-dash-fe
```

#### 2. 安装依赖
```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install
```

#### 3. 环境变量配置
创建 `.env.local` 文件：
```bash
# API 基础地址
VUE_APP_BASE_API=http://localhost:8000/api

# 环境标识
NODE_ENV=development

# 应用名称
VUE_APP_TITLE=保呀保险业务管理系统
```

#### 4. 启动开发服务器
```bash
# 开发模式
npm run dev

# 或指定模式
npm run serve
```

#### 5. 构建项目
```bash
# 生产环境构建
npm run build:prod

# 测试环境构建
npm run build:staging
```

### 重要配置项说明

#### Vue CLI 配置
- **base**: 生产环境使用 `/dashboard/` 路径
- **mode**: 支持 `development`、`staging`、`production` 三种模式
- **legacy**: 启用 OpenSSL 旧版本兼容性

#### 路由配置
- **mode**: `history` 模式，支持 SEO
- **base**: 根据环境动态设置基础路径
- **beforeEach**: 全局路由守卫，处理认证和权限

#### 状态管理
- **modules**: 自动加载 `store/modules` 下的所有模块
- **persistence**: 支持状态持久化存储

## 🔧 核心功能模块

### 1. 认证与权限管理

**文件位置**: `src/views/system/`
- **核心功能**: 用户登录、权限控制、角色管理
- **关键组件**: 
  - `Login.vue` - 登录页面
  - `admins/Index.vue` - 管理员管理
  - `roles/Index.vue` - 角色管理
- **API路由**: `/api/auth/*`, `/api/admin/*`
- **技术特点**: JWT Token 认证，RBAC 权限模型

### 2. 保单管理模块

**文件位置**: `src/views/policies/`
- **核心功能**: 保单创建、编辑、查询、状态管理
- **关键组件**:
  - `Domestic.vue` - 国内保单
  - `Cbec.vue` - CBEC 保单
  - `offline/` - 线下保单管理
- **API路由**: `/api/policies/*`
- **技术特点**: 动态表单，多步骤流程，文件上传

### 3. 理赔管理模块

**文件位置**: `src/views/claim/`
- **核心功能**: 理赔申请、审核、处理、跟踪
- **关键组件**:
  - `Cases.vue` - 理赔案件列表
  - `CaseDetail.vue` - 案件详情
  - `CasesAcceptance.vue` - 理赔受理
- **API路由**: `/api/claims/*`
- **技术特点**: 工作流引擎，状态机，文档管理

### 4. 财务管理模块

**文件位置**: `src/views/finance/`
- **核心功能**: 保费管理、佣金结算、发票管理
- **关键组件**:
  - `CommissionPayment/` - 佣金支付
  - `PremiumPayment/` - 保费支付
  - `Invoice/` - 发票管理
- **API路由**: `/api/finance/*`
- **技术特点**: 财务计算引擎，多币种支持，审计日志

### 5. 产品管理模块

**文件位置**: `src/views/product/`
- **核心功能**: 产品配置、费率管理、条款编辑
- **关键组件**:
  - `Cbec.vue` - CBEC 产品
  - `Channels.vue` - 渠道管理
  - `offline/` - 线下产品
- **API路由**: `/api/products/*`
- **技术特点**: 可视化配置，模板引擎，版本控制

### 6. 数据统计模块

**文件位置**: `src/views/data/`
- **核心功能**: 业务数据统计、图表展示、报表生成
- **关键组件**:
  - `Charts.vue` - 图表展示
  - `Ranking.vue` - 排行榜
- **API路由**: `/api/statistics/*`
- **技术特点**: Chart.js 图表，实时数据更新，导出功能

### 7. 用户管理模块

**文件位置**: `src/views/users/`
- **核心功能**: 用户信息管理、产品配置、发票设置
- **关键组件**:
  - `Index.vue` - 用户列表
  - `Detail.vue` - 用户详情
- **API路由**: `/api/users/*`
- **技术特点**: 用户画像，个性化配置，权限继承

## 📐 技术规范

### 权限系统架构 (RBAC)

#### 权限模型
```javascript
// 权限结构
{
  roles: ['admin', 'manager', 'operator'],
  permissions: ['policy:read', 'policy:write', 'claim:process'],
  resources: ['policies', 'claims', 'users', 'finance']
}
```

#### 权限检查指令
```javascript
// src/directives/can.js
Vue.directive('can', {
  inserted(el, binding) {
    const permission = binding.value
    if (!hasPermission(permission)) {
      el.parentNode.removeChild(el)
    }
  }
})
```

### API 设计规范

#### 路由命名规范
```javascript
// RESTful API 设计
GET    /api/policies          // 获取保单列表
POST   /api/policies          // 创建保单
GET    /api/policies/:id      // 获取保单详情
PUT    /api/policies/:id      // 更新保单
DELETE /api/policies/:id      // 删除保单
```

#### 响应格式规范
```javascript
// 成功响应
{
  "success": true,
  "data": {...},
  "message": "操作成功"
}

// 错误响应
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "验证失败",
    "details": [...]
  }
}
```

### 数据库设计

#### 核心表结构
```sql
-- 用户表
CREATE TABLE users (
  id BIGINT PRIMARY KEY,
  username VARCHAR(50) UNIQUE,
  email VARCHAR(100),
  role_id BIGINT,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);

-- 保单表
CREATE TABLE policies (
  id BIGINT PRIMARY KEY,
  policy_number VARCHAR(50),
  user_id BIGINT,
  product_id BIGINT,
  status ENUM('draft', 'active', 'expired'),
  premium DECIMAL(10,2),
  created_at TIMESTAMP
);

-- 理赔表
CREATE TABLE claims (
  id BIGINT PRIMARY KEY,
  policy_id BIGINT,
  claim_number VARCHAR(50),
  status ENUM('pending', 'processing', 'approved', 'rejected'),
  amount DECIMAL(10,2),
  created_at TIMESTAMP
);
```

### 测试规范

#### 文件结构
```
tests/
├── unit/           # 单元测试
│   ├── components/ # 组件测试
│   ├── utils/      # 工具函数测试
│   └── store/      # Store 测试
├── integration/    # 集成测试
└── e2e/           # 端到端测试
```

#### 运行命令
```bash
# 运行单元测试
npm run test:unit

# 运行集成测试
npm run test:integration

# 运行 E2E 测试
npm run test:e2e

# 生成测试覆盖率报告
npm run test:coverage
```

## 🛠️ 开发工具

### 代码质量检查

#### ESLint 配置
```bash
# 检查代码质量
npm run lint

# 自动修复
npm run lint --fix
```

#### Prettier 格式化
```bash
# 格式化代码
npx prettier --write "src/**/*.{js,vue,scss}"
```

### 调试工具

#### Vue DevTools
```bash
# 安装 Vue DevTools 浏览器扩展
# Chrome: https://chrome.google.com/webstore/detail/vuejs-devtools
# Firefox: https://addons.mozilla.org/en-US/firefox/addon/vue-js-devtools/
```

#### 调试命令
```bash
# 开启调试模式
npm run dev -- --debug

# 查看构建分析
npm run build:prod -- --report
```

### 性能监控

#### 性能分析
```bash
# 生成性能报告
npm run build:prod -- --report

# 分析包大小
npm run build:prod -- --analyze
```

## 📝 代码规范

### 命名规范

#### 文件命名
```javascript
// 组件文件使用 PascalCase
PolicyList.vue
UserDetail.vue

// 工具文件使用 camelCase
axios.js
global.js

// 常量文件使用 UPPER_SNAKE_CASE
constants.js
```

#### 变量命名
```javascript
// 使用 camelCase
const userName = 'admin'
const policyList = []
const isVisible = true

// 常量使用 UPPER_SNAKE_CASE
const API_BASE_URL = 'http://api.example.com'
const MAX_RETRY_COUNT = 3
```

#### 组件命名
```javascript
// 组件名使用 PascalCase
export default {
  name: 'PolicyList',
  components: {
    PolicyCard,
    SearchPanel
  }
}
```

### 文件组织规范

#### 组件结构
```javascript
// 标准组件结构
<template>
  <!-- 模板内容 -->
</template>

<script>
// 导入语句
import { mapState, mapActions } from 'vuex'

export default {
  name: 'ComponentName',
  components: {},
  props: {},
  data() {
    return {}
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {}
}
</script>

<style lang="scss" scoped>
/* 样式内容 */
</style>
```

#### 目录组织
```
src/
├── components/     # 可复用组件
├── views/         # 页面组件
├── router/        # 路由配置
├── store/         # 状态管理
├── utils/         # 工具函数
├── apis/          # API 接口
└── styles/        # 样式文件
```

### 注释规范

#### 文件头部注释
```javascript
/**
 * @description: 保单管理组件
 * @author: 开发团队
 * @date: 2024-01-01
 * @lastEditors: 开发者姓名
 * @lastEditTime: 2024-01-01 10:00:00
 */
```

#### 函数注释
```javascript
/**
 * 创建保单
 * @param {Object} policyData - 保单数据
 * @param {string} policyData.policyNumber - 保单号
 * @param {number} policyData.premium - 保费
 * @returns {Promise<Object>} 创建的保单对象
 */
async function createPolicy(policyData) {
  // 函数实现
}
```

#### 组件注释
```javascript
/**
 * 保单列表组件
 * 
 * @example
 * <PolicyList 
 *   :policies="policyList"
 *   @select="handleSelect"
 * />
 */
export default {
  name: 'PolicyList',
  props: {
    policies: {
      type: Array,
      default: () => []
    }
  }
}
```

## 🚀 部署指南

### 生产环境配置

#### 环境变量
```bash
# .env.production
NODE_ENV=production
VUE_APP_BASE_API=https://api.baoya.com/api
VUE_APP_TITLE=保呀保险业务管理系统
```

#### 构建配置
```javascript
// vue.config.js
module.exports = {
  publicPath: process.env.NODE_ENV === 'production' ? '/dashboard/' : '/',
  outputDir: 'dist',
  assetsDir: 'static',
  productionSourceMap: false,
  configureWebpack: {
    optimization: {
      splitChunks: {
        chunks: 'all'
      }
    }
  }
}
```

### 服务器部署

#### Nginx 配置
```nginx
server {
    listen 80;
    server_name baoya.com;
    
    location /dashboard/ {
        alias /var/www/baoya-dash-fe/dist/;
        try_files $uri $uri/ /dashboard/index.html;
    }
    
    location /api/ {
        proxy_pass http://backend-server;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

#### Docker 部署
```dockerfile
# Dockerfile
FROM nginx:alpine
COPY dist/ /usr/share/nginx/html/
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### 环境变量配置

#### 开发环境
```bash
# .env.development
VUE_APP_BASE_API=http://localhost:8000/api
VUE_APP_ENV=development
```

#### 测试环境
```bash
# .env.staging
VUE_APP_BASE_API=https://staging-api.baoya.com/api
VUE_APP_ENV=staging
```

#### 生产环境
```bash
# .env.production
VUE_APP_BASE_API=https://api.baoya.com/api
VUE_APP_ENV=production
```

### 定时任务配置

#### 数据同步任务
```javascript
// 定时同步保单数据
setInterval(async () => {
  try {
    await syncPolicyData()
  } catch (error) {
    console.error('数据同步失败:', error)
  }
}, 5 * 60 * 1000) // 每5分钟执行一次
```

## 👥 团队协作

### Git 工作流

#### 分支策略
```bash
# 主分支
main          # 生产环境分支
develop       # 开发环境分支

# 功能分支
feature/xxx   # 新功能开发
bugfix/xxx    # 问题修复
hotfix/xxx    # 紧急修复
```

#### 提交规范
```bash
# 提交信息格式
<type>(<scope>): <subject>

# 类型说明
feat:     新功能
fix:      修复问题
docs:     文档更新
style:    代码格式调整
refactor: 代码重构
test:     测试相关
chore:    构建过程或辅助工具的变动

# 示例
feat(policy): 添加保单批量导入功能
fix(auth): 修复登录状态丢失问题
docs(readme): 更新项目文档
```

### 代码审查流程

#### 审查清单
- [ ] 代码符合项目规范
- [ ] 功能测试通过
- [ ] 单元测试覆盖
- [ ] 文档更新完整
- [ ] 性能影响评估
- [ ] 安全性检查

#### 审查工具
```bash
# 代码质量检查
npm run lint

# 测试覆盖率检查
npm run test:coverage

# 构建检查
npm run build:prod
```

### 文档维护要求

#### 文档类型
- **技术文档**: API 文档、架构文档
- **用户文档**: 操作手册、功能说明
- **开发文档**: 开发指南、部署文档

#### 文档更新流程
1. 功能开发完成后更新相关文档
2. 文档变更需要代码审查
3. 重要变更需要团队讨论
4. 定期检查和更新文档

## 🔧 故障排除

### 开发环境常见问题

#### 1. 依赖安装失败
```bash
# 清除缓存重新安装
rm -rf node_modules package-lock.json
npm cache clean --force
npm install
```

#### 2. 端口冲突
```bash
# 查看端口占用
lsof -i :8080

# 杀死进程
kill -9 <PID>

# 或使用不同端口
npm run dev -- --port 8081
```

#### 3. 构建失败
```bash
# 清除构建缓存
rm -rf dist/
npm run build:prod

# 检查 Node.js 版本
node --version
```

#### 4. 路由问题
```javascript
// 检查路由配置
// src/router/index.js
const router = new VueRouter({
  base: process.env.NODE_ENV === 'production' ? '/dashboard/' : '/',
  mode: 'history',
  routes: routes
})
```

### 性能优化建议

#### 1. 代码分割
```javascript
// 路由懒加载
const PolicyList = () => import('@/views/policies/PolicyList.vue')

// 组件懒加载
const HeavyComponent = () => import('@/components/HeavyComponent.vue')
```

#### 2. 图片优化
```javascript
// 使用 WebP 格式
<img src="image.webp" alt="图片" />

// 图片懒加载
<img v-lazy="imageUrl" alt="图片" />
```

#### 3. 缓存策略
```javascript
// API 缓存
const cache = new Map()
const cachedRequest = async (url) => {
  if (cache.has(url)) {
    return cache.get(url)
  }
  const response = await axios.get(url)
  cache.set(url, response)
  return response
}
```

#### 4. 打包优化
```javascript
// vue.config.js
module.exports = {
  configureWebpack: {
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all'
          }
        }
      }
    }
  }
}
```

### 调试技巧

#### 1. Vue DevTools 使用
```javascript
// 开启调试模式
Vue.config.devtools = true

// 组件调试
this.$log('组件数据:', this.data)
```

#### 2. 网络请求调试
```javascript
// Axios 拦截器调试
instance.interceptors.request.use(config => {
  console.log('请求配置:', config)
  return config
})

instance.interceptors.response.use(response => {
  console.log('响应数据:', response)
  return response
})
```

#### 3. 性能监控
```javascript
// 性能监控
const performanceObserver = new PerformanceObserver((list) => {
  for (const entry of list.getEntries()) {
    console.log('性能指标:', entry)
  }
})
performanceObserver.observe({ entryTypes: ['navigation', 'resource'] })
```

---

## 📞 联系方式

- **项目负责人**: 开发团队
- **技术支持**: <EMAIL>
- **问题反馈**: <EMAIL>

## 📄 许可证

本项目采用 MIT 许可证，详情请参阅 [LICENSE](LICENSE) 文件。

---

**最后更新**: 2024年1月1日  
**版本**: 2.0.0  
**维护者**: 保呀开发团队
