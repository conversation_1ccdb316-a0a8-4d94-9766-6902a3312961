# 图表组件功能增强总结

## 概述

根据您的需求，我为图表组件添加了两个重要功能：
1. **饼图百分比显示控制** - 通过参数控制饼图是否显示百分比格式
2. **柱状图数值显示控制** - 通过参数控制柱状图是否在柱体上显示数值

## 修改的文件

### 1. 核心组件修改

#### `src/views/metric/components/CardPie.vue`
- **新增参数**: `showPercentage: Boolean` (默认: false)
- **功能**: 控制饼图是否启用百分比模式
- **效果**:
  - `true`: 标签显示原始值，提示框显示 "原始值 (百分比%)" 格式，如 "1000元 (33.33%)"
  - `false`: 标签和提示框显示传递的值，如 "1000元 (33.33%)"

#### `src/views/metric/components/CardBar.vue`
- **新增参数**: `showLabel: Boolean` (默认: false)
- **功能**: 控制柱状图是否在柱体顶部显示数值
- **效果**:
  - `true`: 在柱体顶部显示数值，如 "65.5%"
  - `false`: 不显示柱体数值，仅在悬停时显示提示框

### 2. 业务页面修改

#### `src/views/metric/partials/Platform.vue`
- **各险种保费饼图**: 添加 `:show-percentage="true"` 显示百分比
- **各险种赔付率柱状图**: 添加 `:show-label="true"` 显示数值
- **简化数据处理**: 直接传入原始数据，组件内部自动转换百分比

#### `src/views/metric/partials/Company.vue`
- **总保费饼图**: 添加 `:show-percentage="true"` 显示百分比
- **移除**: 不必要的 `unit: '%'` 配置

#### `src/views/metric/partials/User.vue`
- **总保费饼图**: 添加 `:show-percentage="true"` 显示百分比
- **移除**: 不必要的 `unit: '%'` 配置

#### `src/views/metric/partials/Claim.vue`
- **赔付率柱状图**: 添加 `:show-label="true"` 显示数值
- **出险率柱状图**: 添加 `:show-label="true"` 显示数值

### 3. 测试文件

#### `src/views/metric/components/__tests__/CardPie.test.js`
- 测试 `showPercentage` 参数的功能
- 验证百分比和数值格式的正确显示

#### `src/views/metric/components/__tests__/CardBar.test.js`
- 测试 `showLabel` 参数的功能
- 验证柱体数值显示和隐藏
- 测试多系列数据支持

### 4. 文档

#### `src/views/metric/components/README.md`
- 详细的使用说明和示例
- 参数说明表格
- 最佳实践建议

## 技术实现细节

### 饼图百分比显示
```javascript
// 在 CardPie.vue 中
tooltip: {
  trigger: 'item',
  formatter: this.showPercentage
    ? `{a} <br/>{b} : {c}% ({d}%)`
    : `{a} <br/>{b} : {c}${this.seriesOption?.unit || '元'} ({d}%)`
},
label: {
  formatter: this.showPercentage
    ? `{name|{b}}\n{amount|{c}%}`
    : `{name|{b}}\n{amount|{c}${this.seriesOption?.unit || '元'}}`
}
```

### 柱状图数值显示
```javascript
// 在 CardBar.vue 中
label: this.showLabel ? {
  show: true,
  position: 'top',
  formatter: (params) => {
    const unit = item.unit || this.seriesOption?.unit || ''
    return `${params.data}${unit}`
  }
} : { show: false }
```

### 数据转换函数
```javascript
// CardPie 组件内部的自动转换方法
convertToPercentages(data) {
  if (!data || !Array.isArray(data) || data.length === 0) {
    return []
  }

  // 计算总值
  const total = data.reduce((sum, item) => {
    const value = parseFloat(item.value) || 0
    return sum + value
  }, 0)

  // 转换为百分比
  if (total === 0) {
    return data.map((item) => ({
      ...item,
      originalValue: item.value,
      value: 0
    }))
  }

  return data.map((item) => {
    const value = parseFloat(item.value) || 0
    return {
      ...item,
      originalValue: item.value,
      value: parseFloat(((value / total) * 100).toFixed(2))
    }
  })
}
```

## 使用示例

### 饼图百分比显示
```vue
<CardPie
  title="各险种保费分布"
  :show-percentage="true"
  :series-option="{
    name: '保费',
    data: value?.product_premium?.map((item) => ({
      name: item.product_type,
      value: parseFloat(item.premium / 100) || 0
    })) || []
  }"
/>
```

### 柱状图数值显示
```vue
<CardBar
  title="各险种赔付率"
  :show-label="true"
  :series-option="{
    name: '赔付率',
    unit: '%',
    categories: ['车险', '财险'],
    data: [65.5, 45.2]
  }"
/>
```

## 兼容性

- **向后兼容**: 所有新参数都有默认值，不会影响现有代码
- **可选功能**: 通过参数控制，可以根据需要启用或禁用
- **灵活配置**: 支持不同图表有不同的显示需求
- **自动转换**: 饼图组件内部自动处理百分比转换，无需手动调用转换函数

## 测试建议

1. 运行单元测试确保功能正常
2. 在浏览器中测试各个页面的图表显示
3. 验证百分比计算的准确性
4. 检查柱体数值显示的位置和格式

## 后续优化建议

1. 可以考虑添加更多的标签位置选项（如 bottom、inside 等）
2. 可以添加标签样式自定义选项
3. 可以考虑添加动画效果控制参数
