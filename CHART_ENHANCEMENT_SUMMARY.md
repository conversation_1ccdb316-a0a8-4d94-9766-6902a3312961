# 图表组件功能增强总结

## 概述

根据您的需求，我为图表组件添加了两个重要功能：
1. **饼图百分比显示控制** - 通过参数控制饼图是否显示百分比格式
2. **柱状图数值显示控制** - 通过参数控制柱状图是否在柱体上显示数值

## 修改的文件

### 1. 核心组件修改

#### `src/views/metric/components/CardPie.vue`
- **新增参数**: `showPercentage: Boolean` (默认: false)
- **功能**: 控制饼图标签和提示框是否显示百分比格式
- **效果**: 
  - `true`: 显示 "33.33%" 格式
  - `false`: 显示 "1000元" 格式

#### `src/views/metric/components/CardBar.vue`
- **新增参数**: `showLabel: Boolean` (默认: false)
- **功能**: 控制柱状图是否在柱体顶部显示数值
- **效果**:
  - `true`: 在柱体顶部显示数值，如 "65.5%"
  - `false`: 不显示柱体数值，仅在悬停时显示提示框

### 2. 业务页面修改

#### `src/views/metric/partials/Platform.vue`
- **各险种保费饼图**: 添加 `:show-percentage="true"` 显示百分比
- **各险种赔付率柱状图**: 添加 `:show-label="true"` 显示数值
- **新增方法**: `convertProductPremiumToPercentages()` 处理险种保费数据转换

#### `src/views/metric/partials/Company.vue`
- **总保费饼图**: 添加 `:show-percentage="true"` 显示百分比
- **移除**: 不必要的 `unit: '%'` 配置

#### `src/views/metric/partials/User.vue`
- **总保费饼图**: 添加 `:show-percentage="true"` 显示百分比
- **移除**: 不必要的 `unit: '%'` 配置

#### `src/views/metric/partials/Claim.vue`
- **赔付率柱状图**: 添加 `:show-label="true"` 显示数值
- **出险率柱状图**: 添加 `:show-label="true"` 显示数值

### 3. 测试文件

#### `src/views/metric/components/__tests__/CardPie.test.js`
- 测试 `showPercentage` 参数的功能
- 验证百分比和数值格式的正确显示

#### `src/views/metric/components/__tests__/CardBar.test.js`
- 测试 `showLabel` 参数的功能
- 验证柱体数值显示和隐藏
- 测试多系列数据支持

### 4. 文档

#### `src/views/metric/components/README.md`
- 详细的使用说明和示例
- 参数说明表格
- 最佳实践建议

## 技术实现细节

### 饼图百分比显示
```javascript
// 在 CardPie.vue 中
tooltip: {
  trigger: 'item',
  formatter: this.showPercentage 
    ? `{a} <br/>{b} : {c}% ({d}%)`
    : `{a} <br/>{b} : {c}${this.seriesOption?.unit || '元'} ({d}%)`
},
label: {
  formatter: this.showPercentage 
    ? `{name|{b}}\n{amount|{c}%}`
    : `{name|{b}}\n{amount|{c}${this.seriesOption?.unit || '元'}}`
}
```

### 柱状图数值显示
```javascript
// 在 CardBar.vue 中
label: this.showLabel ? {
  show: true,
  position: 'top',
  formatter: (params) => {
    const unit = item.unit || this.seriesOption?.unit || ''
    return `${params.data}${unit}`
  }
} : { show: false }
```

### 数据转换函数
```javascript
// 新增的 convertProductPremiumToPercentages 方法
convertProductPremiumToPercentages(productPremiumData) {
  if (!productPremiumData || !Array.isArray(productPremiumData)) return []
  
  const data = productPremiumData.map((item) => ({
    name: item.product_type,
    value: parseFloat(item.premium / 100) || 0
  }))
  
  return convertToPercentages(data, 'value')
}
```

## 使用示例

### 饼图百分比显示
```vue
<CardPie
  title="各险种保费分布"
  :show-percentage="true"
  :series-option="{
    name: '保费',
    data: convertProductPremiumToPercentages(value?.product_premium)
  }"
/>
```

### 柱状图数值显示
```vue
<CardBar
  title="各险种赔付率"
  :show-label="true"
  :series-option="{
    name: '赔付率',
    unit: '%',
    categories: ['车险', '财险'],
    data: [65.5, 45.2]
  }"
/>
```

## 兼容性

- **向后兼容**: 所有新参数都有默认值，不会影响现有代码
- **可选功能**: 通过参数控制，可以根据需要启用或禁用
- **灵活配置**: 支持不同图表有不同的显示需求

## 测试建议

1. 运行单元测试确保功能正常
2. 在浏览器中测试各个页面的图表显示
3. 验证百分比计算的准确性
4. 检查柱体数值显示的位置和格式

## 后续优化建议

1. 可以考虑添加更多的标签位置选项（如 bottom、inside 等）
2. 可以添加标签样式自定义选项
3. 可以考虑添加动画效果控制参数
