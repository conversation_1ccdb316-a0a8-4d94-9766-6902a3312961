<template>
  <div id="baoya">
    <theme-picker :default.sync="primaryColor" />

    <router-view />
  </div>
</template>

<script>
import ThemePicker from '@/components/theme-picker'
import { mapGetters } from 'vuex'

export default {
  name: 'App',
  components: {
    ThemePicker
  },
  computed: {
    ...mapGetters('platform', ['primaryColor'])
  },
  beforeCreate() {
    this.$store.dispatch('platform/findByDomain')
  },
  async created() {
    const currentVersion = await this.fetchAppVersion()
    console.log(`current version ${currentVersion}`)
    let dontRefresh = false
    if (currentVersion !== '') {
      setInterval(async () => {
        const newVersion = await this.fetchAppVersion()
        console.log(`remote version ${newVersion}`)
        if (newVersion !== '' && newVersion !== currentVersion) {
          console.log(`new version detected ${currentVersion} ${newVersion}`)

          try {
            if (dontRefresh) {
              console.log('the user refused to update to the new version')
              return
            }

            await this.$confirm('检测到新版本，是否立即刷新？', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })

            window.location.reload(true)
          } catch (e) {
            dontRefresh = true
          }
        }
      }, 60000)
    }
  },
  methods: {
    async fetchAppVersion() {
      const html = await (await fetch('', { cache: 'no-cache', redirect: 'follow' })).text()

      const regex = /app\.(\w+)\.js/
      const matches = html.match(regex)
      if (matches && matches.length > 1) {
        return matches[1]
      }

      return ''
    }
  }
}
</script>
