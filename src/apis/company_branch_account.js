import { del, get, post, put } from '../utils/axios'

// 获取保险公司出单公司账号.
export const getCompanyBranchAccounts = (companyId, companyBranchId) =>
  get(`companies/${companyId}/branches/${companyBranchId}/accounts`)

// 创建保险公司出单公司账号.
export const createCompanyBranchAccount = (companyId, companyBranchId, data) =>
  post(`companies/${companyId}/branches/${companyBranchId}/accounts`, data)

// 更新保险公司出单公司账号.
export const updateCompanyBranchAccount = (companyId, companyBranchId, accountId, data) =>
  put(`companies/${companyId}/branches/${companyBranchId}/accounts/${accountId}`, data)

// 删除保险公司出单公司账号.
export const deleteCompanyBranchAccount = (companyId, companyBranchId, accountId) =>
  del(`companies/${companyId}/branches/${companyBranchId}/accounts/${accountId}`)
