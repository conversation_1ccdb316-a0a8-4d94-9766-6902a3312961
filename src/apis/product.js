/*
 * @Descripttion:
 * @version:
 * @Author: yanb
 * @Date: 2021-04-06 14:29:47
 * @LastEditors: yanb
 * @LastEditTime: 2023-03-13 15:19:55
 */
import { del, get, patch, postFormDataOfArray, postFormData } from '../utils/axios'

// 获取产品.
export const getProducts = (queries) => get('products', queries)

// 获取平台/用户代理可用产品
export const getEnabledProducts = (userId, queries) => get(`products/enabled-products/${userId}`, queries)

// 获取产品.
export const getProduct = (id) => get(`products/${id}`)

// 创建产品.
export const createProduct = (data) => postFormDataOfArray('products', data)

// 更新产品.
export const updateProduct = (id, data) => postFormDataOfArray(`products/${id}`, data)

// 更新是否可用
export const updateProductEnabled = (id) => patch(`products/${id}/enabled`)

// 删除产品
export const deleteProduct = (id) => del(`products/${id}`)

// 技术配置
export const syncDeveloperConfigurations = (id, config) =>
  patch(`products/${id}/developer-configurations`, {
    config
  })

// 保单录入产品列表
export const getOfflineProducts = (data) => get('products/offline', data)
// 保单录入产品详情
export const getOfflineProduct = (id) => get(`products/offline/${id}`)
// 创建保单录入产品
export const createOfflineProduct = (data) => postFormData('products/offline', data)
// 更新保单录入产品
export const updateOfflineProduct = (id, data) => postFormData(`products/offline/${id}`, data)
// 禁/启用保单录入产品
export const updateEnabled = (id) => patch(`products/offline/${id}/enabled`)

// 保单录入产品分类列表
export const getOfflineProductCategories = (data) => get('products/offline/categories', data)
// 获取分类详情
export const getOfflineCategory = (id) => get(`products/offline/categories/${id}`)
// 创建保单录入产品分类
export const createOfflineProductCategory = (data) => postFormData('products/offline/categories', data)
// 更新保单录入产品分类
export const updateOfflineProductCategory = (id, data) => postFormData(`products/offline/categories/${id}`, data)

// 保单录入产品模板列表
export const getOfflineProductTemplates = (data) => get('products/offline/template', data)
// 创建保单录入产品模板
export const createOfflineProductTemplate = (data) => postFormData('products/offline/template', data)
// 更新保单录入产品模板
export const updateOfflineProductTemplate = (id, data) => postFormData(`products/offline/template/${id}`, data)

// 保单录入产品字段列表
export const getOfflineProductFields = (categoryId, data) =>
  get(`products/offline/categories/${categoryId}/fields`, data)
// 添加字段
export const createOfflineFields = (categoryId, data) =>
  postFormDataOfArray(`products/offline/categories/${categoryId}/fields`, data)
// 更新字段
export const updateOfflineFields = (categoryId, id, data) =>
  postFormDataOfArray(`products/offline/categories/${categoryId}/fields/${id}`, data)
// 删除字段
export const deleteOfflineFields = (categoryId, id) => del(`products/offline/categories/${categoryId}/fields/${id}`)
