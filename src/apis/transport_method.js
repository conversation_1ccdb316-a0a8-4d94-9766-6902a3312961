import { get, post, put, del } from '../utils/axios'

// 获取运输方式.
export const getTransportMethods = (companyId) => get(`companies/${companyId}/transport-methods`)

// 创建.
export const createTransportMethod = (companyId, data) => post(`companies/${companyId}/transport-methods`, data)

// 更新运输方式.
export const updateTransportMethod = (companyId, id, data) =>
  put(`companies/${companyId}/transport-methods/${id}`, data)

// 删除运输方式.
export const deleteTransportMethod = (companyId, id) => del(`companies/${companyId}/transport-methods/${id}`)
