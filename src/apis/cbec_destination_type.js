/*
 * @Descripttion:
 * @version:
 * @Author: yanb
 * @Date: 2023-11-30 17:23:21
 * @LastEditors: yanb
 * @LastEditTime: 2023-12-01 10:55:23
 */
import { get, post, put, del } from '../utils/axios'

// 获取目的地类型
export const getCbecDestinationTypes = (companyId) => get(`companies/${companyId}/cbec-destination-types`)

// 创建目的地类型.
export const createCbecDestinationType = (companyId, data) => post(`companies/${companyId}/cbec-destination-types`, data)

// 更新目的地类型.
export const updateCbecDestinationType = (companyId, id, data) =>
  put(`companies/${companyId}/cbec-destination-types/${id}`, data)

// 删除目的地类型.
export const deleteCbecDestinationType = (companyId, id) => del(`companies/${companyId}/cbec-destination-types/${id}`)
