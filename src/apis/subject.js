import { get, del, post, patch, put } from '../utils/axios'

export const getSubjects = () => get('subjects')

export const updateSubjectKeywordSuggestionStatus = (subjectId, data) =>
  patch(`subjects/${subjectId}/keywords-suggestion`, data)

export const getSubjectKeywords = (subjectId, data) => get(`subjects/${subjectId}/goods-keywords`, data)

export const addSubjectKeyword = (subjectId, data) => post(`subjects/${subjectId}/goods-keywords`, data)

export const convertToKeyword = (subjectId, keywordId, data) =>
  patch(`subjects/${subjectId}/goods-keywords/${keywordId}`, data)

export const deleteSubjectKeyword = (subjectId, keywordId) => del(`subjects/${subjectId}/goods-keywords/${keywordId}`)

export const updateSubjectCategoryStatus = (subjectId, categoryId, data) =>
  patch(`subjects/${subjectId}/categories/${categoryId}/status`, data)

export const getSubjectCategories = (subjectId, data) => get(`subjects/${subjectId}/categories`, data)

export const addSubjectCategory = (subjectId, data) => post(`subjects/${subjectId}/categories`, data)

export const updateSubjectCategory = (subjectId, categoryId, data) =>
  put(`subjects/${subjectId}/categories/${categoryId}`, data)

export const deleteSubjectCategory = (subjectId, categoryId) => del(`subjects/${subjectId}/categories/${categoryId}`)
