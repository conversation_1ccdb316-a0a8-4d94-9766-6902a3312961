/*
 * @Descripttion:
 * @version:
 * @Author: yanb
 * @Date: 2021-11-03 14:26:17
 * @LastEditors: yanb
 * @LastEditTime: 2022-12-09 10:04:49
 */
import { get, patch, post } from '../utils/axios'
import { platformKey } from '@/config'

const currentPlatform = JSON.parse(window.localStorage.getItem(platformKey) || '{}')

// 平台可配置产品.
export const getConfigurableProducts = (id, query) => get(`platforms/${id}/products/configurable`, query)

// 获取平台产品.
export const getPlatformProducts = (id, query) => get(`platforms/${id}/products`, query)

// 分配产品.
export const assignPlatformProduct = (id, data) => post(`platforms/${id}/products`, data)

// 统一配置产品.
export const assignPlatformProductMultiple = (id, data) => post(`platforms/${id}/products/multiple`, data)

// 更新产品状态
export const updateStatus = (id, productId, status) =>
  patch(`platforms/${id}/products/${productId}/statuses`, { status })

// 配置产品的保险公司.
export const getPlatformProductCompanies = () => get(`platforms/${currentPlatform.id}/products/companies`)
