/*
 * @Descripttion:
 * @version:
 * @Author: yanb
 * @Date: 2021-04-13 10:56:27
 * @LastEditors: yanb
 * @LastEditTime: 2024-01-24 15:38:57
 */
import { get, patch, postFormData } from '../utils/axios'

// 获取工单列表
export const getTickets = (params) => get('tickets', params)

// 工单详情
export const getTicket = (id) => get(`tickets/${id}`)

// 领取工单
export const receiveTicket = (id) => patch(`tickets/${id}/receive`)

// 退回工单
export const sendBackTicket = (id, data) => patch(`tickets/${id}/send-back`, data)

// 处理工单
export const handleTicket = (id, data) => postFormData(`tickets/${id}/handle`, data)

// 处理接口批改工单
export const handleApiTicket = (id, data) => postFormData(`tickets/${id}/handle-api`, data)

// 退回工单(补充资料)
export const sendBackSupplementInfoTicket = (id, data) => patch(`tickets/${id}/supplement-info`, data)
