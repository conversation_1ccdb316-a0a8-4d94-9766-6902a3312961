import { get, postFormData, put, del, postFormDataOfArray } from '../utils/axios'

// 获取雇主险产品列表
export const getGroupProducts = (data) => get('products', data)

// 创建雇主产品
export const createGroupProduct = (data) => postFormData('products', data)

// 更新雇主产品
export const updateGroupProduct = (id, data) => postFormDataOfArray(`products/${id}`, data)

// 获取雇主产品详情
export const getGroupProductDetails = (productId) => get(`products/${productId}`)

// 获取雇主产品套餐列表
export const getGroupProductPlans = (productId) => get(`products/${productId}/plans`)

// 创建雇主产品套餐
export const createGroupProductPlan = (productId, payload) => postFormData(`products/${productId}/plans`, payload)

// 更新雇主产品套餐
export const updateGroupProductPlan = (productId, planId, payload) =>
  put(`products/${productId}/plans/${planId}`, payload)

// 删除雇主产品套餐
export const deleteGroupProductPlan = (productId, planId) => del(`products/${productId}/plans/${planId}`)

// 获取雇主套餐详情
export const getGroupProductPlanDetail = (productId, planId) => get(`products/${productId}/plans/${planId}`)

// 获取雇主套餐工种详情
export const getProductPlanJobList = (productId, planId) => get(`products/${productId}/plans/${planId}/jobs`)

// 创建雇主套餐工种
export const createProductPlanJob = (productId, planId, payload) =>
  postFormData(`products/${productId}/plans/${planId}/jobs`, payload)

// 更新雇主工种
export const updateProductPlanJob = (productId, planId, jobId, payload) =>
  put(`products/${productId}/plans/${planId}/jobs/${jobId}`, payload)

export const deleteProductPlanJob = (productId, planId, jobId) =>
  del(`products/${productId}/plans/${planId}/jobs/${jobId}`)

// 产品状态修改
export const updateProductStatus = (productId, data) => put(`products/group/${productId}/status`, data)

// 产品技术配置
export const updateProductTechConfig = (productId, data) => put(`products/group/${productId}/config`, data)

// 导入工种信息
export const importProductPlanJobs = (productId, planId, data) =>
  postFormData(`products/${productId}/plans/${planId}/jobs/import`, data)
