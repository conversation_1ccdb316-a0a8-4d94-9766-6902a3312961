import { get, del, post, patch } from '../utils/axios'

// 获取推荐内容.
export const getAnnouncements = () => get('announcements')

// 创建推荐内容.
export const createAnnouncement = (data) => post('announcements', data)

// 删除推荐内容.
export const deleteAnnouncement = (id) => del(`announcements/${id}`)

// 置顶推荐内容.
export const pinAnnouncement = (id) => patch(`announcements/${id}/pinned`)

// 置顶推荐内容.
export const unpinAnnouncement = (id) => patch(`announcements/${id}/unpinned`)

// 撤销推荐内容.
export const unpublishAnnouncement = (id) => patch(`announcements/${id}/unpublished`)

// 发布推荐内容.
export const publishAnnouncement = (id) => patch(`announcements/${id}/published`)
