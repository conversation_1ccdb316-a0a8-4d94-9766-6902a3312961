// 保费应付
import * as _ from '../utils/axios'
import { tokenKey } from '@/config'
import qs from 'qs'

let baseUrl = process.env.VUE_APP_BASE_API
if (!process.env.VUE_APP_BASE_API.startsWith('http')) {
  baseUrl = `${window.location.origin}${baseUrl}`
}

// 保费应付列表
export const getPremiumPayments = (data) => _.post('finance/premium-payment', data)
// 应付保费统计
export const getPremiumPaymentCount = (data) => _.post('finance/premium-payment/count', data)
// 保费应付结算记录处理数据列表
export const premiumBillsPayments = (id, data) => _.get(`finance/premium-payment/bills/${id}/payments`, data)
// 添加保费应付支付记录
export const createPremiumPayment = (data) => _.postFormDataOfArray('finance/premium-payment/bills', data)
// 保费应付支付记录绑定审核员
export const associatePremiumPaymentAuditor = (id) =>
  _.postFormDataOfArray(`finance/premium-payment/bills/${id}/auditor`)
// 处理保费应付支付记录
export const handlePremiumPayment = (id) => _.postFormDataOfArray(`finance/premium-payment/bills/${id}/handle`)
// 退回保费应付支付记录
export const sendBackPremiumPayment = (id) => _.postFormDataOfArray(`finance/premium-payment/bills/${id}/send-back`)
// 暂存支付记录
export const draftPremiumPayment = (data) => _.postFormDataOfArray('finance/premium-payment/bills/draft', data)
// 暂存应付记录支付
export const premiumPaymentDraftBills = (id, data) =>
  _.postFormDataOfArray(`finance/premium-payment/bills/${id}/draft-payment`, data)
// 上传支付凭证
export const uploadPremiumPaymentProof = (id, data) =>
  _.postFormDataOfArray(`finance/premium-payment/bills/${id}/upload-proof`, data)
// 支付列表
export const premiumPaymentBills = (data) => _.get('finance/premium-payment/bills', data)
// 导出支付列表
export const exportPremiumPaymentBills = (data) => {
  return (
    baseUrl +
    `finance/premium-payment/bills/export?token=` +
    window.localStorage.getItem(tokenKey) +
    '&' +
    qs.stringify(data)
  )
}
// 保单列表导出
export const exportPremiumPayments = (data) => {
  return (
    baseUrl + `finance/premium-payment/export?token=` + window.localStorage.getItem(tokenKey) + '&' + qs.stringify(data)
  )
}

// 经纪费结算
export const getPoundagePayments = (data) => _.post(`finance/poundage-payment`, data)
// 应结经纪费统计
export const getPoundagePaymentCount = (data) => _.post(`finance/poundage-payment/count`, data)
// 经纪费结算记录处理数据列表
export const poundageBillsPayments = (id, data) => _.get(`finance/poundage-payment/bills/${id}/payments`, data)
//添加经纪费结算记录
export const createPoundagePayment = (data) => _.postFormDataOfArray('finance/poundage-payment/bills', data)
//经纪费结算支付记录绑定审核员
export const associatePoundagePaymentAuditor = (id) =>
  _.postFormDataOfArray(`finance/poundage-payment/bills/${id}/auditor`)
//处理经纪费结算记录
export const handlePoundagePayment = (id) => _.postFormDataOfArray(`finance/poundage-payment/bills/${id}/handle`)
//退回经纪费结算记录
export const sendBackPoundagePayment = (id) => _.postFormDataOfArray(`finance/poundage-payment/bills/${id}/send-back`)
// 暂存支付记录
export const draftPoundagePayment = (data) => _.postFormDataOfArray('finance/poundage-payment/bills/draft', data)
// 暂存应付记录支付
export const poundagePaymentDraftBills = (id, data) =>
  _.postFormDataOfArray(`finance/poundage-payment/bills/${id}/draft-payment`, data)
//经纪费结算记录列表
export const poundagePaymentBills = (data) => _.get('finance/poundage-payment/bills', data)
//导出列表
export const exportPoundagePaymentBills = (data) => {
  return (
    baseUrl +
    `finance/poundage-payment/bills/export?token=` +
    window.localStorage.getItem(tokenKey) +
    '&' +
    qs.stringify(data)
  )
}
//导出结算列表
// export const exportPoundagePayments = (data) => {
//   return (
//     baseUrl +
//     `finance/poundage-payment/export?token=` +
//     window.localStorage.getItem(tokenKey) +
//     '&' +
//     qs.stringify(data)
//   )
// }
export const exportPoundagePayments = (data) => _.post('finance/poundage-payment/export', data)

// 经纪费结算对比模板
export const poundageCheckTemplate = () => {
  return baseUrl + `finance/poundage-payment/poundage-check-template?token=` + window.localStorage.getItem(tokenKey)
}
// 经纪费结算对比
export const poundageCheck = (data) => _.postFormData(`finance/poundage-payment/poundage-check`, data)

// 佣金应付
export const getCommissionPayments = (data) => _.post(`finance/commission-payment`, data)
// 应付佣金统计
export const getCommissionPaymentCount = (data) => _.post(`finance/commission-payment/count`, data)
// 佣金结算记录处理数据列表
export const commissionBillsPayments = (id, data) => _.get(`finance/commission-payment/bills/${id}/payments`, data)
//添加佣金应付结算记录
export const createCommissionPayment = (data) => _.postFormDataOfArray(`finance/commission-payment/bills`, data)
//经纪费结算支付记录绑定审核员
export const associateCommissionPaymentAuditor = (id) =>
  _.postFormDataOfArray(`finance/commission-payment/bills/${id}/auditor`)
//处理经纪费结算记录
export const handleCommissionPayment = (id) => _.postFormDataOfArray(`finance/commission-payment/bills/${id}/handle`)
//退回经纪费结算记录
export const sendBackCommissionPayment = (id) =>
  _.postFormDataOfArray(`finance/commission-payment/bills/${id}/send-back`)
// 暂存支付记录
export const draftCommissionPayment = (data) => _.postFormDataOfArray('finance/commission-payment/bills/draft', data)
// 暂存应付记录支付
export const commissionPaymentDraftBills = (id, data) =>
  _.postFormDataOfArray(`finance/commission-payment/bills/${id}/draft-payment`, data)
// 上传支付凭证
export const uploadCommissionPaymentProof = (id, data) =>
  _.postFormDataOfArray(`finance/commission-payment/bills/${id}/upload-proof`, data)
//发放历史记录
export const commissionPaymentBills = (data) => _.get(`finance/commission-payment/bills`, data)
//导出列表
export const exportCommissionPaymentBills = () => {
  return baseUrl + `finance/commission-payment/bills/export?token=` + window.localStorage.getItem(tokenKey)
}
// 导出佣金发放处理数据
//
export const exportCommissionPayments = (data) => {
  return (
    baseUrl +
    `finance/commission-payment/export?token=` +
    window.localStorage.getItem(tokenKey) +
    '&' +
    qs.stringify(data)
  )
}

// 充值审核列表
export const rechargeList = (data) => _.get('finance/payments', data)
// 确认充值
export const sureRecharge = (id) => _.patch(`finance/payments/${id}/handle`)
// 充值申请退回
export const paymentSendBack = (id, data) => _.patch(`finance/payments/${id}/send-back`, data)
// 充值审核导出
export const exportPayments = (data) => {
  return (
    process.env.VUE_APP_BASE_API +
    `finance/payments/export?token=` +
    window.localStorage.getItem(tokenKey) +
    '&' +
    qs.stringify(data)
  )
}

// 申请发票
export const applyInvoice = (data) => _.post(`finance/invoices`, data)
// 全部开票
export const applyInvoiceAll = (data) => _.post(`finance/invoices/apply-invoice-all`, data)
// 可开票列表
export const canInvoicePolicies = (data) => _.get(`policies/can-invoice`, data)
// 发票列表
export const getInvoices = (data) => _.get(`finance/invoices`, data)
// 发票详情
export const invoiceDetail = (id) => _.get(`finance/invoices/${id}`)
// 开票
export const handleInvoice = (id, data) => _.postFormData(`finance/invoices/${id}/handle`, data)
// 退回
export const sendBackInvoice = (id, data) => _.patch(`finance/invoices/${id}/send-back`, data)
// 发送发票邮件
export const sendInvoiceMail = (id) => _.get(`finance/invoices/${id}/send-email`)
// 发票保单导出
export const invoicePolicyExport = (id) => {
  return baseUrl + `finance/invoices/${id}/policy-export?token=` + window.localStorage.getItem(tokenKey)
}
// 发票数据导出
export const exportInvoices = (data) => {
  return baseUrl + `finance/invoices/export?token=` + window.localStorage.getItem(tokenKey) + '&' + qs.stringify(data)
}
// 确认发票开出
export const issuedInvoice = (id) => _.get(`finance/invoices/${id}/issued`)
// 绑定操作员
export const associateOperator = (id) => _.post(`finance/invoices/${id}/operator`)

// 中意检查
export const check_zy = (id) => _.get(`/finance/invoices/${id}/check-applicant`)

// 保费应收
export const getPremiumReceivables = (data) => _.post('finance/premium-receivable', data)
// 应收数据统计
export const getPremiumReceivableCount = (data) => _.post('finance/premium-receivable/count', data)
// 保费应收处理
export const handlePremiumReceivable = (data) => _.postFormDataOfArray('finance/premium-receivable/bills', data)
// 保费应收暂存
export const draftPremiumReceivable = (data) => _.postFormDataOfArray('finance/premium-receivable/bills/draft', data)
// 退回暂存应收记录
export const sendBackDraftPremiumReceivableBills = (id) =>
  _.postFormDataOfArray(`finance/premium-receivable/bills/${id}/send-back`)
// 暂存应收记录收取
export const premiumReceivableDraftBills = (id, data) =>
  _.postFormDataOfArray(`finance/premium-receivable/bills/${id}/draft-receivable`, data)
// 保费应收收取记录
export const premiumReceivableBills = (data) => _.get('finance/premium-receivable/bills', data)
// 可销账保费应收数据
export const getCanSettlementReceivables = (data) => _.post('finance/premium-receivable/can-settlement', data)
// 可销账保费应收导出
export const exportCanSettlementReceivables = (data) => {
  return (
    baseUrl +
    `finance/premium-receivable/can-settlement/export?token=` +
    window.localStorage.getItem(tokenKey) +
    '&' +
    qs.stringify(data)
  )
}
// 已销账保费应收数据
export const getSettlementReceivables = (settlementId, data) =>
  _.get(`finance/premium-receivable/settlement/${settlementId}`, data)
// 保费应收结算记录处理数据列表
export const premiumBillsReceivables = (id, data) => _.get(`finance/premium-receivable/bills/${id}/receivables`, data)
// 保费应收收取记录导出
export const exportPremiumReceivableBills = () => {
  return baseUrl + `finance/premium-receivable/bills/export?token=` + window.localStorage.getItem(tokenKey)
}
// 保费应收结算记录处理数据列表导出
export const exportPremiumReceivables = (data) => {
  return (
    baseUrl +
    `finance/premium-receivable/export?token=` +
    window.localStorage.getItem(tokenKey) +
    '&' +
    qs.stringify(data)
  )
}

// 佣金应收
export const getCommissionReceivables = (data) => _.post('finance/commission-receivable', data)
// 应收佣金统计
export const getCommissionReceivableCount = (data) => _.post('finance/commission-receivable/count', data)
// 佣金应收处理
export const handleCommissionReceivable = (data) => _.postFormDataOfArray('finance/commission-receivable/bills', data)
// 暂存支付记录
export const draftCommissionReceivable = (data) =>
  _.postFormDataOfArray('finance/commission-receivable/bills/draft', data)

// 退回暂存应收记录
export const sendBackDraftCommissionReceivableBills = (id) =>
  _.postFormDataOfArray(`finance/commission-receivable/bills/${id}/send-back`)
// 暂存应付记录支付
export const commissionReceivableDraftBills = (id, data) =>
  _.postFormDataOfArray(`finance/commission-receivable/bills/${id}/draft-receivable`, data)
// 佣金应收收取记录
export const commissionReceivableBills = (data) => _.get('finance/commission-receivable/bills', data)
// 佣金应收结算记录处理数据列表
export const commissionBillsReceivables = (id, data) =>
  _.get(`finance/commission-receivable/bills/${id}/receivables`, data)
// 佣金应收收取记录导出
export const exportCommissionReceivableBills = () => {
  return baseUrl + `finance/commission-receivable/bills/export?token=` + window.localStorage.getItem(tokenKey)
}
// 佣金应收结算记录处理数据列表导出
export const exportCommissionReceivables = (data) => {
  return (
    baseUrl +
    `finance/commission-receivable/export?token=` +
    window.localStorage.getItem(tokenKey) +
    '&' +
    qs.stringify(data)
  )
}

// 平台出单费
export const getPlatformPremiums = (data) => _.get('finance/platform-premium', data)
// 平台出单费导出
export const exportPlatformPremiums = () => {
  return baseUrl + `finance/platform-premium/export?token=` + window.localStorage.getItem(tokenKey)
}

// 获取平台交易流水.
export const getPlatformTransactions = (data) => _.get('finance/platform-transactions', data)
// 导出平台交易流水
export const exportPlatformTransactions = (data) => {
  return (
    baseUrl +
    `finance/platform-transactions/export?token=` +
    window.localStorage.getItem(tokenKey) +
    '&' +
    qs.stringify(data)
  )
}

// 收款人列表
export const getPayees = (type) => _.get(`finance/memo/payees/${type}`)
// 水单列表
export const getMemos = (data) => _.get('finance/memo', data)
// 水单录入
export const createMemo = (data) => _.postFormData('finance/memo', data)
// 水单修改
export const updateMemo = (id, data) => _.postFormData(`finance/memo/${id}`, data)
// 水单删除
export const deleteMemo = (id) => _.del(`finance/memo/${id}`)
// 获取可销账水单
export const getCanSettlementMemos = (data) => _.get('finance/memo/can-settlement', data)
// 可销账水单导出
export const exportCanSettlementMemos = (data) => {
  return (
    baseUrl +
    `finance/memo/can-settlement/export?token=` +
    window.localStorage.getItem(tokenKey) +
    '&' +
    qs.stringify(data)
  )
}
// 已销账水单数据
export const getSettlementMemos = (settlementId, data) => _.get(`finance/memo/settlement/${settlementId}`, data)
// 水单数据导出
export const exportMemos = (data) => {
  return baseUrl + `finance/memo/export?token=` + window.localStorage.getItem(tokenKey) + '&' + qs.stringify(data)
}
// 水单批量录入
export const importMemos = (data) => _.postFormData('finance/memo/import', data)
// 获取水单批量导入模板
export const getMemoImportTemplateExcel = () => {
  return baseUrl + `finance/memo/template?token=` + window.localStorage.getItem(tokenKey)
}

// 销账处理列表
export const getSettlements = (data) => _.get('finance/settlement', data)
// 销账管理详情
export const getSettlement = (id) => _.get(`finance/settlement/${id}`)
// 销账申请
export const applySettlement = (data) => _.post('finance/settlement', data)
// 销账申请领取
export const receiveSettlement = (id) => _.get(`finance/settlement/${id}/receive`)
// 销账申请确认
export const handleSettlement = (id) => _.get(`finance/settlement/${id}/handle`)
// 销账申请退回
export const sendBackSettlement = (id, data) => _.post(`finance/settlement/${id}/send-back`, data)
// 销账数据导出
export const exportSettlement = (data) => {
  return baseUrl + `finance/settlement/export?token=` + window.localStorage.getItem(tokenKey) + '&' + qs.stringify(data)
}
// 销账数据导出
export const exportSettlementPolicies = (id) => {
  return baseUrl + `finance/settlement/${id}/policies-export?token=` + window.localStorage.getItem(tokenKey)
}
// 销账发送邮件
export const sendSettlementMail = (id) => _.get(`finance/settlement/${id}/send-mail`)
// 可销账保费应收保费数据
export const getCanSettlementPremiums = (data) => _.post('finance/premium-receivable/can-settlement-premiums', data)

// 财务综合查询
export const getMixedFinanceData = (data) => _.get(`finance/mixed`, data)
// 财务综合查询保费信息
export const getMixedFinancePremiumsData = (data) => _.get(`finance/mixed-premiums`, data)
// 财务综合查询导出
export const exportMixedFinanceData = (data) => _.get(`finance/mixed/export`, data)
