import { del, get, patch, post, postFormDataOfArray } from '../utils/axios'

/** --- 老保呀官网 --- */
// 获取询价管理列表
export const getEnquiries = (data) => get('enquiries', data)
// 获取询价详情
export const getEnquiry = (id) => get(`enquiries/${id}`)
// 询价处理
export const handleEnquiry = (id) => get(`enquiries/${id}/handle`)
/** --- end 老保呀官网 --- */

export const getInquiries = (data) => get('inquiries', data)

export const getInquiryDetail = (id) => get(`inquiries/${id}`)

export const createInquiry = (data) => postFormDataOfArray('inquiries', data)

export const deleteInquiry = (id) => del(`inquiries/${id}`)

export const updateInquiry = (id, data) => postFormDataOfArray(`inquiries/${id}`, data)

export const rejectInquiry = (id, data) => patch(`inquiries/${id}/rejection`, data)

export const fetchAvailableProducts = (id, data) => get(`inquiries/${id}/available-products`, data)

export const findRequest = (id, data) => get(`inquiries/${id}/available-request`, data)

export const createPolicy = (id, data) => post(`inquiries/${id}/policy`, data)

export const updateAlertContent = (id, text) => patch(`inquiries/${id}/alert`, { text })

export const sendInquiryRequest = (id, data) => post(`inquiries/${id}/requests`, data)

export const updateInquiryRequest = (id, requestId, data) => patch(`inquiries/${id}/requests/${requestId}`, data)

export const deleteInquiryRequest = (id, requestId) => del(`inquiries/${id}/requests/${requestId}`)

export const rejectInquiryRequest = (id, requestId, reason) =>
  patch(`inquiries/${id}/requests/${requestId}/rejection`, { remarks: reason })
