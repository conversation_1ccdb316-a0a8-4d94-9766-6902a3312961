import { get, post, put, del } from '../utils/axios'

export const saveCover = (companyId, companyBranchId, data) =>
  post(`/covers/${companyId}/branches/${companyBranchId}`, data)

export const getCover = (companyId, companyBranchId) => get(`/covers/${companyId}/branches/${companyBranchId}`)

export const getCoverDetails = (companyId, companyBranchId) =>
  get(`/covers/${companyId}/branches/${companyBranchId}/details`)

export const createCoverDetail = (companyId, companyBranchId, data) =>
  post(`/covers/${companyId}/branches/${companyBranchId}/details`, data)

export const updateCoverDetail = (companyId, companyBranchId, coverDetailId, data) =>
  put(`/covers/${companyId}/branches/${companyBranchId}/details/${coverDetailId}`, data)

export const deleteCoverDetail = (companyId, companyBranchId, coverDetailId) =>
  del(`/covers/${companyId}/branches/${companyBranchId}/details/${coverDetailId}`)

export const syncCover = (companyId, companyBranchId) =>
  post(`/covers/${companyId}/branches/${companyBranchId}/syncing`)
