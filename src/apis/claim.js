/*
 * @Descripttion:
 * @version:
 * @Author: yanb
 * @Date: 2023-08-09 14:33:27
 * @LastEditors: yanb
 * @LastEditTime: 2023-08-22 16:58:46
 */
import * as axios from '@/utils/axios'

import { tokenKey } from '@/config'
import qs from 'qs'

let baseUrl = process.env.VUE_APP_BASE_API
if (!process.env.VUE_APP_BASE_API.startsWith('http')) {
  baseUrl = `${window.location.origin}${baseUrl}`
}

export const fetchCases = (params) => axios.get('claims', params)

export const fetchCaseDetail = (id) => axios.get(`claims/${id}`)

export const buildCasesXlsxUrl = (params) =>
  baseUrl + `claims/xlsx?token=` + window.localStorage.getItem(tokenKey) + '&' + qs.stringify(params)

export const createCase = (data) => axios.post('claims', data)

export const uploadAttachment = (id, data) => axios.postFormData(`claims/${id}/attachments`, data)

export const deleteAttachment = (id, attachmentId) => axios.del(`claims/${id}/attachments/${attachmentId}`)

export const transferCase = (id, data) => axios.patch(`claims/${id}/transfer`, data)

export const updateCase = (id, data) => axios.put(`claims/${id}`, data)

export const acceptCase = (id) => axios.patch(`claims/${id}/accept`)

export const updateStatus = (id, status) => axios.patch(`claims/${id}/status`, { status })

export const sendBackCase = (id) => axios.patch(`claims/${id}/send-back`)

// done
export const archiveCase = (id, isTypicalCase) =>
  axios.patch(`claims/${id}/archived`, {
    is_typical_case: isTypicalCase
  })

// done
export const reopenCase = (id) => axios.patch(`claims/${id}/reopen`)

export const importPolicyFile = (id) => axios.post(`claims/${id}/attachments/copy`)

export const fetchBadges = (params) => axios.get('claims/badges', params)

export const renderEmailOfAdjuster = (id) => axios.get(`claims/${id}/adjuster-email`)

export const sendEmailToAdjuster = (id) => axios.post(`claims/${id}/adjuster-email`)
