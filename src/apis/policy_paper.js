import { get, patch } from '../utils/axios'
import { tokenKey } from '@/config'
import qs from 'qs'

export const getPolicyPapers = (data) => get('policy-papers', data)

export const processPolicyPaper = (id, data) => patch(`policy-papers/${id}/handle`, data)

// 退回纸质保单
export const sendBackPolicyPaper = (id, data) => patch(`policy-papers/${id}/send-back`, data)

export const buildExportHref = (data) => {
  let baseUrl = process.env.VUE_APP_BASE_API
  if (!process.env.VUE_APP_BASE_API.startsWith('http')) {
    baseUrl = `${window.location.origin}${baseUrl}`
  }

  return baseUrl + `policy-papers/export?token=` + window.localStorage.getItem(tokenKey) + '&' + qs.stringify(data)
}
