/*
 * @Descripttion:
 * @version:
 * @Author: yanb
 * @Date: 2021-04-06 16:58:06
 * @LastEditors: yanb
 * @LastEditTime: 2023-03-01 14:51:37
 */
import { get, post, put, patch, postFormData, del } from '../utils/axios'
import { tokenKey } from '@/config'
import qs from 'qs'

/**
 * 用户列表
 *
 * @param {*} params
 */
export const getUsers = (params) => get('users', params)

/**
 * 用户详情
 *
 * @param {*} id
 */
export const getUser = (id) => get(`users/${id}`)

/**
 * 添加用户
 *
 * @param {*} data
 */
export const createUser = (data) => postFormData(`users`, data)

/**
 * 修改用户
 *
 * @param {*} id
 * @param {*} data
 */
export const updateUser = (id, data) => postFormData(`users/${id}`, data)

/**
 * 充值
 *
 * @param {*} id
 * @param {*} data
 */
export const charge = (id, data) => patch(`users/${id}/balance`, data)

/**
 * 获取用户财务记录
 *
 * @param {*} id
 * @param {*} data
 */
export const getPayments = (id, data) => get(`users/${id}/payments`, data)

/**
 * 获取用户产品
 *
 * @param {*} id
 */
export const getUserProducts = (id) => get(`users/${id}/products`)

/**
 * 分配用户产品
 *
 * @param {*} id
 * @param {*} productId
 * @param {*} data
 */
export const assignUserProduct = (id, productId, data) => post(`users/${id}/products/${productId}`, data)

/**
 * 分配用户产品(多产品)
 *
 * @param {*} id
 * @param {*} data
 */
export const assignUserProductMultiple = (id, data) => post(`users/${id}/products/multiple`, data)

// 更新用户产品状态
export const updateUserProductStatus = (id, productId, status) =>
  patch(`users/${id}/products/${productId}/status`, { status })

/**
 * 重置密码
 *
 * @param {*} id
 */
export const resetPassword = (id, data) => patch(`users/${id}/password`, data)

/* 注册申请数 */
export const pendingCount = () => get('users/registrations/counting')

/* 获取注册审核数据 */
export const getRegistrations = (data) => get('users/registrations', data)

// 充值
export const recharge = (data) => postFormData('finance/payments', data)

// 用户数据导出
export const userExport = (params) => {
  let baseUrl = process.env.VUE_APP_BASE_API
  if (!process.env.VUE_APP_BASE_API.startsWith('http')) {
    baseUrl = `${window.location.origin}${baseUrl}`
  }

  return baseUrl + `users/export?token=` + window.localStorage.getItem(tokenKey) + '&' + qs.stringify(params)
}

/**
 * 禁/启用客户
 *
 *
 * @param {*} id
 * @returns
 */
export const enabledUser = (id) => patch(`users/${id}/enabled`)

/**
 * 业务员下属客户
 *
 * @param {*} id
 * @returns
 */
export const getSaleClients = (id) => get(`users/${id}/sale-clients`)

/**
 * 修改用户发票信息
 *
 * @param {*} id
 * @param {*} data
 */
export const updateUserInvoiceData = (id, data) => put(`users/${id}/update-invoice-data`, data)

/**
 * 用户投保预设信息设置
 *
 * @param {*} id
 * @param {*} data
 */
export const updateUserInsurePresetData = (id, data) => put(`users/${id}/update-insure-preset-data`, data)

/**
 * 生成 API 接口信息
 *
 * @param {*} id
 * @returns
 */
export const generateApiKeySecret = (id) => post(`users/${id}/key-secret`)

/**
 * 获取用户或代理
 *
 * @param {*} params
 */
export const getUsersOrAgents = (data) => get('users/users-or-agents', data)

/**
 * 获取用户关键字
 *
 * @param {*} id
 * @returns
 */
export const fetchUserGoodsKeywords = (id) => get(`users/${id}/goods-keywords`)

/**
 * 保存用户关键字
 *
 * @param {*} id
 * @param {*} data
 * @returns
 */
export const storeUserGoodsKeywords = (id, data) => post(`users/${id}/goods-keywords`, data)

/**
 * 更新用户关键字
 *
 * @param {*} id
 * @param {*} keywordId
 * @param {*} data
 * @returns
 */
export const updateUserGoodsKeywords = (id, keywordId, data) => put(`users/${id}/goods-keywords/${keywordId}`, data)

/**
 * 获取用户关键字
 *
 * @param {*} id
 * @param {*} keywordId
 * @returns
 */
export const deleteUserGoodsKeywords = (id, keywordId) => del(`users/${id}/goods-keywords/${keywordId}`)

/**
 * 获取用户标的条款
 *
 * @param {*} id
 * @returns
 */
export const getUserSubjectClauses = (id) => get(`users/${id}/subject-clauses`)

/**
 * 保存用户标的条款
 *
 * @param {*} id
 * @param {*} subjectClauseId
 * @param {*} data
 * @returns
 */
export const storeUserSubjectClauses = (id, data) => post(`users/${id}/subject-clauses`, data)

/**
 * 删除用户标的条款
 *
 * @param {*} id
 * @param {*} subjectClauseId
 * @returns
 */
export const deleteUserSubjectClauses = (id, subjectClauseId) => del(`users/${id}/subject-clauses/${subjectClauseId}`)

/**
 * 搜索用户
 *
 * @param {*} keyword
 */
export const search = (keyword) => get('users/search', { keyword })
