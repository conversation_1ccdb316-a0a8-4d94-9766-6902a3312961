import { del, get, postFormData } from '../utils/axios'

// 获取保险公司.
export const getCompanies = () => get('companies')

// 获取保险公司字典.
export const getCompaniesDict = (data) => get('companies/dict', data)

// 根据 ID 获取公司.
export const getCompany = (id) => get(`companies/${id}`)

// 创建公司.
export const createCompany = (data) => postFormData('companies', data)

// 更新公司.
export const updateCompany = (id, data) => postFormData(`companies/${id}`, data)

// 删除公司.
export const deleteCompany = (id) => del(`companies/${id}`)
