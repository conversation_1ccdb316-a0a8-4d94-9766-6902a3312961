/*
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2020-10-22 17:26:55
 * @LastEditors: yanb
 * @LastEditTime: 2024-07-18 10:59:24
 */
import { get, patch, post, postFormData, del } from '../utils/axios'
import { tokenKey } from '@/config'
import qs from 'qs'

let baseUrl = process.env.VUE_APP_BASE_API
if (!process.env.VUE_APP_BASE_API.startsWith('http')) {
  baseUrl = `${window.location.origin}${baseUrl}`
}

/**
 * 保单列表
 * @param { Object } data  { flag: [ 'CARGO_CHN'(国内), 'CARGO_INT'(国际) ]  }
 */
export const getPolicies = (data) => get('policies', data)

// 保单综合查询
export const getMixedPolicies = (data) => get('policies/mixed', data)

// 保单状态统计
export const countPoliciesStatuses = (data) => get('policies/statuses', data)

// 获取保单详情.
export const getPolicy = (id) => get(`policies/${id}`)

// 关联审核员.
export const associatePolicyAuditor = (id) => postFormData(`policies/${id}/auditor`)

// 审核保单.
export const auditPolicy = (id, data) => postFormData(`policies/${id}/auditing`, data)

// 重新提交.
export const resubmitPolicy = (id) => post(`policies/${id}/resubmit`)

// 下载保单地址
export const buildDownloadHref = (id) => {
  return baseUrl + `policies/${id}/download?token=` + window.localStorage.getItem(tokenKey)
}

// 导出
export const buildExportHref = (data) => {
  return baseUrl + `policies/export?token=` + window.localStorage.getItem(tokenKey) + '&' + qs.stringify(data)
}

export const buildMixedExportHref = (data) => {
  return baseUrl + `policies/mixed/export?token=` + window.localStorage.getItem(tokenKey) + '&' + qs.stringify(data)
}

// 导出
export const buildDownloadFilesHref = (data) => {
  return baseUrl + `policies/download-zip?token=` + window.localStorage.getItem(tokenKey) + '&' + qs.stringify(data)
}

// 退回保单.
export const sendBackPolicy = (id, data) => patch(`policies/${id}/send-back`, data)

// 退回保单(未付款).
export const sendBackUnPaidPolicy = (id, data) => patch(`policies/${id}/send-back-unpaid`, data)

// 退回保单(补充资料).
export const sendBackSupplementInfoPolicy = (id, data) => patch(`policies/${id}/send-back-supplement-info`, data)

/**
 * 保单详情
 * @param { String || Number } id  数据id
 */
export const getPolicyDetails = (id) => get(`policies/${id}`)

/**
 * 保单列表
 * @param { Object } data
 */
export const getHistoryPolicies = (data) => get('policies/histories', data)

/* 获取历史保单详情 */
export const getHistoryPolicyDetail = (id) => get(`policies/histories/${id}`)

export const getGroupPolicyList = (data) => get(`policies/group/list`, data)

// 获取雇主保单信息
export const getGroupPolicyInfo = (policyGroupId) => get(`policies/group/preview/${policyGroupId}`)

// 保单开始处理
export const disposeGroupPolicy = (policyId) => post(`policies/group/${policyId}/dispose`)

// 获取雇主保单详情
export const getGroupPolicy = (policyId) => get(`policies/group/${policyId}`)

// 雇主保单是审核
export const groupPolicyAudit = (policyGroupId, data) => postFormData(`policies/group/${policyGroupId}/auditing`, data)

// 获取雇主保单在保人员列表
export const getGroupInsuredEmployeeList = (policyGroupId, data) =>
  get(`policies/group/employees/${policyGroupId}`, data)

// 获取雇主好保单人员批单列表
export const getGroupEndorseList = (policyGroupId, data) => get(`policies/group/endorses/${policyGroupId}`, data)

// 获取雇主批单人员列表
export const getGroupEndorseEmployeeList = (endorseId, data) =>
  get(`policies/group/endorses/${endorseId}/employees`, data)

// 雇主人员批单提交
export const groupEndorseProcess = (endorseId, payload) => postFormData(`policies/group/endorses/${endorseId}`, payload)

// 雇主保单支付记录
export const getGroupTransactions = (policyGroupId, data) => get(`policies/group/transactions/${policyGroupId}`, data)

// 雇主投保邮件
export const sendGroupInsuredEmail = (policyGroupId) => post(`policies/group/${policyGroupId}/send-insure-mail`)

// 雇主保单退回
export const sendGroupPolicyBack = (policyGroupId, data) => post(`policies/group/${policyGroupId}/send-back`, data)

// 雇主保单退保
export const groupPolicySurrender = (policyGroupId, data) => post(`policies/group/${policyGroupId}/surrender`, data)

// 其他险种发送邮件
export const sendGeneralMail = (endorseId) => patch(`policies/general/${endorseId}/send-mail`)

// 其他险种保单审核
export const generalPolicyAudit = (policyId, data) => postFormData(`policies/general/${policyId}/auditing`, data)

// 其他险种保单退回
export const generalPolicySendBack = (policyId, data) => patch(`policies/general/${policyId}/send-back`, data)

// 雇主批单文件下载
export const buildGroupEndorseDownloadHref = (id, file) => {
  const token = window.localStorage.getItem(tokenKey)
  return baseUrl + `policies/group/endorses/detail/${id}/download?token=${token}&file=${file}`
}

// 获取可销账保单
export const getCanSettlementPolicies = (data) => get(`policies/can-settlement`, data)

// 线下录入保单列表
export const getOfflinePolicies = (data) => get('policies/offline', data)
// 线下录入保单导出
export const policyOfflineExport = (data) => {
  return baseUrl + `policies/offline/export?token=` + window.localStorage.getItem(tokenKey) + '&' + qs.stringify(data)
}
// 线下录入保单详情
export const getOfflinePolicy = (id) => get(`policies/offline/${id}`)
// 获取线下录入可投保产品
export const getOfflineInsureProductList = (data) => get(`policies/offline/product-list`, data)
// 线下录入保单表单渲染数据
export const getOfflinePolicyFormData = (id) => get(`policies/offline/${id}/form-data`)
// 创建线下录入保单
export const createOfflinePolicy = (data) => postFormData(`policies/offline`, data)
// 修改线下录入保单
export const updateOfflinePolicy = (id, data) => postFormData(`policies/offline/${id}`, data)
// 确认生效保单
export const identifyIssuePolicy = (id) => get(`policies/offline/${id}/identify-issue`)
// 删除保单
export const deleteOfflinePolicy = (id) => del(`policies/offline/${id}`)
// 线下录入保单退保
export const cancelOfflinePolicy = (id, data) => postFormData(`policies/offline/${id}/cancel`, data)
// 线下录入导入模板
export const offlineImportTemplate = (category_id) => {
  return baseUrl + `policies/offline/${category_id}/import-template?token=` + window.localStorage.getItem(tokenKey)
}
// 线下保单导入
export const importOfflinePolicy = (category_id, data) => postFormData(`policies/offline/${category_id}/import`, data)
// 批量处理线下录入保单
export const multipleHandleOfflinePolicy = (data) => post(`policies/offline/multiple-handle`, data)
// 获取批量处理保单
export const getMultipleOfflinePolicies = (data) => post('policies/offline/multiple', data)
// 通过保单号搜索保单
export const searchByPolicyNo = (policyNo) => get('policies/searching', { policy_no: policyNo })

// 导出太保投保单
export const exportCpicWord = (id) => {
  return baseUrl + `policies/${id}/export-cpic-word?token=` + window.localStorage.getItem(tokenKey)
}
