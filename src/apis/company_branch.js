import { del, get, post, put } from '../utils/axios'

// 获取保险公司出单公司.
export const getCompanyBranches = (companyId) => get(`companies/${companyId}/branches`)

// 创建保险公司出单公司.
export const createCompanyBranch = (companyId, data) => post(`companies/${companyId}/branches`, data)

// 更新保险公司出单公司.
export const updateCompanyBranch = (companyId, companyBranchId, data) =>
  put(`companies/${companyId}/branches/${companyBranchId}`, data)

// 删除保险公司出单公司.
export const deleteCompanyBranch = (companyId, companyBranchId) =>
  del(`companies/${companyId}/branches/${companyBranchId}`)
