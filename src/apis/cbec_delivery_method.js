/*
 * @Descripttion:
 * @version:
 * @Author: yanb
 * @Date: 2023-11-30 17:23:21
 * @LastEditors: yanb
 * @LastEditTime: 2023-12-01 10:18:17
 */
import { get, post, put, del } from '../utils/axios'

// 获取派送方式
export const getCbecDeliveryMethods = (companyId) => get(`companies/${companyId}/cbec-delivery-methods`)

// 创建派送方式.
export const createCbecDeliveryMethod = (companyId, data) => post(`companies/${companyId}/cbec-delivery-methods`, data)

// 更新派送方式.
export const updateCbecDeliveryMethod = (companyId, id, data) =>
  put(`companies/${companyId}/cbec-delivery-methods/${id}`, data)

// 删除派送方式.
export const deleteCbecDeliveryMethod = (companyId, id) => del(`companies/${companyId}/cbec-delivery-methods/${id}`)
