import { get, post, put } from '../utils/axios'

// 获取配置过费率的日期.
export const getConfiguredDates = () => get('currencies')

// 获取汇率详情.
export const getCurrenciesExchange = (date) => get(`currencies/${date}`)

// 创建汇率.
export const createCurrencyExchange = (data) => post(`currencies`, data)

// 更新汇率.
export const updateCurrencyExchange = (date, data) => put(`currencies/${date}`, data)

// 获取最近一次的汇率.
export const getLatestCurrencyExchange = () => get(`currencies/latest`)
