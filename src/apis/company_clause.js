import { get, postFormDataOfArray, del } from '../utils/axios'

// 获取条款.
export const getClauses = (companyId, filter = {}) =>
  get(`companies/${companyId}/clauses`, {
    filter: filter
  })

// 创建条款.
export const createClause = (companyId, data) => postFormDataOfArray(`companies/${companyId}/clauses`, data)

// 更新条款.
export const updateClause = (companyId, id, data) => postFormDataOfArray(`companies/${companyId}/clauses/${id}`, data)

// 删除条款.
export const deleteClause = (companyId, id) => del(`companies/${companyId}/clauses/${id}`)
