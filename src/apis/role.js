import { get, post, put, patch, del } from '../utils/axios'

// 获取角色列表
export const getRoles = () => get('roles')

// 获取角色详情
export const getRole = (id) => get(`roles/${id}`)

// 创建角色
export const createRole = (data) => post('roles', data)

// 更新角色
export const updateRole = (id, data) => put(`roles/${id}`, data)

// 同步权限
export const syncRolePermissions = (id, data) => patch(`roles/${id}/permissions`, data)

// 删除角色
export const deleteRole = (id) => del(`roles/${id}`)
