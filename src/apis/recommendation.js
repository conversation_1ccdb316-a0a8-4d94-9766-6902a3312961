import { get, postFormData, del } from '../utils/axios'

// 获取推荐内容.
export const getRecommendations = () => get('recommendations')

// 创建推荐内容.
export const createRecommendation = (data) => postFormData('recommendations', data)

// 更新推荐内容.
export const updateRecommendation = (id, data) => postFormData(`recommendations/${id}`, data)

// 删除推荐内容.
export const deleteRecommendation = (id) => del(`recommendations/${id}`)
