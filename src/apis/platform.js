import { get, postFormData, del, postFormDataOfArray } from '../utils/axios'

// 根据域名获取平台
export const findByDomain = (domain) =>
  get('platforms/domains', {
    domain
  })

// 获取平台
export const getPlatforms = (params) => get('platforms', params)

// 获取平台字典.
export const getPlatformsDict = () => get('platforms/dict')

// 根据 ID 获取详情.
export const getPlatform = (id) => get(`platforms/${id}`)

// 根据 ID 获取交易流水.
export const getPlatformTransaction = (id, data) => get(`platforms/${id}/transactions`, data)

export const platformCharge = (id, data) => postFormData(`platforms/${id}/charge`, data)

// 更新平台.
export const updatePlatform = (id, params) => postFormDataOfArray(`platforms/${id}`, params)

// 创建平台
export const createPlatform = (params) => postFormDataOfArray('platforms', params)

// 删除平台
export const deletePlatform = (id) => del(`platforms/${id}`)

// 根据名称获取平台
export const findPlatformByName = (name) =>
  get('platforms/search', {
    name
  })
