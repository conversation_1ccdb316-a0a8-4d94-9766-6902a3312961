/*
 * @Descripttion:
 * @version:
 * @Author: yanb
 * @Date: 2023-11-30 17:23:21
 * @LastEditors: yanb
 * @LastEditTime: 2023-11-30 17:25:07
 */
import { get, post, put, del } from '../utils/axios'

// 获取快递公司
export const getCbecExpressCompanies = (companyId) => get(`companies/${companyId}/cbec-express-companies`)

// 创建快递公司.
export const createCbecExpressCompany = (companyId, data) => post(`companies/${companyId}/cbec-express-companies`, data)

// 更新快递公司.
export const updateCbecExpressCompany = (companyId, id, data) =>
  put(`companies/${companyId}/cbec-express-companies/${id}`, data)

// 删除快递公司.
export const deleteCbecExpressCompany = (companyId, id) => del(`companies/${companyId}/cbec-express-companies/${id}`)
