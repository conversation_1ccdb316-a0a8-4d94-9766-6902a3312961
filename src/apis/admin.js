import { get, post, del, put, patch } from '../utils/axios'

// 获取当前登录用户
export const getLoggedAdmin = () => get('admins/logged')

// 获取销售员.
export const getSales = () => get(`admins/sales`)

// 获取指定角色下的用户
export const getClaimsAdjusters = () => get('admins/claims-adjusters')

// 转移用户.
export const transferUsers = (id, data) => put(`admins/${id}/transfer`, data)

// 获取管理员用户.
export const getAdminUsers = (id) => get(`admins/${id}/users`)

// 获取管理员
export const getAdmins = (params) => get('admins', params)

// 可绑定平台用户.
export const getAssociableAdmins = (params) => get('admins/association', params)

// 创建管理员
export const createAdmin = (data) => post('admins', data)

// 更新管理员
export const updateAdmin = (id, data) => put(`admins/${id}`, data)

// 删除管理员
export const deleteAdmin = (id) => del(`admins/${id}`)

// 更新密码
export const updatePassword = (data) => patch('admins/password', data)

// 更新个性化设置
export const updateOptions = (data) => patch('admins/options', data)
