import { get, post, put, del } from '@/utils/axios'

export const fetchFeatures = async (params) => {
  const data = await get('/features', params)
  return data.data
}

export const createFeature = async (data) => {
  await post('/features', data)
}

export const updateFeature = async (id, data) => {
  await put(`/features/${id}`, data)
}

export const deleteFeature = async (id) => {
  await del(`/features/${id}`)
}
