import { get, post, put, del } from '../utils/axios'

// 获取装载方式.
export const getLoadingMethods = (companyId) => get(`companies/${companyId}/loading-methods`)

// 创建.
export const createLoadingMethod = (companyId, data) => post(`companies/${companyId}/loading-methods`, data)

// 更新装载方式.
export const updateLoadingMethod = (companyId, id, data) => put(`companies/${companyId}/loading-methods/${id}`, data)

// 删除装载方式.
export const deleteLoadingMethod = (companyId, id) => del(`companies/${companyId}/loading-methods/${id}`)
