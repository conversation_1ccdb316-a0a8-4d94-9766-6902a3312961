import { get } from '../utils/axios'
import { tokenKey } from '@/config'
import qs from 'qs'

let baseUrl = process.env.VUE_APP_BASE_API
if (!process.env.VUE_APP_BASE_API.startsWith('http')) {
  baseUrl = `${window.location.origin}${baseUrl}`
}

export const fetchMetrics = async (params) => {
  const data = await get('/metrics', params)
  return data.data
}

export const fetchRanking = async (params) => {
  const data = await get('/metrics/ranking', params)
  return data.data
}

export const downloadRankingXlsx = (data) => {
  return baseUrl + `metrics/ranking/download?token=` + window.localStorage.getItem(tokenKey) + '&' + qs.stringify(data)
}

export const fetchFilterOptions = async () => {
  const data = await get('/metrics/filter-options')
  return data.data
}
