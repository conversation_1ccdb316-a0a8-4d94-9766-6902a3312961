import { get, post, put, del } from '../utils/axios'

// 获取包装方式.
export const getPackingMethods = (companyId) => get(`companies/${companyId}/packing-methods`)

// 创建.
export const createPackingMethod = (companyId, data) => post(`companies/${companyId}/packing-methods`, data)

// 更新包装方式.
export const updatePackingMethod = (companyId, id, data) => put(`companies/${companyId}/packing-methods/${id}`, data)

// 删除包装方式.
export const deletePackingMethod = (companyId, id) => del(`companies/${companyId}/packing-methods/${id}`)
