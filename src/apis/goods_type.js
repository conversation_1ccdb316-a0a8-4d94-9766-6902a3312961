import { get, post, put, del } from '../utils/axios'

// 获取货物类别.
export const getGoodsTypes = (companyId) => get(`companies/${companyId}/goods-types`)

// 创建货物类别.
export const createGoodsType = (companyId, data) => post(`companies/${companyId}/goods-types`, data)

// 更新货物类别.
export const updateGoodsType = (companyId, id, data) => put(`companies/${companyId}/goods-types/${id}`, data)

// 删除货物类别.
export const deleteGoodsType = (companyId, id) => del(`companies/${companyId}/goods-types/${id}`)
