/*
 * @Descripttion:
 * @version:
 * @Author: yanb
 * @Date: 2021-06-10 15:58:25
 * @LastEditors: yanb
 * @LastEditTime: 2022-03-21 14:49:47
 */
import * as _ from '../utils/axios'
import { patchFormData, del } from '../utils/axios'

//其他产品-产品列表
export const productList = (data) => _.get(`products/general`, data)
//其他产品-更新
export const updateOtherProduct = (id, data) => _.postFormData(`products/${id}`, data)
//其他险种-详情
export const productDetail = (id) => _.get(`products/general/${id}`)

// 删除产品
export const deleteProduct = (id) => _.del(`products/${id}`)

//其他险种-添加产品
export const addProduct = (data) => _.postFormData(`products`, data)

//保险分类列表
export const insuranceTypeList = (data) => _.get(`products/general/categories`, data)
//添加保险分类
export const addInsuranceType = (data) => _.post(`products/general/categories`, data)
//修改保险分类
export const EditInsuranceType = (id, data) => _.put(`products/general/categories/${id}`, data)
//删除保险分类
export const DelInsuranceType = (id) => _.del(`products/general/categories/${id}`)

//保险模型列表
export const ModelList = (data) => _.get(`products/general/models`, data)
//添加保险模型列表
export const AddModel = (data) => _.post(`products/general/models`, data)
//修改保险模型列表
export const EditModel = (id, data) => _.put(`products/general/models/${id}`, data)
//删除保险模型列表
export const DelModel = (id) => _.del(`products/general/models/${id}`)

//保险模型-字段管理
//字段列表
export const WordList = (_id, data) => _.get(`products/general/models/${_id}/fields`, data)
//字段添加
export const wordAdd = (_id, data) => _.postFormData(`products/general/models/${_id}/fields`, data)
//字段修改
export const wordEdit = (_id, id, data) => _.postFormData(`products/general/models/${_id}/fields/${id}`, data)
//字段删除
export const wordDel = (_id, id) => _.del(`products/general/models/${_id}/fields/${id}`)

//更新产品标签
export const updateLabel = (id, data) => _.patch(`/products/general/${id}/label`, data)
//保障权益
export const addBenefit = (id, data) => _.patch(`products/general/${id}/benefit`, data)
//相关资料
export const aboutFile = (id, data) => _.postFormData(`products/general/${id}/related_file`, data)

//投保须知
export const aboutNotice = (id, data) => _.patch(`products/general/${id}/notice`, data)
