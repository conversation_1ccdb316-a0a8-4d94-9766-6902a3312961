export default {
  computed: {
    searchPageHash() {
      return `search.caches.uid.${this.$store.getters['auth/admin'].id}.page.${this.$route.fullPath}`
    }
  },
  methods: {
    hasCached() {
      const cached = this.$store.getters['cache/item'](this.searchPageHash) || {}

      return Object.keys(cached).length > 0
    },
    restoreSearchCache(ref) {
      const cached = this.$store.getters['cache/item'](this.searchPageHash)
      if (cached !== undefined && Object.keys(cached).length > 0) {
        Object.keys(cached).forEach((key) => {
          ref[key] = cached[key]
        })
      }
      ref = Object.assign({}, ref)

      return ref
    },
    setSearchCache(value) {
      value = Object.assign({}, value)
      this.$store.dispatch('cache/setCache', {
        key: this.searchPageHash,
        value
      })
    },
    forgetSearchCache() {
      this.$store.dispatch('cache/forget', this.searchPageHash)
    }
  }
}
