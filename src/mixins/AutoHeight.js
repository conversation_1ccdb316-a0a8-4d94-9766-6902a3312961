/*
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2020-10-26 15:17:10
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-21 18:15:49
 * @Example:
 *
 *  el-table attrs
        id="auto-height"
        :height="autoHeight"
 *  import AutoHeight from '@/mixins/AutoHeight'
 *  =>
 *  mixins: [AutoHeight],
 */

/* eslint-disable */
export default {
  data() {
    return {
      autoHeight: '200px'
    }
  },
  mounted() {
    this.getAutoHeight()
    const _vm = this
    window.onresize = function () {
      _vm.getAutoHeight()
    }
  },
  methods: {
    // 这个方法用来动态设置 height
    getAutoHeight() {
      try {
        let el = document.querySelector('#auto-height')
        if (!el) return
        let elParent = el.parentNode,
          pt = this.getStyle(elParent, 'paddingTop'),
          pb = this.getStyle(elParent, 'paddingBottom')
        // 一定要使用 nextTick 来改变height 不然不会起作用
        this.$nextTick(() => {
          this.autoHeight = elParent.clientHeight - (pt + pb) + 'px'
        })
      } catch (e) {}
    },
    // 获取样式 我们需要减掉 padding-top， padding-bottom的值
    getStyle(obj, attr) {
      // 兼容IE浏览器
      let result = obj.currentStyle
        ? obj.currentStyle[attr].replace('px', '')
        : document.defaultView.getComputedStyle(obj, null)[attr].replace('px', '')
      return Number(result)
    }
  }
}
