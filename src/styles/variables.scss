// 主色调
$app-color-primary: #ff7f4c;
$app-color-blue: #409eff !default;
$app-color-success: #67c23a !default;
$app-color-warning: #e6a23c !default;
$app-color-danger: #f56c6c !default;
$app-color-info: #909399 !default;
$app-size-extra-large: 20px !default;
$app-size-large: 18px !default;
$app-size-medium: 16px !default;
$app-size-base: 14px !default;
$app-size-small: 13px !default;
$app-size-extra-small: 12px !default;
$app-size-mini: 10px !default;
// 间距
$app-space-extra-large: $app-size-extra-large;
// 间距
$app-space-mini: $app-size-mini;

// 布局 - header - 高度
$site-app-header-hight: 60px;
// 布局 - header - 背景颜色
$site-app-header-bg: #fff;
// 布局 - header - balance - icon - size
$site-app-header-balance-icon-size: 36px;
// 布局 - header - balance - label - padding
$site-app-header-balance-label-padding: 12px;
// 布局 - header - balance - radius
$site-app-header-balance-radius: 4px;
// 布局 - main - 背景颜色
$site-app-main-bg: #f6f6f6;
// 布局 - aside - 宽度
$site-app-aside-width: 240px;
// 布局 - aside - 收起宽度
$site-app-aside-width-collapsed: 70px;
$site-app-aside-width: 240px;
// 布局 - aside - 阴影
$site-app-aside-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1);
// 布局 - aside - 背景
$site-app-aside-bg: #364666;
// 布局 - aside - 背景 - opened
$site-app-aside-bg-opened: #303d56;
// 布局 - aside - 背景 - opened
$site-app-aside-menu-item-active-bg: #4d5974;
// 布局 - aside - border - right
$site-app-aside-border-right: 1px solid #ededed;
