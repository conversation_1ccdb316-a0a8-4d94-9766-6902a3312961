const state = {
  caches: new Map()
}

const mutations = {
  SET_CACHE(state, { key, value }) {
    state.caches.set(key, value)
  },
  FORGET_CACHE(state, key) {
    state.caches.delete(key)
  },
  FLUSH(state) {
    state.caches = new Map()
  }
}

const getters = {
  item: (state) => (key) => state.caches.get(key)
}

const actions = {
  setCache({ commit }, { key, value }) {
    commit('SET_CACHE', { key, value })
  },
  forget({ commit }, key) {
    commit('FORGET_CACHE', key)
  },
  flush({ commit }) {
    commit('FLUSH')
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
