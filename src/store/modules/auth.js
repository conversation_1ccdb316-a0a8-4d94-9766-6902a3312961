import { token<PERSON><PERSON>, admin<PERSON><PERSON> } from '@/config'
import { getLoggedAdmin } from '@/apis/admin'

const state = {
  admin: JSON.parse(window.localStorage.getItem(adminKey)) || {},
  token: ''
}

const getters = {
  admin: (state) => state.admin,
  roleName: (state) => state.admin?.roles[0]?.name,
  roleId: (state) => state.admin?.roles[0]?.id
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token

    window.localStorage.setItem(tokenKey, token)
  },
  SET_ADMIN: (state, admin) => {
    state.admin = admin

    window.localStorage.setItem(adminKey, JSON.stringify(admin))
  }
}

const actions = {
  setToken({ commit, dispatch }, data) {
    commit('SET_TOKEN', data.access_token)

    getLoggedAdmin().then((r) => {
      commit('SET_ADMIN', r.data)

      dispatch('app/fetchRolePermissions', {}, { root: true })
    })
  },
  refreshAdmin({ commit }) {
    getLoggedAdmin().then((r) => {
      commit('SET_ADMIN', r.data)
    })
  },
  removeToken() {
    window.localStorage.removeItem(tokenKey)
    window.localStorage.removeItem(adminKey)
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
