import { findByDomain as apiFindByDomain, getPlatform as apiFindPlatformById } from '@/apis/platform'
import { platformKey } from '@/config'
import logo from '@/assets/images/logo.png'
import logoCollapsed from '@/assets/images/logo_collapsed.png'
import { Message } from 'element-ui'

const state = {
  platform: JSON.stringify(window.localStorage.getItem(platformKey)) || {}
}

const mutations = {}

const getters = {
  name: (state) => state.platform.name || '赢睿科技平台管理系统',
  logo: (state) => state.platform.logo || '',
  favicon: (state) => state.platform.favicon || '',
  title: (state) => state.platform.title || '赢睿科技平台管理系统',
  slogan: (state) => state.platform.slogan || '',
  balance: (state) => state.platform.balance || 0,
  primaryColor: (state) => state.platform.primary_color || '#ff9429'
}

const actions = {
  findByDomain({ state }) {
    apiFindByDomain(window.location.hostname)
      .then((r) => {
        state.platform = r.data

        if (Object.keys(r.data).length <= 0) {
          state.platform.logo = logo
          state.platform.logoCollapsed = logoCollapsed
        }

        document.querySelector('link[rel=icon]').setAttribute('href', r.data.favicon || logoCollapsed)
        document.title = r.data.title || '赢睿科技平台管理系统'
      })
      .catch(() => {
        Message.error('未授权的访问')
      })
  },
  refresh({ state }, platformId) {
    if (platformId == -1) {
      state.platform = {
        title: '赢睿科技平台管理系统',
        logo: logo,
        logoCollapsed: logoCollapsed
      }
      window.localStorage.setItem(platformKey, JSON.stringify(state.platform))
      return
    }

    apiFindPlatformById(platformId).then((r) => {
      state.platform = r.data
      window.localStorage.setItem(platformKey, JSON.stringify(r.data))
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  getters,
  actions
}
