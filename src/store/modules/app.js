/*
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-07 21:36:30
 * @LastEditors: yanb
 * @LastEditTime: 2023-10-20 15:10:25
 */

import { array2Tree } from '@/utils'
import { getRoles, getRole } from '@/apis/role'
import { getBadges } from '@/apis/home'
import { getPermissions } from '@/apis/permission'
import { rolePermissionsKey, permissionsKey, rolesKey } from '@/config'

const state = {
  roles: JSON.parse(window.localStorage.getItem(rolesKey)) || [],
  permissions: JSON.parse(window.localStorage.getItem(permissionsKey)) || [],
  rolePermissions: JSON.parse(window.localStorage.getItem(rolePermissionsKey)) || {},
  badges: {
    Policies: 0,
    PoliciesDomestic: 0,
    PoliciesIntl: 0,
    PoliciesLbt: 0,
    PoliciesOther: 0,
    PoliciesGroup: 0,
    OfflinePolicies: 0,
    Tickets: 0,
    Finances: 0,
    Invoice: 0,
    Recharge: 0,
    Settlement: 0,
    ClaimCasesAcceptance: 0,
    AuditPremiumPaymentBill: 0,
    AuditPoundagePaymentBill: 0,
    AuditCommissionPaymentBill: 0,
    PremiumPayment: 0,
    PoundagePayment: 0,
    CommissionPayment: 0
  }
}

const mutations = {
  SET_ROLE_PERMISSIONS: (state, rolePermissions) => {
    state.rolePermissions = rolePermissions

    window.localStorage.setItem(rolePermissionsKey, JSON.stringify(rolePermissions))
  },
  SET_PERMISSIONS: (state, permissions) => {
    state.permissions = permissions

    window.localStorage.setItem(permissionsKey, JSON.stringify(permissions))
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles

    window.localStorage.setItem(rolesKey, JSON.stringify(roles))
  },
  SET_BADGES: (state, badges) => {
    let badgesMap = {
      PoliciesDomestic: badges.find((e) => e.type === 1)?.count || 0,
      PoliciesIntl: badges.find((e) => e.type === 2)?.count || 0,
      PoliciesCbec: badges.find((e) => e.type === 7)?.count || 0,
      PoliciesLbt: badges.find((e) => e.type === 3)?.count || 0,
      PoliciesOther: badges.find((e) => e.type === 4)?.count || 0,
      PoliciesGroup: badges.find((e) => e.type === 5)?.count || 0,
      OfflinePolicies: badges.find((e) => e.type === 6)?.count || 0,
      PoliciesPaper: badges.find((e) => e.type === 100)?.count || 0,
      Tickets: badges.find((e) => e.type === 101)?.count || 0,
      Invoice: badges.find((e) => e.type === 102)?.count || 0,
      Recharge: badges.find((e) => e.type === 103)?.count || 0,
      Settlement: badges.find((e) => e.type === 104)?.count || 0,
      ClaimCasesAcceptance: badges.find((e) => e.type === 105)?.count || 0,
      ClaimWorkplace: badges.find((e) => e.type === 106)?.count || 0,
      AuditPremiumPaymentBill: badges.find((e) => e.type === 108)?.count || 0,
      AuditPoundagePaymentBill: badges.find((e) => e.type === 109)?.count || 0,
      AuditCommissionPaymentBill: badges.find((e) => e.type === 110)?.count || 0,
      PremiumPayment: badges.find((e) => e.type === 111)?.count || 0,
      PoundagePayment: badges.find((e) => e.type === 112)?.count || 0,
      CommissionPayment: badges.find((e) => e.type === 113)?.count || 0
    }

    badgesMap.Policies =
      badgesMap.PoliciesDomestic +
      badgesMap.PoliciesIntl +
      badgesMap.PoliciesCbec +
      badgesMap.PoliciesLbt +
      badgesMap.PoliciesOther +
      badgesMap.PoliciesGroup +
      badgesMap.OfflinePolicies +
      badgesMap.PoliciesPaper

    badgesMap.Finances =
      badgesMap.Invoice +
      badgesMap.Recharge +
      badgesMap.Settlement +
      badgesMap.AuditPremiumPaymentBill +
      badgesMap.AuditPoundagePaymentBill +
      badgesMap.AuditCommissionPaymentBill
    badgesMap.Claim = badgesMap.ClaimCasesAcceptance + badgesMap.ClaimWorkplace

    state.badges = badgesMap
  }
}

const getters = {
  menus: (state) => {
    const _menus = state.rolePermissions.permissions?.filter((item) => item.is_menu)

    return array2Tree(_menus, 'id', 'parent_id', -1, 'display_name')
  },
  menuTree: (state) => {
    return array2Tree(state.rolePermissions.permissions || [], 'id', 'parent_id', -1, 'display_name')
  },
  rolePermissions: (state) => state.rolePermissions?.permissions || [],
  permissions: (state) => array2Tree(state.permissions, 'id', 'parent_id', -1, 'display_name'),
  roles: (state) => state.roles,
  badges: (state) => state.badges
}

const actions = {
  fetchPermissions({ commit, rootState }) {
    getPermissions().then((r) => {
      commit('SET_PERMISSIONS', r.data)
    })

    const roleId = rootState.auth.admin?.roles?.[0]?.id
    getRole(roleId).then((r) => {
      commit('SET_ROLE_PERMISSIONS', r.data)
    })
  },
  fetchRoles({ commit }) {
    getRoles().then((r) => {
      commit('SET_ROLES', r.data)
    })
  },
  fetchRolePermissions: ({ commit, rootState }) => {
    const roleId = rootState.auth.admin?.roles?.[0]?.id

    getRoles().then((r) => {
      commit('SET_ROLES', r.data)
    })

    getPermissions().then((r) => {
      commit('SET_PERMISSIONS', r.data)
    })

    getRole(roleId).then((r) => {
      commit('SET_ROLE_PERMISSIONS', r.data)
    })
  },
  fetchBadges({ commit }) {
    getBadges().then((r) => {
      commit('SET_BADGES', r.data)
    })
  },
  removeAll() {
    window.localStorage.removeItem(rolePermissionsKey)
    window.localStorage.removeItem(permissionsKey)
    window.localStorage.removeItem(rolesKey)
  }
}

const app = {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
export default app
