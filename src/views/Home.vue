<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2020-10-19 12:30:41
 * @LastEditors: yanb
 * @LastEditTime: 2021-10-29 18:48:38
-->
<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-y">
    <h1>平台主页</h1>

    <el-card shadow="never" class="basic">
      <div class="basic-heading">
        <div class="title">👏 {{ name }}, {{ admin.name }}</div>
        <div class="slogan" :style="{ color: primaryColor }">{{ slogan }}</div>
      </div>
      <el-divider class="h-line" />
      <div class="stats-card">
        <div class="stats-item">
          <div class="stats-icon">
            <div class="icon-circle">
              <img src="../assets/images/icons/home-icon-customers.svg" alt="" />
            </div>
          </div>
          <div class="stats-data">
            <div class="stats-data-name">客户数</div>
            <div class="stats-data-value">{{ statsCount.customers_count || 0 }}</div>
          </div>
        </div>

        <div class="stats-item">
          <div class="stats-icon">
            <div class="icon-circle">
              <img src="../assets/images/icons/home-icon-policy-count.svg" alt="" />
            </div>
          </div>
          <div class="stats-data">
            <div class="stats-data-name">当天保单数</div>
            <div class="stats-data-value">{{ statsCount.today_policies_count || 0 }}</div>
          </div>
        </div>

        <div class="stats-item">
          <div class="stats-icon">
            <div class="icon-circle">
              <img src="../assets/images/icons/home-icon-today-premium.svg" alt="" />
            </div>
          </div>
          <div class="stats-data">
            <div class="stats-data-name">当天保费</div>
            <div class="stats-data-value">{{ statsCount.today_premium || 0 }}</div>
          </div>
        </div>

        <div class="stats-item">
          <div class="stats-icon">
            <div class="icon-circle">
              <img src="../assets/images/icons/home-icon-total-count.svg" alt="" />
            </div>
          </div>
          <div class="stats-data">
            <div class="stats-data-name">历史保单数</div>
            <div class="stats-data-value">{{ statsCount.recent_policies_count || 0 }}</div>
          </div>
        </div>

        <div class="stats-item">
          <div class="stats-icon">
            <div class="icon-circle">
              <img src="../assets/images/icons/home-icon-total-premium.svg" alt="" />
            </div>
          </div>
          <div class="stats-data">
            <div class="stats-data-name">历史保费</div>
            <div class="stats-data-value">{{ statsCount.recent_premium || 0 }}</div>
          </div>
        </div>
      </div>
    </el-card>

    <el-row class="m-extra-large-t" :gutter="12">
      <el-col :span="12">
        <el-card class="chart-box" shadow="never" style="max-height: 504px">
          <span slot="header">月度保费统计</span>
          <el-tabs v-model="activeChart">
            <el-tab-pane v-for="chart in chartData" :label="chart.text" :name="chart.text" :key="chart.type">
              <div style="height: 335px">
                <bar-chart
                  :data="chart.chart"
                  :options="{
                    maintainAspectRatio: false,
                    responsive: true,
                    plugins: {
                      legend: {
                        display: false
                      }
                    }
                  }"
                />
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="never">
          <span slot="header">历史累计保费统计</span>
          <div class="stats-blocks">
            <div class="stats-block">
              <div class="stats-block-title">国内货运险</div>
              <div class="stats-block-content">
                <div>
                  <p>单数</p>
                  <p>{{ recentStats?.domestic?.count || 0 }}单</p>
                </div>
                <div>
                  <p>交易额</p>
                  <p>{{ recentStats?.domestic?.premium || 0 }}元</p>
                </div>
              </div>
            </div>
            <div class="stats-block">
              <div class="stats-block-title">单车货运险</div>
              <div class="stats-block-content">
                <div>
                  <p>单数</p>
                  <p>{{ recentStats?.lbt?.count || 0 }}单</p>
                </div>
                <div>
                  <p>交易额</p>
                  <p>{{ recentStats?.lbt?.premium || 0 }}元</p>
                </div>
              </div>
            </div>
            <div class="stats-block">
              <div class="stats-block-title">国际货运险</div>
              <div class="stats-block-content">
                <div>
                  <p>单数</p>
                  <p>{{ recentStats?.intl?.count || 0 }}单</p>
                </div>
                <div>
                  <p>交易额</p>
                  <p>{{ recentStats?.intl?.premium || 0 }}元</p>
                </div>
              </div>
            </div>
            <div class="stats-block">
              <div class="stats-block-title">跨境电商货运险</div>
              <div class="stats-block-content">
                <div>
                  <p>单数</p>
                  <p>{{ recentStats?.cbec?.count || 0 }}单</p>
                </div>
                <div>
                  <p>交易额</p>
                  <p>{{ recentStats?.cbec?.premium || 0 }}元</p>
                </div>
              </div>
            </div>
            <div class="stats-block">
              <div class="stats-block-title">雇主责任险</div>
              <div class="stats-block-content">
                <div>
                  <p>单数</p>
                  <p>{{ recentStats?.group?.count || 0 }}单</p>
                </div>
                <div>
                  <p>交易额</p>
                  <p>{{ recentStats?.group?.premium || 0 }}元</p>
                </div>
              </div>
            </div>
            <div class="stats-block">
              <div class="stats-block-title">线下录入保单</div>
              <div class="stats-block-content">
                <div>
                  <p>单数</p>
                  <p>{{ recentStats?.offline?.count || 0 }}单</p>
                </div>
                <div>
                  <p>交易额</p>
                  <p>{{ recentStats?.offline?.premium || 0 }}元</p>
                </div>
              </div>
            </div>
            <div class="stats-block">
              <div class="stats-block-title">通用财产险</div>
              <div class="stats-block-content">
                <div>
                  <p>单数</p>
                  <p>{{ recentStats?.other?.count || 0 }}单</p>
                </div>
                <div>
                  <p>交易额</p>
                  <p>{{ recentStats?.other?.premium || 0 }}元</p>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row class="m-extra-large-t" :gutter="12">
      <el-col :span="12">
        <el-card shadow="never">
          <span slot="header">保单信息</span>

          <define-table :cols="policyCols" :data="policies" />
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="never" class="finance-table">
          <span slot="header">财务信息</span>

          <define-table :cols="financeCols" :data="financeData" />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { aggregate } from '@/apis/home'
import { Bar as BarChart } from 'vue-chartjs'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  BarElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js'
import { Loading } from 'element-ui'

ChartJS.register(Title, Tooltip, Legend, PointElement, BarElement, CategoryScale, LinearScale)

export default {
  name: 'Home',
  components: {
    BarChart
  },
  computed: {
    ...mapGetters('platform', ['name', 'slogan', 'primaryColor']),
    ...mapGetters('auth', ['admin'])
  },
  data() {
    return {
      activeChart: '国内货运险',
      chartData: [],
      policyCols: [
        { label: '险种', prop: 'product_name' },
        { label: '流水号', prop: 'order_no' },
        { label: '更新时间', prop: 'created_at' },
        { label: '状态', prop: 'status', width: 80 },
        {
          label: '操作',
          align: 'center',
          width: 100,
          scopedSlots: {
            default: (scoped) => {
              return (
                <el-button
                  onClick={() => this.showDetail(scoped.row)}
                  type="primary"
                  plain
                  size="mini"
                  icon="el-icon-view"
                >
                  详情
                </el-button>
              )
            }
          }
        }
      ],
      policies: [],
      financeCols: [
        { label: '流水号', prop: 'order_no' },
        { label: '类型', prop: 'type' },
        { label: '金额', prop: 'amount' },
        { label: '退回原因', prop: 'reason' },
        { label: '状态', prop: 'status' }
      ],
      financeData: [],
      statsCount: {},
      recentStats: {},
      loading: null
    }
  },
  created() {
    this.$nextTick(() => {
      this.loading = Loading.service({
        fullscreen: false,
        target: '.site-container__content'
      })
      aggregate()
        .then((r) => {
          const data = r.data
          this.statsCount = data.stats_count
          this.recentStats = data.recent_stats
          this.chartData = data.chart_data

          this.financeData = data.charges
          this.policies = data.policies
        })
        .finally(() => {
          this.loading.close()
        })
    })
  },
  beforeRouteLeave(to, from, next) {
    next()
    this.loading.close()
  },
  methods: {
    showDetail(row) {
      const routes = {
        1: 'PoliciesDomesticDetails',
        2: 'PoliciesIntlDetails',
        3: 'PoliciesLbtDetails',
        4: 'PoliciesOtherDetails',
        5: 'PoliciesGroupDetails'
      }

      this.$router.push({ name: routes[row.type], params: { id: row.id, policyGroupId: row?.group_id } })
    }
  }
}
</script>

<style lang="scss" scoped>
.basic {
  .basic-heading {
    display: flex;
    justify-content: space-between;
    .title {
      font-size: 22px;
    }
    .slogan {
      font-size: 18px;
    }
  }

  .h-line {
    margin-top: 10px;
    margin-bottom: 10px;
  }

  .stats-card {
    display: flex;
    padding-top: 40px;
    padding-bottom: 40px;

    .stats-item {
      flex: 1;
      display: flex;
      text-align: center;
      padding-left: 20px;
      gap: 0px 20px;

      &:not(:first-child) {
        border-left: 1px solid #ebeef5;
      }

      .stats-icon {
        .icon-circle {
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }

      .stats-data {
        text-align: left;

        .stats-data-name {
          font-size: 16px;
        }

        .stats-data-value {
          margin-top: 10px;
          font-size: 24px;
        }
      }
    }
  }
}

.stats-blocks {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;

  .stats-block {
    background-color: #f7f8fa;
    padding: 10px;
    border-radius: 10px;

    .stats-block-title {
      font-size: 16px;
      margin-bottom: 10px;
    }

    .stats-block-content {
      display: flex;
      justify-content: space-between;
      background-color: #ffffff;
      padding: 10px;
      border-radius: 10px;
      font-weight: 300;

      div {
        flex: 1;
      }

      p {
        margin: 0;
        &:last-child {
          margin-top: 10px;
        }
      }
    }
  }
}

.finance-table {
  /deep/ .el-table__row {
    height: 45.5px;
    line-height: 45.5px;
  }
}
</style>
