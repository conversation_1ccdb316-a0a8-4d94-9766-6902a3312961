<template>
  <div class="w-100 p-extra-large-x m-extra-large-b o-hidden-x">
    <SearchPanel
      size="small"
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
    ></SearchPanel>
    <el-alert type="error" class="m-extra-large-t" :closable="false">
      <span>
        当前应付保费
        <el-tag type="danger" effect="dark"> {{ countPayment }} </el-tag>
      </span>
    </el-alert>
    <el-card shadow="never" class="box-card-options m-extra-large-t">
      <div class="d-flex">
        <el-button
          type="primary"
          v-can="{ name: 'finance.premium-payment.bills.create' }"
          @click="handlePayment"
          style="margin-bottom: 15px"
          >保费支付</el-button
        >
        <el-button
          type="primary"
          v-can="{ name: 'finance.premium-payment.bills.create' }"
          @click="allCheckout"
          style="margin-bottom: 15px"
          >全部处理</el-button
        >
        <el-button
          type="primary"
          v-can="{ name: 'finance.premium-payment.bills.draft' }"
          @click="draftBills"
          style="margin-bottom: 15px"
          >暂存记录</el-button
        >
        <el-button
          type="primary"
          v-can="{ name: 'finance.premium-payment.bills.index' }"
          @click="paymentBills"
          style="margin-bottom: 15px"
          >支付记录</el-button
        >
      </div>
      <DefineTable
        :cols="cols"
        :data="tableData"
        :paging="pagingAttrs"
        :pagingEvents="pagingEvents"
        :events="tableEvents"
        :rowKey="getRowKeys"
      ></DefineTable>
    </el-card>
  </div>
</template>

<script>
import { getPremiumPayments, getPremiumPaymentCount, exportPremiumPayments } from '@/apis/finance'
import { getPlatformProductCompanies } from '@/apis/platform_product'
import { getOfflineProductCategories } from '@/apis/product'
import { getPlatformsDict } from '@/apis/platform'
import { mapGetters } from 'vuex'
import { Loading } from 'element-ui'

export default {
  name: 'PremiumPayment',
  data() {
    return {
      searchFields: [
        {
          type: 'textarea',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'select',
          valKey: 'types',
          hintText: '险种',
          isMultiple: true,
          options: []
        },
        {
          type: 'select',
          valKey: 'company_id',
          hintText: '保险公司',
          onChangeReset: 'company_branch_id',
          options: []
        },
        {
          type: 'select',
          valKey: 'company_branch_id',
          hintText: '出单公司',
          options: []
        },
        {
          type: 'daterange',
          valKey: 'issued_at_range'
        },
        {
          type: 'input',
          valKey: 'policyholder',
          hintText: '投保人'
        }
      ],
      tableData: [],
      searchData: {},
      searchQuery: {},
      pagingAttrs: {
        currentPage: 1,
        pageSize: 15,
        layout: 'total, prev, pager, next, jumper',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.pagingAttrs.currentPage = page

          this.fetchPremiumPayments()
        }
      },
      tableEvents: {
        'selection-change': (val) => {
          this.multipleSelection = val
        }
      },
      multipleSelection: [],
      rawCompanies: [],
      platforms: [],
      offlineCategories: [],
      countPayment: 0
    }
  },
  computed: {
    ...mapGetters('auth', ['admin']),
    cols() {
      const cols = [
        { align: 'center', type: 'selection', width: '80', reserveSelection: true },
        // { label: '流水号', prop: 'order_no' },
        { label: '保单号', prop: 'policy.policy_no', width: '200', fixed: 'left' },
        {
          label: '险种',
          scopedSlots: {
            default: (scoped) => {
              const types = {
                1: '国内货运险',
                2: '国际货运险',
                3: '单车责任险',
                4: '其他险种',
                5: '雇主责任险',
                6: '线下录入保单',
                7: '跨境电商险'
              }

              return scoped.row.policy.type === 6 ? scoped.row.policy.offline_type : types[scoped.row.policy.type]
            }
          }
        },
        { label: '投保人', prop: 'policy.policyholder' },
        {
          label: '业务来源',
          prop: 'business_source.name',
          isHide: this.admin.platform.id != -1
        },
        { label: '应付保费', prop: 'premium' },
        {
          label: '应付对象',
          prop: 'payment_platform.name',
          isHide: this.admin.platform.id != -1
        },
        { label: '出单公司', prop: 'company_branch.name' },
        // { label: '来源', prop: 'f' },
        { label: '生效时间', prop: 'issued_at' },
        {
          label: '操作',
          align: 'center',
          width: '140',
          fixed: 'right',
          scopedSlots: {
            default: (scoped) => {
              const routes = {
                1: { name: 'PoliciesDomesticDetails', params: { id: scoped.row.policy.id } },
                2: { name: 'PoliciesIntlDetails', params: { id: scoped.row.policy.id } },
                3: { name: 'PoliciesLbtDetails', params: { id: scoped.row.policy.id } },
                4: { name: 'PoliciesOtherDetails', params: { id: scoped.row.policy.id } },
                5: { name: 'PoliciesGroupDetails', params: { policyGroupId: scoped.row.policy.policy_group_id } },
                6: { name: 'OfflinePolicyDetails', params: { id: scoped.row.policy.id } }
              }
              return (
                <el-button
                  onClick={() => this.$open(routes[scoped.row.policy.type])}
                  type="primary"
                  disabled={this.admin.platform.id != scoped.row.policy.platform_id}
                  plain
                  size="small"
                  icon="el-icon-view"
                >
                  详情
                </el-button>
              )
            }
          }
        }
      ]
      return cols.filter((e) => e.isHide === undefined || e.isHide === false)
    }
  },
  created() {
    if (this.admin.platform.id == -1) {
      this.searchFields.push(
        {
          type: 'select',
          valKey: 'business_source',
          hintText: '业务来源',
          options: []
        },
        {
          type: 'select',
          valKey: 'payment_platform',
          hintText: '应付对象',
          options: []
        }
      )
      getPlatformsDict({
        is_enabled: 1
      }).then((r) => {
        this.platforms = r.data

        this.loadBusinessSource()
        this.loadPaymentPlatform()
      })
    }
    getOfflineProductCategories({ is_parent: 0, is_pageable: 0 }).then((r) => {
      this.offlineCategories = r.data

      this.loadCategories()
    })
    getPlatformProductCompanies().then((r) => {
      this.rawCompanies = r.data

      this.loadCompanies()
      this.loadCompanyBranches()
    })
    this.fetchPremiumPayments()
    this.fetchCountPayment()
  },
  methods: {
    handleSearchPanel(command, data) {
      this.searchQuery = data
      this.pagingAttrs.currentPage = 1
      if (command === 'export') {
        this.paymentsExport()
      } else {
        this.fetchPremiumPayments()
        this.fetchCountPayment()
      }
    },
    getRowKeys(row) {
      return row.id
    },
    fetchPremiumPayments() {
      const loading = Loading.service()
      getPremiumPayments({
        page: this.pagingAttrs.currentPage,
        filter: this.searchQuery
      })
        .then((r) => {
          this.tableData = r.data

          this.pagingAttrs.currentPage = r.meta.current_page
          this.pagingAttrs.pageSize = r.meta.per_page
          this.pagingAttrs.total = r.meta.total
          loading.close()
        })
        .finally(() => loading.close())
    },
    fetchCountPayment() {
      getPremiumPaymentCount({
        filter: this.searchQuery
      }).then((r) => {
        this.countPayment = r.data.premium
      })
    },
    loadCompanies() {
      const options = this.rawCompanies.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('company_id', options)
    },
    loadCompanyBranches() {
      let options = []
      if (this.searchData?.company_id !== '' && this.searchData?.company_id !== undefined) {
        const company = this.rawCompanies.find((e) => e.id === this.searchData?.company_id)
        if (company) {
          options = company.branches.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      } else {
        this.rawCompanies.forEach((e) => {
          e?.branches.forEach((e) => {
            options.push({
              label: e.name,
              value: e.id
            })
          })
        })
      }

      this.assignSelectOptions('company_branch_id', options)
    },
    loadBusinessSource() {
      const platfrom_ops = this.platforms.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('business_source', platfrom_ops)
    },
    loadPaymentPlatform() {
      const platfrom_ops = this.platforms.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('payment_platform', platfrom_ops)
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    },
    handlePayment() {
      if (!this.multipleSelection.length) {
        this.$message({
          type: 'warning',
          message: '请至少选择一项'
        })
        return
      }
      this.$router.push({
        name: 'HandlePremiumPayment',
        query: {
          id: this.multipleSelection.map((e) => e.id).join(',')
        }
      })
    },
    paymentBills() {
      this.$router.push({
        name: 'PremiumPaymentBills'
      })
    },
    draftBills() {
      this.$router.push({
        name: 'DraftPremiumPaymentBills'
      })
    },
    paymentsExport() {
      const link = exportPremiumPayments({ filter: this.searchQuery })
      window.open(link, '_blank')
    },
    allCheckout() {
      this.$router.push({
        name: 'HandlePremiumPayment',
        query: {
          all_checkout: 1,
          ...this.searchQuery
        }
      })
    },
    loadCategories() {
      var options = [
        {
          label: '国内货运险',
          value: 1
        },
        {
          label: '国际货运险',
          value: 2
        },
        {
          label: '单车责任险',
          value: 3
        },
        {
          label: '其他险种',
          value: 4
        },
        {
          label: '雇主责任险',
          value: 5
        }
      ]
      options = options.concat(
        this.offlineCategories.map((e) => {
          return {
            label: e.name + '-线下录入',
            value: 'OF-' + e.id
          }
        })
      )
      this.assignSelectOptions('types', options)
    }
  },
  watch: {
    searchData: {
      immediate: true,
      deep: true,
      handler(value, old) {
        if (old?.company_id !== value?.company_id) {
          this.loadCompanyBranches()
        }
      }
    }
  }
}
</script>

<style scoped>
.documents {
  padding: 0 20px;
}
.el-select,
.el-select--small {
  width: 100%;
}
.box-card-options {
  margin-bottom: 20px;
}
</style>

<style lang="scss" scoped>
.policy-search {
  /deep/.el-select,
  /deep/.el-date-editor {
    width: 100% !important;
  }
  /deep/.el-card__body {
    display: flex;
  }
}
</style>
