<template>
  <div class="w-100 invoice">
    <SearchPanel
      size="small"
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
    ></SearchPanel>
    <el-card shadow="never" class="box-card-options m-extra-large-t" :body-style="{ padding: '20px' }">
      <DefineTable :cols="cols" :data="tableData" :paging="pagingAttrs" :pagingEvents="pagingEvents"></DefineTable>
    </el-card>
  </div>
</template>

<script>
import {
  commissionPaymentBills,
  exportCommissionPaymentBills,
  exportCommissionPayments,
  associateCommissionPaymentAuditor,
  handleCommissionPayment,
  sendBackCommissionPayment
} from '@/apis/finance' //exportCommissionPaymentBills
import { tokenKey } from '@/config'
import { Loading } from 'element-ui'
import { mapGetters } from 'vuex'

export default {
  name: 'CommissionPaymentBills',
  data() {
    return {
      searchFields: [
        {
          type: 'textarea',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'input',
          valKey: 'operator',
          hintText: '处理人'
        },
        {
          type: 'input',
          valKey: 'payment_name',
          hintText: '佣金收取方'
        },
        {
          type: 'select',
          valKey: 'audit_status',
          hintText: '状态',
          options: [
            {
              label: '已提交',
              value: 1
            },
            {
              label: '审核中',
              value: 2
            }
          ]
        },
        {
          type: 'daterange',
          hintText: '处理',
          valKey: 'operation_at_range'
        }
      ],
      cols: [
        { label: '流水号', prop: 'order_no' },
        {
          label: '单据数',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    type="primary"
                    onClick={() => {
                      this.$router.push({
                        name: 'AuditCommissionPaymentBillPayments',
                        params: {
                          id: scoped.row.id
                        }
                      })
                    }}
                  >
                    {scoped.row.payment_num}
                  </el-link>
                </div>
              )
            }
          }
        },
        // { label: '保单数', prop: 'policy_num' },
        { label: '客户保费总数', prop: 'premium' },
        { label: '佣金应发', prop: 'commission' },
        { label: '佣金实发', prop: 'actual_commission' },
        {
          label: '佣金收取方',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.payment_data.is_own_platform) {
                return <span class="text-info">{scoped.row.payment_data.payment_name}</span>
              } else {
                return <span class="text-primary">{scoped.row.payment_data.payment_name}</span>
              }
            }
          }
        },
        { label: '处理人', prop: 'operation.name' },
        { label: '处理时间', prop: 'operation_at' },
        {
          label: '状态',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.operation.id != -1) {
                return '审核中'
              }

              return '已提交'
            }
          }
        },
        { label: '备注', prop: 'remark' },
        // JSX 插槽
        // {
        //   label: '支付凭证',
        //   align: 'center',
        //   scopedSlots: {
        //     default: (scoped) => {
        //       if (scoped.row.proof) {
        //         return (
        //           <div>
        //             <el-link
        //               type="primary"
        //               href={`${scoped.row.proof}?token=` + window.localStorage.getItem(tokenKey)}
        //               download=""
        //               target="_blank"
        //             >
        //               点击查看
        //             </el-link>
        //           </div>
        //         )
        //       } else {
        //         return '-'
        //       }
        //     }
        //   }
        // },
        {
          label: '操作',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.operation.id == -1) {
                return (
                  <div>
                    <el-link
                      style="margin-right: 10px"
                      type="primary"
                      plain
                      size="small"
                      v-can={{ name: 'finance.commission-payment.bills.auditor' }}
                      onClick={() => this.associateAuditor(scoped.row.id)}
                    >
                      领取
                    </el-link>
                    <el-link
                      type="primary"
                      plain
                      size="small"
                      v-can={{ name: 'finance.commission-payment.bills.export' }}
                      href={exportCommissionPayments({ bill_id: scoped.row.id })}
                      download=""
                      target="_blank"
                    >
                      导出清单
                    </el-link>
                  </div>
                )
              } else {
                return (
                  <div>
                    <el-link
                      style="margin-right: 10px"
                      type="primary"
                      plain
                      size="small"
                      v-can={{ name: 'finance.commission-payment.bills.handle' }}
                      onClick={() => this.handlePayment(scoped.row.id)}
                      disabled={scoped.row.operation.id !== this.admin.id}
                    >
                      确认支付
                    </el-link>
                    <el-link
                      style="margin-right: 10px"
                      type="primary"
                      plain
                      size="small"
                      v-can={{ name: 'finance.commission-payment.bills.send-back' }}
                      onClick={() => this.sendBackPayment(scoped.row.id)}
                      disabled={scoped.row.operation.id !== this.admin.id}
                    >
                      退回
                    </el-link>
                  </div>
                )
              }
            }
          }
        }
      ],
      tableData: [],
      pagingAttrs: {
        align: 'center',
        currentPage: 1,
        pageSize: 15,
        layout: 'total, prev, pager, next, jumper',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.pagingAttrs.currentPage = page

          this.fetchCommissionPaymentBills()
        }
      }
    }
  },
  computed: {
    ...mapGetters('auth', ['admin'])
  },
  mounted() {
    this.fetchCommissionPaymentBills()
  },
  methods: {
    handleSearchPanel(command, data) {
      this.searchQuery = data
      this.pagingAttrs.currentPage = 1
      if (command === 'export') {
        this.exportData()
      } else {
        this.fetchCommissionPaymentBills()
      }
    },
    fetchCommissionPaymentBills() {
      const loading = Loading.service()
      commissionPaymentBills({
        page: this.pagingAttrs.currentPage,
        filter: Object.assign({ status: 1 }, this.searchQuery)
      })
        .then((r) => {
          this.tableData = r.data

          this.pagingAttrs.currentPage = r.meta.current_page
          this.pagingAttrs.pageSize = r.meta.per_page
          this.pagingAttrs.total = r.meta.total

          loading.close()
        })
        .finally(() => loading.close())
    },
    associateAuditor(id) {
      this.$confirm('是否确定领取?', '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        type: 'warning'
      }).then(() => {
        associateCommissionPaymentAuditor(id).then(() => {
          this.$message.success('领取成功')
          this.fetchCommissionPaymentBills()
        })
      })
    },
    handlePayment(id) {
      this.$confirm('是否确定完成支付?', '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        type: 'warning'
      }).then(() => {
        handleCommissionPayment(id).then(() => {
          this.$message.success('操作成功')
          this.fetchCommissionPaymentBills()
        })
      })
    },
    sendBackPayment(id) {
      this.$confirm('是否确定退回?', '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        type: 'warning'
      }).then(() => {
        sendBackCommissionPayment(id).then(() => {
          this.$message.success('操作成功')
          this.fetchCommissionPaymentBills()
        })
      })
    },
    exportData() {
      const link = exportCommissionPaymentBills({
        filter: Object.assign({ status: 1 }, this.searchQuery)
      })
      window.open(link, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.invoice {
  padding: 0 20px;
}
.a_style {
  color: #fff;
  text-decoration: none;
}
</style>
