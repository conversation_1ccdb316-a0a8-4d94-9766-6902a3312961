<template>
  <div class="w-100 invoice">
    <SearchPanel
      size="small"
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
    ></SearchPanel>
    <el-card shadow="never" class="box-card-options m-extra-large-t" :body-style="{ padding: '20px' }">
      <DefineTable :cols="cols" :data="tableData" :paging="pagingAttrs" :pagingEvents="pagingEvents"></DefineTable>
    </el-card>
    <el-dialog title="提示" :visible.sync="draftDialog.visible" width="30%">
      <el-form
        ref="draftForm"
        label-position="left"
        label-width="100px"
        :model="draftDialog.form"
        :rules="draftDialog.rules"
      >
        <el-form-item label="发放金额" prop="actual_commission">
          <el-input type="number" v-model="draftDialog.form.actual_commission" placeholder="请输入发放金额"></el-input>
        </el-form-item>
        <!-- <el-form-item label="支付凭证" prop="proof">
          <upload-file v-model="draftDialog.form.proof"></upload-file>
          <el-link
            icon="el-icon-view"
            v-if="draftDialog.form.proof && typeof draftDialog.form.proof === 'string'"
            :href="draftDialog.form.proof"
          >
            点击查看
          </el-link>
        </el-form-item> -->
        <el-form-item label="备注">
          <el-input type="textarea" placeholder="请输入备注" :rows="4" v-model="draftDialog.form.remark"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="draftDialog.visible = false">取 消</el-button>
        <el-button type="primary" @click="draftPayment">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  commissionPaymentBills,
  exportCommissionPaymentBills,
  exportCommissionPayments,
  commissionPaymentDraftBills,
  sendBackCommissionPayment
} from '@/apis/finance' //exportCommissionPaymentBills
import { tokenKey } from '@/config'
import { Loading } from 'element-ui'
import { mapGetters } from 'vuex'

export default {
  name: 'CommissionPaymentBills',
  data() {
    return {
      draftDialog: {
        visible: false,
        form: {
          id: '',
          actual_commission: '',
          // proof: '',
          remark: ''
        },
        rules: {
          actual_commission: [{ required: true, message: '请输入发放金额', trigger: 'blur' }]
          // proof: [{ required: true, message: '请上传支付凭证', trigger: 'blur' }]
        }
      },
      searchFields: [
        {
          type: 'textarea',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'input',
          valKey: 'operator',
          hintText: '处理人'
        }
      ],
      cols: [
        { label: '流水号', prop: 'order_no' },
        {
          label: '单据数',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    type="primary"
                    onClick={() => {
                      this.$router.push({
                        name: 'DraftCommissionPaymentBillPayments',
                        params: {
                          id: scoped.row.id
                        }
                      })
                    }}
                  >
                    {scoped.row.payment_num}
                  </el-link>
                </div>
              )
            }
          }
        },
        { label: '客户保费总数', prop: 'premium' },
        { label: '佣金应发', prop: 'commission' },
        { label: '佣金实发', prop: 'actual_commission' },
        { label: '备注', prop: 'remark' },
        // JSX 插槽
        // {
        //   label: '支付凭证',
        //   align: 'center',
        //   scopedSlots: {
        //     default: (scoped) => {
        //       if (scoped.row.proof) {
        //         return (
        //           <div>
        //             <el-link
        //               type="primary"
        //               href={`${scoped.row.proof}?token=` + window.localStorage.getItem(tokenKey)}
        //               download=""
        //               target="_blank"
        //             >
        //               点击查看
        //             </el-link>
        //           </div>
        //         )
        //       } else {
        //         return '-'
        //       }
        //     }
        //   }
        // },
        {
          label: '操作',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    style="margin-right: 10px"
                    type="primary"
                    plain
                    size="small"
                    v-can={{ name: 'finance.commission-payment.bills.draft' }}
                    onClick={() => this.handleDraft(scoped.row)}
                    disabled={scoped.row.apply.id !== this.admin.id}
                  >
                    提交审核
                  </el-link>
                  <el-link
                    style="margin-right: 10px"
                    type="primary"
                    plain
                    size="small"
                    v-can={{ name: 'finance.commission-payment.bills.send-back' }}
                    onClick={() => this.sendBackPayment(scoped.row.id)}
                    disabled={scoped.row.apply.id !== this.admin.id}
                  >
                    退回
                  </el-link>
                </div>
              )
            }
          }
        }
      ],
      tableData: [],
      pagingAttrs: {
        align: 'center',
        currentPage: 1,
        pageSize: 15,
        layout: 'total, prev, pager, next, jumper',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.pagingAttrs.currentPage = page

          this.fetchCommissionPaymentBills()
        }
      }
    }
  },
  mounted() {
    this.fetchCommissionPaymentBills()
  },
  computed: {
    ...mapGetters('auth', ['admin'])
  },
  methods: {
    handleSearchPanel(command, data) {
      this.searchQuery = data
      this.pagingAttrs.currentPage = 1
      if (command === 'export') {
        this.exportData()
      } else {
        this.fetchCommissionPaymentBills()
      }
    },
    fetchCommissionPaymentBills() {
      const loading = Loading.service()
      commissionPaymentBills({
        page: this.pagingAttrs.currentPage,
        filter: Object.assign({ status: 0 }, this.searchQuery)
      })
        .then((r) => {
          this.tableData = r.data

          this.pagingAttrs.currentPage = r.meta.current_page
          this.pagingAttrs.pageSize = r.meta.per_page
          this.pagingAttrs.total = r.meta.total

          loading.close()
        })
        .finally(() => loading.close())
    },
    exportData() {
      const link = exportCommissionPaymentBills({
        filter: Object.assign({ status: 0 }, this.searchQuery)
      })
      window.open(link, '_blank')
    },
    handleDraft(row) {
      this.draftDialog.visible = true
      this.draftDialog.form.id = row.id
      this.draftDialog.form.actual_commission = row.actual_commission
      // this.draftDialog.form.proof = row.proof
      this.draftDialog.form.remark = row.remark
    },
    draftPayment() {
      this.$refs?.draftForm?.validate((valid) => {
        if (valid) {
          commissionPaymentDraftBills(this.draftDialog.form.id, Object.assign({}, this.draftDialog.form)).then(() => {
            this.draftDialog.visible = false
            this.draftDialog.form = {
              id: '',
              actual_commission: '',
              // proof: '',
              remark: ''
            }
            this.fetchCommissionPaymentBills()
            this.$message({
              type: 'success',
              message: '提交成功'
            })
          })
        }
      })
    },
    sendBackPayment(id) {
      this.$confirm('是否确定退回?', '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        type: 'warning'
      }).then(() => {
        sendBackCommissionPayment(id).then(() => {
          this.$message.success('操作成功')
          this.fetchCommissionPaymentBills()
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.invoice {
  padding: 0 20px;
}
.a_style {
  color: #fff;
  text-decoration: none;
}
</style>
