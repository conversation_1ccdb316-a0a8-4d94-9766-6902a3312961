<template>
  <div class="w-100 invoice">
    <SearchPanel
      size="small"
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
    ></SearchPanel>
    <el-card shadow="never" class="box-card-options m-extra-large-t" :body-style="{ padding: '20px' }">
      <DefineTable :cols="cols" :data="tableData" :paging="pagingAttrs" :pagingEvents="pagingEvents"></DefineTable>
    </el-card>
    <el-dialog title="请上传凭证" :visible.sync="dialogData.visible" width="30%">
      <el-form
        ref="proofForm"
        label-position="left"
        label-width="100px"
        :model="dialogData.form"
        :rules="dialogData.rules"
      >
        <el-form-item label="支付凭证" prop="proof">
          <upload-file v-model="dialogData.form.proof"></upload-file>
        </el-form-item>
        <el-form-item prop="paid_at" label="支付时间">
          <el-date-picker
            v-model="dialogData.form.paid_at"
            :picker-options="pickerOptions"
            class="w-100"
            type="date"
            placeholder="请选择支付时间"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogData.visible = false">取 消</el-button>
        <el-button type="primary" @click="submitUpload">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  commissionPaymentBills,
  exportCommissionPaymentBills,
  exportCommissionPayments,
  uploadCommissionPaymentProof
} from '@/apis/finance' //exportCommissionPaymentBills
import { tokenKey } from '@/config'
import { Loading } from 'element-ui'
import dayjs from 'dayjs'
import { mapGetters } from 'vuex'

export default {
  name: 'CommissionPaymentBills',
  data() {
    return {
      pickerOptions: {
        disabledDate: (time) => {
          if (time.getTime() > dayjs()) {
            return true
          }
        }
      },
      dialogData: {
        visible: false,
        form: {
          id: '',
          proof: '',
          paid_at: ''
        },
        rules: {
          proof: [{ required: true, message: '请上传支付凭证', trigger: 'blur' }],
          paid_at: [{ required: true, message: '请选择支付时间', trigger: 'blur' }]
        }
      },
      searchFields: [
        {
          type: 'textarea',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'input',
          valKey: 'operator',
          hintText: '处理人'
        },
        {
          type: 'daterange',
          hintText: '支付',
          valKey: 'paid_at_range'
        }
      ],
      cols: [
        { label: '流水号', prop: 'order_no' },
        {
          label: '单据数',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    type="primary"
                    onClick={() => {
                      this.$router.push({
                        name: 'CommissionBillPayments',
                        params: {
                          id: scoped.row.id
                        }
                      })
                    }}
                  >
                    {scoped.row.payment_num}
                  </el-link>
                </div>
              )
            }
          }
        },
        // { label: '保单数', prop: 'policy_num' },
        { label: '客户保费总数', prop: 'premium' },
        { label: '佣金应发', prop: 'commission' },
        { label: '佣金实发', prop: 'actual_commission' },
        {
          label: '佣金收取方',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.payment_data.is_own_platform) {
                return <span class="text-info">{scoped.row.payment_data.payment_name}</span>
              } else {
                return <span class="text-primary">{scoped.row.payment_data.payment_name}</span>
              }
            }
          }
        },
        { label: '处理人', prop: 'operation.name' },
        {
          label: '状态',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.status === 2 && !scoped.row.proof) {
                return '审核完成'
              }
              if (scoped.row.operation.id !== -1 && scoped.row.status === 1) {
                return '审核中'
              }
              const types = {
                0: '未提交',
                1: '已提交',
                2: '已完成',
                3: '已退回'
              }

              return types[scoped.row.status]
            }
          }
        },
        {
          label: '支付时间',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.status !== 2) {
                return '-'
              }
              return scoped.row.paid_at
            }
          }
        },
        // JSX 插槽
        {
          label: '支付凭证',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.proof) {
                return (
                  <div>
                    <el-link
                      type="primary"
                      href={`${scoped.row.proof}?token=` + window.localStorage.getItem(tokenKey)}
                      download=""
                      target="_blank"
                    >
                      点击查看
                    </el-link>
                  </div>
                )
              } else {
                return '-'
              }
            }
          }
        },
        { label: '备注', prop: 'remark' },
        {
          label: '操作',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.status === 2 && !scoped.row.proof) {
                return (
                  <div>
                    <el-link
                      style="margin-right: 10px"
                      type="primary"
                      plain
                      size="small"
                      v-can={{ name: 'finance.commission-payment.bills.upload-proof' }}
                      onClick={() => this.upload(scoped.row)}
                      disabled={scoped.row.apply.id !== this.admin.id}
                    >
                      上传凭证
                    </el-link>
                    <el-link
                      type="primary"
                      v-can={{ name: 'finance.commission-payment.bills.export' }}
                      href={exportCommissionPayments({ bill_id: scoped.row.id })}
                      download=""
                      target="_blank"
                    >
                      导出清单
                    </el-link>
                  </div>
                )
              } else {
                return (
                  <div>
                    <el-link
                      type="primary"
                      href={exportCommissionPayments({ bill_id: scoped.row.id })}
                      download=""
                      disabled={scoped.row.status === 3}
                      target="_blank"
                    >
                      导出清单
                    </el-link>
                  </div>
                )
              }
            }
          }
        }
      ],
      tableData: [],
      pagingAttrs: {
        align: 'center',
        currentPage: 1,
        pageSize: 15,
        layout: 'total, prev, pager, next, jumper',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.pagingAttrs.currentPage = page

          this.fetchCommissionPaymentBills()
        }
      }
    }
  },
  computed: {
    ...mapGetters('auth', ['admin'])
  },
  mounted() {
    this.fetchCommissionPaymentBills()
  },
  methods: {
    handleSearchPanel(command, data) {
      this.searchQuery = data
      this.pagingAttrs.currentPage = 1
      if (command === 'export') {
        this.exportData()
      } else {
        this.fetchCommissionPaymentBills()
      }
    },
    fetchCommissionPaymentBills() {
      const loading = Loading.service()
      commissionPaymentBills({
        page: this.pagingAttrs.currentPage,
        filter: this.searchQuery
      })
        .then((r) => {
          this.tableData = r.data

          this.pagingAttrs.currentPage = r.meta.current_page
          this.pagingAttrs.pageSize = r.meta.per_page
          this.pagingAttrs.total = r.meta.total

          loading.close()
        })
        .finally(() => loading.close())
    },
    exportData() {
      const link = exportCommissionPaymentBills({
        filter: this.searchQuery
      })
      window.open(link, '_blank')
    },
    upload(row) {
      this.dialogData.visible = true
      this.dialogData.form.id = row.id
      this.dialogData.form.proof = row.proof
      this.dialogData.form.paid_at = row.paid_at
    },
    submitUpload() {
      this.$refs?.proofForm?.validate((valid) => {
        if (valid) {
          this.dialogData.form.paid_at = dayjs(this.dialogData.form.paid_at).format('YYYY-MM-DD HH:mm:ss')
          uploadCommissionPaymentProof(this.dialogData.form.id, Object.assign({}, this.dialogData.form)).then(() => {
            this.dialogData.visible = false
            this.dialogData.form = {
              id: '',
              proof: '',
              paid_at: ''
            }
            this.fetchCommissionPaymentBills()
            this.$message({
              type: 'success',
              message: '处理完成'
            })
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.invoice {
  padding: 0 20px;
}
.a_style {
  color: #fff;
  text-decoration: none;
}
</style>
