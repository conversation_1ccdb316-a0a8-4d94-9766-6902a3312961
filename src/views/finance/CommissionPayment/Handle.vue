<template>
  <div class="w-100 feePayment">
    <el-card shadow="never" class="box-card-options" :body-style="{ padding: '20px' }">
      <div class="d-flex justify-content-between">
        <h3>
          保单数：{{ pagingAttrs.total }}单 客户总保费： ￥{{ Number(totalPremium).toFixed(2) + '元' }} 应发佣金： ￥{{
            Number(totalCommission).toFixed(2) + '元'
          }}
        </h3>
        <div>
          <el-button
            type="primary"
            v-can="{ name: 'finance.commission-payment.bills.create' }"
            @click="dialogVisible = true"
            >确认发放</el-button
          >
        </div>
      </div>
      <div style="width: 30%; display: flex; align-items: center">
        <el-input v-model="search" size="mini" placeholder="保单号" clearable />
        <el-button
          style="margin-left: 10px"
          type="primary"
          v-can="{ name: 'finance.commission-payment.bills.create' }"
          @click="handleSearch"
          >搜索
        </el-button>
      </div>
      <DefineTable :cols="cols" :data="data" :paging="pagingAttrs" :pagingEvents="pagingEvents"></DefineTable>
    </el-card>

    <el-dialog title="提示" :visible.sync="dialogVisible" width="30%" :before-close="handleClose">
      <el-form ref="commissionForm" label-position="left" label-width="100px" :model="form" :rules="rules">
        <el-form-item label="发放金额" prop="actual_commission">
          <el-input type="number" v-model="form.actual_commission" placeholder="请输入发放金额"></el-input>
        </el-form-item>
        <!-- <el-form-item label="支付凭证" prop="proof">
          <upload-file v-model="formLabelAlign.proof"></upload-file>
        </el-form-item> -->
        <el-form-item label="备注">
          <el-input type="textarea" placeholder="请输入备注" :rows="4" v-model="form.remark"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" v-can="{ name: 'finance.commission-payment.bills.draft' }" @click="draft"
          >暂 存</el-button
        >
        <el-button type="primary" v-can="{ name: 'finance.commission-payment.bills.create' }" @click="handle"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getCommissionPayments,
  getCommissionPaymentCount,
  createCommissionPayment,
  draftCommissionPayment
} from '@/apis/finance.js' //createCommissionPayment
import { Loading } from 'element-ui'

export default {
  name: 'HandleCommissionPayment',
  data() {
    return {
      form: {
        actual_commission: '',
        remark: ''
      },
      search: '',
      totalPremium: 0,
      totalCommission: 0,
      removeIds: [],
      handleIds: [],
      dialogVisible: false,
      cols: [
        // { label: '流水号', prop: 'order_no' },
        { label: '保单号', prop: 'policy.policy_no', width: '200', fixed: 'left' },
        {
          prop: 'type',
          label: '险种',
          scopedSlots: {
            default: (scoped) => {
              const types = {
                1: '国内货运险',
                2: '国际货运险',
                3: '单车责任险',
                4: '其他险种',
                5: '雇主责任险',
                6: '线下录入保单',
                7: '跨境电商险'
              }

              return scoped.row.policy.type === 6 ? scoped.row.policy.offline_type : types[scoped.row.policy.type]
            }
          }
        },
        { prop: 'premium', label: '客户保费' },
        { label: '佣金', prop: 'commission' },
        {
          label: '佣金收取方',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.platform.id == scoped.row.payment_platform.id) {
                return (
                  <span class="text-info">{scoped.row.agent.name ? scoped.row.agent.name : scoped.row.user.name}</span>
                )
              } else {
                return <span class="text-primary">{scoped.row.payment_platform.name}</span>
              }
            }
          }
        },
        { prop: 'issued_at', label: '生效时间' },
        // JSX 插槽
        {
          label: '操作',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    type="primary"
                    onClick={() => {
                      this.remove(scoped.row)
                    }}
                  >
                    移除
                  </el-link>
                </div>
              )
            }
          }
        }
      ],
      data: [],
      searchQuery: {},
      pagingAttrs: {
        currentPage: 1,
        pageSize: 15,
        layout: 'total, prev, pager, next, jumper',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.pagingAttrs.currentPage = page

          this.fetchCommissionPayments()
        }
      },
      rules: {
        actual_commission: [{ required: true, message: '请输入发放金额', trigger: 'blur' }]
      }
    }
  },
  mounted() {
    this.searchQuery = { ...this.$route?.query }
    delete this.searchQuery.all_checkout
    delete this.searchQuery.id

    this.fetchData()
  },
  methods: {
    handleSearch() {
      this.searchQuery = { ...this.searchQuery, policy_no: this.search }
      this.fetchData()
    },
    fetchData() {
      this.fetchCommissionPayments()
      this.fetchCommissionPaymentsCount()
    },
    fetchCommissionPayments() {
      const loading = Loading.service()
      getCommissionPayments({
        page: this.pagingAttrs.currentPage,
        ids: this.$route?.query?.id?.split(','),
        all_checkout: this.$route?.query?.all_checkout,
        filter: this.searchQuery
      })
        .then((r) => {
          this.data = r.data

          this.pagingAttrs.currentPage = r.meta.current_page
          this.pagingAttrs.pageSize = r.meta.per_page
          this.pagingAttrs.total = r.meta.total
          loading.close()
        })
        .finally(() => loading.close())
    },
    fetchCommissionPaymentsCount() {
      getCommissionPaymentCount({
        ids: this.$route?.query?.id?.split(','),
        all_checkout: this.$route?.query?.all_checkout,
        filter: this.searchQuery
      }).then((r) => {
        this.totalPremium = r.data.premium
        this.totalCommission = r.data.commission
        this.handleIds = r.data.ids
      })
    },
    remove(row) {
      this.removeIds.push(row.id)
      this.search = ''
      this.searchQuery = { ...this.searchQuery, policy_no: '' }
      this.searchQuery = { ...this.searchQuery, without: this.removeIds }
      this.fetchData()
    },
    handle() {
      this.$refs?.commissionForm?.validate((valid) => {
        if (valid) {
          const loading = Loading.service()
          createCommissionPayment(Object.assign({}, this.marshalData(this.form)))
            .then(() => {
              this.dialogVisible = false
              this.$router.push({
                name: 'CommissionPayment'
              })
              this.$message({
                type: 'success',
                message: '结算成功'
              })
              loading.close()
            })
            .finally(() => loading.close())
        }
      })
    },
    draft() {
      const loading = Loading.service()
      draftCommissionPayment(Object.assign({}, this.marshalData(this.form)))
        .then(() => {
          this.dialogVisible = false
          this.$router.push({
            name: 'CommissionPayment'
          })
          this.$message({
            type: 'success',
            message: '暂存成功'
          })
          loading.close()
        })
        .finally(() => loading.close())
    },
    handleClose() {
      this.dialogVisible = false
    },
    marshalData(data) {
      data.premium = Number(this.totalPremium).toFixed(2)
      data.commission = Number(this.totalCommission).toFixed(2)
      data.ids = this.handleIds.filter((item) => !this.removeIds.includes(item)).join(',')
      return data
    }
  }
}
</script>

<style scoped>
.feePayment {
  padding: 0 20px;
}
</style>
