<template>
  <div class="w-100 p-extra-large-x m-extra-large-b o-hidden-x">
    <SearchPanel
      size="small"
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
    ></SearchPanel>
    <el-alert type="error" class="m-extra-large-t" :closable="false">
      <span>
        当前保费
        <el-tag type="danger" effect="dark"> {{ countPayment.premium }} </el-tag>，
        <span class="hover-cursor">应付佣金: </span>
        <el-tag type="danger" effect="dark"> {{ countPayment.commission }} </el-tag>
      </span>
    </el-alert>
    <el-card shadow="never" class="box-card-options m-extra-large-t">
      <div class="d-flex">
        <el-button
          type="primary"
          v-can="{ name: 'finance.commission-payment.bills.create' }"
          @click="handlePayment"
          style="margin-bottom: 15px"
          >佣金发放</el-button
        >
        <el-button
          type="primary"
          v-can="{ name: 'finance.commission-payment.bills.create' }"
          @click="allCheckout"
          style="margin-bottom: 15px"
          >全部处理</el-button
        >
        <el-button
          type="primary"
          v-can="{ name: 'finance.commission-payment.bills.draft' }"
          @click="draftBills"
          style="margin-bottom: 15px"
          >暂存记录</el-button
        >
        <el-button
          type="primary"
          v-can="{ name: 'finance.commission-payment.bills.index' }"
          @click="paymentBills"
          style="margin-bottom: 15px"
          >发放记录</el-button
        >
      </div>
      <DefineTable
        :cols="cols"
        :data="tableData"
        :paging="pagingAttrs"
        :pagingEvents="pagingEvents"
        :events="tableEvents"
        :rowKey="getRowKeys"
      ></DefineTable>
    </el-card>
  </div>
</template>

<script>
import { getCommissionPayments, getCommissionPaymentCount, exportCommissionPayments } from '@/apis/finance'
import { getPlatformProductCompanies } from '@/apis/platform_product'
import { getOfflineProductCategories } from '@/apis/product'
import { mapGetters } from 'vuex'
import { Loading } from 'element-ui'

export default {
  name: 'CommissionPayment',
  data() {
    return {
      searchFields: [
        {
          type: 'textarea',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'input',
          valKey: 'payment_name',
          hintText: '佣金收取方'
        },
        {
          type: 'select',
          valKey: 'types',
          hintText: '险种',
          isMultiple: true,
          options: []
        },
        {
          type: 'daterange',
          valKey: 'issued_at_range'
        },
        {
          type: 'select',
          valKey: 'company_id',
          hintText: '保险公司',
          onChangeReset: 'company_branch_id',
          options: []
        },
        {
          type: 'select',
          valKey: 'company_branch_id',
          hintText: '出单公司',
          options: []
        }
      ],
      cols: [
        { align: 'center', type: 'selection', width: '80', reserveSelection: true },
        {
          label: '保单号/批单号',
          width: '200',
          fixed: 'left',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.policy.policy_no}</label>
                  <br />
                  <small>{scoped.row.policy_group_endorse.endorse_no}</small>
                </div>
              )
            }
          }
        },
        {
          label: '险种',
          scopedSlots: {
            default: (scoped) => {
              const types = {
                1: '国内货运险',
                2: '国际货运险',
                3: '单车责任险',
                4: '其他险种',
                5: '雇主责任险',
                6: '线下录入保单',
                7: '跨境电商险'
              }

              return scoped.row.policy.type === 6 ? scoped.row.policy.offline_type : types[scoped.row.policy.type]
            }
          }
        },
        { label: '客户保费', prop: 'premium' },
        { label: '佣金', prop: 'commission' },
        {
          label: '佣金收取方',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.is_own_platform) {
                return <span class="text-info">{scoped.row.payment_name}</span>
              } else {
                return <span class="text-primary">{scoped.row.payment_name}</span>
              }
            }
          }
        },
        { label: '出单公司', prop: 'company_branch.name' },
        { label: '生效时间', prop: 'issued_at' }
      ],
      tableData: [],
      searchData: {},
      searchQuery: {},
      pagingAttrs: {
        currentPage: 1,
        pageSize: 15,
        layout: 'total, prev, pager, next, jumper',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.pagingAttrs.currentPage = page

          this.fetchCommissionPayments()
        }
      },
      tableEvents: {
        'selection-change': (val) => {
          this.multipleSelection = val
        }
      },
      multipleSelection: [],
      rawCompanies: [],
      offlineCategories: [],
      countPayment: {
        premium: 0,
        commission: 0
      }
    }
  },
  computed: {
    ...mapGetters('auth', ['admin'])
  },
  created() {
    getOfflineProductCategories({ is_parent: 0, is_pageable: 0 }).then((r) => {
      this.offlineCategories = r.data

      this.loadCategories()
    })
    getPlatformProductCompanies().then((r) => {
      this.rawCompanies = r.data

      this.loadCompanies()
      this.loadCompanyBranches()
    })
    this.fetchCommissionPayments()
    this.fetchCountPayment()
  },
  methods: {
    handleSearchPanel(command, data) {
      this.searchQuery = data
      this.pagingAttrs.currentPage = 1
      if (command === 'export') {
        this.paymentsExport()
      } else {
        this.fetchCommissionPayments()
        this.fetchCountPayment()
      }
    },
    getRowKeys(row) {
      return row.id
    },
    fetchCommissionPayments() {
      const loading = Loading.service()
      getCommissionPayments({
        page: this.pagingAttrs.currentPage,
        filter: this.searchQuery
      })
        .then((r) => {
          this.tableData = r.data

          this.pagingAttrs.currentPage = r.meta.current_page
          this.pagingAttrs.pageSize = r.meta.per_page
          this.pagingAttrs.total = r.meta.total
          loading.close()
        })
        .finally(() => loading.close())
    },
    fetchCountPayment() {
      getCommissionPaymentCount({
        filter: this.searchQuery
      }).then((r) => {
        this.countPayment.premium = r.data.premium
        this.countPayment.commission = r.data.commission
      })
    },
    loadCompanies() {
      const options = this.rawCompanies.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('company_id', options)
    },
    loadCompanyBranches() {
      let options = []
      if (this.searchData?.company_id !== '' && this.searchData?.company_id !== undefined) {
        const company = this.rawCompanies.find((e) => e.id === this.searchData?.company_id)
        if (company) {
          options = company.branches.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      } else {
        this.rawCompanies.forEach((e) => {
          e?.branches.forEach((e) => {
            options.push({
              label: e.name,
              value: e.id
            })
          })
        })
      }

      this.assignSelectOptions('company_branch_id', options)
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    },
    handlePayment() {
      if (!this.multipleSelection.length) {
        this.$message({
          type: 'warning',
          message: '请至少选择一项'
        })
        return
      }

      const payment1 = this.multipleSelection[0]
      if (
        this.multipleSelection.find(
          (item) => item.is_own_platform != payment1.is_own_platform || item.payment_name != payment1.payment_name
        )
      ) {
        this.$message({
          type: 'warning',
          message: '请选择同一个佣金收取方的保单'
        })
        return false
      }

      // if (this.multipleSelection.find((item) => item.company_branch.id != payment1.company_branch.id)) {
      //   this.$message({
      //     type: 'warning',
      //     message: '请选择同一个出单公司的保单'
      //   })
      //   return false
      // }

      this.$router.push({
        name: 'HandleCommissionPayment',
        query: {
          id: this.multipleSelection.map((e) => e.id).join(',')
        }
      })
    },
    paymentBills() {
      this.$router.push({
        name: 'CommissionPaymentBills'
      })
    },
    draftBills() {
      this.$router.push({
        name: 'DraftCommissionPaymentBills'
      })
    },
    paymentsExport() {
      const link = exportCommissionPayments({ filter: this.searchQuery })
      window.open(link, '_blank')
    },
    allCheckout() {
      this.$router.push({
        name: 'HandleCommissionPayment',
        query: {
          all_checkout: 1,
          ...this.searchQuery
        }
      })
    },
    loadCategories() {
      var options = [
        {
          label: '国内货运险',
          value: 1
        },
        {
          label: '国际货运险',
          value: 2
        },
        {
          label: '单车责任险',
          value: 3
        },
        {
          label: '其他险种',
          value: 4
        },
        {
          label: '雇主责任险',
          value: 5
        }
      ]
      options = options.concat(
        this.offlineCategories.map((e) => {
          return {
            label: e.name + '-线下录入',
            value: 'OF-' + e.id
          }
        })
      )
      this.assignSelectOptions('types', options)
    }
  },
  watch: {
    searchData: {
      immediate: true,
      deep: true,
      handler(value, old) {
        if (old?.company_id !== value?.company_id) {
          this.loadCompanyBranches()
        }
      }
    }
  }
}
</script>

<style scoped>
.documents {
  padding: 0 20px;
}
.el-select,
.el-select--small {
  width: 100%;
}
.box-card-options {
  margin-bottom: 20px;
}
</style>

<style lang="scss" scoped>
.policy-search {
  /deep/.el-select,
  /deep/.el-date-editor {
    width: 100% !important;
  }
  /deep/.el-card__body {
    display: flex;
  }
}
</style>
