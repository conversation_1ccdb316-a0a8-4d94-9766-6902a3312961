<template>
  <div class="w-100 invoice">
    <SearchPanel
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
    ></SearchPanel>
    <el-card shadow="never" class="box-card-options m-extra-large-t" :body-style="{ padding: '20px' }">
      <DefineTable :cols="cols" :data="tableData" :paging="paging" :pagingEvents="pagingEvents"></DefineTable>
    </el-card>
    <el-dialog title="提示" :visible.sync="draftDialog.visible" width="30%">
      <el-form
        ref="draftForm"
        label-position="left"
        label-width="100px"
        :model="draftDialog.form"
        :rules="draftDialog.rules"
      >
        <el-form-item label="结算金额" prop="actual_poundage">
          <el-input type="number" v-model="draftDialog.form.actual_poundage" placeholder="请输入结算金额"></el-input>
        </el-form-item>
        <el-form-item prop="paid_at" label="支付时间">
          <el-date-picker v-model="draftDialog.form.paid_at" class="w-100" type="date" placeholder="请选择支付时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="支付凭证" prop="proof">
          <upload-file v-model="draftDialog.form.proof"></upload-file>
          <el-link
            icon="el-icon-view"
            v-if="draftDialog.form.proof && typeof draftDialog.form.proof === 'string'"
            :href="draftDialog.form.proof"
          >
            点击查看
          </el-link>
        </el-form-item>
        <el-form-item label="备注">
          <el-input type="textarea" placeholder="请输入备注" :rows="4" v-model="draftDialog.form.remark"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="draftDialog.visible = false">取 消</el-button>
        <el-button type="primary" @click="draftPayment">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import DefineTable from '@/components/globals/DefineTable/DefineTable'
import { getPlatformProductCompanies } from '@/apis/platform_product'
import {
  poundagePaymentBills,
  exportPoundagePaymentBills,
  sendBackPoundagePayment,
  poundagePaymentDraftBills
} from '@/apis/finance' //exportPoundagePaymentBills
import { tokenKey } from '@/config'
import { Loading } from 'element-ui'
import { mapGetters } from 'vuex'
import dayjs from 'dayjs'

export default {
  name: 'PoundagePaymentBills',
  components: {
    DefineTable
  },
  data() {
    return {
      draftDialog: {
        visible: false,
        form: {
          id: '',
          paid_at: '',
          actual_poundage: '',
          proof: '',
          remark: ''
        },
        rules: {
          paid_at: [{ required: true, message: '请选择支付时间', trigger: 'blur' }],
          actual_poundage: [{ required: true, message: '请输入结算金额', trigger: 'blur' }],
          proof: [{ required: true, message: '请上传支付凭证', trigger: 'blur' }]
        }
      },
      searchFields: [
        {
          type: 'select',
          valKey: 'company_id',
          hintText: '保险公司',
          onChangeReset: 'company_branch_id',
          options: []
        },
        {
          type: 'select',
          valKey: 'company_branch_id',
          hintText: '出单公司',
          options: []
        },
        {
          type: 'textarea',
          valKey: 'policy_no',
          hintText: '保单号'
        }
      ],
      cols: [
        { label: '流水号', prop: 'order_no' },
        // { label: '出单公司', prop: 'company_branch.name' },
        {
          label: '单据数',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    type="primary"
                    onClick={() => {
                      this.$router.push({
                        name: 'DraftPoundagePaymentBillPayments',
                        params: {
                          id: scoped.row.id
                        }
                      })
                    }}
                  >
                    {scoped.row.payment_num}
                  </el-link>
                </div>
              )
            }
          }
        },
        // { label: '保单数', prop: 'policy_num' },
        { label: '保费总数', prop: 'premium' },
        { label: '经纪费应结', prop: 'poundage' },
        { label: '经纪费实结', prop: 'actual_poundage' },
        { label: '备注', prop: 'remark' },
        // JSX 插槽
        {
          label: '支付凭证',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.proof) {
                return (
                  <div>
                    <el-link
                      type="primary"
                      href={`${scoped.row.proof}?token=` + window.localStorage.getItem(tokenKey)}
                      download=""
                      target="_blank"
                    >
                      点击查看
                    </el-link>
                  </div>
                )
              } else {
                return '-'
              }
            }
          }
        },
        {
          label: '操作',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    style="margin-right: 10px"
                    type="primary"
                    plain
                    size="small"
                    v-can={{ name: 'finance.poundage-payment.bills.draft' }}
                    onClick={() => this.handleDraft(scoped.row)}
                    disabled={scoped.row.apply.id !== this.admin.id}
                  >
                    提交审核
                  </el-link>
                  <el-link
                    style="margin-right: 10px"
                    type="primary"
                    plain
                    size="small"
                    v-can={{ name: 'finance.poundage-payment.bills.send-back' }}
                    onClick={() => this.sendBackPayment(scoped.row.id)}
                    disabled={scoped.row.apply.id !== this.admin.id}
                  >
                    退回
                  </el-link>
                </div>
              )
            }
          }
        }
      ],
      tableData: [],
      paging: {
        currentPage: 1,
        pageSize: 0,
        layout: 'prev, pager, next, jumper, total',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page

          this.fetchBills()
        }
      },
      searchQuery: {},
      searchData: {},
      rawCompanies: []
    }
  },
  watch: {
    searchData: {
      immediate: true,
      deep: true,
      handler(value, old) {
        if (old?.company_id !== value?.company_id) {
          this.loadCompanyBranches()
        }
      }
    }
  },
  computed: {
    ...mapGetters('auth', ['admin'])
  },
  created() {
    getPlatformProductCompanies().then((r) => {
      this.rawCompanies = r.data

      this.loadCompanies()
      this.loadCompanyBranches()
    })
    this.fetchBills()
  },
  methods: {
    handleSearchPanel(command, data) {
      this.searchQuery = data
      this.paging.page = 1
      if (command === 'export') {
        this.exportData()
      } else {
        this.fetchBills()
      }
    },
    fetchBills() {
      const loading = Loading.service()
      poundagePaymentBills({
        page: this.paging.page,
        filter: Object.assign({ status: 0 }, this.searchQuery)
      })
        .then((r) => {
          this.tableData = r.data

          this.paging.currentPage = r.meta.current_page
          this.paging.pageSize = r.meta.per_page
          this.paging.total = r.meta.total

          loading.close()
        })
        .finally(() => loading.close())
    },
    loadCompanies() {
      const options = this.rawCompanies.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('company_id', options)
    },
    loadCompanyBranches() {
      let options = []
      if (this.searchData?.company_id !== '' && this.searchData?.company_id !== undefined) {
        const company = this.rawCompanies.find((e) => e.id === this.searchData?.company_id)
        if (company) {
          options = company.branches.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      } else {
        this.rawCompanies.forEach((e) => {
          e?.branches.forEach((e) => {
            options.push({
              label: e.name,
              value: e.id
            })
          })
        })
      }

      this.assignSelectOptions('company_branch_id', options)
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    },
    exportData() {
      const link = exportPoundagePaymentBills({
        filter: Object.assign({ status: 0 }, this.searchQuery)
      })
      window.open(link, '_blank')
    },
    handleDraft(row) {
      this.draftDialog.visible = true
      this.draftDialog.form.id = row.id
      this.draftDialog.form.paid_at = row.paid_at
      this.draftDialog.form.actual_poundage = row.actual_poundage
      this.draftDialog.form.proof = row.proof
      this.draftDialog.form.remark = row.remark
    },
    draftPayment() {
      this.$refs?.draftForm?.validate((valid) => {
        if (valid) {
          if (this.draftDialog.form.paid_at) {
            this.draftDialog.form.paid_at = dayjs(this.draftDialog.form.paid_at).format('YYYY-MM-DD HH:mm:ss')
          }
          poundagePaymentDraftBills(this.draftDialog.form.id, Object.assign({}, this.draftDialog.form)).then(() => {
            this.draftDialog.visible = false
            this.draftDialog.form = {
              id: '',
              paid_at: '',
              actual_poundage: '',
              proof: '',
              remark: ''
            }
            this.fetchBills()
            this.$message({
              type: 'success',
              message: '提交成功'
            })
          })
        }
      })
    },
    sendBackPayment(id) {
      this.$confirm('是否确定退回?', '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        type: 'warning'
      }).then(() => {
        sendBackPoundagePayment(id).then(() => {
          this.$message.success('退回成功')
          this.fetchBills()
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.invoice {
  padding: 0 20px;
}
.a_style {
  color: #fff;
  text-decoration: none;
}
</style>
