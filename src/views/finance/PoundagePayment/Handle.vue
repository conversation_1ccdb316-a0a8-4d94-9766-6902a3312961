<template>
  <div class="w-100 feePayment">
    <el-card shadow="never" class="box-card-options" :body-style="{ padding: '20px' }">
      <div class="d-flex justify-content-between">
        <h3>
          保单数：{{ this.pagingAttrs.total }}单 需支付保险公司： ￥{{
            Number(totalPremium).toFixed(2) + '元'
          }}
          经纪费应结： ￥{{ Number(totalPoundage).toFixed(2) + '元' }}
        </h3>
        <div>
          <el-button
            type="primary"
            v-can="{ name: 'finance.poundage-payment.bills.create' }"
            @click="dialogVisible = true"
            >确认支付</el-button
          >
        </div>
      </div>
      <div style="width: 30%; display: flex; align-items: center">
        <el-input v-model="search" size="mini" placeholder="保单号" clearable />
        <el-button
          style="margin-left: 10px"
          type="primary"
          v-can="{ name: 'finance.poundage-payment.bills.create' }"
          @click="handleSearch"
          >搜索
        </el-button>
      </div>
      <DefineTable :cols="cols" :data="data" :paging="pagingAttrs" :pagingEvents="pagingEvents"></DefineTable>
    </el-card>

    <el-dialog title="提示" :visible.sync="dialogVisible" width="30%" :before-close="handleClose">
      <el-form ref="poundageForm" label-position="left" label-width="100px" :model="form" :rules="rules">
        <el-form-item label="结算金额" prop="actual_poundage">
          <el-input type="number" v-model="form.actual_poundage" placeholder="请输入结算金额"></el-input>
        </el-form-item>
        <el-form-item prop="paid_at" label="支付时间">
          <el-date-picker
            v-model="form.paid_at"
            :picker-options="pickerOptions"
            class="w-100"
            type="date"
            placeholder="请选择支付时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="支付凭证" prop="proof">
          <upload-file v-model="form.proof"></upload-file>
        </el-form-item>
        <el-form-item label="备注">
          <el-input type="textarea" placeholder="请输入备注" :rows="4" v-model="form.remark"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" v-can="{ name: 'finance.poundage-payment.bills.draft' }" @click="draft"
          >暂 存</el-button
        >
        <el-button type="primary" v-can="{ name: 'finance.poundage-payment.bills.create' }" @click="handle"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getPoundagePayments,
  getPoundagePaymentCount,
  createPoundagePayment,
  draftPoundagePayment
} from '@/apis/finance.js'
import dayjs from 'dayjs'
import { Loading } from 'element-ui'

export default {
  name: 'HandlePoundagePayment',
  data() {
    return {
      pickerOptions: {
        disabledDate: (time) => {
          if (time.getTime() > dayjs()) {
            return true
          }
        }
      },
      form: {
        actual_poundage: '',
        proof: '',
        paid_at: '',
        remark: ''
      },
      search: '',
      total: 0,
      totalPremium: 0,
      totalPoundage: 0,
      removeIds: [],
      handleIds: [],
      dialogVisible: false,
      cols: [
        // { label: '流水号', prop: 'order_no' },
        { label: '保单号', prop: 'policy.policy_no', width: '200', fixed: 'left' },
        { prop: 'user.name', label: '投保用户' },
        {
          label: '险种',
          scopedSlots: {
            default: (scoped) => {
              const types = {
                1: '国内货运险',
                2: '国际货运险',
                3: '单车责任险',
                4: '其他险种',
                5: '雇主责任险',
                6: '线下录入保单',
                7: '跨境电商险'
              }

              return scoped.row.policy.type === 6 ? scoped.row.policy.offline_type : types[scoped.row.policy.type]
            }
          }
        },
        { prop: 'premium', label: '保险公司保费' },
        { prop: 'poundage', label: '经纪费应结' },
        { prop: 'company_branch.name', label: '出单公司' },
        { prop: 'issued_at', label: '生效时间' },
        // JSX 插槽
        {
          label: '操作',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    type="primary"
                    onClick={() => {
                      this.remove(scoped.row)
                    }}
                  >
                    移除
                  </el-link>
                </div>
              )
            }
          }
        }
      ],
      data: [],
      searchQuery: {},
      pagingAttrs: {
        currentPage: 1,
        pageSize: 15,
        layout: 'total, prev, pager, next, jumper',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.pagingAttrs.currentPage = page

          this.fetchPoundagePayments()
        }
      },
      rules: {
        actual_poundage: [{ required: true, message: '请输入结算金额', trigger: 'blur' }],
        paid_at: [{ required: true, message: '请选择支付时间', trigger: ['change', 'blur'] }],
        proof: [{ required: true, message: '请上传支付凭证', trigger: 'blur' }]
      }
    }
  },
  mounted() {
    this.searchQuery = { ...this.$route?.query }
    delete this.searchQuery.all_checkout
    delete this.searchQuery.id
    this.fetchData()
  },
  methods: {
    handleSearch() {
      this.searchQuery = { ...this.searchQuery, policy_no: this.search }
      this.fetchData()
    },
    fetchData() {
      this.fetchPoundagePayments()
      this.fetchPoundagePaymentsCount()
    },
    fetchPoundagePayments() {
      const loading = Loading.service()
      getPoundagePayments({
        page: this.pagingAttrs.currentPage,
        ids: this.$route?.query?.id?.split(','),
        all_checkout: this.$route?.query?.all_checkout,
        filter: this.searchQuery
      })
        .then((r) => {
          this.data = r.data

          this.pagingAttrs.currentPage = r.meta.current_page
          this.pagingAttrs.pageSize = r.meta.per_page
          this.pagingAttrs.total = r.meta.total
          loading.close()
        })
        .finally(() => loading.close())
    },
    fetchPoundagePaymentsCount() {
      getPoundagePaymentCount({
        ids: this.$route?.query?.id?.split(','),
        all_checkout: this.$route?.query?.all_checkout,
        filter: this.searchQuery
      }).then((r) => {
        this.totalPremium = r.data.premium
        this.totalPoundage = r.data.poundage
        this.handleIds = r.data.ids
      })
    },
    remove(row) {
      this.removeIds.push(row.id)
      this.search = ''
      this.searchQuery = { ...this.searchQuery, policy_no: '' }
      this.searchQuery = { ...this.searchQuery, without: this.removeIds }
      this.fetchData()
    },
    handle() {
      const loading = Loading.service()
      this.$refs?.poundageForm?.validate((valid) => {
        if (valid) {
          this.form.premium = Number(this.totalPremium).toFixed(2)
          this.form.poundage = Number(this.totalPoundage).toFixed(2)
          this.form.ids = this.handleIds.filter((item) => !this.removeIds.includes(item)).join(',')
          const temp = Object.assign({}, this.marshalData(this.form))
          createPoundagePayment(temp)
            .then(() => {
              this.dialogVisible = false
              this.$router.push({
                name: 'PoundagePayment'
              })
              this.$message({
                type: 'success',
                message: '结算成功'
              })
              loading.close()
            })
            .finally(() => loading.close())
        }
      })
    },
    draft() {
      const loading = Loading.service()
      this.form.premium = Number(this.totalPremium).toFixed(2)
      this.form.poundage = Number(this.totalPoundage).toFixed(2)
      this.form.ids = this.handleIds.filter((item) => !this.removeIds.includes(item)).join(',')
      draftPoundagePayment(Object.assign({}, this.marshalData(this.form)))
        .then(() => {
          this.dialogVisible = false
          this.$router.push({
            name: 'PoundagePayment'
          })
          this.$message({
            type: 'success',
            message: '暂存成功'
          })
          loading.close()
        })
        .finally(() => loading.close())
    },
    handleClose() {
      this.dialogVisible = false
    },
    marshalData(data) {
      if (Object.keys(data).length <= 0) {
        return data
      }
      if (data.paid_at) {
        data.paid_at = dayjs(data.paid_at).format('YYYY-MM-DD HH:mm:ss')
      }
      return data
    }
  }
}
</script>

<style scoped>
.feePayment {
  padding: 0 20px;
}
</style>
