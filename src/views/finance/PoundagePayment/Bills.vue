<template>
  <div class="w-100 invoice">
    <SearchPanel
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
    ></SearchPanel>
    <el-card shadow="never" class="box-card-options m-extra-large-t" :body-style="{ padding: '20px' }">
      <DefineTable :cols="cols" :data="tableData" :paging="paging" :pagingEvents="pagingEvents"></DefineTable>
    </el-card>
  </div>
</template>

<script>
import DefineTable from '@/components/globals/DefineTable/DefineTable'
import { getPlatformProductCompanies } from '@/apis/platform_product'
import { poundagePaymentBills, exportPoundagePaymentBills, exportPoundagePayments } from '@/apis/finance' //exportPoundagePaymentBills
import { tokenKey } from '@/config'
import { Loading } from 'element-ui'

export default {
  name: 'PoundagePaymentBills',
  components: {
    DefineTable
  },
  data() {
    return {
      draftDialog: {
        visible: false,
        form: {
          id: '',
          paid_at: '',
          actual_poundage: '',
          proof: '',
          remark: ''
        },
        rules: {
          paid_at: [{ required: true, message: '请选择支付时间', trigger: 'blur' }],
          actual_poundage: [{ required: true, message: '请输入结算金额', trigger: 'blur' }],
          proof: [{ required: true, message: '请上传支付凭证', trigger: 'blur' }]
        }
      },
      searchFields: [
        {
          type: 'select',
          valKey: 'company_id',
          hintText: '保险公司',
          onChangeReset: 'company_branch_id',
          options: []
        },
        {
          type: 'select',
          valKey: 'company_branch_id',
          hintText: '出单公司',
          options: []
        },
        {
          type: 'textarea',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'input',
          valKey: 'operator',
          hintText: '处理人'
        },
        {
          type: 'daterange',
          hintText: '支付',
          valKey: 'paid_at_range'
        },
        {
          type: 'select',
          valKey: 'bill_status',
          hintText: '状态',
          options: [
            {
              label: '未提交',
              value: 0
            },
            {
              label: '已提交',
              value: 1
            },
            {
              label: '审核中',
              value: 3
            },
            {
              label: '已完成',
              value: 2
            }
          ]
        }
      ],
      cols: [
        { label: '流水号', prop: 'order_no' },
        // { label: '出单公司', prop: 'company_branch.name' },
        {
          label: '单据数',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    type="primary"
                    onClick={() => {
                      this.$router.push({
                        name: 'PoundageBillPayments',
                        params: {
                          id: scoped.row.id
                        }
                      })
                    }}
                  >
                    {scoped.row.payment_num}
                  </el-link>
                </div>
              )
            }
          }
        },
        // { label: '保单数', prop: 'policy_num' },
        { label: '保费总数', prop: 'premium' },
        { label: '出单公司', prop: 'company_branch.name' },
        { label: '经纪费应结', prop: 'poundage' },
        { label: '经纪费实结', prop: 'actual_poundage' },
        { label: '处理人', prop: 'operation.name' },
        {
          label: '支付时间',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.status !== 2) {
                return '-'
              }
              return scoped.row.paid_at
            }
          }
        },
        { label: '备注', prop: 'remark' },
        {
          label: '状态',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.operation.id !== -1 && scoped.row.status === 1) {
                return '审核中'
              }
              const types = {
                0: '未提交',
                1: '已提交',
                2: '已完成',
                3: '已退回'
              }

              return types[scoped.row.status]
            }
          }
        },
        // JSX 插槽
        {
          label: '支付凭证',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.proof) {
                return (
                  <div>
                    <el-link
                      type="primary"
                      href={`${scoped.row.proof}?token=` + window.localStorage.getItem(tokenKey)}
                      download=""
                      target="_blank"
                    >
                      点击查看
                    </el-link>
                  </div>
                )
              } else {
                return '-'
              }
            }
          }
        },
        {
          label: '操作',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    type="primary"
                    plain
                    size="small"
                    v-can={{ name: 'finance.poundage-payment.bills.export' }}
                    onClick={() => this.paymentsExport(scoped.row.id)}
                    disabled={scoped.row.status === 3}
                    target="_blank"
                  >
                    导出清单
                  </el-link>
                </div>
              )
            }
          }
        }
      ],
      tableData: [],
      paging: {
        currentPage: 1,
        pageSize: 0,
        layout: 'prev, pager, next, jumper, total',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page

          this.fetchBills()
        }
      },
      searchQuery: {},
      searchData: {},
      rawCompanies: []
    }
  },
  watch: {
    searchData: {
      immediate: true,
      deep: true,
      handler(value, old) {
        if (old?.company_id !== value?.company_id) {
          this.loadCompanyBranches()
        }
      }
    }
  },
  created() {
    getPlatformProductCompanies().then((r) => {
      this.rawCompanies = r.data

      this.loadCompanies()
      this.loadCompanyBranches()
    })
    this.fetchBills()
  },
  methods: {
    handleSearchPanel(command, data) {
      this.searchQuery = data
      this.paging.page = 1
      if (command === 'export') {
        this.exportData()
      } else {
        this.fetchBills()
      }
    },
    fetchBills() {
      const loading = Loading.service()
      poundagePaymentBills({
        page: this.paging.page,
        filter: this.searchQuery
      })
        .then((r) => {
          this.tableData = r.data

          this.paging.currentPage = r.meta.current_page
          this.paging.pageSize = r.meta.per_page
          this.paging.total = r.meta.total

          loading.close()
        })
        .finally(() => loading.close())
    },
    loadCompanies() {
      const options = this.rawCompanies.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('company_id', options)
    },
    loadCompanyBranches() {
      let options = []
      if (this.searchData?.company_id !== '' && this.searchData?.company_id !== undefined) {
        const company = this.rawCompanies.find((e) => e.id === this.searchData?.company_id)
        if (company) {
          options = company.branches.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      } else {
        this.rawCompanies.forEach((e) => {
          e?.branches.forEach((e) => {
            options.push({
              label: e.name,
              value: e.id
            })
          })
        })
      }

      this.assignSelectOptions('company_branch_id', options)
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    },
    exportData() {
      const link = exportPoundagePaymentBills({
        filter: this.searchQuery
      })
      window.open(link, '_blank')
    },
    paymentsExport(billId) {
      exportPoundagePayments({
        bill_id: billId
      }).then(() => {
        this.$message({
          type: 'success',
          message: '提交申请已提交至处理队列,处理结果请查看登录账号邮箱'
        })
        this.fetchBills()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.invoice {
  padding: 0 20px;
}
.a_style {
  color: #fff;
  text-decoration: none;
}
</style>
