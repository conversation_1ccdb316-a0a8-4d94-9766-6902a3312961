<template>
  <div class="w-100 invoice">
    <SearchPanel
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
    ></SearchPanel>
    <el-card shadow="never" class="box-card-options m-extra-large-t" :body-style="{ padding: '20px' }">
      <DefineTable :cols="cols" :data="tableData" :paging="paging" :pagingEvents="pagingEvents"></DefineTable>
    </el-card>
  </div>
</template>

<script>
import DefineTable from '@/components/globals/DefineTable/DefineTable'
import { getPlatformProductCompanies } from '@/apis/platform_product'
import {
  poundagePaymentBills,
  exportPoundagePaymentBills,
  exportPoundagePayments,
  associatePoundagePaymentAuditor,
  handlePoundagePayment,
  sendBackPoundagePayment
} from '@/apis/finance' //exportPoundagePaymentBills
import { tokenKey } from '@/config'
import { Loading } from 'element-ui'
import { mapGetters } from 'vuex'

export default {
  name: 'PoundagePaymentBills',
  components: {
    DefineTable
  },
  data() {
    return {
      searchFields: [
        {
          type: 'select',
          valKey: 'company_id',
          hintText: '保险公司',
          onChangeReset: 'company_branch_id',
          options: []
        },
        {
          type: 'select',
          valKey: 'company_branch_id',
          hintText: '出单公司',
          options: []
        },
        {
          type: 'textarea',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'input',
          valKey: 'operator',
          hintText: '处理人'
        },
        {
          type: 'select',
          valKey: 'audit_status',
          hintText: '状态',
          options: [
            {
              label: '已提交',
              value: 1
            },
            {
              label: '审核中',
              value: 2
            }
          ]
        }
      ],
      cols: [
        { label: '流水号', prop: 'order_no' },
        // { label: '出单公司', prop: 'company_branch.name' },
        {
          label: '单据数',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    type="primary"
                    onClick={() => {
                      this.$router.push({
                        name: 'AuditPoundagePaymentBillPayments',
                        params: {
                          id: scoped.row.id
                        }
                      })
                    }}
                  >
                    {scoped.row.payment_num}
                  </el-link>
                </div>
              )
            }
          }
        },
        // { label: '保单数', prop: 'policy_num' },
        { label: '保费总数', prop: 'premium' },
        { label: '经纪费应结', prop: 'poundage' },
        { label: '经纪费实结', prop: 'actual_poundage' },
        { label: '处理人', prop: 'operation.name' },
        {
          label: '状态',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.operation.id != -1) {
                return '审核中'
              }

              return '已提交'
            }
          }
        },
        // JSX 插槽
        {
          label: '支付凭证',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.proof) {
                return (
                  <div>
                    <el-link
                      type="primary"
                      href={`${scoped.row.proof}?token=` + window.localStorage.getItem(tokenKey)}
                      download=""
                      target="_blank"
                    >
                      点击查看
                    </el-link>
                  </div>
                )
              } else {
                return '-'
              }
            }
          }
        },
        { label: '备注', prop: 'remark' },
        {
          label: '操作',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.operation.id == -1) {
                return (
                  <div>
                    <el-link
                      style="margin-right: 10px"
                      type="primary"
                      plain
                      size="small"
                      v-can={{ name: 'finance.poundage-payment.bills.auditor' }}
                      onClick={() => this.associateAuditor(scoped.row.id)}
                    >
                      领取
                    </el-link>
                    <el-link
                      type="primary"
                      plain
                      size="small"
                      v-can={{ name: 'finance.poundage-payment.bills.export' }}
                      onClick={() => this.paymentsExport(scoped.row.id)}
                      download=""
                      target="_blank"
                    >
                      导出清单
                    </el-link>
                  </div>
                )
              } else {
                return (
                  <div>
                    <el-link
                      style="margin-right: 10px"
                      type="primary"
                      plain
                      size="small"
                      v-can={{ name: 'finance.poundage-payment.bills.handle' }}
                      onClick={() => this.handlePayment(scoped.row.id)}
                      disabled={scoped.row.operation.id !== this.admin.id}
                    >
                      确认支付
                    </el-link>
                    <el-link
                      style="margin-right: 10px"
                      type="primary"
                      plain
                      size="small"
                      v-can={{ name: 'finance.poundage-payment.bills.send-back' }}
                      onClick={() => this.sendBackPayment(scoped.row.id)}
                      disabled={scoped.row.operation.id !== this.admin.id}
                    >
                      退回
                    </el-link>
                  </div>
                )
              }
            }
          }
        }
      ],
      tableData: [],
      paging: {
        currentPage: 1,
        pageSize: 0,
        layout: 'prev, pager, next, jumper, total',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page

          this.fetchBills()
        }
      },
      searchQuery: {},
      searchData: {},
      rawCompanies: []
    }
  },
  watch: {
    searchData: {
      immediate: true,
      deep: true,
      handler(value, old) {
        if (old?.company_id !== value?.company_id) {
          this.loadCompanyBranches()
        }
      }
    }
  },
  computed: {
    ...mapGetters('auth', ['admin'])
  },
  created() {
    getPlatformProductCompanies().then((r) => {
      this.rawCompanies = r.data

      this.loadCompanies()
      this.loadCompanyBranches()
    })
    this.fetchBills()
  },
  methods: {
    handleSearchPanel(command, data) {
      this.searchQuery = data
      this.paging.page = 1
      if (command === 'export') {
        this.exportData()
      } else {
        this.fetchBills()
      }
    },
    fetchBills() {
      const loading = Loading.service()
      poundagePaymentBills({
        page: this.paging.page,
        filter: Object.assign({ status: 1 }, this.searchQuery)
      })
        .then((r) => {
          this.tableData = r.data

          this.paging.currentPage = r.meta.current_page
          this.paging.pageSize = r.meta.per_page
          this.paging.total = r.meta.total

          loading.close()
        })
        .finally(() => loading.close())
    },
    associateAuditor(id) {
      this.$confirm('是否确定领取?', '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        type: 'warning'
      }).then(() => {
        associatePoundagePaymentAuditor(id).then(() => {
          this.$message.success('领取成功')
          this.fetchBills()
        })
      })
    },
    handlePayment(id) {
      this.$confirm('是否确定完成支付?', '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        type: 'warning'
      }).then(() => {
        handlePoundagePayment(id).then(() => {
          this.$message.success('操作成功')
          this.fetchBills()
        })
      })
    },
    sendBackPayment(id) {
      this.$confirm('是否确定退回?', '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        type: 'warning'
      }).then(() => {
        sendBackPoundagePayment(id).then(() => {
          this.$message.success('退回成功')
          this.fetchBills()
        })
      })
    },
    loadCompanies() {
      const options = this.rawCompanies.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('company_id', options)
    },
    loadCompanyBranches() {
      let options = []
      if (this.searchData?.company_id !== '' && this.searchData?.company_id !== undefined) {
        const company = this.rawCompanies.find((e) => e.id === this.searchData?.company_id)
        if (company) {
          options = company.branches.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      } else {
        this.rawCompanies.forEach((e) => {
          e?.branches.forEach((e) => {
            options.push({
              label: e.name,
              value: e.id
            })
          })
        })
      }

      this.assignSelectOptions('company_branch_id', options)
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    },
    exportData() {
      const link = exportPoundagePaymentBills({
        filter: Object.assign({ status: 1 }, this.searchQuery)
      })
      window.open(link, '_blank')
    },
    paymentsExport(billId) {
      exportPoundagePayments({
        bill_id: billId
      }).then(() => {
        this.$message({
          type: 'success',
          message: '提交申请已提交至处理队列,处理结果请查看登录账号邮箱'
        })
        this.fetchBills()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.invoice {
  padding: 0 20px;
}
.a_style {
  color: #fff;
  text-decoration: none;
}
</style>
