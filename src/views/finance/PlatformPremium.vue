<template>
  <div class="w-100 borderbox documents">
    <el-card class="policy-search" shadow="never">
      <div slot="header">
        <el-row :gutter="10">
          <el-col :span="6">
            <el-input v-model="formInline.order_no" placeholder="请输入流水号"></el-input>
          </el-col>
          <el-col :span="6">
            <el-input v-model="formInline.policy_no" placeholder="请输入保单号"></el-input>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="formInline.business_source"
              placeholder="请选择业务来源"
              clearable
              filterable
              style="width: 100%"
            >
              <el-option v-for="p in platforms" :key="p.id" :value="p.id" :label="p.name" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="formInline.product_source"
              placeholder="请选择产品来源"
              clearable
              filterable
              style="width: 100%"
            >
              <el-option v-for="p in platforms" :key="p.id" :value="p.id" :label="p.name" />
            </el-select>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="m-mini-t">
          <el-col :span="6">
            <el-select v-model="formInline.type" placeholder="请选择险种">
              <el-option v-for="t in types" :key="t.value" :value="t.value" :label="t.label"></el-option>
            </el-select>
          </el-col>
          <el-col :span="6">
            <company-branches v-model="formInline.company_branch_id" />
          </el-col>
          <el-col :span="6">
            <el-date-picker
              v-model="formInline.time"
              type="daterange"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-col>
        </el-row>
      </div>
      <el-button type="primary" @click="onSubmit">查询</el-button>
      <el-button type="primary" @click="resetTable">重置</el-button>
    </el-card>
    <el-card shadow="never" class="box-card-options m-extra-large-t">
      <!-- <div class="d-flex justify-content-between">
        <el-button type="primary" @click="receivable" style="margin-bottom: 15px">保费收取</el-button>
        <el-button type="primary" @click="receivableBills" style="margin-bottom: 15px">收取记录</el-button>
      </div> -->
      <div>
        <el-button type="primary" @click="exportData">导出列表 </el-button>
      </div>
      <DefineTable
        :cols="cols"
        :data="tableData"
        :paging="pagingAttrs"
        :pagingEvents="pagingEvents"
        :events="tableEvents"
      ></DefineTable>
    </el-card>
  </div>
</template>

<script>
import CompanyBranches from '@/components/selectors/CompanyBranches'
import { getPlatformPremiums, exportPlatformPremiums } from '@/apis/finance'
import { getPlatformsDict } from '@/apis/platform'

export default {
  name: 'PlatformPremium',
  components: {
    CompanyBranches
  },
  data() {
    return {
      types: [
        { value: 1, label: '国内货运险' },
        { value: 2, label: '国际货运险' },
        { value: 3, label: '单车责任险' },
        { value: 4, label: '其他险种' },
        { value: 5, label: '雇主责任险' }
      ],
      options: [],
      platforms: [],
      // 搜索
      formInline: {
        order_no: void 0,
        company_branch_id: void 0,
        business_source: void 0,
        product_source: void 0,
        policy_no: void 0,
        name: void 0,
        starts_at: '',
        ends_at: '',
        time: []
      },
      cols: [
        // { align: 'center', type: 'selection', width: '80' },
        { label: '流水号', prop: 'order_no' },
        { label: '保单号', prop: 'policy_no', width: '200', fixed: 'left' },
        { label: '业务来源', prop: 'business_source.name' },
        { label: '产品来源', prop: 'product_source.name' },
        {
          label: '险种',
          scopedSlots: {
            default: (scoped) => {
              const types = {
                1: '国内货运险',
                2: '国际货运险',
                3: '单车责任险',
                4: '其他险种',
                5: '雇主责任险',
                6: '线下录入保单',
                7: '跨境电商险'
              }

              return types[scoped.row.policy.type]
            }
          }
        },
        { label: '平台出单费', prop: 'premium' },
        { label: '出单公司', prop: 'company_branch_name' },
        // { label: '来源', prop: 'f' },
        { label: '生效时间', prop: 'issued_at' }
      ],
      tableData: [],
      pagingAttrs: {
        align: 'center',
        currentPage: 1,
        pageSize: 15,
        layout: 'total, prev, pager, next, jumper',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          const _obj = Object.assign({}, this.formInline, { page: page })
          _obj.starts_at = _obj?.time?.[0]
          _obj.ends_at = _obj?.time?.[1]
          delete _obj.time
          this.getList(_obj)
        }
      },
      tableEvents: {
        'selection-change': (val) => {
          this.multipleSelection = val
        }
      },
      multipleSelection: []
    }
  },
  created() {
    getPlatformsDict({
      is_enabled: 1
    }).then((r) => (this.platforms = r.data))
    this.getList()
  },
  methods: {
    getList(data = {}) {
      const page = data?.page || 1
      delete data.page
      getPlatformPremiums({ filter: data, page }).then((r) => {
        const _temp = r.data
        _temp.forEach((item) => {
          item.policy_no = item.policy.policy_no
          item.user_name = item.user.name
          item.company_branch_name = item.company_branch.name
        })
        this.tableData = _temp
        const _meta = r.meta
        this.pagingAttrs.currentPage = _meta.current_page
        this.pagingAttrs.total = _meta.total
      })
    },
    receivable() {
      const count = this.multipleSelection.length
      if (!count) {
        this.$message({
          type: 'warning',
          message: '请至少选择一项'
        })
        return
      }
      this.$router.push({
        name: 'HandlePremiumReceivable',
        query: {
          id: this.multipleSelection.map((e) => e.id).join(',')
        }
      })
    },
    onSubmit() {
      const _obj = Object.assign({}, this.formInline)
      if (_obj.time) {
        _obj.starts_at = _obj?.time?.[0]
        _obj.ends_at = _obj?.time?.[1]
      }
      delete _obj.time
      this.getList(_obj)
    },
    resetTable() {
      // this.$refs['fromData'].resetFields()
      this.formInline = {
        order_no: void 0,
        company_branch_id: void 0,
        business_source: void 0,
        product_source: void 0,
        policy_no: void 0,
        name: void 0,
        starts_at: '',
        ends_at: '',
        time: []
      }
    },
    receivableBills() {
      this.$router.push({
        name: 'PremiumReceivableBills'
      })
    },
    exportData() {
      const _obj = Object.assign({}, this.formInline)
      if (_obj.time) {
        _obj.starts_at = _obj?.time?.[0]
        _obj.ends_at = _obj?.time?.[1]
      }
      delete _obj.time
      const link = exportPlatformPremiums(_obj)
      window.open(link, '_blank')
    }
  }
}
</script>

<style scoped>
.documents {
  padding: 0 20px;
}
.el-select,
.el-select--small {
  width: 100%;
}
.box-card-options {
  margin-bottom: 20px;
}
</style>

<style lang="scss" scoped>
.policy-search {
  /deep/.el-select,
  /deep/.el-date-editor {
    width: 100% !important;
  }
  /deep/.el-card__body {
    display: flex;
  }
}
</style>
