<template>
  <div class="w-100 p-extra-large-x m-extra-large-b o-hidden-x">
    <SearchPanel
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
    ></SearchPanel>
    <el-card shadow="never" class="box-card-options m-extra-large-t">
      <DefineTable :cols="cols" :data="tableData" :paging="pagingAttrs" :pagingEvents="pagingEvents"></DefineTable>
      <el-dialog :title="sendDialog.title" :visible.sync="sendDialog.visible" width="30%">
        <el-form
          label-position="left"
          label-width="100px"
          :model="sendDialog.form"
          ref="form"
          :rules="sendDialog.rules"
        >
          <el-form-item label="退回理由：" prop="reason">
            <el-input v-model="sendDialog.form.reason" placeholder="请输入退回理由"></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="sendDialog.visible = false">取 消</el-button>
          <el-button type="primary" @click="snedBack(sendDialog.paymentId)">确 定</el-button>
        </span>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import DefineTable from '@/components/globals/DefineTable/DefineTable'
import { rechargeList, sureRecharge, paymentSendBack, exportPayments } from '@/apis/finance'
import { Loading } from 'element-ui'
import { tokenKey } from '@/config'

export default {
  name: 'invoice',
  components: {
    DefineTable
  },
  data() {
    return {
      searchFields: [
        {
          type: 'input',
          valKey: 'name',
          hintText: '充值用户'
        },
        {
          type: 'select',
          valKey: 'type',
          hintText: '类型',
          options: [
            { value: 1, label: '充值' },
            { value: 2, label: '支付' },
            { value: 3, label: '扣费' }
          ]
        },
        {
          type: 'select',
          valKey: 'charge_type',
          hintText: '充值方式',
          options: [
            { value: 1, label: '真实充值' },
            { value: 2, label: '虚拟充值' },
            { value: 3, label: '补缴欠款' },
            { value: 4, label: '支付保司' },
            { value: 5, label: '系统扣费' },
            { value: 6, label: '代理充值' }
          ]
        },
        {
          type: 'select',
          valKey: 'status',
          hintText: '状态',
          options: [
            { value: 1, label: '已提交' },
            { value: 2, label: '已完成' },
            { value: 3, label: '已退回' },
            { value: 4, label: '未支付' }
          ]
        },
        {
          type: 'daterange',
          valKey: 'time'
        }
      ],
      cols: [
        { label: '流水号', prop: 'order_no', width: 150, fixed: 'left' },
        { label: '充值用户', prop: 'user.name', fixed: 'left' },
        {
          label: '类型',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              switch (scoped.row.type) {
                case 1:
                  return <span class="text-primary">充值</span>
                case 2:
                  return <span class="text-info">支付</span>
                case 3:
                  return <span class="text-warning">扣费</span>
                default:
                  return <span class="text-info">未知</span>
              }
            }
          }
        },
        {
          label: '方式',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              switch (scoped.row.charge_type) {
                case 1:
                  return <span class="text-info">真实充值</span>
                case 2:
                  return <span class="text-warning">虚拟充值</span>
                case 3:
                  return <span class="text-success">补缴欠款</span>
                case 4:
                  return <span class="text-blue">支付保司</span>
                case 5:
                  return <span class="text-warning">系统扣费</span>
                case 6:
                  return <span class="text-warning">代理充值</span>
                case 7:
                  return <span class="text-warning">代理扣费</span>
                case 8:
                  return <span class="text-info">在线充值</span>
                default:
                  return <span class="text-info">未知</span>
              }
            }
          }
        },
        { label: '充值金额', prop: 'amount' },
        {
          label: '支付凭证',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.proof != '') {
                return (
                  <div>
                    <el-link
                      type="primary"
                      href={`${scoped.row.proof}?token=` + window.localStorage.getItem(tokenKey)}
                      download=""
                      target="_blank"
                    >
                      点击查看
                    </el-link>
                  </div>
                )
              } else {
                return <span>-</span>
              }
            }
          }
        },
        { label: '申请人', prop: 'apply.name' },
        { label: '处理人', prop: 'operated.name' },
        { label: '处理时间', prop: 'operated_at' },
        {
          label: '状态',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              switch (scoped.row.status) {
                case 1:
                  return <span class="text-warning">已提交</span>
                case 2:
                  return <span class="text-success">已完成</span>
                case 3:
                  return <span class="text-info">已退回</span>
                case 4:
                  return <span class="text-danger">未支付</span>
                default:
                  return <span class="text-info">未知</span>
              }
            }
          }
        },
        { label: '申请时间', prop: 'created_at' },
        // JSX 插槽
        {
          label: '操作',
          align: 'center',
          width: 150,
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.status === 1) {
                return (
                  <div>
                    <el-link
                      type="primary"
                      v-can={{ name: 'finance.payment.handle' }}
                      onClick={() => this.sureR(scoped.row.id)}
                    >
                      确认处理
                    </el-link>
                    <el-link
                      type="danger"
                      class="m-extra-large-x"
                      v-can={{ name: 'finance.payment.send-back' }}
                      onClick={() => ((this.sendDialog.visible = true), (this.sendDialog.paymentId = scoped.row.id))}
                    >
                      退回
                    </el-link>
                  </div>
                )
              } else {
                return '-'
              }
            }
          }
        }
      ],
      sendDialog: {
        visible: false,
        title: '充值申请退回',
        paymentId: void 0,
        form: {
          reason: ''
        },
        rules: {
          reason: [{ required: true, message: '请输入退回理由', trigger: 'blur' }]
        }
      },
      tableData: [],
      searchData: {},
      pagingAttrs: {
        align: 'center',
        currentPage: 1,
        pageSize: 15,
        layout: 'total, prev, pager, next, jumper',
        total: 4
      },
      pagingEvents: {
        currentChange: (page) => {
          this.pagingAttrs.page = page
          this.getPayments()
        }
      }
    }
  },
  mounted() {
    this.getPayments()
  },
  methods: {
    // 获取列表
    getPayments() {
      const _obj = Object.assign({}, this.searchData)
      if (_obj.time) {
        _obj.starts_at = _obj?.time?.[0]
        _obj.ends_at = _obj?.time?.[1]
      }
      delete _obj.time

      rechargeList({ filter: _obj, page: this.pagingAttrs.page }).then((r) => {
        this.tableData = r.data
        this.pagingAttrs.currentPage = r.meta.current_page
        this.pagingAttrs.total = r.meta.total
      })
    },
    // 确认充值
    sureR(id) {
      const loading = Loading.service()
      sureRecharge(id)
        .then(() => {
          this.getPayments()
          this.$message({
            type: 'success',
            message: '确认充值成功'
          })
        })
        .finally(() => loading.close())
    },
    snedBack(id) {
      const loading = Loading.service()
      this.sendDialog.visible = false
      paymentSendBack(id, this.sendDialog.form)
        .then(() => {
          this.getPayments()
          this.$message({
            type: 'success',
            message: '退回成功'
          })
        })
        .finally(() => loading.close())
    },
    handleSearchPanel(cmd, data) {
      this.searchData = data
      this.pagingAttrs.page = 1

      if (cmd === 'export') {
        this.exportData()
      }

      this.getPayments()
    },
    exportData() {
      const _obj = Object.assign({}, this.searchData)
      if (_obj.time) {
        _obj.starts_at = _obj?.time?.[0]
        _obj.ends_at = _obj?.time?.[1]
      }
      delete _obj.time

      const link = exportPayments({ filter: _obj })
      window.open(link, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.invoice {
  padding: 0 20px;
}
</style>
