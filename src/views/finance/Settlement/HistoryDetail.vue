<!--
 * @Descripttion: 
 * @version: 
 * @Author: yanb
 * @Date: 2022-03-22 15:45:38
 * @LastEditors: yanb
 * @LastEditTime: 2023-04-18 15:31:35
-->
<template>
  <div class="w-100">
    <div class="m-extra-large-x p-extra-large bg-white m-extra-large-b flex-fill o-hidden o-y-auto">
      <div></div>
      <define-details :data="settlementDetail"></define-details>
    </div>
    <div class="m-extra-large-x p-extra-large bg-white m-extra-large-b flex-fill o-hidden o-y-auto">
      <el-card shadow="never" class="box-card-options m-extra-large-t">
        <DefineTable :cols="memoCols" :data="memos" :paging="memoPaging" :pagingEvents="memoPagingEvents"></DefineTable>
      </el-card>
      <el-card shadow="never" class="box-card-options m-extra-large-t">
        <DefineTable
          :cols="receivableCols"
          :data="receivables"
          :paging="receivablePaging"
          :pagingEvents="receivablePagingEvents"
        ></DefineTable>
      </el-card>
    </div>
  </div>
</template>

<script>
import { getSettlement, getSettlementMemos, getSettlementReceivables } from '@/apis/finance'
import { Loading } from 'element-ui'
import { mapGetters } from 'vuex'

export default {
  name: 'SettlementDetail',
  data() {
    return {
      settlement: [],
      memos: [],
      memoCols: [
        { label: '水单号', prop: 'order_no', align: 'center' },
        { label: '水单金额', prop: 'amount', align: 'center' },
        { label: '录入人', prop: 'entry.name', align: 'center' },
        { label: '付款时间', prop: 'paid_at', align: 'center' },
        { label: '付款人', prop: 'payer', align: 'center' },
        { label: '收款人', prop: 'payee.name', align: 'center' },
        {
          label: '水单状态',
          scopedSlots: {
            default: (scoped) => {
              switch (scoped.row.status) {
                case 1:
                  return '未使用'
                case 2:
                  return '已使用'
                case 3:
                  return '待使用'
                default:
                  break
              }
            }
          }
        },
        {
          label: '水单类型',
          scopedSlots: {
            default: (scoped) => {
              switch (scoped.row.type) {
                case 1:
                  return '支付保司'
                case 2:
                  return '支付平台'
                default:
                  break
              }
            }
          }
        },
        { label: '备注', prop: 'remark', align: 'center' }
      ],
      memoPaging: {
        align: 'center',
        currentPage: 1,
        pageSize: 15,
        layout: 'total, prev, pager, next, jumper',
        total: 0
      },
      memoPagingEvents: {
        currentChange: (page) => {
          this.memoPaging.currentPage = page

          this.fetchSettlementMemos()
        }
      },
      receivables: [],
      receivableCols: [
        { label: '保单号', prop: 'policy.policy_no', align: 'center' },
        { label: '出单公司', prop: 'company_branch.name', align: 'center' },
        {
          label: '险种',
          scopedSlots: {
            default: (scoped) => {
              switch (scoped.row.policy.type) {
                case 1:
                  return '国内货运险'
                case 2:
                  return '国际货运险'
                case 3:
                  return '单车责任险'
                case 4:
                  return '其他险种'
                case 5:
                  return '雇主责任险'
                default:
                  break
              }
            }
          }
        },
        { label: '保费', prop: 'policy.user_premium', align: 'center' },
        { label: '投保人', prop: 'policy.policyholder', align: 'center' },
        { label: '被保人', prop: 'policy.insured', align: 'center' },
        { label: '投保时间', prop: 'policy.submitted_at', align: 'center' },
        { label: '投保用户', prop: 'user.name', align: 'center' }
      ],
      receivablePaging: {
        align: 'center',
        currentPage: 1,
        pageSize: 15,
        layout: 'total, prev, pager, next, jumper',
        total: 0
      },
      receivablePagingEvents: {
        currentChange: (page) => {
          this.receivablePaging.currentPage = page

          this.fetchSettlementReceivables()
        }
      }
    }
  },
  computed: {
    ...mapGetters('auth', ['admin']),
    settlementDetail() {
      return {
        title: '销账详情',
        data: [
          {
            title: '保单信息',
            groups: [
              { label: '流水号', value: this.settlement?.order_no ?? '' },
              { label: '申请人', value: this.settlement?.apply?.name ?? '' },
              { label: '水单总金额', value: this.settlement?.memo_amount ?? '' },
              { label: '保单总金额', value: this.settlement?.policy_amount ?? '' },
              { label: '水单数', value: this.settlement?.memo_num ?? '' },
              { label: '保单数', value: this.settlement?.policy_num ?? '' },
              { label: '收款人', value: this.settlement?.payee?.name ?? '' },
              {
                label: '状态',
                value: {
                  1: '已提交',
                  2: '审核中',
                  3: '已完成',
                  4: '已退回'
                }[this.settlement?.status]
              },
              { label: '申请时间', value: this.settlement?.created_at ?? '' },
              { label: '处理时间', value: this.settlement?.operated_at ?? '' }
            ]
          }
        ]
      }
    }
  },
  created() {
    this.fetchSettlement()
    this.fetchSettlementMemos()
    this.fetchSettlementReceivables()
  },
  methods: {
    fetchSettlement() {
      getSettlement(this.$route.params.id).then((r) => {
        this.settlement = r.data
      })
    },
    fetchSettlementMemos() {
      getSettlementMemos(this.$route.params.id).then((r) => {
        this.memos = r.data

        this.memoPaging.currentPage = r.meta.current_page
        this.memoPaging.pageSize = r.meta.per_page
        this.memoPaging.total = r.meta.total
      })
    },
    fetchSettlementReceivables() {
      getSettlementReceivables(this.$route.params.id).then((r) => {
        this.receivables = r.data

        this.receivablePaging.currentPage = r.meta.current_page
        this.receivablePaging.pageSize = r.meta.per_page
        this.receivablePaging.total = r.meta.total
      })
    },
    getSettlementStatus() {
      switch (this.settlement.status) {
        case 1:
          return '已提交'
        case 2:
          return <span class="text-primary">审核中</span>
        case 3:
          return <span class="text-success">已完成</span>
        case -1:
          return <span class="text-info">已退回</span>
        default:
          break
      }
    }
  }
}
</script>
