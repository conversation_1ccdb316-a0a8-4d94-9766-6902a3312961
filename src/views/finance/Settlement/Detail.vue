<!--
 * @Descripttion: 
 * @version: 
 * @Author: yanb
 * @Date: 2022-03-22 15:45:38
 * @LastEditors: yanb
 * @LastEditTime: 2024-08-01 11:12:24
-->
<template>
  <div class="w-100">
    <div class="m-extra-large-x p-extra-large bg-white m-extra-large-b flex-fill o-hidden o-y-auto">
      <el-alert
        v-if="settlement?.status === 4"
        style="margin: 0 0 10px"
        type="warning"
        title="销账申请已被退回"
        :description="settlement?.reason"
        show-icon
        :closable="false"
      />
      <div>
        <el-row>
          <el-col>
            <div style="float: right">
              <el-popconfirm
                v-can="{ name: 'finance.settlement.receive' }"
                v-if="settlement?.status === 1"
                style="margin-right: 10px"
                confirm-button-text="确定"
                cancel-button-text="取消"
                icon="el-icon-info"
                icon-color="#ff7f4c"
                title="确定进行该操作吗?"
                @confirm="receiveSettlement"
              >
                <el-button type="primary" slot="reference"> 领取 </el-button>
              </el-popconfirm>
              <el-button
                v-can="{ name: 'finance.settlement.send-mail' }"
                type="primary"
                v-if="settlement?.type === 1 && settlement?.status === 2 && admin.id === settlement?.operation?.id"
                @click="sendSettlementMail"
              >
                {{ settlement?.send_email_count > 0 ? '再次发送' : '发送邮件' }}
              </el-button>
              <el-button
                v-can="{ name: 'finance.settlement.handle' }"
                type="primary"
                v-if="settlement?.status === 2 && admin.id === settlement?.operation?.id"
                @click="handleSettlement"
              >
                销账处理
              </el-button>
              <el-button
                type="primary"
                v-can="{ name: 'finance.settlement.send-back' }"
                v-if="settlement?.status === 2 && admin.id === settlement?.operation?.id"
                @click="sendBackDialog.visible = true"
              >
                退回
              </el-button>
            </div>
          </el-col>
        </el-row>
      </div>
      <define-details :data="settlementDetail"></define-details>
    </div>
    <div class="m-extra-large-x p-extra-large bg-white m-extra-large-b flex-fill o-hidden o-y-auto">
      <el-card shadow="never" class="box-card-options m-extra-large-t">
        <DefineTable :cols="memoCols" :data="memos" :paging="memoPaging" :pagingEvents="memoPagingEvents"></DefineTable>
      </el-card>
      <el-card shadow="never" class="box-card-options m-extra-large-t">
        <div class="d-flex justify-content-start">
          <el-button type="primary" style="margin-bottom: 15px" @click="settlementPoliciesExport">保单导出</el-button>
        </div>
        <DefineTable
          :cols="receivableCols"
          :data="receivables"
          :paging="receivablePaging"
          :pagingEvents="receivablePagingEvents"
        ></DefineTable>
      </el-card>
    </div>
    <el-dialog :title="sendBackDialog.title" :visible.sync="sendBackDialog.visible" width="30%">
      <el-form
        label-position="left"
        label-width="100px"
        :model="sendBackDialog.form"
        ref="form"
        :rules="sendBackDialog.rules"
      >
        <el-form-item label="退回理由：" prop="reason">
          <el-input v-model="sendBackDialog.form.reason" placeholder="请输入退回理由"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="sendBackDialog.visible = false">取 消</el-button>
        <el-button type="primary" @click="sendBackSettlement">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getSettlement,
  getSettlementMemos,
  getSettlementReceivables,
  receiveSettlement,
  handleSettlement,
  sendBackSettlement,
  exportSettlementPolicies,
  sendSettlementMail
} from '@/apis/finance'
import { Loading } from 'element-ui'
import { mapGetters } from 'vuex'

export default {
  name: 'SettlementDetail',
  data() {
    return {
      sendBackDialog: {
        visible: false,
        title: '退回理由',
        form: {
          reason: ''
        },
        rules: {
          reason: [{ required: true, message: '请输入退回理由', trigger: 'blur' }]
        }
      },
      settlement: [],
      memos: [],
      memoCols: [
        { label: '水单号', prop: 'order_no', align: 'center' },
        { label: '水单金额', prop: 'amount', align: 'center' },
        { label: '录入人', prop: 'entry.name', align: 'center' },
        { label: '付款时间', prop: 'paid_at', align: 'center' },
        { label: '付款人', prop: 'payer', align: 'center' },
        { label: '收款人', prop: 'payee.name', align: 'center' },
        {
          label: '支付凭证',
          prop: 'proof',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.proof) {
                return (
                  <el-link type="primary" href={scoped.row.proof} target="_blank">
                    查看凭证
                  </el-link>
                )
              }

              return '-'
            }
          }
        },
        {
          label: '水单状态',
          scopedSlots: {
            default: (scoped) => {
              switch (scoped.row.status) {
                case 1:
                  return '未使用'
                case 2:
                  return '已使用'
                case 3:
                  return '待使用'
                default:
                  break
              }
            }
          }
        },
        {
          label: '水单类型',
          scopedSlots: {
            default: (scoped) => {
              switch (scoped.row.type) {
                case 1:
                  return '支付保司'
                case 2:
                  return '支付平台'
                default:
                  break
              }
            }
          }
        },
        { label: '备注', prop: 'remark', align: 'center' }
      ],
      memoPaging: {
        align: 'center',
        currentPage: 1,
        pageSize: 15,
        layout: 'total, prev, pager, next, jumper',
        total: 0
      },
      memoPagingEvents: {
        currentChange: (page) => {
          this.memoPaging.currentPage = page

          this.fetchSettlementMemos()
        }
      },
      receivables: [],
      receivableCols: [
        { label: '保单号', prop: 'policy.policy_no', align: 'center' },
        { label: '出单公司', prop: 'company_branch.name', align: 'center' },
        {
          label: '险种',
          scopedSlots: {
            default: (scoped) => {
              switch (scoped.row.policy.type) {
                case 1:
                  return '国内货运险'
                case 2:
                  return '国际货运险'
                case 3:
                  return '单车责任险'
                case 4:
                  return '其他险种'
                case 5:
                  return '雇主责任险'
                default:
                  break
              }
            }
          }
        },
        { label: '保费', prop: 'policy.settlement_premium', align: 'center' },
        { label: '投保人', prop: 'policy.policyholder', align: 'center' },
        { label: '被保人', prop: 'policy.insured', align: 'center' },
        { label: '投保时间', prop: 'policy.submitted_at', align: 'center' },
        { label: '投保用户', prop: 'user.name', align: 'center' }
      ],
      receivablePaging: {
        align: 'center',
        currentPage: 1,
        pageSize: 15,
        layout: 'total, prev, pager, next, jumper',
        total: 0
      },
      receivablePagingEvents: {
        currentChange: (page) => {
          this.receivablePaging.currentPage = page

          this.fetchSettlementReceivables()
        }
      }
    }
  },
  computed: {
    ...mapGetters('auth', ['admin']),
    settlementDetail() {
      return {
        title: '销账详情',
        data: [
          {
            title: '保单信息',
            groups: [
              { label: '流水号', value: this.settlement?.order_no ?? '' },
              { label: '申请人', value: this.settlement?.apply?.name ?? '' },
              { label: '水单总金额', value: this.settlement?.memo_amount ?? '' },
              { label: '保单总金额', value: this.settlement?.policy_amount ?? '' },
              { label: '水单数', value: this.settlement?.memo_num ?? '' },
              { label: '保单数', value: this.settlement?.policy_num ?? '' },
              { label: '收款人', value: this.settlement?.payee?.name ?? '' },
              {
                label: '状态',
                value: {
                  1: '已提交',
                  2: '审核中',
                  3: '已完成',
                  4: '已退回'
                }[this.settlement?.status]
              },
              { label: '申请时间', value: this.settlement?.created_at ?? '' },
              { label: '处理时间', value: this.settlement?.operated_at ?? '' }
            ]
          }
        ]
      }
    }
  },
  created() {
    this.fetchSettlement()
    this.fetchSettlementMemos()
    this.fetchSettlementReceivables()
  },
  methods: {
    fetchSettlement() {
      getSettlement(this.$route.params.id).then((r) => {
        this.settlement = r.data
      })
    },
    fetchSettlementMemos() {
      getSettlementMemos(this.$route.params.id, {
        page: this.memoPaging.currentPage
      }).then((r) => {
        this.memos = r.data

        this.memoPaging.currentPage = r.meta.current_page
        this.memoPaging.pageSize = r.meta.per_page
        this.memoPaging.total = r.meta.total
      })
    },
    fetchSettlementReceivables() {
      getSettlementReceivables(this.$route.params.id, {
        page: this.receivablePaging.currentPage
      }).then((r) => {
        this.receivables = r.data

        this.receivablePaging.currentPage = r.meta.current_page
        this.receivablePaging.pageSize = r.meta.per_page
        this.receivablePaging.total = r.meta.total
      })
    },
    receiveSettlement() {
      const loading = Loading.service()
      receiveSettlement(this.$route.params.id)
        .then(() => {
          this.fetchSettlement()
          this.$message({
            type: 'success',
            message: '领取成功'
          })
        })
        .finally(() => loading.close())
    },
    handleSettlement() {
      this.$confirm(`是否确认销账`, '销账处理', {
        confirmButtonText: '确认',
        cancelButtonText: '关闭',
        type: 'warning'
      })
        .then(() => {
          const loading = this.$loading({
            lock: true,
            text: '提交中...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          handleSettlement(this.settlement.id)
            .then(() => {
              this.$message({
                type: 'success',
                message: '数据已提交至处理队列,处理结果请查看登录账号邮箱'
              })

              this.fetchSettlement(this.settlement.id)
            })
            .finally(() => {
              loading.close()
            })
        })
        .catch(() => {})
    },
    sendBackSettlement() {
      const loading = Loading.service()
      this.sendBackDialog.visible = false
      sendBackSettlement(this.$route.params.id, this.sendBackDialog.form)
        .then(() => {
          this.fetchSettlement()
          this.$message({
            type: 'success',
            message: '退回成功'
          })
        })
        .finally(() => loading.close())
    },
    getSettlementStatus() {
      switch (this.settlement.status) {
        case 1:
          return '已提交'
        case 2:
          return <span class="text-primary">审核中</span>
        case 3:
          return <span class="text-success">已完成</span>
        case -1:
          return <span class="text-info">已退回</span>
        default:
          break
      }
    },
    settlementPoliciesExport() {
      const link = exportSettlementPolicies(this.settlement.id)
      window.open(link, '_blank')
    },
    sendSettlementMail() {
      this.$confirm(
        `是否确认发送邮件,该操作将会发送邮件至保险公司。 当前已发送邮件次数：[ ${this.settlement.send_email_count} ]`,
        '发送邮件',
        {
          confirmButtonText: '发送',
          cancelButtonText: '关闭',
          type: 'warning'
        }
      )
        .then(() => {
          const loading = this.$loading({
            lock: true,
            text: '发送中...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          sendSettlementMail(this.settlement.id)
            .then(() => {
              this.$message({
                type: 'success',
                message: '发送成功'
              })

              this.fetchSettlement(this.settlement.id)
            })
            .finally(() => {
              loading.close()
            })
        })
        .catch(() => {})
    }
  }
}
</script>
