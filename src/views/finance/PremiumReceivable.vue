<template>
  <div class="w-100 o-hidden-x p-extra-large-x m-extra-large-b">
    <SearchPanel
      size="small"
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
    ></SearchPanel>
    <el-alert type="error" class="m-extra-large-t" :closable="false">
      <span>
        当前应收保费
        <el-tag type="danger" effect="dark"> {{ countReceivable }} </el-tag>
      </span>
    </el-alert>
    <el-card shadow="never" class="box-card-options m-extra-large-t">
      <div class="d-flex">
        <el-button
          type="primary"
          v-can="{ name: 'finance.premium-receivable.bills.create' }"
          @click="handleReceivable"
          style="margin-bottom: 15px"
          >保费收取</el-button
        >
        <el-button
          type="primary"
          v-can="{ name: 'finance.premium-receivable.bills.create' }"
          @click="allCheckout"
          style="margin-bottom: 15px"
          >全部处理</el-button
        >
        <el-button
          type="primary"
          v-can="{ name: 'finance.premium-receivable.bills.draft' }"
          @click="draftBills"
          style="margin-bottom: 15px"
          >暂存记录</el-button
        >
        <el-button
          type="primary"
          v-can="{ name: 'finance.premium-receivable.bills.index' }"
          @click="receivableBills"
          style="margin-bottom: 15px"
          >收取记录</el-button
        >
      </div>
      <DefineTable
        :cols="cols"
        :data="tableData"
        :paging="pagingAttrs"
        :pagingEvents="pagingEvents"
        :events="tableEvents"
        :rowKey="getRowKeys"
      ></DefineTable>
    </el-card>
  </div>
</template>

<script>
import { getPremiumReceivables, getPremiumReceivableCount, exportPremiumReceivables } from '@/apis/finance'
import { getPlatformProductCompanies } from '@/apis/platform_product'
import { getOfflineProductCategories } from '@/apis/product'
import { getSales } from '@/apis/admin'
import { Loading } from 'element-ui'

export default {
  name: 'PremiumReceivable',
  data() {
    return {
      searchFields: [
        {
          type: 'textarea',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'input',
          valKey: 'business_source',
          hintText: '业务来源'
        },
        {
          type: 'select',
          valKey: 'types',
          hintText: '险种',
          isMultiple: true,
          options: []
        },
        {
          type: 'daterange',
          valKey: 'issued_at_range'
        },
        {
          type: 'select',
          valKey: 'salesman_id',
          hintText: '业务员',
          options: []
        },
        {
          type: 'select',
          valKey: 'company_id',
          hintText: '保险公司',
          onChangeReset: 'company_branch_id',
          options: []
        },
        {
          type: 'select',
          valKey: 'company_branch_id',
          hintText: '出单公司',
          options: []
        }
      ],
      cols: [
        { align: 'center', type: 'selection', width: '80', reserveSelection: true },
        // { label: '流水号', prop: 'order_no' },
        { label: '保单号', prop: 'policy.policy_no', width: '200', fixed: 'left' },
        {
          label: '险种',
          scopedSlots: {
            default: (scoped) => {
              const types = {
                1: '国内货运险',
                2: '国际货运险',
                3: '单车责任险',
                4: '其他险种',
                5: '雇主责任险',
                6: '线下录入保单',
                7: '跨境电商险'
              }

              return scoped.row.policy.type === 6 ? scoped.row.policy.offline_type : types[scoped.row.policy.type]
            }
          }
        },
        {
          label: '业务来源',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.platform.id == scoped.row.receivable_platform.id) {
                if (scoped.row.policy.type === 6 && scoped.row.policy.offline_business_type == 3) {
                  return <span class="text-primary">{scoped.row.policy.offline_business_from}</span>
                }
                return (
                  <span class="text-info">{scoped.row.agent.name ? scoped.row.agent.name : scoped.row.user.name}</span>
                )
              } else {
                return <span class="text-primary">{scoped.row.receivable_platform.name}</span>
              }
            }
          }
        },
        { label: '应收保费', prop: 'receivable' },
        {
          label: '类型',
          scopedSlots: {
            default: (scoped) => {
              const types = {
                1: '投保',
                2: '批改',
                3: '退保',
                4: '雇主批改',
                5: '退回'
              }

              return types[scoped.row.type]
            }
          }
        },
        { label: '应收平台', prop: 'receivable_platform.name' },
        { label: '出单公司', prop: 'company_branch.name' },
        // { label: '来源', prop: 'f' },
        { label: '生效时间', prop: 'policy.issued_at' }
      ],
      tableData: [],
      searchData: {},
      searchQuery: {},
      pagingAttrs: {
        currentPage: 1,
        pageSize: 15,
        layout: 'total, prev, pager, next, jumper',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.pagingAttrs.currentPage = page

          this.fetchPremiumReceivables()
        }
      },
      tableEvents: {
        'selection-change': (val) => {
          this.multipleSelection = val
        }
      },
      multipleSelection: [],
      rawCompanies: [],
      offlineCategories: [],
      countReceivable: 0
    }
  },
  created() {
    getOfflineProductCategories({ is_parent: 0, is_pageable: 0 }).then((r) => {
      this.offlineCategories = r.data

      this.loadCategories()
    })

    getPlatformProductCompanies().then((r) => {
      this.rawCompanies = r.data

      this.loadCompanies()
      this.loadCompanyBranches()
    })

    getSales().then((r) => {
      r.data.push({ name: '自然来源', id: -1 })
      this.assignSelectOptions(
        'salesman_id',
        r.data.map((e) => {
          return { label: e.name, value: e.id }
        })
      )
    })

    this.fetchPremiumReceivables()
    this.fetchCountReceivable()
  },
  methods: {
    handleSearchPanel(command, data) {
      this.searchQuery = data
      this.pagingAttrs.currentPage = 1
      if (command === 'export') {
        this.receivableExport()
      } else {
        this.fetchPremiumReceivables()
        this.fetchCountReceivable()
      }
    },
    getRowKeys(row) {
      return row.id
    },
    fetchPremiumReceivables() {
      const loading = Loading.service()
      getPremiumReceivables({
        page: this.pagingAttrs.currentPage,
        filter: this.searchQuery
      })
        .then((r) => {
          this.tableData = r.data

          this.pagingAttrs.currentPage = r.meta.current_page
          this.pagingAttrs.pageSize = r.meta.per_page
          this.pagingAttrs.total = r.meta.total

          loading.close()
        })
        .finally(() => loading.close())
    },
    fetchCountReceivable() {
      getPremiumReceivableCount({
        filter: this.searchQuery
      }).then((r) => {
        this.countReceivable = r.data.receivable
      })
    },
    loadCompanies() {
      const options = this.rawCompanies.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('company_id', options)
    },
    loadCompanyBranches() {
      let options = []
      if (this.searchData?.company_id !== '' && this.searchData?.company_id !== undefined) {
        const company = this.rawCompanies.find((e) => e.id === this.searchData?.company_id)
        if (company) {
          options = company.branches.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      } else {
        this.rawCompanies.forEach((e) => {
          e?.branches.forEach((e) => {
            options.push({
              label: e.name,
              value: e.id
            })
          })
        })
      }

      this.assignSelectOptions('company_branch_id', options)
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    },
    handleReceivable() {
      if (!this.multipleSelection.length) {
        this.$message({
          type: 'warning',
          message: '请至少选择一项'
        })
        return
      }
      this.$router.push({
        name: 'HandlePremiumReceivable',
        query: {
          id: this.multipleSelection.map((e) => e.id).join(',')
        }
      })
    },
    receivableBills() {
      this.$router.push({
        name: 'PremiumReceivableBills'
      })
    },
    draftBills() {
      this.$router.push({
        name: 'DraftPremiumReceivableBills'
      })
    },
    receivableExport() {
      const link = exportPremiumReceivables({ filter: this.searchQuery })
      window.open(link, '_blank')
    },
    allCheckout() {
      this.$router.push({
        name: 'HandlePremiumReceivable',
        query: {
          all_checkout: 1,
          ...this.searchQuery
        }
      })
    },
    loadCategories() {
      var options = [
        {
          label: '国内货运险',
          value: 1
        },
        {
          label: '国际货运险',
          value: 2
        },
        {
          label: '单车责任险',
          value: 3
        },
        {
          label: '其他险种',
          value: 4
        },
        {
          label: '雇主责任险',
          value: 5
        }
      ]
      options = options.concat(
        this.offlineCategories.map((e) => {
          return {
            label: e.name + '-线下录入',
            value: 'OF-' + e.id
          }
        })
      )
      this.assignSelectOptions('types', options)
    }
  },
  watch: {
    searchData: {
      immediate: true,
      deep: true,
      handler(value, old) {
        if (old?.company_id !== value?.company_id) {
          this.loadCompanyBranches()
        }
      }
    }
  }
}
</script>

<style scoped>
.documents {
  padding: 0 20px;
}
.el-select,
.el-select--small {
  width: 100%;
}
.box-card-options {
  margin-bottom: 20px;
}
</style>

<style lang="scss" scoped>
.policy-search {
  /deep/.el-select,
  /deep/.el-date-editor {
    width: 100% !important;
  }
  /deep/.el-card__body {
    display: flex;
  }
}
</style>
