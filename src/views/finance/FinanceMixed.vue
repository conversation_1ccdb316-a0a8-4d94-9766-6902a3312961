<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <SearchPanel
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
    ></SearchPanel>
    <el-alert type="error" class="m-extra-large-t" :closable="false">
      <span>
        当前数据
        <!-- <span class="hover-cursor">成本保费: </span>
        <el-tag type="danger" effect="dark"> {{ mixedPremiums?.premium ?? 0 }} </el-tag>，
        <span class="hover-cursor">代理保费: </span>
        <el-tag type="danger" effect="dark"> {{ mixedPremiums?.agent_premium ?? 0 }} </el-tag>， -->
        <span class="hover-cursor">用户保费: </span>
        <el-tag type="danger" effect="dark"> {{ mixedPremiums?.user_premium ?? 0 }} </el-tag>，
        <!-- <span class="hover-cursor">经纪费: </span>
        <el-tag type="danger" effect="dark"> {{ mixedPremiums?.poundage ?? 0 }} </el-tag>，
        <span class="hover-cursor">平台佣金: </span>
        <el-tag type="danger" effect="dark"> {{ mixedPremiums?.platform_commission ?? 0 }} </el-tag>，
        <span class="hover-cursor">代理佣金: </span>
        <el-tag type="danger" effect="dark"> {{ mixedPremiums?.agent_commission ?? 0 }} </el-tag>， -->
      </span>
    </el-alert>
    <el-card shadow="never" class="m-extra-large-t">
      <div class="d-flex justify-content-start">
        <!-- <el-button type="primary" style="margin-bottom: 15px">保单下载</el-button> -->
      </div>
      <define-table :data="tableData" :cols="cols" :paging="paging" :paging-events="pagingEvents" />
    </el-card>
  </div>
</template>

<script>
import { getMixedFinanceData, getMixedFinancePremiumsData, exportMixedFinanceData } from '@/apis/finance'
import { getOfflineProductCategories } from '@/apis/product'
// import { getPlatformProductCompanies } from '@/apis/platform_product'
import { getCompaniesDict } from '@/apis/company'
import { getPlatformsDict } from '@/apis/platform'
import { getSales } from '@/apis/admin'
import { Loading } from 'element-ui'
import { mapGetters } from 'vuex'

export default {
  name: 'FinanceMixed',
  data() {
    return {
      mixedPremiums: [],
      searchFields: [
        {
          type: 'textarea',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'input',
          valKey: 'policyholder',
          hintText: '投保人'
        },
        {
          type: 'select',
          valKey: 'company_id',
          hintText: '保险公司',
          onChangeReset: 'company_branch_id',
          options: []
        },
        {
          type: 'select',
          valKey: 'company_branch_id',
          hintText: '出单公司',
          isMultiple: true,
          options: []
        },
        {
          type: 'select',
          valKey: 'types',
          hintText: '险种',
          isMultiple: true,
          options: []
        },
        {
          type: 'select',
          valKey: 'salesman_id',
          hintText: '业务员',
          options: []
        },
        {
          type: 'input',
          valKey: 'name',
          hintText: '投保用户'
        },
        {
          type: 'input',
          valKey: 'agent_name',
          hintText: '代理人'
        },
        {
          type: 'daterange',
          valKey: 'submitted_at_range',
          hintText: '投保'
        },
        {
          type: 'daterange',
          valKey: 'issued_at_range',
          hintText: '出单'
        },
        {
          type: 'daterange',
          valKey: 'settlement_at_range',
          hintText: '销账'
        },
        {
          type: 'select',
          valKey: 'business_from',
          hintText: '业务来源',
          options: []
        }
      ],
      cols: [
        {
          label: '保单号',
          prop: 'policy_no',
          width: 200
        },
        {
          label: '险种',
          prop: 'type',
          scopedSlots: {
            default: (scoped) => {
              const types = {
                1: '国内货运险',
                2: '国际货运险',
                3: '单车责任险',
                4: '其他险种',
                5: '雇主责任险',
                6: '线下录入保单',
                7: '跨境电商险'
              }

              return scoped.row.type == 6 ? scoped.row.offline_type : types[scoped.row.type]
            }
          }
        },
        {
          label: '投保人',
          prop: 'policyholder'
        },
        {
          label: '保费',
          prop: 'premium'
        },
        {
          label: '出单公司',
          prop: 'company_branch.name'
        },
        {
          label: '业务员',
          prop: 'salesman.name'
        },
        {
          label: '投保用户',
          width: 180,
          scopedSlots: {
            default: (scoped) => {
              return scoped.row.user.platform_id === this.admin.platform.id ? (
                scoped.row.user.name
              ) : (
                <span class="text-primary">{scoped.row.user.platform.name}</span>
              )
            }
          }
        },
        {
          label: '发票状态',
          prop: 'is_invoice',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.is_invoice) {
                return '已申请'
              } else {
                return '未申请'
              }
            }
          }
        },
        {
          label: '销账状态',
          prop: 'is_settlement',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.is_settlement) {
                return '已销账'
              } else {
                return '未销账'
              }
            }
          }
        },
        {
          label: '投保时间',
          prop: 'submitted_at'
        }
      ],
      tableData: [],
      // 分页器 不传递 -> 没有分页
      paging: {
        currentPage: 1,
        pageSize: 0,
        layout: 'prev, pager, next, jumper, total',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.currentPage = page

          this.fetchMixedFinanceDatas()
        }
      },
      searchQuery: {},
      searchData: {},
      rawCompanies: [],
      platforms: [],
      payees: [],
      offlineCategories: []
    }
  },
  watch: {
    searchData: {
      immediate: true,
      deep: true,
      handler(value, old) {
        if (old?.company_id !== value?.company_id) {
          this.loadCompanyBranches()
        }
      }
    }
  },
  computed: {
    ...mapGetters('auth', ['admin'])
  },
  created() {
    getOfflineProductCategories({ is_parent: 0, is_pageable: 0 }).then((r) => {
      this.offlineCategories = r.data

      this.loadCategories()
    })
    getCompaniesDict().then((r) => {
      this.rawCompanies = r.data

      this.loadCompanies()
      this.loadCompanyBranches()
    })
    getSales().then((r) => {
      r.data.push({ name: '自然来源', id: -1 })
      this.assignSelectOptions(
        'salesman_id',
        r.data.map((e) => {
          return { label: e.name, value: e.id }
        })
      )
    })
    this.fetchMixedFinanceDatas()

    getPlatformsDict({
      is_enabled: 1
    }).then((r) => {
      this.platforms = r.data

      this.loadBusinessFrom()
    })
  },
  methods: {
    handleSearchPanel(command, data) {
      this.searchQuery = data
      this.paging.currentPage = 1
      if (command === 'export') {
        this.mixedFinanceExport()
      } else {
        this.fetchMixedFinanceDatas()
        this.fetchMixedFinancePremiumDatas()
      }
    },
    fetchMixedFinanceDatas() {
      const loading = Loading.service()
      getMixedFinanceData({
        page: this.paging.currentPage,
        filter: this.searchQuery
      })
        .then((r) => {
          this.tableData = r.data

          this.paging.currentPage = r.meta.current_page
          this.paging.pageSize = r.meta.per_page
          this.paging.total = r.meta.total
          loading.close()
        })
        .finally(() => loading.close())
    },
    fetchMixedFinancePremiumDatas() {
      getMixedFinancePremiumsData({
        filter: this.searchQuery
      }).then((r) => {
        this.mixedPremiums = r.data
        console.log(this.mixedPremiums)
      })
    },
    mixedFinanceExport() {
      exportMixedFinanceData({
        filter: this.searchQuery
      }).then(() => {
        this.$message({
          type: 'success',
          message: '提交申请已提交至处理队列,处理结果请查看登录账号邮箱'
        })
        this.fetchMixedFinanceDatas()
      })
    },
    loadCompanies() {
      const options = this.rawCompanies.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('company_id', options)
    },
    loadCompanyBranches() {
      let options = []
      if (this.searchData?.company_id !== '' && this.searchData?.company_id !== undefined) {
        const company = this.rawCompanies.find((e) => e.id === this.searchData?.company_id)
        if (company) {
          options = company.branches.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      } else {
        this.rawCompanies.forEach((e) => {
          e?.branches.forEach((e) => {
            options.push({
              label: e.name,
              value: e.id
            })
          })
        })
      }

      this.assignSelectOptions('company_branch_id', options)
    },
    loadCategories() {
      var options = [
        {
          label: '国内货运险',
          value: 1
        },
        {
          label: '国际货运险',
          value: 2
        },
        {
          label: '单车责任险',
          value: 3
        },
        {
          label: '其他险种',
          value: 4
        },
        {
          label: '雇主责任险',
          value: 5
        },
        {
          label: '跨境电商险',
          value: 7
        }
      ]
      options = options.concat(
        this.offlineCategories.map((e) => {
          return {
            label: e.name + '-线下录入',
            value: 'OF-' + e.id
          }
        })
      )
      this.assignSelectOptions('types', options)
    },
    loadBusinessFrom() {
      const platfrom_ops = this.platforms.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('business_from', platfrom_ops)
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    }
  }
}
</script>
