<template>
  <div class="w-100 p-extra-large-x m-extra-large-b o-hidden-x">
    <SearchPanel
      size="small"
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
    ></SearchPanel>
    <el-card shadow="never" class="box-card-options m-extra-large-t">
      <div class="d-flex justify-content-between">
        <el-button
          type="primary"
          style="margin-bottom: 15px"
          @click="applyInvoice"
          v-show="this.admin.platform.id != -1"
          v-can="{ name: 'finance.invoices.create' }"
          >{{ roleName === 'sales' ? '申请发票' : '单据开票' }}</el-button
        >
      </div>
      <DefineTable
        :cols="cols"
        :data="tableData"
        :paging="pagingAttrs"
        :pagingEvents="pagingEvents"
        :events="tableEvents"
        :rowKey="getRowKeys"
      ></DefineTable>
    </el-card>
  </div>
</template>

<script>
import DefineTable from '@/components/globals/DefineTable/DefineTable'
import { getInvoices, exportInvoices } from '@/apis/finance'
import { getPlatformProductCompanies } from '@/apis/platform_product'
import { mapGetters } from 'vuex'

export default {
  name: 'invoice',
  components: {
    DefineTable
  },
  data() {
    return {
      statusList: [
        { label: '已提交', value: 1 },
        { label: '审核中', value: 2 },
        { label: '已完成', value: 3 },
        { label: '已退回', value: 4 }
      ],
      cols: [
        { align: 'center', type: 'selection', reserveSelection: true },
        { label: '发票抬头', prop: 'company_name', align: 'center' },
        {
          label: '险种',
          scopedSlots: {
            default: (scoped) => {
              const types = {
                1: '国内货运险',
                2: '国际货运险',
                3: '单车责任险',
                4: '其他险种',
                5: '雇主责任险',
                6: '线下录入保单',
                7: '跨境电商险'
              }

              return types[scoped.row.policy_type]
            }
          }
        },
        { label: '出单公司', prop: 'company_branch.name', align: 'center' },
        { label: '发票金额', prop: 'amount', align: 'center' },
        { label: '申请人', prop: 'apply.name', align: 'center' },
        { label: '申请时间', prop: 'apply_at', align: 'center' },
        {
          label: '状态',
          prop: 'status',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return <div>{this.statusList[scoped.row.status - 1]?.label}</div>
            }
          }
        },
        // JSX 插槽
        {
          label: '操作',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    v-can={{ name: 'finance.invoices.show' }}
                    type="primary"
                    onClick={() => this.checkDetail(scoped.row)}
                  >
                    详情
                  </el-link>
                </div>
              )
            }
          }
        }
      ],
      searchFields: [
        {
          type: 'input',
          valKey: 'order_no',
          hintText: '流水号'
        },
        {
          type: 'textarea',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'input',
          valKey: 'company_name',
          hintText: '发票抬头'
        },
        {
          type: 'input',
          valKey: 'amount_gt',
          hintText: '发票金额大于'
        },
        {
          type: 'input',
          valKey: 'amount_lt',
          hintText: '发票金额小于'
        },
        {
          type: 'select',
          valKey: 'company_id',
          hintText: '保险公司',
          onChangeReset: 'company_branch_id',
          options: []
        },
        {
          type: 'select',
          valKey: 'company_branch_id',
          hintText: '出单公司',
          options: []
        },
        {
          type: 'input',
          valKey: 'apply_name',
          hintText: '申请人'
        },
        {
          type: 'daterange',
          valKey: 'apply_at_range',
          hintText: '申请'
        },
        {
          type: 'select',
          valKey: 'policy_type',
          hintText: '险种',
          options: [
            {
              label: '国内货运险',
              value: 1
            },
            {
              label: '国际货运险',
              value: 2
            },
            {
              label: '单车责任险',
              value: 3
            },
            {
              label: '其他险种',
              value: 4
            },
            {
              label: '雇主责任险',
              value: 5
            },
            {
              label: '跨境电商险',
              value: 7
            }
          ]
        },
        {
          type: 'select',
          valKey: 'type',
          hintText: '发票类型',
          options: [
            {
              label: '普票',
              value: 1
            },
            {
              label: '专票',
              value: 2
            }
          ]
        },
        {
          type: 'select',
          valKey: 'status',
          hintText: '状态',
          options: [
            {
              label: '已提交',
              value: 1
            },
            {
              label: '审核中',
              value: 2
            },
            {
              label: '已完成',
              value: 3
            },
            {
              label: '已退回',
              value: 4
            }
          ]
        }
      ],
      tableData: [],
      pagingAttrs: {
        align: 'center',
        currentPage: 1,
        pageSize: 15,
        layout: 'total, prev, pager, next, jumper',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.pagingAttrs.currentPage = page

          this.fetchCanInvoicePolicies()
        }
      },
      tableEvents: {
        'selection-change': (val) => {
          this.multipleSelection = val
        }
      },
      searchData: {},
      searchQuery: {},
      multipleSelection: [],
      rawCompanies: []
    }
  },
  computed: {
    ...mapGetters('auth', ['admin', 'roleName'])
  },
  created() {
    getPlatformProductCompanies().then((r) => {
      this.rawCompanies = r.data

      this.loadCompanies()
      this.loadCompanyBranches()
    })
    this.fetchCanInvoicePolicies()
  },
  methods: {
    handleSearchPanel(command, data) {
      this.searchQuery = data
      this.pagingAttrs.currentPage = 1
      if (command === 'export') {
        this.invoiceExport()
      } else {
        this.fetchCanInvoicePolicies()
      }
    },
    getRowKeys(row) {
      return row.id
    },
    fetchCanInvoicePolicies() {
      getInvoices({
        page: this.pagingAttrs.currentPage,
        filter: this.searchQuery
      }).then((r) => {
        this.tableData = r.data

        this.pagingAttrs.currentPage = r.meta.current_page
        this.pagingAttrs.pageSize = r.meta.per_page
        this.pagingAttrs.total = r.meta.total
      })
    },
    applyInvoice() {
      this.$router.push({
        name: 'ApplyInvoice'
      })
    },
    checkDetail(row) {
      this.$open({
        name: 'BillDetail',
        params: {
          id: row.id
        }
      })
    },
    invoiceExport() {
      const link = exportInvoices({
        filter: this.searchQuery,
        ids: this.multipleSelection.map((item) => {
          return item.id
        })
      })
      window.open(link, '_blank')
    },
    loadCompanies() {
      const options = this.rawCompanies.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('company_id', options)
    },
    loadCompanyBranches() {
      let options = []
      if (this.searchData?.company_id !== '' && this.searchData?.company_id !== undefined) {
        const company = this.rawCompanies.find((e) => e.id === this.searchData?.company_id)
        if (company) {
          options = company.branches.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      } else {
        this.rawCompanies.forEach((e) => {
          e?.branches.forEach((e) => {
            options.push({
              label: e.name,
              value: e.id
            })
          })
        })
      }

      this.assignSelectOptions('company_branch_id', options)
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    }
  },
  watch: {
    searchData: {
      immediate: true,
      deep: true,
      handler(value, old) {
        if (old?.company_id !== value?.company_id) {
          this.loadCompanyBranches()
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.invoice {
  padding: 0 20px;
}
</style>
