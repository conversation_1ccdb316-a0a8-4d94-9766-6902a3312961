<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <div>
      <el-card shadow="never" class="m-extra-large-t">
        <div class="d-flex justify-content-start">
          <el-button
            type="primary"
            v-can="{ name: 'finance.settlement.apply' }"
            style="margin-bottom: 15px"
            @click="showSettlementPreview"
            >销账申请</el-button
          >
          <el-button
            type="primary"
            v-can="{ name: 'finance.settlement.apply' }"
            style="margin-bottom: 15px"
            @click="settlementHistory"
            >销账记录</el-button
          >
          <el-button
            type="primary"
            v-can="{ name: 'finance.settlement.apply' }"
            style="margin-bottom: 15px"
            @click="allApplySettlement"
            >全部销账</el-button
          >
        </div>
        <SearchPanel
          size="small"
          @command="handleMemoSearchPanel"
          @change="(data) => (memoSearchData = data)"
          :custom="memoSearchFields"
        ></SearchPanel>
        <div class="mo_table">
          <div class="mo_table_wrap">
            <el-table
              ref="memoMultipleTable"
              :data="memoTableData"
              @selection-change="memoHandleSelectionChange"
              tooltip-effect="dark"
              style="width: 100%"
              :row-key="getRowKeys"
            >
              <el-table-column type="selection" width="80" align="center" :reserve-selection="true"> </el-table-column>
              <el-table-column prop="order_no" label="流水号" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="amount" label="水单金额" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="entry.name" label="录入人" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="paid_at" label="付款时间" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="payer" label="付款人" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="payee.name" label="收款人" show-overflow-tooltip> </el-table-column>
              <el-table-column label="水单状态" show-overflow-tooltip>
                <template slot-scope="scope">
                  {{ getMemoStatus(scope.row.status) }}
                </template>
              </el-table-column>
              <el-table-column label="水单类型" show-overflow-tooltip>
                <template slot-scope="scope">
                  {{ getMemoType(scope.row.type) }}
                </template>
              </el-table-column>
            </el-table>
          </div>
          <el-pagination
            @current-change="memoPagingEvents"
            :current-page="memoPaging.currentPage"
            :page-size="memoPaging.pageSize"
            layout="prev, pager, next, jumper, total"
            :total="memoPaging.total"
          >
          </el-pagination>
        </div>
      </el-card>
    </div>
    <div>
      <el-card shadow="never" class="m-extra-large-t">
        <SearchPanel
          size="small"
          @command="handlePolicySearchPanel"
          @change="(data) => (policySearchData = data)"
          :custom="policySearchFields"
        ></SearchPanel>
        <el-alert type="error" class="m-extra-large-t" :closable="false">
          <span>
            <span class="hover-cursor">可销账保单总计: </span>
            <el-tag type="danger" effect="dark"> {{ policyPaging.total }} </el-tag>，
            <span class="hover-cursor">可销账保单保费总计: </span>
            <el-tag type="danger" effect="dark"> {{ canSettlementPremiums }} </el-tag>
          </span>
        </el-alert>
        <div class="mo_table">
          <div class="mo_table_wrap">
            <el-table
              ref="policyMultipleTable"
              :data="policyTableData"
              @selection-change="policyHandleSelectionChange"
              tooltip-effect="dark"
              style="width: 100%"
              :row-key="getRowKeys"
            >
              <el-table-column type="selection" width="80" align="center" :reserve-selection="true"> </el-table-column>
              <el-table-column prop="policy.policy_no" label="保单号" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="company_branch.name" label="出单公司" show-overflow-tooltip> </el-table-column>
              <el-table-column label="险种" show-overflow-tooltip>
                <template slot-scope="scope"> {{ getPolicyType(scope.row.policy) }} </template>
              </el-table-column>
              <el-table-column prop="policy.settlement_premium" label="保费" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="policy.policyholder" label="投保人" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="policy.insured" label="被保人" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="policy.submitted_at" label="投保时间" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="user.name" label="投保用户" show-overflow-tooltip> </el-table-column>
            </el-table>
          </div>
          <el-pagination
            @current-change="policyPagingEvents"
            @size-change="policySizeChangeEvents"
            :current-page="policyPaging.currentPage"
            :page-sizes="policyPaging.pageSizes"
            :page-size="policyPaging.pageSize"
            :layout="policyPaging.layout"
            :total="policyPaging.total"
          >
          </el-pagination>
        </div>
      </el-card>
    </div>
    <settlement-preview
      :visible.sync="settlementPreview.visible"
      :previewData="settlementPreview.data"
      @submit="handleApplySettlement"
    ></settlement-preview>
  </div>
</template>

<script>
import {
  getCanSettlementMemos,
  applySettlement,
  getPayees,
  getCanSettlementReceivables,
  getCanSettlementPremiums,
  exportCanSettlementMemos,
  exportCanSettlementReceivables
} from '@/apis/finance'
import SettlementPreview from '@/components/settlement/SettlementPreview'
import { getPlatformProductCompanies } from '@/apis/platform_product'
import { getOfflineProductCategories } from '@/apis/product'
import { Loading } from 'element-ui'

export default {
  name: 'ApplySettlement',
  components: {
    SettlementPreview
  },
  data() {
    return {
      isAllApplySettlement: false,
      canSettlementNum: 0,
      canSettlementPremiums: 0,
      allCanSettlementReceivableIds: [],
      settlementPreview: {
        visible: false,
        data: {
          memoCount: 0,
          policyCount: 0,
          memoAmount: 0,
          policyAmount: 0,
          memoIds: [],
          receivableIds: []
        }
      },
      memoSearchFields: [
        {
          type: 'select',
          valKey: 'type',
          hintText: '水单类型',
          options: [
            { label: '支付保司', value: 1 },
            { label: '支付平台', value: 2 }
          ]
        },
        {
          type: 'input',
          valKey: 'payer',
          hintText: '付款人'
        },
        {
          type: 'select',
          valKey: 'payee_id',
          hintText: '收款人',
          options: []
        },
        {
          type: 'select',
          valKey: 'status',
          hintText: '水单状态',
          options: [
            { label: '未使用', value: 1 },
            { label: '已使用', value: 2 }
          ]
        },
        {
          type: 'input',
          valKey: 'amount_gt',
          hintText: '水单金额大于等于'
        },
        {
          type: 'input',
          valKey: 'amount_lt',
          hintText: '水单金额小于等于'
        },
        {
          type: 'daterange',
          valKey: 'paid_at_range'
        }
      ],
      policySearchFields: [
        {
          type: 'textarea',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'select',
          valKey: 'company_id',
          hintText: '保险公司',
          onChangeReset: 'company_branch_id',
          options: []
        },
        {
          type: 'select',
          valKey: 'company_branch_id',
          hintText: '出单公司',
          options: []
        },
        {
          type: 'select',
          valKey: 'types',
          hintText: '险种',
          isMultiple: true,
          options: []
        },
        {
          type: 'input',
          valKey: 'policyholder',
          hintText: '投保人'
        },
        {
          type: 'input',
          valKey: 'name',
          hintText: '投保用户'
        },
        {
          type: 'daterange',
          valKey: 'submitted_at_range'
        }
      ],
      memoTableData: [],
      // 分页器 不传递 -> 没有分页
      memoPaging: {
        currentPage: 1,
        pageSize: 0,
        layout: 'prev, pager, next, jumper, total',
        total: 0
      },
      memoSearchQuery: {},
      memoSearchData: {},
      memoMultipleSelection: {},

      policyTableData: [],
      // 分页器 不传递 -> 没有分页
      policyPaging: {
        currentPage: 1,
        pageSizes: [15, 30, 50, 100],
        pageSize: 15,
        layout: 'sizes, prev, pager, next, jumper, total',
        total: 0
      },
      policySearchQuery: {},
      policySearchData: {},
      policyMultipleSelection: {},
      rawCompanies: [],
      offlineCategories: []
    }
  },
  created() {
    this.fetchMemos()
    this.fetchPolicies()
    getOfflineProductCategories({ is_parent: 0, is_pageable: 0 }).then((r) => {
      this.offlineCategories = r.data

      this.loadCategories()
    })
    getPlatformProductCompanies().then((r) => {
      this.rawCompanies = r.data

      this.loadCompanies()
      this.loadCompanyBranches()
    })
  },
  methods: {
    policyPagingEvents(page) {
      this.policyPaging.currentPage = page

      this.fetchPolicies()
    },
    policySizeChangeEvents(size) {
      this.policyPaging.pageSize = size

      this.fetchPolicies()
    },
    memoPagingEvents(page) {
      this.memoPaging.currentPage = page
      this.fetchMemos()
    },
    memoHandleSelectionChange(val) {
      this.memoMultipleSelection = val
    },
    policyHandleSelectionChange(val) {
      this.policyMultipleSelection = val
    },
    getRowKeys(row) {
      return row.id
    },
    handleMemoSearchPanel(command, data) {
      this.memoSearchQuery = data
      this.memoPaging.currentPage = 1
      if (command === 'export') {
        this.canSettlementMemosExport()
      } else {
        this.fetchMemos()
      }
    },
    handlePolicySearchPanel(command, data) {
      this.policySearchQuery = data
      this.policyPaging.currentPage = 1
      if (command === 'export') {
        this.canSettlementPremiumReceivablesExport()
      } else {
        this.fetchPolicies()
        this.fetchPremiums()
      }
    },
    fetchMemos() {
      getCanSettlementMemos({
        page: this.memoPaging.currentPage,
        filter: this.memoSearchQuery
      }).then((r) => {
        this.memoTableData = r.data

        this.memoPaging.currentPage = r.meta.current_page
        this.memoPaging.pageSize = r.meta.per_page
        this.memoPaging.total = r.meta.total
      })
    },
    fetchPolicies() {
      const loading = Loading.service()
      getCanSettlementReceivables({
        page: this.policyPaging.currentPage,
        per_page: this.policyPaging.pageSize,
        filter: this.policySearchQuery
      })
        .then((r) => {
          this.policyTableData = r.data

          this.policyPaging.currentPage = r.meta.current_page
          this.policyPaging.pageSize = r.meta.per_page
          this.policyPaging.total = r.meta.total
          loading.close()
        })
        .finally(() => loading.close())
    },
    getRowKey(row) {
      return row.id
    },
    getMemoStatus(status) {
      switch (status) {
        case 1:
          return '未使用'
        case 2:
          return '已使用'
        case 3:
          return '待使用'
        default:
          break
      }
    },
    getMemoType(type) {
      switch (type) {
        case 1:
          return '支付保司'
        case 2:
          return '支付平台'
        default:
          break
      }
    },
    getPolicyType(policy) {
      const types = {
        1: '国内货运险',
        2: '国际货运险',
        3: '单车责任险',
        4: '其他险种',
        5: '雇主责任险',
        6: '线下录入保单',
        7: '跨境电商险'
      }

      return policy.type == 6 ? policy.offline_type : types[policy.type]
    },
    showSettlementPreview() {
      if (this.applyValidate()) {
        this.isAllApplySettlement = false
        this.settlementPreview.visible = true
        this.settlementPreview.data.memoIds = this.memoMultipleSelection.map((memo) => {
          return memo.id
        })
        this.settlementPreview.data.receivableIds = this.policyMultipleSelection.map((receivable) => {
          return receivable.id
        })
      }
    },
    applyValidate() {
      this.settlementPreview.data.memoAmount = 0
      this.settlementPreview.data.policyAmount = 0
      this.settlementPreview.data.memoCount = this.memoMultipleSelection.length
      this.settlementPreview.data.policyCount = this.policyMultipleSelection.length

      if (!this.settlementPreview.data.memoCount || !this.settlementPreview.data.policyCount) {
        this.$message({
          type: 'warning',
          message: '水单或保单未勾选,请先勾选对应数据!'
        })
        return false
      }
      this.memoMultipleSelection.forEach((item) => {
        this.settlementPreview.data.memoAmount += Number(item.amount)
      })
      this.policyMultipleSelection.forEach((item) => {
        this.settlementPreview.data.policyAmount += Number(item.policy.settlement_premium)
      })

      this.settlementPreview.data.memoAmount = this.settlementPreview.data.memoAmount.toFixed(2)
      this.settlementPreview.data.policyAmount = this.settlementPreview.data.policyAmount.toFixed(2)
      console.log(this.settlementPreview.data.memoAmount)
      console.log(this.settlementPreview.data.policyAmount)

      if (Number(this.settlementPreview.data.memoAmount) < Number(this.settlementPreview.data.policyAmount)) {
        console.log(this.settlementPreview.data)
        this.$message({
          type: 'warning',
          message: '水单金额小于保单金额,无法提交申请'
        })
        return false
      }
      var memo1 = this.memoMultipleSelection[0]
      if (this.memoMultipleSelection.find((item) => item.type != memo1.type)) {
        this.$message({
          type: 'warning',
          message: '请选择同一个类型的水单'
        })
        return false
      }

      if (this.memoMultipleSelection.find((item) => item.payee.id != memo1.payee.id)) {
        this.$message({
          type: 'warning',
          message: '请选择同一个收款人的水单'
        })
        return false
      }
      var policy1 = this.policyMultipleSelection[0]
      if (
        memo1.type == 1 &&
        this.policyMultipleSelection.find((item) => item.company_branch.id != policy1.company_branch.id)
      ) {
        this.$message({
          type: 'warning',
          message: '请选择与水单同一个出单公司的保单'
        })
        return false
      }
      if (memo1.type === 1 && memo1.payee.id !== policy1.company_branch.id) {
        this.$message({
          type: 'warning',
          message: '水单收款人与保单出单公司不一致'
        })
        return false
      }
      return true
    },
    handleApplySettlement(data) {
      // const loading = Loading.service({ lock: true })
      applySettlement(data).then(() => {
        this.$message.success('数据已提交至申请队列,申请结果请查看登录账号邮箱')
        this.memoPaging.currentPage = 1
        this.policyPaging.currentPage = 1
        this.$refs.memoMultipleTable.clearSelection()
        this.$refs.policyMultipleTable.clearSelection()
        this.policyMultipleSelection = {}
        this.settlementPreview.data = {
          memoCount: 0,
          policyCount: 0,
          memoAmount: 0,
          policyAmount: 0,
          memoIds: [],
          receivableIds: []
        }
        this.fetchMemos()
        this.fetchPolicies()
      })
      // .finally(() => loading.close())
    },
    settlementHistory() {
      this.$router.push({
        name: 'SettlementHistory'
      })
    },
    loadCompanies() {
      const options = this.rawCompanies.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignPolicySelectOptions('company_id', options)
    },
    loadCompanyBranches() {
      let options = []
      if (this.policySearchData?.company_id !== '' && this.policySearchData?.company_id !== undefined) {
        const company = this.rawCompanies.find((e) => e.id === this.policySearchData?.company_id)
        if (company) {
          options = company.branches.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      } else {
        this.rawCompanies.forEach((e) => {
          e?.branches.forEach((e) => {
            options.push({
              label: e.name,
              value: e.id
            })
          })
        })
      }

      this.assignPolicySelectOptions('company_branch_id', options)
    },
    loadCategories() {
      var options = [
        {
          label: '国内货运险',
          value: 1
        },
        {
          label: '国际货运险',
          value: 2
        },
        {
          label: '单车责任险',
          value: 3
        },
        {
          label: '其他险种',
          value: 4
        },
        {
          label: '雇主责任险',
          value: 5
        }
      ]
      options = options.concat(
        this.offlineCategories.map((e) => {
          return {
            label: e.name + '-线下录入',
            value: 'OF-' + e.id
          }
        })
      )
      this.assignPolicySelectOptions('types', options)
    },
    assignMemoSelectOptions(target, options) {
      const targetIdx = this.memoSearchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.memoSearchFields[targetIdx].options = options
      }
    },
    assignPolicySelectOptions(target, options) {
      const targetIdx = this.policySearchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.policySearchFields[targetIdx].options = options
      }
    },
    canSettlementMemosExport() {
      const link = exportCanSettlementMemos({
        filter: this.memoSearchQuery
      })
      window.open(link, '_blank')
    },
    canSettlementPremiumReceivablesExport() {
      const link = exportCanSettlementReceivables({
        filter: this.policySearchQuery
      })
      window.open(link, '_blank')
    },
    fetchPremiums() {
      getCanSettlementPremiums({
        page: this.policyPaging.currentPage,
        filter: this.policySearchQuery
      }).then((r) => {
        this.allCanSettlementReceivableIds = r.data.settlement_ids
        this.canSettlementNum = r.data.settlement_num
        this.canSettlementPremiums = r.data.settlement_premium
      })
    },
    allApplySettlement() {
      this.settlementPreview.data.memoAmount = 0
      this.settlementPreview.data.memoCount = this.memoMultipleSelection.length
      this.settlementPreview.data.policyCount = this.canSettlementNum

      if (!this.settlementPreview.data.memoCount || !this.settlementPreview.data.policyCount) {
        this.$message({
          type: 'warning',
          message: '水单未勾选或当前条件无可销账保单,请先勾选对应数据!'
        })
        return false
      }

      this.memoMultipleSelection.forEach((item) => {
        this.settlementPreview.data.memoAmount += Number(item.amount)
      })

      this.settlementPreview.data.memoAmount = this.settlementPreview.data.memoAmount.toFixed(2)
      this.settlementPreview.data.policyAmount = this.canSettlementPremiums.toFixed(2)
      console.log(this.settlementPreview.data.memoAmount)
      console.log(this.settlementPreview.data.policyAmount)

      if (Number(this.settlementPreview.data.memoAmount) < Number(this.settlementPreview.data.policyAmount)) {
        console.log(this.settlementPreview.data)
        this.$message({
          type: 'warning',
          message: '水单金额小于保单金额,无法提交申请'
        })
        return false
      }

      var memo1 = this.memoMultipleSelection[0]
      if (this.memoMultipleSelection.find((item) => item.type != memo1.type)) {
        this.$message({
          type: 'warning',
          message: '请选择同一个类型的水单'
        })
        return false
      }

      if (this.memoMultipleSelection.find((item) => item.payee.id != memo1.payee.id)) {
        this.$message({
          type: 'warning',
          message: '请选择同一个收款人的水单'
        })
        return false
      }
      this.isAllApplySettlement = true
      this.settlementPreview.visible = true

      this.settlementPreview.data.memoIds = this.memoMultipleSelection.map((memo) => {
        return memo.id
      })
      this.settlementPreview.data.receivableIds = this.allCanSettlementReceivableIds
    }
  },
  watch: {
    'memoSearchData.type'(type) {
      if (type) {
        getPayees(type).then((r) => {
          this.assignMemoSelectOptions('payee_id', r.data)
        })
      }
    },
    'policySearchData.company_id'() {
      this.loadCompanyBranches()
    }
  }
}
</script>
<style scope>
.mo_table {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.mo_table > .mo_table_wrap {
  flex: 1;
}
.mo_table > .el-pagination,
.mo_table > .mo_table_wrap {
  margin-bottom: 1rem;
}
</style>
