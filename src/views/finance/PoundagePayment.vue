<template>
  <div class="w-100 p-extra-large-x m-extra-large-b o-hidden-x">
    <SearchPanel
      size="small"
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
    ></SearchPanel>
    <el-alert type="error" class="m-extra-large-t" :closable="false">
      <span>
        当前保费
        <el-tag type="danger" effect="dark"> {{ countPayment.premium }} </el-tag>，
        <span class="hover-cursor">应结经纪费: </span>
        <el-tag type="danger" effect="dark"> {{ countPayment.poundage }} </el-tag>
      </span>
    </el-alert>
    <el-card shadow="never" class="box-card-options m-extra-large-t">
      <div class="d-flex">
        <el-button
          type="primary"
          v-can="{ name: 'finance.poundage-payment.bills.create' }"
          @click="handlePayment"
          style="margin-bottom: 15px"
          >经纪费结算</el-button
        >
        <el-button
          type="primary"
          v-can="{ name: 'finance.poundage-payment.bills.create' }"
          @click="allCheckout"
          style="margin-bottom: 15px"
          >全部处理</el-button
        >
        <el-button
          type="primary"
          v-can="{ name: 'finance.poundage-payment.bills.draft' }"
          @click="draftBills"
          style="margin-bottom: 15px"
          >暂存记录</el-button
        >
        <el-button
          type="primary"
          v-can="{ name: 'finance.poundage-payment.bills.index' }"
          @click="paymentBills"
          style="margin-bottom: 15px"
          >结算记录</el-button
        >
        <el-button
          type="primary"
          v-can="{ name: 'finance.poundage-payment.data-check' }"
          @click="poundageCheckDialog.visible = true"
          style="margin-bottom: 15px"
          >数据对比</el-button
        >
      </div>
      <DefineTable
        :cols="cols"
        :data="tableData"
        :paging="pagingAttrs"
        :pagingEvents="pagingEvents"
        :events="tableEvents"
        :rowKey="getRowKeys"
      ></DefineTable>
      <el-dialog title="经纪费清单对比" width="520px" :visible.sync="poundageCheckDialog.visible">
        <el-form
          ref="poundageCheckForm"
          :model="poundageCheckDialog.form"
          :rules="checkRules"
          label-suffix=":"
          label-width="120px"
        >
          <el-form-item prop="file" label="清单对比">
            <el-link type="primary" :underline="false" @click="downloadPoundageCheckTemplateExcel()">
              (点击下载模板)
            </el-link>
            <upload-file v-model="poundageCheckDialog.form.file" />
          </el-form-item>
          <el-form-item>
            <el-button icon="fas fa-times" @click="poundageCheckDialog.visible = false">取消</el-button>
            <el-button type="primary" icon="fas fa-check" @click="handlePoundageCheck()">提交</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import {
  getPoundagePayments,
  getPoundagePaymentCount,
  exportPoundagePayments,
  poundageCheckTemplate,
  poundageCheck
} from '@/apis/finance'
import { getCompaniesDict } from '@/apis/company'
import { getOfflineProductCategories } from '@/apis/product'
import { Loading } from 'element-ui'

export default {
  name: 'PoundagePayment',
  data() {
    return {
      searchFields: [
        {
          type: 'textarea',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'select',
          valKey: 'business_type',
          hintText: '业务来源',
          options: [
            {
              label: '自有业务',
              value: 1
            },
            {
              label: '其他业务',
              value: 2
            }
          ]
        },
        {
          type: 'select',
          valKey: 'types',
          hintText: '险种',
          isMultiple: true,
          options: []
        },
        {
          type: 'daterange',
          valKey: 'issued_at_range'
        },
        {
          type: 'select',
          valKey: 'company_id',
          hintText: '保险公司',
          onChangeReset: 'company_branch_id',
          options: []
        },
        {
          type: 'select',
          valKey: 'company_branch_id',
          hintText: '出单公司',
          options: []
        },
        {
          type: 'textarea',
          valKey: 'endorse_no',
          hintText: '批单号'
        },
        {
          type: 'input',
          valKey: 'policyholder',
          hintText: '投保人'
        }
      ],
      cols: [
        { align: 'center', type: 'selection', width: '80', reserveSelection: true },
        {
          label: '保单号/批单号',
          width: '200',
          fixed: 'left',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.policy.policy_no}</label>
                  <br />
                  <small>{scoped.row.policy_group_endorse.endorse_no}</small>
                </div>
              )
            }
          }
        },
        {
          label: '业务来源',
          scopedSlots: {
            default: (scoped) => {
              return scoped.row.business_type ? '自有业务' : '非自有业务'
            }
          }
        },
        {
          label: '险种',
          scopedSlots: {
            default: (scoped) => {
              const types = {
                1: '国内货运险',
                2: '国际货运险',
                3: '单车责任险',
                4: '其他险种',
                5: '雇主责任险',
                6: '线下录入保单',
                7: '跨境电商险'
              }

              return scoped.row.policy.type === 6 ? scoped.row.policy.offline_type : types[scoped.row.policy.type]
            }
          }
        },
        { label: '投保人', width: '200', prop: 'policy.policyholder' },
        { label: '保险公司保费', prop: 'premium' },
        { label: '经纪费', prop: 'poundage' },
        { label: '出单公司', prop: 'company_branch.name' },
        { label: '生效时间', prop: 'issued_at' }
      ],
      tableData: [],
      searchData: {},
      searchQuery: {},
      pagingAttrs: {
        currentPage: 1,
        pageSize: 15,
        layout: 'total, prev, pager, next, jumper',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.pagingAttrs.currentPage = page

          this.fetchPoundagePayments()
        }
      },
      tableEvents: {
        'selection-change': (val) => {
          this.multipleSelection = val
        }
      },
      multipleSelection: [],
      rawCompanies: [],
      companiesDict: [],
      offlineCategories: [],
      countPayment: {
        premium: 0,
        poundage: 0
      },
      poundageCheckDialog: {
        visible: false,
        form: {
          file: ''
        }
      },
      checkRules: {
        file: [{ required: true, message: '请上传需要对比的文件', trigger: ['blur', 'change'] }]
      }
    }
  },
  created() {
    getOfflineProductCategories({ is_parent: 0, is_pageable: 0 }).then((r) => {
      this.offlineCategories = r.data

      this.loadCategories()
    })
    getCompaniesDict().then((r) => {
      this.companiesDict = r.data
      this.loadCompanies()
      this.loadCompanyBranches()
    })

    this.fetchPoundagePayments()
    this.fetchCountPayment()
  },
  methods: {
    handleSearchPanel(command, data) {
      this.searchQuery = data
      this.pagingAttrs.currentPage = 1
      if (command === 'export') {
        this.paymentsExport()
      } else {
        this.fetchPoundagePayments()
        this.fetchCountPayment()
      }
    },
    getRowKeys(row) {
      return row.id
    },

    fetchPoundagePayments() {
      const loading = Loading.service()
      getPoundagePayments({
        page: this.pagingAttrs.currentPage,
        filter: this.searchQuery
      })
        .then((r) => {
          this.tableData = r.data

          this.pagingAttrs.currentPage = r.meta.current_page
          this.pagingAttrs.pageSize = r.meta.per_page
          this.pagingAttrs.total = r.meta.total
          loading.close()
        })
        .finally(() => loading.close())
    },
    fetchCountPayment() {
      getPoundagePaymentCount({
        filter: this.searchQuery
      }).then((r) => {
        this.countPayment.premium = r.data.premium
        this.countPayment.poundage = r.data.poundage
      })
    },
    loadCompanies() {
      const options = this.companiesDict.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('company_id', options)
    },
    loadCompanyBranches() {
      let options = []
      if (this.searchData?.company_id !== '' && this.searchData?.company_id !== undefined) {
        const company = this.companiesDict.find((e) => e.id === this.searchData?.company_id)
        if (company) {
          options = company.branches.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      } else {
        this.companiesDict.forEach((e) => {
          e?.branches.forEach((e) => {
            options.push({
              label: e.name,
              value: e.id
            })
          })
        })
      }

      this.assignSelectOptions('company_branch_id', options)
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    },
    handlePayment() {
      if (!this.multipleSelection.length) {
        this.$message({
          type: 'warning',
          message: '请至少选择一项'
        })
        return
      }
      this.$router.push({
        name: 'HandlePoundagePayment',
        query: {
          id: this.multipleSelection.map((e) => e.id).join(',')
        }
      })
    },
    paymentBills() {
      this.$router.push({
        name: 'PoundagePaymentBills'
      })
    },
    draftBills() {
      this.$router.push({
        name: 'DraftPoundagePaymentBills'
      })
    },
    paymentsExport() {
      exportPoundagePayments({
        filter: this.searchQuery
      }).then(() => {
        this.$message({
          type: 'success',
          message: '提交申请已提交至处理队列,处理结果请查看登录账号邮箱'
        })
        this.fetchPoundagePayments()
      })
    },
    allCheckout() {
      this.$router.push({
        name: 'HandlePoundagePayment',
        query: {
          all_checkout: 1,
          ...this.searchQuery
        }
      })
    },
    loadCategories() {
      var options = [
        {
          label: '国内货运险',
          value: 1
        },
        {
          label: '国际货运险',
          value: 2
        },
        {
          label: '单车责任险',
          value: 3
        },
        {
          label: '其他险种',
          value: 4
        },
        {
          label: '雇主责任险',
          value: 5
        }
      ]
      options = options.concat(
        this.offlineCategories.map((e) => {
          return {
            label: e.name + '-线下录入',
            value: 'OF-' + e.id
          }
        })
      )
      this.assignSelectOptions('types', options)
    },
    downloadPoundageCheckTemplateExcel() {
      const link = poundageCheckTemplate()
      window.open(link, '_blank')
    },
    handlePoundageCheck() {
      this.$refs.poundageCheckForm.validate((valid) => {
        if (valid) {
          const loading = Loading.service()
          poundageCheck(this.poundageCheckDialog.form)
            .then(() => {
              this.poundageCheckDialog.visible = false
              this.poundageCheckDialog.form = {
                file: ''
              }
              this.$message.success('数据已提交至导入队列,导入结果请查看登录账号邮箱')
            })
            .finally(() => {
              loading.close()
            })
        }
      })
    }
  },
  watch: {
    searchData: {
      immediate: true,
      deep: true,
      handler(value, old) {
        if (old?.company_id !== value?.company_id) {
          this.loadCompanyBranches()
        }
      }
    }
  }
}
</script>

<style scoped>
.documents {
  padding: 0 20px;
}
.el-select,
.el-select--small {
  width: 100%;
}
.box-card-options {
  margin-bottom: 20px;
}
</style>

<style lang="scss" scoped>
.policy-search {
  /deep/.el-select,
  /deep/.el-date-editor {
    width: 100% !important;
  }
  /deep/.el-card__body {
    display: flex;
  }
}
</style>
