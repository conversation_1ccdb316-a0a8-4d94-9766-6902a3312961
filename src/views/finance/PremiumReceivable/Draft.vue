<template>
  <div class="w-100 invoice">
    <SearchPanel
      size="small"
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
    ></SearchPanel>
    <el-card shadow="never" class="box-card-options m-extra-large-t" :body-style="{ padding: '20px' }">
      <DefineTable :cols="cols" :data="tableData" :paging="pagingAttrs" :pagingEvents="pagingEvents"></DefineTable>
    </el-card>
    <el-dialog title="提示" :visible.sync="draftDialog.visible" width="30%">
      <el-form
        ref="draftForm"
        label-position="left"
        label-width="100px"
        :model="draftDialog.form"
        :rules="draftDialog.rules"
      >
        <el-form-item label="收取总保费" prop="actual_receivable">
          <el-input
            type="number"
            v-model="draftDialog.form.actual_receivable"
            placeholder="请输入收取总保费"
          ></el-input>
        </el-form-item>
        <el-form-item label="保费支付类型" prop="payee_type">
          <el-select placeholder="请选择保费支付类型" v-model="draftDialog.form.payee_type" class="w-100">
            <el-option
              v-for="payeeType in payeeTypes"
              :key="payeeType.value"
              :label="payeeType.label"
              :value="payeeType.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="支付凭证" prop="proof">
          <upload-file v-model="draftDialog.form.proof"></upload-file>
          <el-link
            icon="el-icon-view"
            v-if="draftDialog.form.proof && typeof draftDialog.form.proof === 'string'"
            :href="draftDialog.form.proof"
          >
            点击查看
          </el-link>
        </el-form-item>
        <el-form-item label="备注">
          <el-input type="textarea" placeholder="请输入备注" :rows="4" v-model="draftDialog.form.remark"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="draftDialog.visible = false">取 消</el-button>
        <el-button type="primary" @click="draftReceivable">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  premiumReceivableBills,
  exportPremiumReceivableBills,
  sendBackDraftPremiumReceivableBills,
  premiumReceivableDraftBills
} from '@/apis/finance'
import { getPlatformProductCompanies } from '@/apis/platform_product'
import { tokenKey } from '@/config'
import { Loading } from 'element-ui'

export default {
  name: 'PremiumReceivableBills',
  data() {
    return {
      draftDialog: {
        visible: false,
        form: {
          id: '',
          payee_type: '',
          actual_receivable: '',
          proof: '',
          remark: ''
        },
        rules: {
          payee_type: [{ required: true, message: '请选择收取类型', trigger: 'blur' }],
          actual_receivable: [{ required: true, message: '请输入实际收取保费', trigger: 'blur' }],
          proof: [{ required: true, message: '请上传支付凭证', trigger: 'blur' }]
        }
      },
      payeeTypes: [
        {
          label: '客户支付保险公司',
          value: 1
        },
        {
          label: '客户支付平台',
          value: 2
        }
      ],
      searchFields: [
        {
          type: 'textarea',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'input',
          valKey: 'operator',
          hintText: '处理人'
        },
        {
          type: 'daterange',
          hintText: '收取',
          valKey: 'charge_at_range'
        }
      ],
      cols: [
        { label: '流水号', prop: 'order_no' },
        {
          label: '收取类型',
          scopedSlots: {
            default: (scoped) => {
              const types = {
                1: '支付保司',
                2: '支付平台'
              }

              return types[scoped.row.payee_type]
            }
          }
        },
        {
          label: '单据数',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    type="primary"
                    onClick={() => {
                      this.$router.push({
                        name: 'DraftPremiumBillReceivables',
                        params: {
                          id: scoped.row.id
                        }
                      })
                    }}
                  >
                    {scoped.row.payment_num}
                  </el-link>
                </div>
              )
            }
          }
        },
        // { label: '保单数', prop: 'policy_num' },
        { label: '应收保费', prop: 'receivable' },
        { label: '实际收取', prop: 'actual_receivable' },
        { label: '处理人', prop: 'operation.name' },
        { label: '备注', prop: 'remark' },
        { label: '收取时间', prop: 'charge_at' },
        // JSX 插槽
        {
          label: '支付凭证',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.proof) {
                return (
                  <div>
                    <el-link
                      type="primary"
                      href={`${scoped.row.proof}?token=` + window.localStorage.getItem(tokenKey)}
                      download=""
                      target="_blank"
                    >
                      点击查看
                    </el-link>
                  </div>
                )
              } else {
                return '-'
              }
            }
          }
        },
        {
          label: '操作',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    style="margin-right: 10px"
                    type="primary"
                    plain
                    size="small"
                    v-can={{ name: 'finance.premium-receivable.bills.create' }}
                    onClick={() => this.handleDraft(scoped.row)}
                    disabled={scoped.row.status === 1}
                  >
                    确认提交
                  </el-link>
                  <el-link
                    style="margin-right: 10px"
                    type="primary"
                    plain
                    size="small"
                    v-can={{ name: 'finance.premium-receivable.bills.send-back' }}
                    onClick={() => this.sendBackReceivable(scoped.row.id)}
                  >
                    退回
                  </el-link>
                </div>
              )
            }
          }
        }
      ],
      tableData: [],
      searchData: {},
      pagingAttrs: {
        align: 'center',
        currentPage: 1,
        pageSize: 15,
        layout: 'total, prev, pager, next, jumper',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.pagingAttrs.currentPage = page

          this.fetchPremiumReceivableBills()
        }
      },
      rawCompanies: []
    }
  },
  mounted() {
    getPlatformProductCompanies().then((r) => {
      this.rawCompanies = r.data

      this.loadCompanies()
      this.loadCompanyBranches()
    })
    this.fetchPremiumReceivableBills()
  },
  methods: {
    handleSearchPanel(command, data) {
      this.searchQuery = data
      this.pagingAttrs.currentPage = 1
      if (command === 'export') {
        this.exportBills()
      } else {
        this.fetchPremiumReceivableBills()
      }
    },
    fetchPremiumReceivableBills() {
      const loading = Loading.service()
      premiumReceivableBills({
        page: this.pagingAttrs.currentPage,
        filter: Object.assign({ status: 0 }, this.searchQuery)
      })
        .then((r) => {
          this.tableData = r.data

          this.pagingAttrs.currentPage = r.meta.current_page
          this.pagingAttrs.pageSize = r.meta.per_page
          this.pagingAttrs.total = r.meta.total

          loading.close()
        })
        .finally(() => loading.close())
    },
    exportBills() {
      const link = exportPremiumReceivableBills({
        filter: Object.assign({ status: 0 }, this.searchQuery)
      })
      window.open(link, '_blank')
    },
    loadCompanies() {
      const options = this.rawCompanies.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('company_id', options)
    },
    loadCompanyBranches() {
      let options = []
      if (this.searchData?.company_id !== '' && this.searchData?.company_id !== undefined) {
        const company = this.rawCompanies.find((e) => e.id === this.searchData?.company_id)
        if (company) {
          options = company.branches.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      } else {
        this.rawCompanies.forEach((e) => {
          e?.branches.forEach((e) => {
            options.push({
              label: e.name,
              value: e.id
            })
          })
        })
      }

      this.assignSelectOptions('company_branch_id', options)
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    },
    handleDraft(row) {
      this.draftDialog.visible = true
      this.draftDialog.form.id = row.id
      this.draftDialog.form.payee_type = row.payee_type
      this.draftDialog.form.actual_receivable = row.actual_receivable
      this.draftDialog.form.proof = row.proof
      this.draftDialog.form.remark = row.remark
    },
    draftReceivable() {
      this.$refs?.draftForm?.validate((valid) => {
        if (valid) {
          const loading = Loading.service()
          premiumReceivableDraftBills(this.draftDialog.form.id, Object.assign({}, this.draftDialog.form))
            .then(() => {
              this.draftDialog.visible = false
              this.draftDialog.form = {
                id: '',
                payee_type: '',
                actual_receivable: '',
                proof: '',
                remark: ''
              }
              this.fetchPremiumReceivableBills()
              this.$message({
                type: 'success',
                message: '处理完成'
              })
              loading.close()
            })
            .finally(() => loading.close())
        }
      })
    },
    sendBackReceivable(id) {
      this.$confirm('是否确定退回?', '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        type: 'warning'
      }).then(() => {
        sendBackDraftPremiumReceivableBills(id).then(() => {
          this.$message.success('操作成功')
          this.fetchPremiumReceivableBills()
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.invoice {
  padding: 0 20px;
}
.a_style {
  color: #fff;
  text-decoration: none;
}
</style>
