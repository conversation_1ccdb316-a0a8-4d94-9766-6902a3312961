<template>
  <div class="w-100 invoice">
    <SearchPanel
      size="small"
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
    ></SearchPanel>
    <el-card shadow="never" class="box-card-options m-extra-large-t" :body-style="{ padding: '20px' }">
      <DefineTable :cols="cols" :data="tableData" :paging="pagingAttrs" :pagingEvents="pagingEvents"></DefineTable>
    </el-card>
  </div>
</template>

<script>
import {
  premiumReceivableBills,
  exportPremiumReceivableBills,
  exportPremiumReceivables,
  premiumReceivableDraftBills
} from '@/apis/finance'
import { getPlatformProductCompanies } from '@/apis/platform_product'
import { tokenKey } from '@/config'
import { Loading } from 'element-ui'

export default {
  name: 'PremiumReceivableBills',
  data() {
    return {
      payeeTypes: [
        {
          label: '客户支付保险公司',
          value: 1
        },
        {
          label: '客户支付平台',
          value: 2
        }
      ],
      searchFields: [
        {
          type: 'textarea',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'input',
          valKey: 'operator',
          hintText: '处理人'
        },
        {
          type: 'daterange',
          hintText: '收取',
          valKey: 'charge_at_range'
        }
      ],
      cols: [
        { label: '流水号', prop: 'order_no' },
        {
          label: '收取类型',
          scopedSlots: {
            default: (scoped) => {
              const types = {
                1: '支付保司',
                2: '支付平台'
              }

              return types[scoped.row.payee_type]
            }
          }
        },
        {
          label: '单据数',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    type="primary"
                    onClick={() => {
                      this.$router.push({
                        name: 'PremiumBillReceivables',
                        params: {
                          id: scoped.row.id
                        }
                      })
                    }}
                  >
                    {scoped.row.payment_num}
                  </el-link>
                </div>
              )
            }
          }
        },
        // { label: '保单数', prop: 'policy_num' },
        { label: '应收保费', prop: 'receivable' },
        { label: '实际收取', prop: 'actual_receivable' },
        { label: '处理人', prop: 'operation.name' },
        { label: '备注', prop: 'remark' },
        {
          label: '状态',
          scopedSlots: {
            default: (scoped) => {
              const types = {
                0: '未提交',
                1: '已提交',
                2: '已完成'
              }

              return types[scoped.row.status]
            }
          }
        },
        {
          label: '收取时间',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.status !== 2) {
                return '-'
              }
              return scoped.row.charge_at
            }
          }
        },
        // JSX 插槽
        {
          label: '支付凭证',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.proof) {
                return (
                  <div>
                    <el-link
                      type="primary"
                      href={`${scoped.row.proof}?token=` + window.localStorage.getItem(tokenKey)}
                      download=""
                      target="_blank"
                    >
                      点击查看
                    </el-link>
                  </div>
                )
              } else {
                return '-'
              }
            }
          }
        },
        {
          label: '操作',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    type="primary"
                    v-can={{ name: 'finance.premium-receivable.bills.export' }}
                    href={exportPremiumReceivables({ bill_id: scoped.row.id })}
                    download=""
                    target="_blank"
                  >
                    导出清单
                  </el-link>
                </div>
              )
            }
          }
        }
      ],
      tableData: [],
      searchData: {},
      pagingAttrs: {
        align: 'center',
        currentPage: 1,
        pageSize: 15,
        layout: 'total, prev, pager, next, jumper',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.pagingAttrs.currentPage = page

          this.fetchPremiumReceivableBills()
        }
      },
      rawCompanies: []
    }
  },
  mounted() {
    getPlatformProductCompanies().then((r) => {
      this.rawCompanies = r.data

      this.loadCompanies()
      this.loadCompanyBranches()
    })
    this.fetchPremiumReceivableBills()
  },
  methods: {
    handleSearchPanel(command, data) {
      this.searchQuery = data
      this.pagingAttrs.currentPage = 1
      if (command === 'export') {
        this.exportBills()
      } else {
        this.fetchPremiumReceivableBills()
      }
    },
    fetchPremiumReceivableBills() {
      const loading = Loading.service()
      premiumReceivableBills({
        page: this.pagingAttrs.currentPage,
        filter: this.searchQuery
      })
        .then((r) => {
          this.tableData = r.data

          this.pagingAttrs.currentPage = r.meta.current_page
          this.pagingAttrs.pageSize = r.meta.per_page
          this.pagingAttrs.total = r.meta.total

          loading.close()
        })
        .finally(() => loading.close())
    },
    exportBills() {
      const link = exportPremiumReceivableBills({
        filter: this.searchQuery
      })
      window.open(link, '_blank')
    },
    loadCompanies() {
      const options = this.rawCompanies.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('company_id', options)
    },
    loadCompanyBranches() {
      let options = []
      if (this.searchData?.company_id !== '' && this.searchData?.company_id !== undefined) {
        const company = this.rawCompanies.find((e) => e.id === this.searchData?.company_id)
        if (company) {
          options = company.branches.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      } else {
        this.rawCompanies.forEach((e) => {
          e?.branches.forEach((e) => {
            options.push({
              label: e.name,
              value: e.id
            })
          })
        })
      }

      this.assignSelectOptions('company_branch_id', options)
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.invoice {
  padding: 0 20px;
}
.a_style {
  color: #fff;
  text-decoration: none;
}
</style>
