<template>
  <div class="w-100 feePayment">
    <el-card shadow="never" class="box-card-options" :body-style="{ padding: '20px' }">
      <div class="d-flex justify-content-between">
        <h3>已选单据：{{ pagingAttrs.total }}单 需收取： ￥{{ Number(totalReceivable).toFixed(2) + '元' }}</h3>
        <div>
          <el-button
            type="primary"
            v-can="{ name: 'finance.premium-receivable.bills.create' }"
            @click="dialogVisible = true"
            >确认收取</el-button
          >
        </div>
      </div>
      <div style="width: 30%; display: flex; align-items: center">
        <el-input v-model="search" size="mini" placeholder="保单号" clearable />
        <el-button
          style="margin-left: 10px"
          type="primary"
          v-can="{ name: 'finance.premium-receivable.bills.create' }"
          @click="handleSearch"
          >搜索
        </el-button>
      </div>
      <DefineTable :cols="cols" :data="data" :paging="pagingAttrs" :pagingEvents="pagingEvents"></DefineTable>
    </el-card>

    <el-dialog title="保费应付处理" :visible.sync="dialogVisible" width="30%" :before-close="handleClose">
      <el-form ref="premiumForm" label-position="left" label-width="100px" :model="form" :rules="rules">
        <el-form-item label="收取总保费" prop="actual_receivable">
          <el-input type="number" v-model="form.actual_receivable" placeholder="请输入收取总保费"></el-input>
        </el-form-item>
        <el-form-item label="保费支付类型" prop="payee_type">
          <el-select placeholder="请选择保费支付类型" v-model="form.payee_type" class="w-100">
            <el-option
              v-for="payeeType in payeeTypes"
              :key="payeeType.value"
              :label="payeeType.label"
              :value="payeeType.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="收取凭证" prop="proof">
          <upload-file v-model="form.proof"></upload-file>
        </el-form-item>
        <el-form-item label="备注">
          <el-input type="textarea" placeholder="请输入备注" :rows="4" v-model="form.remark"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" v-can="{ name: 'finance.premium-receivable.bills.draft' }" @click="draft"
          >暂 存</el-button
        >
        <el-button type="primary" v-can="{ name: 'finance.premium-receivable.bills.create' }" @click="handle"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getPremiumReceivables,
  getPremiumReceivableCount,
  handlePremiumReceivable,
  draftPremiumReceivable
} from '@/apis/finance'
import { Loading } from 'element-ui'

export default {
  name: 'HandlePremiumReceivable',
  data() {
    return {
      form: {
        actual_receivable: '',
        proof: '',
        payee_type: '',
        remark: ''
      },
      search: '',
      totalReceivable: 0,
      removeIds: [],
      handleIds: [],
      dialogVisible: false,
      cols: [
        // { label: '流水号', prop: 'order_no' },
        { label: '保单号', prop: 'policy.policy_no', width: '200', fixed: 'left' },
        { label: '应收取平台', prop: 'receivable_platform.name' },
        {
          label: '险种',
          scopedSlots: {
            default: (scoped) => {
              const types = {
                1: '国内货运险',
                2: '国际货运险',
                3: '单车责任险',
                4: '其他险种',
                5: '雇主责任险',
                6: '线下录入保单',
                7: '跨境电商险'
              }

              return scoped.row.policy.type === 6 ? scoped.row.policy.offline_type : types[scoped.row.policy.type]
            }
          }
        },
        { prop: 'receivable', label: '应收保费' },
        { prop: 'company_branch.name', label: '出单公司' },
        { prop: 'issued_at', label: '生效时间' },
        // JSX 插槽
        {
          label: '操作',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    type="primary"
                    onClick={() => {
                      this.remove(scoped.row)
                    }}
                  >
                    移除
                  </el-link>
                </div>
              )
            }
          }
        }
      ],
      data: [],
      searchQuery: {},
      pagingAttrs: {
        currentPage: 1,
        pageSize: 15,
        layout: 'total, prev, pager, next, jumper',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.pagingAttrs.currentPage = page

          this.fetchPremiumReceivables()
        }
      },
      rules: {
        actual_receivable: [{ required: true, message: '请输入实际收取保费', trigger: 'blur' }],
        payee_type: [{ required: true, message: '请选择收款类型', trigger: ['blur', 'change'] }],
        proof: [{ required: true, message: '请上传收取凭证', trigger: 'blur' }]
      }
    }
  },
  computed: {
    payeeTypes() {
      return [
        {
          label: '客户支付保险公司',
          value: 1
        },
        {
          label: '客户支付平台',
          value: 2
        }
      ]
    }
  },
  mounted() {
    this.searchQuery = { ...this.$route?.query }
    delete this.searchQuery.all_checkout
    delete this.searchQuery.id

    this.fetchData()
  },
  methods: {
    handleSearch() {
      this.searchQuery = { ...this.searchQuery, policy_no: this.search }
      this.fetchData()
    },
    fetchData() {
      this.fetchPremiumReceivables()
      this.fetchPremiumReceivablesCount()
    },
    fetchPremiumReceivables() {
      const loading = Loading.service()
      getPremiumReceivables({
        page: this.pagingAttrs.currentPage,
        ids: this.$route?.query?.id?.split(','),
        all_checkout: this.$route?.query?.all_checkout,
        filter: this.searchQuery
      })
        .then((r) => {
          this.data = r.data

          this.pagingAttrs.currentPage = r.meta.current_page
          this.pagingAttrs.pageSize = r.meta.per_page
          this.pagingAttrs.total = r.meta.total
          loading.close()
        })
        .finally(() => loading.close())
    },
    fetchPremiumReceivablesCount() {
      getPremiumReceivableCount({
        ids: this.$route?.query?.id?.split(','),
        all_checkout: this.$route?.query?.all_checkout,
        filter: this.searchQuery
      }).then((r) => {
        this.totalReceivable = r.data.receivable
        this.handleIds = r.data.ids
      })
    },
    remove(row) {
      this.removeIds.push(row.id)
      this.search = ''
      this.searchQuery = { ...this.searchQuery, policy_no: '' }
      this.searchQuery = { ...this.searchQuery, without: this.removeIds }
      this.fetchData()
    },
    handle() {
      this.$refs?.premiumForm?.validate((valid) => {
        if (valid) {
          const loading = Loading.service()
          handlePremiumReceivable(Object.assign({}, this.marshalData(this.form)))
            .then(() => {
              this.dialogVisible = false
              this.$router.push({
                name: 'PremiumReceivable'
              })
              this.$message({
                type: 'success',
                message: '结算成功'
              })
              loading.close()
            })
            .finally(() => loading.close())
        }
      })
    },
    draft() {
      const loading = Loading.service()
      draftPremiumReceivable(Object.assign({}, this.marshalData(this.form)))
        .then(() => {
          this.dialogVisible = false
          this.$router.push({
            name: 'PremiumReceivable'
          })
          this.$message({
            type: 'success',
            message: '暂存成功'
          })
          loading.close()
        })
        .finally(() => loading.close())
    },
    handleClose() {
      this.dialogVisible = false
    },
    marshalData(data) {
      data.receivable = Number(this.totalReceivable).toFixed(2)
      data.ids = this.handleIds.filter((item) => !this.removeIds.includes(item)).join(',')
      return data
    }
  }
}
</script>

<style scoped>
.feePayment {
  padding: 0 20px;
}
</style>
