<!--
 * @Descripttion: 
 * @version: 
 * @Author: yanb
 * @Date: 2021-08-17 17:29:04
 * @LastEditors: yanb
 * @LastEditTime: 2024-06-19 15:16:45
-->
<template>
  <div class="w-100 invoice">
    <el-card shadow="never" class="box-card-options" :body-style="{ padding: '20px' }">
      <div>
        <el-button type="primary" v-can="{ name: 'finance.premium-receivable.bills.export' }" @click="exportReceivables"
          >导出列表
        </el-button>
      </div>
      <DefineTable :cols="cols" :data="tableData" :paging="pagingAttrs" :pagingEvents="pagingEvents"></DefineTable>
    </el-card>
  </div>
</template>

<script>
import { premiumBillsReceivables, exportPremiumReceivables } from '@/apis/finance'

export default {
  name: 'PremiumBillReceivables',
  data() {
    return {
      cols: [
        // { label: '流水号', prop: 'order_no' },
        { label: '保单号', prop: 'policy.policy_no', width: '200', fixed: 'left' },
        {
          label: '险种',
          scopedSlots: {
            default: (scoped) => {
              const types = {
                1: '国内货运险',
                2: '国际货运险',
                3: '单车责任险',
                4: '其他险种',
                5: '雇主责任险',
                6: '线下录入保单',
                7: '跨境电商险'
              }
              return scoped.row.policy.type === 6 ? scoped.row.policy.offline_type : types[scoped.row.policy.type]
            }
          }
        },
        { label: '业务来源', prop: 'business_source.name' },
        { label: '应收保费', prop: 'receivable' },
        { label: '应收平台', prop: 'receivable_platform.name' },
        { label: '出单公司', prop: 'company_branch.name' },
        { label: '生效时间', prop: 'issued_at' }
      ],
      tableData: [],
      pagingAttrs: {
        align: 'center',
        currentPage: 1,
        pageSize: 15,
        layout: 'total, prev, pager, next, jumper',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.pagingAttrs.currentPage = page

          this.getListData()
        }
      }
    }
  },
  mounted() {
    this.getListData()
  },
  methods: {
    getListData() {
      premiumBillsReceivables(this.$route.params.id, {
        page: this.pagingAttrs.currentPage
      }).then((r) => {
        this.tableData = r.data
        this.pagingAttrs.currentPage = r.meta.current_page
        this.pagingAttrs.total = r.meta.total
      })
    },
    exportReceivables() {
      const link = exportPremiumReceivables({ bill_id: this.$route.params.id })
      window.open(link, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.invoice {
  padding: 0 20px;
}
.a_style {
  color: #fff;
  text-decoration: none;
}
</style>
