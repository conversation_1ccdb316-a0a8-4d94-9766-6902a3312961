<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <SearchPanel
      size="small"
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
    ></SearchPanel>
    <el-card shadow="never" class="m-extra-large-t">
      <define-table
        :data="tableData"
        :cols="cols"
        :paging="paging"
        :paging-events="pagingEvents"
        :events="tableEvents"
        :rowKey="getRowKeys"
      />
    </el-card>
  </div>
</template>

<script>
import { getSettlements, getPayees, exportSettlement } from '@/apis/finance'
import { Loading } from 'element-ui'

export default {
  name: 'Settlement',
  data() {
    return {
      searchFields: [
        {
          type: 'input',
          valKey: 'order_no',
          hintText: '流水号'
        },
        {
          type: 'textarea',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'select',
          valKey: 'type',
          hintText: '类型',
          onChangeReset: 'payee_id',
          options: [
            { label: '支付保司', value: 1 },
            { label: '支付平台', value: 2 }
          ]
        },
        {
          type: 'input',
          valKey: 'payer',
          hintText: '付款人'
        },
        {
          type: 'select',
          valKey: 'payee_id',
          hintText: '收款人',
          options: []
        },
        {
          type: 'select',
          valKey: 'status',
          hintText: '状态',
          options: [
            { label: '已提交', value: 1 },
            { label: '审核中', value: 2 },
            { label: '已完成', value: 3 }
          ]
        },
        {
          type: 'daterange',
          valKey: 'created_at_range',
          hintText: '申请'
        },
        {
          type: 'daterange',
          valKey: 'operated_at_range',
          hintText: '处理'
        }
      ],
      cols: [
        { align: 'center', type: 'selection', reserveSelection: true },
        {
          label: '流水号',
          prop: 'order_no'
        },
        {
          label: '申请人',
          prop: 'apply.name'
        },
        {
          label: '保单总金额',
          prop: 'policy_amount'
        },
        {
          label: '水单总金额',
          prop: 'memo_amount'
        },
        {
          label: '收款人',
          prop: 'payee.name'
        },
        {
          label: '申请时间',
          prop: 'created_at'
        },
        {
          label: '处理时间',
          prop: 'operated_at'
        },
        {
          label: '状态',
          prop: 'status',
          scopedSlots: {
            default: (scoped) => {
              switch (parseInt(scoped.row.status, 10)) {
                case 1:
                  return <span class="text-info">已提交</span>
                case 2:
                  return <span class="text-primary">审核中</span>
                case 3:
                  return <span class="text-success">已完成</span>
                case 4:
                  return <span class="text-info">已退回</span>
                default:
                  break
              }
            }
          }
        },
        {
          label: '操作',
          fixed: 'right',
          width: 150,
          alian: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    size="small"
                    type="text"
                    onClick={() => {
                      this.settlementDetail(scoped.row.id)
                    }}
                  >
                    详情
                  </el-button>
                </div>
              )
            }
          }
        }
      ],
      tableData: [],
      // 分页器 不传递 -> 没有分页
      paging: {
        currentPage: 1,
        pageSize: 0,
        layout: 'prev, pager, next, jumper, total',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page

          this.fetchSettlements()
        }
      },
      multipleSelection: [],
      tableEvents: {
        'selection-change': (val) => {
          this.multipleSelection = val
        }
      },
      searchQuery: {},
      searchData: {},
      payees: [],
      sendBackDialog: {
        visible: false,
        title: '退回理由',
        settlementId: 0,
        form: {
          reason: ''
        },
        rules: {
          reason: [{ required: true, message: '请输入退回理由', trigger: 'blur' }]
        }
      }
    }
  },
  created() {
    this.fetchSettlements()
  },
  methods: {
    getRowKeys(row) {
      return row.id
    },
    handleSearchPanel(command, data) {
      this.searchQuery = data
      this.paging.page = 1
      if (command === 'export') {
        this.settlementExport()
      } else {
        this.fetchSettlements()
      }
    },
    fetchSettlements() {
      const loading = Loading.service()
      getSettlements({
        page: this.paging.page,
        filter: this.searchQuery
      })
        .then((r) => {
          this.tableData = r.data

          this.paging.currentPage = r.meta.current_page
          this.paging.pageSize = r.meta.per_page
          this.paging.total = r.meta.total

          loading.close()
        })
        .finally(() => loading.close())
    },
    settlementDetail(id) {
      this.$open({
        name: 'SettlementDetail',
        params: {
          id: id
        }
      })
    },
    settlementExport() {
      const link = exportSettlement({
        filter: this.searchQuery,
        ids: this.multipleSelection.map((item) => {
          return item.id
        })
      })
      window.open(link, '_blank')
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    }
  },
  watch: {
    'searchData.type'(type) {
      if (type) {
        getPayees(type).then((r) => {
          this.assignSelectOptions('payee_id', r.data)
        })
      }
    }
  }
}
</script>
