<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <el-card shadow="never" class="box-card-options" :body-style="{ padding: '20px' }">
      <h1>开票金额</h1>
      <p style="color: #d9001b; font-size: 24px">￥ {{ $route.query.amount }}元</p>
      <h1>开票信息</h1>
      <el-form :rules="rules" ref="form" :model="form" label-width="80px" label-suffix=":" label-position="top">
        <el-row :gutter="40">
          <el-col :span="12" label-width="80px">
            <el-form-item label="发票抬头" prop="company_name">
              <el-input v-model="form.company_name" placeholder="请输入发票抬头"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="纳税人识别码" prop="tax_no">
              <el-input v-model="form.tax_no" placeholder="请输入纳税人识别码"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="40">
          <el-col :span="12" label-width="80px">
            <el-form-item label="发票类型（不选默认为普票）">
              <el-radio v-model="form.type" label="1">普票</el-radio>
              <el-radio v-model="form.type" label="2">专票</el-radio>
            </el-form-item>
          </el-col>
        </el-row>
        <div v-if="is_zy">
          <el-row :gutter="40">
            <el-col :span="12" label-width="80px">
              <el-form-item label="邮箱" prop="email">
                <el-input v-model="form.email" placeholder="请输入邮箱"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开票人角色" prop="customer_role_type">
                <el-input v-model="form.customer_role_type" placeholder="请输入开票人角色"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div v-if="form.type === '2'">
          <el-row :gutter="40">
            <el-col :span="12" label-width="80px">
              <el-form-item label="银行卡号" prop="bankcard_no">
                <el-input v-model="form.bankcard_no" placeholder="请输入银行卡号"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开户行" prop="bank_name">
                <el-input v-model="form.bank_name" placeholder="请输入开户行"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="40">
            <el-col :span="12" label-width="80px">
              <el-form-item label="公司地址" prop="registered_addr">
                <el-input v-model="form.registered_addr" placeholder="请输入公司地址"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="公司电话" prop="registered_phone_number">
                <el-input v-model="form.registered_phone_number" placeholder="请输入公司电话"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <h1>邮寄信息</h1>
          <el-row :gutter="40">
            <el-col :span="12" label-width="80px">
              <el-form-item label="是否邮寄">
                <el-radio v-model="form.is_mail" :label="1">是</el-radio>
                <el-radio v-model="form.is_mail" :label="0">否</el-radio>
              </el-form-item>
            </el-col>
          </el-row>

          <div v-if="form.is_mail === 1">
            <el-row :gutter="40">
              <el-col :span="12" label-width="80px">
                <el-form-item label="收件人" prop="recipient">
                  <el-input v-model="form.recipient" placeholder="请输入收件人"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="收件人电话" prop="recipient_phone_number">
                  <el-input v-model="form.recipient_phone_number" placeholder="请输入收件人电话"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="40">
              <el-col label-width="80px">
                <el-form-item label="收件人地址" prop="delivery_address">
                  <el-input v-model="form.delivery_address" placeholder="请输入收件人地址"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
        <el-row>
          <el-col>
            <el-button type="primary" @click="sureSubmit">确认开票</el-button>
            <el-button type="primary" @click="$router.go(-1)">返回</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { applyInvoice } from '@/apis/finance.js'

export default {
  name: 'billInfo',
  data() {
    return {
      is_zy: void 0,
      form: {
        company_name: '',
        tax_no: '',
        type: '1',
        bankcard_no: '',
        bank_name: '',
        registered_addr: '',
        registered_phone_number: '',
        is_mail: 0,
        recipient: '',
        recipient_phone_number: '',
        delivery_address: '',
        is_all: 0,
        ids: []
      },
      rules: {
        company_name: [{ required: true, message: '请输入发票抬头', trigger: 'blur' }],
        tax_no: [{ required: true, message: '请输入纳税人识别码', trigger: 'blur' }],
        type: [{ required: true, message: '请选择发票类型', trigger: 'blur' }],
        bankcard_no: [{ required: true, message: '请输入银行卡号', trigger: 'blur' }],
        bank_name: [{ required: true, message: '请输入开户行', trigger: 'blur' }],
        registered_addr: [{ required: true, message: '请输入公司地址', trigger: 'blur' }],
        registered_phone_number: [{ required: true, message: '请输入公司电话', trigger: 'blur' }],
        recipient: [{ required: true, message: '请输入收件人', trigger: 'blur' }],
        recipient_phone_number: [{ required: true, message: '请输入收件人电话', trigger: 'blur' }],
        delivery_address: [{ required: true, message: '请输入收件人地址', trigger: 'blur' }]
      }
    }
  },
  mounted() {
    this.is_zy = this.$route.query.is_zy === 'true' ? true : false
    this.form.ids = this.$route.query.id !== undefined ? this.$route.query.id.split(',') : []
  },
  methods: {
    sureSubmit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const is_zy = {
            policy_mode: 'NORMAL'
          }
          if (this.is_zy) {
            is_zy.policy_mode = 'GROUP_ZY'
          }
          const _data = Object.assign({}, this.form, is_zy)
          applyInvoice(_data).then(() => {
            this.$message({
              type: 'success',
              message: '开票成功'
            })
            this.$router.push({
              name: 'Invoice'
            })
          })
        }
      })
    }
  }
}
</script>

<style scoped>
.billInfo {
  padding: 0 20px;
}
h1::before {
  content: '';
  display: inline-block;
  width: 4px;
  height: 16px;
  /* line-height: 16px; */
  background-color: #ff7f4c;
  margin-right: 16px;
}
</style>
