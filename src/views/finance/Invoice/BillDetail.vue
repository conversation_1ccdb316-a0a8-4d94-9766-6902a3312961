<template>
  <div class="w-100 billdetail">
    <div class="m-extra-large-x p-extra-large bg-white m-extra-large-b flex-fill o-hidden o-y-auto">
      <el-alert
        v-if="invoice.status === 4"
        type="warning"
        title="开票申请已被退回"
        show-icon
        :description="'退回原因：' + invoice.reason"
        :closable="false"
      />
      <div>
        <el-row>
          <el-col>
            <el-button-group style="float: right">
              <el-button
                type="primary"
                v-can="{ name: 'finance.invoices.operator' }"
                v-if="invoice?.status === 1"
                @click="associate"
              >
                领取
              </el-button>
              <el-button
                type="primary"
                v-can="{ name: 'finance.invoices.send-email' }"
                v-if="!invoice?.is_zy && invoice.status === 2 && isAssociateOperator"
                @click="sendInvoiceMail"
              >
                发送邮件
              </el-button>
              <el-button
                type="primary"
                v-can="{ name: 'finance.invoices.handle' }"
                v-if="!invoice?.is_zy && [2, 3].includes(invoice.status) && isAssociateOperator"
                @click="handleInvoiceDialog.visible = true"
              >
                {{ invoice.status === 3 ? '重新上传' : '开票' }}
              </el-button>
              <el-button
                type="primary"
                v-can="{ name: 'finance.invoices.handle' }"
                v-if="invoice?.is_zy && invoice.status === 2 && isAssociateOperator"
                @click="applyZYInvoice"
              >
                申请开票
              </el-button>
              <el-button
                type="primary"
                v-can="{ name: 'finance.invoices.send-back' }"
                v-if="invoice.status === 2 && isAssociateOperator"
                @click="sendBackDialog.visible = true"
              >
                退回
              </el-button>
              <el-button
                type="primary"
                v-can="{ name: 'finance.invoices.show' }"
                v-if="!invoice?.is_zy"
                @click="downloadInvoice"
              >
                下载
              </el-button>
              <el-button type="primary" v-can="{ name: 'finance.invoices.show' }" @click="policiesExport">
                导出保单列表
              </el-button>
              <el-button
                type="primary"
                v-can="{ name: 'finance.invoices.check' }"
                v-if="invoice?.is_zy && invoice?.status === 2 && invoice?.type === 1 && isAssociateOperator"
                @click="checkInvoice"
              >
                查看开票情况
              </el-button>
              <el-button
                type="primary"
                v-can="{ name: 'finance.invoices.issued' }"
                v-if="invoice?.is_zy && invoice?.status === 2 && invoice?.type === 2 && isAssociateOperator"
                @click="issuedInvoice"
              >
                确认开票成功
              </el-button>
            </el-button-group>
          </el-col>
        </el-row>
      </div>
      <define-details :data="dataList"></define-details>
      <DefineTable :data="invoice.policies" :cols="cols" v-if="invoice" />
    </div>

    <el-dialog title="开票" :visible.sync="handleInvoiceDialog.visible" width="40%">
      <el-form ref="form" :rules="rules" label-position="left" label-width="80px" :model="handleInvoiceDialog.data">
        <el-form-item label="发票号：" prop="invoice_no">
          <el-input placeholder="请输入发票号" v-model="handleInvoiceDialog.data.invoice_no"></el-input>
        </el-form-item>
        <el-form-item label="发票文件" prop="file">
          <upload-file v-model="handleInvoiceDialog.data.file"></upload-file>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleInvoiceDialog.visible = false">取 消</el-button>
        <el-button type="primary" @click="handleInvoice">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="退回" :visible.sync="sendBackDialog.visible" width="40%">
      <el-form ref="form1" label-position="left" label-width="80px" :model="sendBackDialog.data">
        <el-form-item
          label="退回理由"
          prop="reason"
          :rules="[{ required: true, message: '请输入退回理由', trigger: 'blur' }]"
        >
          <el-input placeholder="请输入退回理由" v-model="sendBackDialog.data.reason"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="sendBackDialog.visible = false">取 消</el-button>
        <el-button type="primary" @click="sendBack">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  invoiceDetail,
  handleInvoice,
  sendBackInvoice,
  sendInvoiceMail,
  invoicePolicyExport,
  check_zy,
  issuedInvoice,
  associateOperator
} from '@/apis/finance'
import dayjs from 'dayjs'
import { mapGetters } from 'vuex'

export default {
  name: 'billdetail',
  data() {
    return {
      handleInvoiceDialog: {
        visible: false,
        data: {
          invoice_no: '',
          file: '',
          policy_mode: 'NORMAL'
        }
      },
      sendBackDialog: {
        visible: false,
        data: {
          reason: ''
        }
      },
      invoice: [],
      cols: [
        { align: '', type: 'index' },
        // { prop: '', label: '流水号' },
        { prop: 'policy_no', label: '保单号' },
        {
          label: '险种',
          scopedSlots: {
            default: (scoped) => {
              const types = {
                1: '国内货运险',
                2: '国际货运险',
                3: '单车责任险',
                4: '其他险种',
                5: '雇主责任险',
                6: '线下录入保单',
                7: '跨境电商险'
              }

              return types[scoped.row.type]
            }
          }
        },
        {
          label: '金额',
          scopedSlots: {
            default: (scoped) => {
              // 雇主责任险
              if (scoped.row.type === 5) {
                return (
                  <div>
                    ¥ {scoped.row.amount}
                    <el-popover placement="left" width="680" trigger="hover">
                      <el-table data={scoped.row.group_endorses}>
                        <el-table-column width="80" property="id" label="#"></el-table-column>
                        <el-table-column width="150" property="batch_no" label="批次号"></el-table-column>
                        <el-table-column width="200" property="endorse_no" label="批单号"></el-table-column>
                        <el-table-column width="100" property="total_fee" label="金额"></el-table-column>
                        <el-table-column width="150" property="created_at" label="时间"></el-table-column>
                      </el-table>
                      <span class="text-primary" style="cursor: pointer;" slot="reference">
                        &nbsp;&nbsp;详情
                      </span>
                    </el-popover>
                  </div>
                )
              } else {
                return <span>¥ {scoped.row.amount}</span>
              }
            }
          }
        },
        { prop: 'company_branch_name', label: '出单公司' },
        { prop: 'submitted_at', label: '投保时间' }
      ],
      tableData: [],
      rules: {
        invoice_no: [{ required: true, message: '请输入发票号', trigger: 'blur' }],
        file: [{ required: true, message: '请上传发票文件', trigger: 'blur' }]
      }
    }
  },
  computed: {
    ...mapGetters('auth', ['admin']),
    dataList() {
      let dataList = {
        title: '发票详情',
        data: [
          {
            title: '账户信息',
            groups: [
              { label: '流水号', value: this.invoice?.order_no ?? '' },
              { label: '发票类型', value: this.invoiceType },
              { label: '申请人', value: this.invoice?.apply_name ?? '' },
              { label: '申请时间', value: this.invoice?.apply_at ?? '' }
            ]
          },
          {
            title: '开票信息',
            groups: [
              { label: '发票抬头', value: this.invoice?.company_name ?? '' },
              { label: '纳税识别号', value: this.invoice?.tax_no ?? '' },
              { label: '开票金额', value: this.invoice?.amount + '元' ?? '' },
              { label: '发票号', value: this.invoice?.invoice_no }
            ]
          },
          {
            title: '专票信息',
            groups: [
              { label: '注册地址', value: this.invoice?.registered_addr ?? '' },
              { label: '注册电话', value: this.invoice?.registered_phone_number ?? '' },
              { label: '开户银行', value: this.invoice?.bank_name ?? '' },
              { label: '银行账号', value: this.invoice?.bankcard_no ?? '' }
            ]
          },
          {
            title: '发票文件',
            isHide: !this.invoice?.is_zy,
            groups: [{ label: '发票文件', value: this.invoiceFiles ?? '', row: true }]
          },
          {
            title: '邮寄信息',
            isHide: this.admin.platform.id != this.invoice?.product_platform_id && !this.is_own_invoice,
            groups: [
              { label: '是否邮寄', value: this.invoice?.is_mail ? '是' : '否', row: true }
              // { label: '收件人', value: this.invoice?.recipient ?? '' },
              // { label: '收件人电话', value: this.invoice?.recipient_phone_number ?? '' },
              // { label: '发票寄送地址', value: this.invoice?.delivery_address ?? '' }
            ]
          },
          {
            title: '开票保单',
            groups: []
          }
        ]
      }
      dataList.data = dataList.data.filter((e) => e.isHide === undefined || e.isHide === false)
      return dataList
    },
    invoiceType() {
      return (this.invoice?.type ?? '') === 1 ? '普票' : '专票'
    },
    isAssociateOperator() {
      return this.invoice?.operator_id !== -1
    },
    invoiceFiles() {
      if (this.invoice?.file) {
        if ((this.invoice?.file ?? '').indexOf(';') != -1) {
          var data = ''
          this.invoice?.file.split(';').forEach((item, index) => {
            if (item) {
              data +=
                '<a style="text-decoration:none; margin-right: 10px" href="' +
                item +
                '" target="_blank">发票文件' +
                (Number(index) + 1) +
                '</a>'
            }
          })
          return data
        }
        return '<a style="text-decoration:none;" href="' + this.invoice?.file + '" target="_blank">发票文件</a>'
      }

      return ''
    }
  },
  created() {
    const _id = this.$route.params.id
    if (_id === undefined) {
      return this.$message.error('参数错误')
    }

    this.fetchInvoice(_id)
  },
  methods: {
    fetchInvoice(id) {
      invoiceDetail(id).then((r) => {
        r.data.apply_at = dayjs(r.data?.apply_at).format('YYYY/MM/DD HH:mm:ss')
        r.data.policies.forEach((item) => {
          item.created_at = dayjs(item.created_at).format('YYYY/MM/DD HH:mm:ss')
        })
        if (r.data.is_zy) {
          this.handleInvoiceDialog.data.policy_mode = 'GROUP_ZY'
        }
        this.invoice = r.data
      })
    },
    // 检查
    checkInvoice() {
      check_zy(this.$route.params.id).then((res) => {
        this.$message({
          type: res.success === true ? 'success' : 'error',
          message: res.msg
        })
        this.fetchInvoice(this.$route.params.id)
      })
    },
    applyZYInvoice() {
      handleInvoice(this.$route.params.id, this.handleInvoiceDialog.data).then(() => {
        this.$message({
          type: 'success',
          message: '开票申请提交成功'
        })
        this.fetchInvoice(this.$route.params.id)
      })
    },
    // 开票
    handleInvoice() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          handleInvoice(this.$route.params.id, this.handleInvoiceDialog.data).then(() => {
            this.$message({
              type: 'success',
              message: '开票成功'
            })
            this.handleInvoiceDialog.visible = false
            this.fetchInvoice(this.$route.params.id)
          })
        } else {
          return false
        }
      })
    },
    // 退回
    sendBack() {
      this.$refs.form1.validate((valid) => {
        if (valid) {
          sendBackInvoice(this.$route.params.id, this.sendBackDialog.data).then(() => {
            this.$message({
              type: 'success',
              message: '退回成功'
            })

            this.fetchInvoice(this.$route.params.id)
          })
          this.sendBackDialog.visible = false
        } else {
          return false
        }
      })
    },
    downloadInvoice() {
      window.open(this.invoice.file, '_blank')
    },
    policiesExport() {
      let link = invoicePolicyExport(this.invoice.id)
      window.open(link, '_blank')
    },
    sendInvoiceMail() {
      this.$confirm(
        `是否确认发送邮件,该操作将会发送邮件至保险公司,该发票当前已发送邮件次数：[ ${this.invoice.mail_send_num} ]`,
        '发送邮件',
        {
          confirmButtonText: '发送',
          cancelButtonText: '关闭',
          type: 'warning'
        }
      )
        .then(() => {
          const loading = this.$loading({
            lock: true,
            text: '发送中...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          sendInvoiceMail(this.$route.params.id)
            .then(() => {
              this.$message({
                type: 'success',
                message: '发送成功'
              })

              this.fetchInvoice(this.$route.params.id)
            })
            .finally(() => {
              loading.close()
            })
        })
        .catch(() => {})
    },
    issuedInvoice() {
      this.$confirm(`是否已确认发票开出,请谨慎操作`, '确认发票开出', {
        confirmButtonText: '确认',
        cancelButtonText: '关闭',
        type: 'warning'
      })
        .then(() => {
          const loading = this.$loading({
            lock: true,
            text: '处理中...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          issuedInvoice(this.$route.params.id)
            .then(() => {
              this.$message({
                type: 'success',
                message: '处理完成'
              })

              this.fetchInvoice(this.$route.params.id)
            })
            .finally(() => {
              loading.close()
            })
        })
        .catch(() => {})
    },
    associate() {
      this.$confirm(`是否确定领取,领取后其他业务员将无法操作改单`, '确认领取', {
        confirmButtonText: '确认',
        cancelButtonText: '关闭',
        type: 'warning'
      })
        .then(() => {
          const loading = this.$loading({
            lock: true,
            text: '处理中...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          associateOperator(this.$route.params.id)
            .then(() => {
              this.$message({
                type: 'success',
                message: '领取成功'
              })

              this.fetchInvoice(this.$route.params.id)
            })
            .finally(() => {
              loading.close()
            })
        })
        .catch(() => {})
    }
  }
}
</script>
