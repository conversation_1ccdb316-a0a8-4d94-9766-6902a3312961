<template>
  <div class="w-100 borderbox documents">
    <SearchPanel
      size="small"
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
    ></SearchPanel>
    <el-alert
      class="m-extra-large-t"
      type="warning"
      title="如需雇主开票请先按险种筛选"
      show-icon
      :closable="false"
    ></el-alert>
    <el-card shadow="never" class="m-extra-large-t">
      <div class="d-flex justify-content-start">
        <el-button type="primary" @click="showRequestInvoiceForm" style="margin-bottom: 15px">申请发票</el-button>
        <el-button type="primary" @click="applyInvoiceAll" style="margin-bottom: 15px">全部申请</el-button>
        <el-switch
          style="margin: 5px 0 0 10px"
          active-text="按被保人开票"
          inactive-text="按投保人开票"
          :active-value="2"
          :inactive-value="1"
          v-model="makeInvoiceClientType"
        ></el-switch>
      </div>

      <div class="mo_table">
        <div class="mo_table_wrap">
          <el-table
            :data="tableData"
            @selection-change="policyHandleSelectionChange"
            tooltip-effect="dark"
            style="width: 100%"
            :row-key="getRowKeys"
          >
            <el-table-column type="selection" width="80" align="center" :reserve-selection="true"> </el-table-column>
            <el-table-column prop="order_no" label="流水号" show-overflow-tooltip> </el-table-column>
            <el-table-column width="250" :label="searchData.type === 5 ? '批单号' : '保单号'" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ scope.row.type === 5 ? scope.row.endorse_no : scope.row.policy_no }}
              </template>
            </el-table-column>
            <el-table-column prop="policyholder" label="投保人" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="insured" label="被保人" show-overflow-tooltip> </el-table-column>
            <el-table-column label="险种" show-overflow-tooltip>
              <template slot-scope="scope"> {{ getPolicyType(scope.row.type) }} </template>
            </el-table-column>
            <el-table-column prop="company_branch.name" label="出单公司" show-overflow-tooltip> </el-table-column>
            <el-table-column label="保费" show-overflow-tooltip>
              <template slot-scope="props"> ¥ {{ props.row.amount }} </template>
            </el-table-column>
            <el-table-column prop="issued_at" label="出单时间" show-overflow-tooltip> </el-table-column>
          </el-table>
        </div>
        <el-pagination
          @current-change="policyPagingEvents"
          :current-page="policyPaging.currentPage"
          :page-size="policyPaging.pageSize"
          layout="prev, pager, next, jumper, total"
          :total="policyPaging.total"
        >
        </el-pagination>
      </div>
    </el-card>

    <request-invoice
      :fill-data.sync="userInvoiceInfo"
      :visible.sync="invoiceFormVisible"
      :amount="invoiceAmount"
      :isZhongyi.sync="isZhongyi"
      :isAllApply.sync="isAllApply"
      @submit="applyInvoice"
      @all-apply-submit="allApplyInvoice"
    />
  </div>
</template>

<script>
import { canInvoicePolicies, applyInvoice, applyInvoiceAll } from '@/apis/finance' //canInvoicePolicies
import { getPlatformProductCompanies } from '@/apis/platform_product'
import RequestInvoice from '@/components/finance/RequestInvoice'

export default {
  name: 'ApplyInvoice',
  components: { RequestInvoice },
  data() {
    return {
      invoiceFormVisible: false,
      userInvoiceInfo: {},
      isZhongyi: false,
      isAllApply: false,
      invoiceAmount: 0,
      makeInvoiceClientType: 1,
      // 搜索
      searchFields: [
        {
          type: 'daterange',
          valKey: 'submitted_at_range'
        },
        {
          type: 'select',
          valKey: 'type',
          hintText: '险种',
          options: [
            {
              label: '国内货运险',
              value: 1
            },
            {
              label: '国际货运险',
              value: 2
            },
            {
              label: '单车责任险',
              value: 3
            },
            {
              label: '其他险种',
              value: 4
            },
            {
              label: '雇主责任险',
              value: 5
            },
            {
              label: '跨境电商险',
              value: 7
            }
          ]
        },
        {
          type: 'select',
          valKey: 'company_id',
          hintText: '保险公司',
          onChangeReset: 'company_branch_id',
          options: []
        },
        {
          type: 'select',
          valKey: 'company_branch_id',
          hintText: '出单公司',
          options: []
        },
        {
          type: 'textarea',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'input',
          valKey: 'policyholder',
          hintText: '投保人'
        },
        {
          type: 'input',
          valKey: 'insured',
          hintText: '被保人'
        }
      ],
      cols: [
        { align: 'center', type: 'selection', width: '80' },
        { label: '流水号', prop: 'order_no' },
        { label: '保单号', prop: 'policy_no' },
        { label: '投保人', prop: 'policyholder' },
        {
          label: '险种',
          scopedSlots: {
            default: (scoped) => {
              const types = {
                1: '国内货运险',
                2: '国际货运险',
                3: '单车责任险',
                4: '其他险种',
                5: '雇主责任险',
                6: '线下录入保单',
                7: '跨境电商险'
              }

              return types[scoped.row.type]
            }
          }
        },
        { label: '出单公司', prop: 'company_branch.name' },
        { label: '客户保费', prop: 'user_premium' },
        { label: '生效时间', prop: 'issued_at' }
      ],
      tableData: [],
      policyPaging: {
        currentPage: 1,
        pageSize: 0,
        layout: 'prev, pager, next, jumper, total',
        total: 0
      },
      searchData: {},
      searchQuery: {},
      rawCompanies: [],
      multipleSelection: []
    }
  },
  mounted() {
    getPlatformProductCompanies().then((r) => {
      this.rawCompanies = r.data

      this.loadCompanies()
      this.loadCompanyBranches()
    })
    this.fetchPolicies()
  },
  methods: {
    handleSearchPanel(command, data) {
      this.searchQuery = data
      this.policyPaging.currentPage = 1
      if (command === 'export') {
        //
      } else {
        this.fetchPolicies()
      }
    },
    fetchPolicies() {
      canInvoicePolicies({
        page: this.policyPaging.currentPage,
        filter: this.searchQuery
      }).then((r) => {
        this.tableData = r.data

        this.policyPaging.currentPage = r.meta.current_page
        this.policyPaging.pageSize = r.meta.per_page
        this.policyPaging.total = r.meta.total
      })
    },
    getPolicyType(type) {
      switch (type) {
        case 1:
          return '国内货运险'
        case 2:
          return '国际货运险'
        case 3:
          return '单车责任险'
        case 4:
          return '其他险种'
        case 5:
          return '雇主责任险'
        case 7:
          return '跨境电商险'
        default:
          break
      }
    },
    showRequestInvoiceForm() {
      this.isAllApply = false
      if (this.applyValidate()) {
        this.invoiceFormVisible = true
        this.multipleSelection.forEach((e) => {
          this.invoiceAmount += Number(e.user_premium)
        })
        this.$nextTick(() => {
          this.userInvoiceInfo = {
            company_name:
              this.makeInvoiceClientType == 1
                ? this.multipleSelection[0].policyholder
                : this.multipleSelection[0].insured
          }
        })
      }
    },
    applyInvoice(data) {
      if (this.applyValidate()) {
        data.ids = []
        this.multipleSelection.forEach((e) => {
          data.ids.push(e.id)
        })
        data.filter = Object.assign({}, this.searchData)
        data.client_type = this.makeInvoiceClientType

        applyInvoice(data).then(() => {
          this.$message.success('申请成功')

          this.fetchPolicies()
        })
      }
    },
    applyValidate() {
      var policy1 = this.multipleSelection[0]
      if (!this.multipleSelection.length) {
        this.$message({
          type: 'warning',
          message: '请至少选择一项!'
        })
        return false
      }
      if (this.multipleSelection.find((item) => item.type != policy1.type)) {
        this.$message({
          type: 'warning',
          message: '请选择同一个险种的保单'
        })
        return false
      }

      if (this.searchData.type === 5 && this.multipleSelection.find((item) => item.policy_id != policy1.policy_id)) {
        this.$message({
          type: 'warning',
          message: '请选择同一个保单的批单'
        })
        return false
      }

      if (
        this.multipleSelection.find((item) => item.policyholder != policy1.policyholder) &&
        this.makeInvoiceClientType == 1
      ) {
        this.$message({
          type: 'warning',
          message: '请选择同一个投保人的保单'
        })
        return false
      }

      if (this.multipleSelection.find((item) => item.insured != policy1.insured) && this.makeInvoiceClientType == 2) {
        this.$message({
          type: 'warning',
          message: '请选择同一个被保人的保单'
        })
        return false
      }

      if (this.multipleSelection.find((item) => item.company_branch.id != policy1.company_branch.id)) {
        this.$message({
          type: 'warning',
          message: '请选择同一个出单公司的保单'
        })
        return false
      }

      this.isZhongyi = policy1.is_zy
      return true
    },
    policyPagingEvents(page) {
      this.policyPaging.currentPage = page

      this.fetchPolicies()
    },
    getRowKeys(row) {
      return row.id
    },
    policyHandleSelectionChange(val) {
      this.multipleSelection = val
    },
    hrefpayhistory() {
      this.$router.push({
        name: 'PaymentRecord'
      })
    },
    loadCompanies() {
      const options = this.rawCompanies.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('company_id', options)
    },
    loadCompanyBranches() {
      let options = []
      if (this.searchData?.company_id !== '' && this.searchData?.company_id !== undefined) {
        const company = this.rawCompanies.find((e) => e.id === this.searchData?.company_id)
        if (company) {
          options = company.branches.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      } else {
        this.rawCompanies.forEach((e) => {
          e?.branches.forEach((e) => {
            options.push({
              label: e.name,
              value: e.id
            })
          })
        })
      }

      this.assignSelectOptions('company_branch_id', options)
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    },
    applyInvoiceAll() {
      this.isAllApply = true
      this.invoiceFormVisible = true
    },
    allApplyInvoice(data) {
      data.filter = Object.assign({}, this.searchData)
      data.client_type = this.makeInvoiceClientType

      applyInvoiceAll(data).then(() => {
        this.$message.success('申请成功')

        this.fetchPolicies()
      })
    }
  },
  watch: {
    searchData: {
      immediate: true,
      deep: true,
      handler(value, old) {
        if (old?.company_id !== value?.company_id) {
          this.loadCompanyBranches()
        }
      }
    }
  }
}
</script>

<style scoped>
.documents {
  padding: 0 20px;
}
.el-select,
.el-select--small {
  width: 100%;
}
.box-card-options {
  margin-bottom: 20px;
}
.mo_table {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.mo_table > .mo_table_wrap {
  flex: 1;
}
.mo_table > .el-pagination,
.mo_table > .mo_table_wrap {
  margin-bottom: 1rem;
}
</style>
