<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <SearchPanel size="small" @command="handleSearchPanel" :custom="searchFields"></SearchPanel>
    <el-card shadow="never" class="m-extra-large-t">
      <define-table :data="tableData" :cols="cols" :paging="paging" :paging-events="pagingEvents" />
    </el-card>
  </div>
</template>

<script>
import { getPlatformTransactions, exportPlatformTransactions } from '@/apis/finance'

export default {
  name: 'PlatformTransactions',
  data() {
    return {
      searchFields: [
        {
          type: 'select',
          valKey: 'type',
          hintText: '状态',
          options: [
            { label: '充值', value: 1 },
            { label: '扣费', value: 2 },
            { label: '退款', value: 3 }
          ]
        },
        {
          type: 'daterange',
          valKey: 'created_at_range'
        },
        {
          type: 'input',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'input',
          valKey: 'username',
          hintText: '支出用户'
        }
      ],
      cols: [
        {
          label: '类型',
          prop: 'type',
          width: 80,
          scopedSlots: {
            default: (scoped) => {
              switch (parseInt(scoped.row.type, 10)) {
                case 1:
                  return <span class="text-primary">充值</span>
                case 2:
                  return <span class="text-info">扣费</span>
                case 3:
                  return <span class="text-warning">退款</span>
                default:
                  break
              }
            }
          }
        },
        {
          label: '支出保单',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row?.policy?.policy_no) {
                return scoped.row?.policy?.policy_no
              }

              return '-'
            }
          }
        },
        {
          label: '支出用户',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row?.policy?.user?.name) {
                return scoped.row?.policy?.user?.name
              }

              return '-'
            }
          }
        },
        {
          label: '金额',
          prop: 'amount'
        },
        {
          label: '余额',
          prop: 'balance'
        },
        {
          label: '凭据',
          prop: 'proof',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.proof) {
                return (
                  <a href={scoped.row.proof} target="_blank">
                    查看
                  </a>
                )
              }

              return '-'
            }
          }
        },
        {
          label: '备注',
          prop: 'remark'
        },
        {
          label: '时间',
          prop: 'created_at'
        }
      ],
      tableData: [],
      // 分页器 不传递 -> 没有分页
      paging: {
        currentPage: 1,
        pageSize: 0,
        layout: 'prev, pager, next, jumper, total',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page

          this.fetchPlatformTransactions()
        }
      },
      searchQuery: {}
    }
  },
  created() {
    this.fetchPlatformTransactions()
  },
  methods: {
    handleSearchPanel(command, data) {
      this.searchQuery = data
      this.paging.page = 1
      if (command === 'export') {
        this.platformTransactionsExport()
      } else {
        this.fetchPlatformTransactions()
      }
    },
    fetchPlatformTransactions() {
      getPlatformTransactions({
        page: this.paging.page,
        filter: this.searchQuery
      }).then((r) => {
        this.tableData = r.data

        this.paging.currentPage = r.meta.current_page
        this.paging.pageSize = r.meta.per_page
        this.paging.total = r.meta.total
      })
    },
    platformTransactionsExport() {
      const link = exportPlatformTransactions({
        filter: this.searchQuery
      })
      window.open(link, '_blank')
    }
  }
}
</script>
