<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <SearchPanel
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
    ></SearchPanel>
    <el-card shadow="never" class="m-extra-large-t">
      <div class="d-flex justify-content-start">
        <el-button
          type="primary"
          v-can="{ name: 'finance.memo.create' }"
          style="margin-bottom: 15px"
          @click="handleCreateMemo"
          >水单录入</el-button
        >
        <el-button
          type="primary"
          v-can="{ name: 'finance.memo.import' }"
          style="margin-bottom: 15px"
          @click="importDialog.visible = true"
          >批量录入</el-button
        >
      </div>
      <define-table :data="tableData" :cols="cols" :paging="paging" :paging-events="pagingEvents" />
    </el-card>
    <memo-editor
      :visible.sync="memoEditor.visible"
      :model="memoEditor.model"
      @submit="handleEditorSubmit"
    ></memo-editor>
    <el-dialog title="批量导入" width="520px" :visible.sync="importDialog.visible">
      <el-form ref="importForm" :model="importDialog.form" :rules="importRules" label-suffix=":" label-width="120px">
        <el-form-item prop="file" label="批量导入文件">
          <el-link type="primary" :underline="false" @click="downloadImportTemplateExcel()"> (点击下载模板) </el-link>
          <upload-file v-model="importDialog.form.file" />
        </el-form-item>
        <el-form-item>
          <el-button icon="fas fa-redo" @click="importDialog.visible = false">取消</el-button>
          <el-button type="primary" icon="fas fa-check" @click="handleImportMemosSubmit()">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  getMemos,
  createMemo,
  updateMemo,
  deleteMemo,
  getPayees,
  exportMemos,
  importMemos,
  getMemoImportTemplateExcel
} from '@/apis/finance'
import MemoEditor from '@/components/memo/MemoEditor'
import { Loading } from 'element-ui'

export default {
  name: 'Memo',
  components: {
    MemoEditor
  },
  computed: {
    importRules() {
      return {
        file: [{ required: true, message: '请上传批量导入文件', trigger: ['blur', 'change'] }]
      }
    }
  },
  data() {
    return {
      memoEditor: {
        visible: false
      },
      importDialog: {
        visible: false,
        form: {
          file: ''
        }
      },
      searchFields: [
        {
          type: 'select',
          valKey: 'type',
          hintText: '水单类型',
          onChangeReset: 'payee_id',
          options: [
            { label: '支付保司', value: 1 },
            { label: '支付平台', value: 2 }
          ]
        },
        {
          type: 'input',
          valKey: 'payer',
          hintText: '付款人'
        },
        {
          type: 'select',
          valKey: 'payee_id',
          hintText: '收款人',
          options: []
        },
        {
          type: 'select',
          valKey: 'status',
          hintText: '水单状态',
          options: [
            { label: '未使用', value: 1 },
            { label: '已使用', value: 2 },
            { label: '待使用', value: 3 }
          ]
        },
        {
          type: 'input',
          valKey: 'amount_gt',
          hintText: '水单金额大于'
        },
        {
          type: 'input',
          valKey: 'amount_lt',
          hintText: '水单金额小于'
        },
        {
          type: 'daterange',
          valKey: 'paid_at_range',
          hintText: '付款'
        }
      ],
      cols: [
        {
          label: '水单号',
          prop: 'order_no'
        },
        {
          label: '水单金额',
          prop: 'amount'
        },
        {
          label: '录入人',
          prop: 'entry.name'
        },
        {
          label: '付款时间',
          prop: 'paid_at'
        },
        {
          label: '付款人',
          prop: 'payer'
        },
        {
          label: '收款人',
          prop: 'payee.name'
        },
        {
          label: '领用人',
          prop: 'receive.name'
        },
        {
          label: '支付凭证',
          prop: 'proof',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.proof) {
                return (
                  <el-link type="primary" href={scoped.row.proof} target="_blank">
                    查看凭证
                  </el-link>
                )
              }

              return '-'
            }
          }
        },
        {
          label: '水单类型',
          prop: 'type',
          scopedSlots: {
            default: (scoped) => {
              switch (parseInt(scoped.row.type, 10)) {
                case 1:
                  return <span class="text-primary">支付保司</span>
                case 2:
                  return <span class="text-info">支付平台</span>
                default:
                  break
              }
            }
          }
        },
        {
          label: '状态',
          prop: 'status',
          scopedSlots: {
            default: (scoped) => {
              switch (parseInt(scoped.row.status, 10)) {
                case 1:
                  return <span class="text-info">未使用</span>
                case 2:
                  return <span class="text-success">已使用</span>
                case 3:
                  return <span class="text-primary">待使用</span>
                default:
                  break
              }
            }
          }
        },
        {
          label: '操作',
          fixed: 'right',
          width: 150,
          alian: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    size="small"
                    type="text"
                    v-can={{ name: 'finance.memo.update' }}
                    disabled={scoped.row.status !== 1}
                    onClick={() => this.handleEditMemo({ rowData: scoped.row })}
                  >
                    编辑
                  </el-button>
                  <el-button
                    size="small"
                    type="text"
                    v-can={{ name: 'finance.memo.delete' }}
                    disabled={scoped.row.status !== 1}
                    onClick={() => this.deleteMemo(scoped.row.id)}
                  >
                    删除
                  </el-button>
                </div>
              )
            }
          }
        }
      ],
      tableData: [],
      // 分页器 不传递 -> 没有分页
      paging: {
        currentPage: 1,
        pageSize: 0,
        layout: 'prev, pager, next, jumper, total',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page

          this.fetchMemos()
        }
      },
      searchQuery: {},
      searchData: {},
      payees: []
    }
  },
  created() {
    this.fetchMemos()
  },
  methods: {
    handleSearchPanel(command, data) {
      this.searchQuery = data
      this.paging.page = 1
      if (command === 'export') {
        this.memoExport()
      } else {
        this.fetchMemos()
      }
    },
    fetchMemos() {
      getMemos({
        page: this.paging.page,
        filter: this.searchQuery
      }).then((r) => {
        this.tableData = r.data

        this.paging.currentPage = r.meta.current_page
        this.paging.pageSize = r.meta.per_page
        this.paging.total = r.meta.total
      })
    },
    handleCreateMemo() {
      this.$nextTick(() => {
        this.memoEditor.visible = true
        this.memoEditor.model = {}
      })
    },
    handleEditMemo({ rowData }) {
      this.$nextTick(() => {
        this.memoEditor.visible = true
        this.memoEditor.model = rowData
      })
    },
    handleEditorSubmit(data) {
      const loading = Loading.service({ lock: true })

      const action = data.id !== undefined ? updateMemo(data.id, data) : createMemo(data)
      // const action = createMemo(data)
      action
        .then(() => {
          this.$message.success('操作成功')

          this.fetchMemos()
        })
        .finally(() => loading.close())
    },
    deleteMemo(id) {
      const loading = Loading.service({ lock: true })
      deleteMemo(id)
        .then(() => {
          this.$message.success('操作成功')

          this.fetchMemos()
        })
        .finally(() => loading.close())
    },
    memoExport() {
      const link = exportMemos({ filter: this.searchQuery })
      window.open(link, '_blank')
    },
    handleImportMemosSubmit() {
      this.$refs.importForm.validate((valid) => {
        if (valid) {
          importMemos(this.importDialog.form).then(() => {
            this.$message.success('上传成功')
            this.importDialog.visible = false

            this.fetchMemos()
            this.$refs.importForm.resetFields()
          })
        }
      })
    },
    downloadImportTemplateExcel() {
      const link = getMemoImportTemplateExcel()
      window.open(link, '_blank')
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    }
  },
  watch: {
    'searchData.type'(type) {
      if (type) {
        getPayees(type).then((r) => {
          this.assignSelectOptions('payee_id', r.data)
        })
      }
    }
  }
}
</script>
