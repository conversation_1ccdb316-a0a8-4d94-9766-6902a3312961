<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <SearchPanel
      size="small"
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
    ></SearchPanel>
    <el-card shadow="never" class="box-card-options m-extra-large-t" :body-style="{ padding: '20px' }">
      <DefineTable :cols="cols" :data="tableData" :paging="pagingAttrs" :pagingEvents="pagingEvents"></DefineTable>
    </el-card>
    <el-dialog title="提示" :visible.sync="draftDialog.visible" width="30%">
      <el-form
        ref="draftForm"
        label-position="left"
        label-width="100px"
        :model="draftDialog.form"
        :rules="draftDialog.rules"
      >
        <el-form-item label="支付总保费" prop="actual_premium">
          <el-input type="number" v-model="draftDialog.form.actual_premium" placeholder="请输入支付总保费"></el-input>
        </el-form-item>
        <!-- <el-form-item label="支付凭证" prop="proof">
          <upload-file v-model="draftDialog.form.proof"></upload-file>
          <el-link
            icon="el-icon-view"
            v-if="draftDialog.form.proof && typeof draftDialog.form.proof === 'string'"
            :href="draftDialog.form.proof"
          >
            点击查看
          </el-link>
        </el-form-item> -->
        <el-form-item label="备注">
          <el-input type="textarea" placeholder="请输入备注" :rows="4" v-model="draftDialog.form.remark"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="draftDialog.visible = false">取 消</el-button>
        <el-button type="primary" @click="draftPayment">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  premiumPaymentBills,
  exportPremiumPaymentBills,
  sendBackPremiumPayment,
  premiumPaymentDraftBills
} from '@/apis/finance'
import { tokenKey } from '@/config'
import { Loading } from 'element-ui'
import { mapGetters } from 'vuex'

export default {
  name: 'PrmeiumPaymentBill',
  data() {
    return {
      draftDialog: {
        visible: false,
        form: {
          id: '',
          actual_premium: '',
          // proof: '',
          remark: ''
        },
        rules: {
          actual_premium: [{ required: true, message: '请输入实际支付保费', trigger: 'blur' }]
          // proof: [{ required: true, message: '请上传支付凭证', trigger: 'blur' }]
        }
      },
      searchFields: [
        {
          type: 'textarea',
          valKey: 'policy_no',
          hintText: '保单号'
        }
      ],
      types: [
        { value: 1, label: '国内货运险' },
        { value: 2, label: '国际货运险' },
        { value: 3, label: '单车责任险' },
        { value: 4, label: '其他险种' },
        { value: 5, label: '雇主责任险' }
      ],
      searchData: {},
      cols: [
        { label: '流水号', prop: 'order_no' },
        {
          label: '单据数',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    type="primary"
                    onClick={() => {
                      this.$router.push({
                        name: 'DraftPremiumPaymentBillPayments',
                        params: {
                          id: scoped.row.id
                        }
                      })
                    }}
                  >
                    {scoped.row.payment_num}
                  </el-link>
                </div>
              )
            }
          }
        },
        // { label: '保单数', prop: 'policy_num' },
        { label: '保险公司保费', prop: 'premium' },
        { label: '实际支付', prop: 'actual_premium' },
        { label: '备注', prop: 'remark' },
        // JSX 插槽
        // {
        //   label: '支付凭证',
        //   align: 'center',
        //   scopedSlots: {
        //     default: (scoped) => {
        //       if (scoped.row.proof) {
        //         return (
        //           <div>
        //             <el-link
        //               type="primary"
        //               href={`${scoped.row.proof}?token=` + window.localStorage.getItem(tokenKey)}
        //               download=""
        //               target="_blank"
        //             >
        //               点击查看
        //             </el-link>
        //           </div>
        //         )
        //       } else {
        //         return ''
        //       }
        //     }
        //   }
        // },
        {
          label: '操作',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    style="margin-right: 10px"
                    type="primary"
                    plain
                    size="small"
                    v-can={{ name: 'finance.premium-payment.bills.create' }}
                    onClick={() => this.handleDraft(scoped.row)}
                    disabled={scoped.row.apply.id !== this.admin.id}
                  >
                    提交审核
                  </el-link>
                  <el-link
                    style="margin-right: 10px"
                    type="primary"
                    plain
                    size="small"
                    v-can={{ name: 'finance.premium-payment.bills.send-back' }}
                    onClick={() => this.sendBackPayment(scoped.row.id)}
                    disabled={scoped.row.apply.id !== this.admin.id}
                  >
                    退回
                  </el-link>
                </div>
              )
            }
          }
        }
      ],
      tableData: [],
      pagingAttrs: {
        align: 'center',
        currentPage: 1,
        pageSize: 15,
        layout: 'total, prev, pager, next, jumper',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.pagingAttrs.currentPage = page

          this.getPremiumPaymentBills()
        }
      }
    }
  },
  computed: {
    ...mapGetters('auth', ['admin'])
  },
  mounted() {
    this.getPremiumPaymentBills()
  },
  methods: {
    handleSearchPanel(command, data) {
      this.searchQuery = data
      this.pagingAttrs.currentPage = 1
      if (command === 'export') {
        this.exportData()
      } else {
        this.getPremiumPaymentBills()
      }
    },
    getPremiumPaymentBills() {
      const loading = Loading.service()
      premiumPaymentBills({
        page: this.pagingAttrs.currentPage,
        filter: Object.assign({ status: 0 }, this.searchQuery)
      })
        .then((r) => {
          this.tableData = r.data

          this.pagingAttrs.currentPage = r.meta.current_page
          this.pagingAttrs.pageSize = r.meta.per_page
          this.pagingAttrs.total = r.meta.total

          loading.close()
        })
        .finally(() => loading.close())
    },
    handleDraft(row) {
      this.draftDialog.visible = true
      this.draftDialog.form.id = row.id
      this.draftDialog.form.actual_premium = row.actual_premium
      // this.draftDialog.form.proof = row.proof
      this.draftDialog.form.remark = row.remark
    },
    draftPayment() {
      this.$refs?.draftForm?.validate((valid) => {
        if (valid) {
          premiumPaymentDraftBills(this.draftDialog.form.id, Object.assign({}, this.draftDialog.form)).then(() => {
            this.draftDialog.visible = false
            this.draftDialog.form = {
              id: '',
              actual_premium: '',
              // proof: '',
              remark: ''
            }
            this.getPremiumPaymentBills()
            this.$message({
              type: 'success',
              message: '提交成功'
            })
          })
        }
      })
    },
    sendBackPayment(id) {
      this.$confirm('是否确定退回?', '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        type: 'warning'
      }).then(() => {
        sendBackPremiumPayment(id).then(() => {
          this.$message.success('退回成功')
          this.getPremiumPaymentBills()
        })
      })
    },
    exportData() {
      const _obj = Object.assign({ status: 0 }, this.searchData)
      if (_obj.time) {
        _obj.starts_at = _obj?.time?.[0]
        _obj.ends_at = _obj?.time?.[1]
      }
      delete _obj.time
      const link = exportPremiumPaymentBills({ filter: _obj })
      window.open(link, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.invoice {
  padding: 0 20px;
}
.a_style {
  color: #fff;
  text-decoration: none;
}
</style>
