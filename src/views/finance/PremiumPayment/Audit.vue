<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <SearchPanel
      size="small"
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
    ></SearchPanel>
    <el-card shadow="never" class="box-card-options m-extra-large-t" :body-style="{ padding: '20px' }">
      <DefineTable :cols="cols" :data="tableData" :paging="pagingAttrs" :pagingEvents="pagingEvents"></DefineTable>
    </el-card>
  </div>
</template>

<script>
import {
  premiumPaymentBills,
  exportPremiumPaymentBills,
  associatePremiumPaymentAuditor,
  exportPremiumPayments,
  handlePremiumPayment,
  sendBackPremiumPayment
} from '@/apis/finance'
import { tokenKey } from '@/config'
import { Loading } from 'element-ui'
import { mapGetters } from 'vuex'

export default {
  name: 'PrmeiumPaymentBill',
  data() {
    return {
      searchFields: [
        {
          type: 'textarea',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'input',
          valKey: 'operator',
          hintText: '审核员'
        }
      ],
      types: [
        { value: 1, label: '国内货运险' },
        { value: 2, label: '国际货运险' },
        { value: 3, label: '单车责任险' },
        { value: 4, label: '其他险种' },
        { value: 5, label: '雇主责任险' }
      ],
      searchData: {},
      cols: [
        { label: '流水号', prop: 'order_no' },
        {
          label: '单据数',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    type="primary"
                    onClick={() => {
                      this.$router.push({
                        name: 'AuditPremiumPaymentBillPayments',
                        params: {
                          id: scoped.row.id
                        }
                      })
                    }}
                  >
                    {scoped.row.payment_num}
                  </el-link>
                </div>
              )
            }
          }
        },
        // { label: '保单数', prop: 'policy_num' },
        { label: '保险公司保费', prop: 'premium' },
        { label: '实际支付', prop: 'actual_premium' },
        { label: '审核员', prop: 'operation.name' },
        {
          label: '状态',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.operation.id != -1) {
                return '审核中'
              }

              return '已提交'
            }
          }
        },
        // JSX 插槽
        // {
        //   label: '支付凭证',
        //   align: 'center',
        //   scopedSlots: {
        //     default: (scoped) => {
        //       if (scoped.row.proof) {
        //         return (
        //           <div>
        //             <el-link
        //               type="primary"
        //               href={`${scoped.row.proof}?token=` + window.localStorage.getItem(tokenKey)}
        //               download=""
        //               target="_blank"
        //             >
        //               点击查看
        //             </el-link>
        //           </div>
        //         )
        //       } else {
        //         return ''
        //       }
        //     }
        //   }
        // },
        { label: '备注', prop: 'remark' },
        {
          label: '操作',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.operation.id == -1) {
                return (
                  <div>
                    <el-link
                      style="margin-right: 10px"
                      type="primary"
                      plain
                      size="small"
                      v-can={{ name: 'finance.premium-payment.bills.auditor' }}
                      onClick={() => this.associateAuditor(scoped.row.id)}
                    >
                      领取
                    </el-link>
                    <el-link
                      type="primary"
                      plain
                      size="small"
                      v-can={{ name: 'finance.premium-payment.bills.export' }}
                      href={exportPremiumPayments({ bill_id: scoped.row.id })}
                      download=""
                      target="_blank"
                    >
                      导出清单
                    </el-link>
                  </div>
                )
              } else {
                return (
                  <div>
                    <el-link
                      style="margin-right: 10px"
                      type="primary"
                      plain
                      size="small"
                      v-can={{ name: 'finance.premium-payment.bills.handle' }}
                      onClick={() => this.handlePayment(scoped.row.id)}
                      disabled={scoped.row.operation.id !== this.admin.id}
                    >
                      确认支付
                    </el-link>
                    <el-link
                      style="margin-right: 10px"
                      type="primary"
                      plain
                      size="small"
                      v-can={{ name: 'finance.premium-payment.bills.send-back' }}
                      onClick={() => this.sendBackPayment(scoped.row.id)}
                      disabled={scoped.row.operation.id !== this.admin.id}
                    >
                      退回
                    </el-link>
                  </div>
                )
              }
            }
          }
        }
      ],
      tableData: [],
      pagingAttrs: {
        align: 'center',
        currentPage: 1,
        pageSize: 15,
        layout: 'total, prev, pager, next, jumper',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.pagingAttrs.currentPage = page

          this.getPremiumPaymentBills()
        }
      }
    }
  },
  computed: {
    ...mapGetters('auth', ['admin'])
  },
  mounted() {
    this.getPremiumPaymentBills()
  },
  methods: {
    handleSearchPanel(command, data) {
      this.searchQuery = data
      this.pagingAttrs.currentPage = 1
      if (command === 'export') {
        this.exportData()
      } else {
        this.getPremiumPaymentBills()
      }
    },
    getPremiumPaymentBills() {
      const loading = Loading.service()
      premiumPaymentBills({
        page: this.pagingAttrs.currentPage,
        filter: Object.assign({ status: 1 }, this.searchQuery)
      })
        .then((r) => {
          this.tableData = r.data

          this.pagingAttrs.currentPage = r.meta.current_page
          this.pagingAttrs.pageSize = r.meta.per_page
          this.pagingAttrs.total = r.meta.total

          loading.close()
        })
        .finally(() => loading.close())
    },
    associateAuditor(id) {
      this.$confirm('是否确定领取?', '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        type: 'warning'
      }).then(() => {
        associatePremiumPaymentAuditor(id).then(() => {
          this.$message.success('领取成功')
          this.getPremiumPaymentBills()
        })
      })
    },
    handlePayment(id) {
      this.$confirm('是否确定完成支付?', '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        type: 'warning'
      }).then(() => {
        handlePremiumPayment(id).then(() => {
          this.$message.success('操作成功')
          this.getPremiumPaymentBills()
        })
      })
    },
    sendBackPayment(id) {
      this.$confirm('是否确定退回?', '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        type: 'warning'
      }).then(() => {
        sendBackPremiumPayment(id).then(() => {
          this.$message.success('退回成功')
          this.getPremiumPaymentBills()
        })
      })
    },
    exportData() {
      const _obj = Object.assign({ status: 1 }, this.searchData)
      if (_obj.time) {
        _obj.starts_at = _obj?.time?.[0]
        _obj.ends_at = _obj?.time?.[1]
      }
      delete _obj.time
      const link = exportPremiumPaymentBills({ filter: _obj })
      window.open(link, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.invoice {
  padding: 0 20px;
}
.a_style {
  color: #fff;
  text-decoration: none;
}
</style>
