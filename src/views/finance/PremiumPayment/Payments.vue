<!--
 * @Descripttion:
 * @version:
 * @Author: yanb
 * @Date: 2021-06-10 15:58:26
 * @LastEditors: yanb
 * @LastEditTime: 2024-06-19 15:17:59
-->
<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <el-card shadow="never" class="box-card-options" :body-style="{ padding: '20px' }">
      <div>
        <el-button type="primary" v-can="{ name: 'finance.premium-payment.bills.export' }" @click="CDownloadFile"
          >导出列表
        </el-button>
      </div>
      <DefineTable :cols="cols" :data="tableData" :paging="pagingAttrs" :pagingEvents="pagingEvents"></DefineTable>
    </el-card>
  </div>
</template>

<script>
import { premiumBillsPayments, exportPremiumPayments } from '@/apis/finance'

export default {
  name: 'invoice',
  data() {
    return {
      tableData: [],
      pagingAttrs: {
        align: 'center',
        currentPage: 1,
        pageSize: 15,
        layout: 'total, prev, pager, next, jumper',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.pagingAttrs.currentPage = page

          this.getListData()
        }
      }
    }
  },
  computed: {
    cols() {
      const cols = [
        { align: 'center', type: 'selection', width: '80' },
        { label: '保单号', prop: 'policy.policy_no', width: '200', fixed: 'left' },
        { label: '投保用户', prop: 'user.name' },
        {
          label: '险种',
          scopedSlots: {
            default: (scoped) => {
              const types = {
                1: '国内货运险',
                2: '国际货运险',
                3: '单车责任险',
                4: '其他险种',
                5: '雇主责任险',
                6: '线下录入保单',
                7: '跨境电商险'
              }

              return scoped.row.policy.type === 6 ? scoped.row.policy.offline_type : types[scoped.row.policy.type]
            }
          }
        },
        { label: '应付保费', prop: 'premium' },
        // { label: '业务来源', prop: 'business_source.name' },
        { label: '出单公司', prop: 'company_branch.name' },
        // { label: '来源', prop: 'f' },
        { label: '生效时间', prop: 'issued_at' }
      ]

      return cols.filter((e) => e.isHide === undefined || e.isHide === false)
    }
  },
  mounted() {
    this.getListData()
  },
  methods: {
    getListData() {
      premiumBillsPayments(this.$route.params.id, {
        page: this.pagingAttrs.currentPage
      }).then((r) => {
        this.tableData = r.data
        this.pagingAttrs.currentPage = r.meta.current_page
        this.pagingAttrs.total = r.meta.total
      })
    },
    CDownloadFile() {
      const link = exportPremiumPayments({ bill_id: this.$route.params.id })
      window.open(link, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.invoice {
  padding: 0 20px;
}
.a_style {
  color: #fff;
  text-decoration: none;
}
</style>
