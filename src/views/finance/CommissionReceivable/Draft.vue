<template>
  <div class="w-100 invoice">
    <SearchPanel
      size="small"
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
    ></SearchPanel>
    <el-card shadow="never" class="box-card-options m-extra-large-t" :body-style="{ padding: '20px' }">
      <DefineTable :cols="cols" :data="tableData" :paging="pagingAttrs" :pagingEvents="pagingEvents"></DefineTable>
    </el-card>
    <el-dialog title="提示" :visible.sync="draftDialog.visible" width="30%">
      <el-form
        ref="draftForm"
        label-position="left"
        label-width="100px"
        :model="draftDialog.form"
        :rules="draftDialog.rules"
      >
        <el-form-item label="收取金额" prop="actual_commission">
          <el-input type="number" v-model="draftDialog.form.actual_commission" placeholder="请输入收取金额"></el-input>
        </el-form-item>
        <el-form-item label="支付凭证" prop="proof">
          <upload-file v-model="draftDialog.form.proof"></upload-file>
          <el-link
            icon="el-icon-view"
            v-if="draftDialog.form.proof && typeof draftDialog.form.proof === 'string'"
            :href="draftDialog.form.proof"
          >
            点击查看
          </el-link>
        </el-form-item>
        <el-form-item label="备注">
          <el-input type="textarea" placeholder="请输入备注" :rows="4" v-model="draftDialog.form.remark"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="draftDialog.visible = false">取 消</el-button>
        <el-button type="primary" @click="draftPayment">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  commissionReceivableBills,
  exportCommissionReceivableBills,
  exportCommissionReceivables,
  sendBackDraftCommissionReceivableBills,
  commissionReceivableDraftBills
} from '@/apis/finance'
import { tokenKey } from '@/config'
import { Loading } from 'element-ui'

export default {
  name: 'CommissionReceivableBills',
  data() {
    return {
      draftDialog: {
        visible: false,
        form: {
          id: '',
          actual_commission: '',
          proof: '',
          remark: ''
        },
        rules: {
          actual_commission: [{ required: true, message: '请输入收取金额', trigger: 'blur' }],
          proof: [{ required: true, message: '请上传支付凭证', trigger: 'blur' }]
        }
      },
      searchFields: [
        {
          type: 'textarea',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'input',
          valKey: 'operator',
          hintText: '处理人'
        },
        {
          type: 'daterange',
          hintText: '收取',
          valKey: 'charge_at_range'
        }
      ],
      cols: [
        // { label: '流水号', prop: 'order_no' },
        { label: '应收平台', prop: 'receivable_platform.name' },
        {
          label: '单据数',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    type="primary"
                    onClick={() => {
                      this.$router.push({
                        name: 'DraftCommissionBillReceivables',
                        params: {
                          id: scoped.row.id
                        }
                      })
                    }}
                  >
                    {scoped.row.payment_num}
                  </el-link>
                </div>
              )
            }
          }
        },
        // { label: '保单数', prop: 'policy_num' },
        { label: '客户保费总数', prop: 'premium' },
        { label: '佣金应收', prop: 'commission' },
        { label: '佣金实收', prop: 'actual_commission' },
        { label: '备注', prop: 'remark' },
        // JSX 插槽
        {
          label: '支付凭证',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.proof) {
                return (
                  <div>
                    <el-link
                      type="primary"
                      href={`${scoped.row.proof}?token=` + window.localStorage.getItem(tokenKey)}
                      download=""
                      target="_blank"
                    >
                      点击查看
                    </el-link>
                  </div>
                )
              } else {
                return '-'
              }
            }
          }
        },
        {
          label: '操作',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    style="margin-right: 10px"
                    type="primary"
                    plain
                    size="small"
                    v-can={{ name: 'finance.commission-receivable.bills.create' }}
                    onClick={() => this.handleDraft(scoped.row)}
                    disabled={scoped.row.status === 1}
                  >
                    确认提交
                  </el-link>
                  <el-link
                    style="margin-right: 10px"
                    type="primary"
                    plain
                    size="small"
                    v-can={{ name: 'finance.commission-receivable.bills.send-back' }}
                    onClick={() => this.sendBackReceivable(scoped.row.id)}
                  >
                    退回
                  </el-link>
                </div>
              )
            }
          }
        }
      ],
      tableData: [],
      pagingAttrs: {
        align: 'center',
        currentPage: 1,
        pageSize: 15,
        layout: 'total, prev, pager, next, jumper',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.pagingAttrs.currentPage = page

          this.fetchCommissionReceivableBills()
        }
      }
    }
  },
  mounted() {
    this.fetchCommissionReceivableBills()
  },
  methods: {
    handleSearchPanel(command, data) {
      this.searchQuery = data
      this.pagingAttrs.currentPage = 1
      if (command === 'export') {
        this.exportBills()
      } else {
        this.fetchCommissionReceivableBills()
      }
    },
    fetchCommissionReceivableBills() {
      const loading = Loading.service()
      commissionReceivableBills({
        page: this.pagingAttrs.currentPage,
        filter: Object.assign({ status: 0 }, this.searchQuery)
      })
        .then((r) => {
          console.log(r)
          this.tableData = r.data

          this.pagingAttrs.currentPage = r.meta.current_page
          this.pagingAttrs.pageSize = r.meta.per_page
          this.pagingAttrs.total = r.meta.total

          loading.close()
        })
        .finally(() => loading.close())
    },
    exportBills() {
      const link = exportCommissionReceivableBills({
        filter: Object.assign({ status: 0 }, this.searchQuery)
      })
      window.open(link, '_blank')
    },
    handleDraft(row) {
      this.draftDialog.visible = true
      this.draftDialog.form.id = row.id
      this.draftDialog.form.actual_commission = row.actual_commission
      this.draftDialog.form.proof = row.proof
      this.draftDialog.form.remark = row.remark
    },
    draftPayment() {
      this.$refs?.draftForm?.validate((valid) => {
        if (valid) {
          commissionReceivableDraftBills(this.draftDialog.form.id, Object.assign({}, this.draftDialog.form)).then(
            () => {
              this.draftDialog.visible = false
              this.draftDialog.form = {
                id: '',
                actual_commission: '',
                proof: '',
                remark: ''
              }
              this.fetchCommissionReceivableBills()
              this.$message({
                type: 'success',
                message: '处理完成'
              })
            }
          )
        }
      })
    },
    sendBackReceivable(id) {
      this.$confirm('是否确定退回?', '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        type: 'warning'
      }).then(() => {
        sendBackDraftCommissionReceivableBills(id).then(() => {
          this.$message.success('操作成功')
          this.fetchCommissionReceivableBills()
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.invoice {
  padding: 0 20px;
}
.a_style {
  color: #fff;
  text-decoration: none;
}
</style>
