<template>
  <div class="w-100 invoice">
    <SearchPanel
      size="small"
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
    ></SearchPanel>
    <el-card shadow="never" class="box-card-options m-extra-large-t" :body-style="{ padding: '20px' }">
      <DefineTable :cols="cols" :data="tableData" :paging="pagingAttrs" :pagingEvents="pagingEvents"></DefineTable>
    </el-card>
  </div>
</template>

<script>
import { commissionReceivableBills, exportCommissionReceivableBills, exportCommissionReceivables } from '@/apis/finance'
import { tokenKey } from '@/config'
import { Loading } from 'element-ui'

export default {
  name: 'CommissionReceivableBills',
  data() {
    return {
      searchFields: [
        {
          type: 'textarea',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'input',
          valKey: 'operator',
          hintText: '处理人'
        },
        {
          type: 'daterange',
          hintText: '收取',
          valKey: 'charge_at_range'
        }
      ],
      cols: [
        // { label: '流水号', prop: 'order_no' },
        { label: '应收平台', prop: 'receivable_platform.name' },
        {
          label: '单据数',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    type="primary"
                    onClick={() => {
                      this.$router.push({
                        name: 'CommissionBillReceivables',
                        params: {
                          id: scoped.row.id
                        }
                      })
                    }}
                  >
                    {scoped.row.payment_num}
                  </el-link>
                </div>
              )
            }
          }
        },
        // { label: '保单数', prop: 'policy_num' },
        { label: '客户保费总数', prop: 'premium' },
        { label: '佣金应收', prop: 'commission' },
        { label: '佣金实收', prop: 'actual_commission' },
        { label: '处理人', prop: 'operation.name' },
        {
          label: '收取时间',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.status !== 2) {
                return '-'
              }
              return scoped.row.charge_at
            }
          }
        },
        { label: '备注', prop: 'remark' },
        {
          label: '状态',
          scopedSlots: {
            default: (scoped) => {
              const types = {
                0: '未提交',
                1: '已提交',
                2: '已完成'
              }

              return types[scoped.row.status]
            }
          }
        },
        // JSX 插槽
        {
          label: '支付凭证',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.proof) {
                return (
                  <div>
                    <el-link
                      type="primary"
                      href={`${scoped.row.proof}?token=` + window.localStorage.getItem(tokenKey)}
                      download=""
                      target="_blank"
                    >
                      点击查看
                    </el-link>
                  </div>
                )
              } else {
                return '-'
              }
            }
          }
        },
        {
          label: '操作',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    type="primary"
                    v-can={{ name: 'finance.commission-receivable.bills.export' }}
                    href={exportCommissionReceivables({ bill_id: scoped.row.id })}
                    download=""
                    target="_blank"
                  >
                    导出清单
                  </el-link>
                </div>
              )
            }
          }
        }
      ],
      tableData: [],
      pagingAttrs: {
        align: 'center',
        currentPage: 1,
        pageSize: 15,
        layout: 'total, prev, pager, next, jumper',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.pagingAttrs.currentPage = page

          this.fetchCommissionReceivableBills()
        }
      }
    }
  },
  mounted() {
    this.fetchCommissionReceivableBills()
  },
  methods: {
    handleSearchPanel(command, data) {
      this.searchQuery = data
      this.pagingAttrs.currentPage = 1
      if (command === 'export') {
        this.exportBills()
      } else {
        this.fetchCommissionReceivableBills()
      }
    },
    fetchCommissionReceivableBills() {
      const loading = Loading.service()
      commissionReceivableBills({
        page: this.pagingAttrs.currentPage,
        filter: this.searchQuery
      })
        .then((r) => {
          console.log(r)
          this.tableData = r.data

          this.pagingAttrs.currentPage = r.meta.current_page
          this.pagingAttrs.pageSize = r.meta.per_page
          this.pagingAttrs.total = r.meta.total

          loading.close()
        })
        .finally(() => loading.close())
    },
    exportBills() {
      const link = exportCommissionReceivableBills({
        filter: this.searchQuery
      })
      window.open(link, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.invoice {
  padding: 0 20px;
}
.a_style {
  color: #fff;
  text-decoration: none;
}
</style>
