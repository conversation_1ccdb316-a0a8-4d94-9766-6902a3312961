<template>
  <div class="w-100 feePayment">
    <el-card shadow="never" class="box-card-options" :body-style="{ padding: '20px' }">
      <div class="d-flex justify-content-between">
        <h3>
          已选单据：{{ pagingAttrs.total }}单 客户总保费： ￥{{ Number(totalPremium).toFixed(2) + '元' }} 应收佣金：
          ￥{{ Number(totalCommission).toFixed(2) + '元' }}
        </h3>
        <div>
          <el-button
            type="primary"
            v-can="{ name: 'finance.commission-receivable.bills.create' }"
            @click="dialogVisible = true"
            >确认收取</el-button
          >
        </div>
      </div>
      <div style="width: 30%; display: flex; align-items: center">
        <el-input v-model="search" size="mini" placeholder="保单号" clearable />
        <el-button
          style="margin-left: 10px"
          type="primary"
          v-can="{ name: 'finance.commission-receivable.bills.create' }"
          @click="handleSearch"
          >搜索
        </el-button>
      </div>
      <DefineTable :cols="cols" :data="data" :paging="pagingAttrs" :pagingEvents="pagingEvents"></DefineTable>
    </el-card>

    <el-dialog title="提示" :visible.sync="dialogVisible" width="30%" :before-close="handleClose">
      <el-form ref="commissionForm" label-position="left" label-width="100px" :model="form" :rules="rules">
        <el-form-item label="实收金额" prop="actual_commission">
          <el-input type="number" v-model="form.actual_commission" placeholder="请输入实际收取金额"></el-input>
        </el-form-item>
        <el-form-item label="支付凭证" prop="proof">
          <upload-file v-model="form.proof"></upload-file>
        </el-form-item>
        <el-form-item label="备注">
          <el-input type="textarea" placeholder="请输入备注" :rows="4" v-model="form.remark"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" v-can="{ name: 'finance.commission-receivable.bills.draft' }" @click="draft"
          >暂 存</el-button
        >
        <el-button type="primary" v-can="{ name: 'finance.commission-receivable.bills.create' }" @click="handle"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getCommissionReceivables,
  getCommissionReceivableCount,
  handleCommissionReceivable,
  draftCommissionReceivable
} from '@/apis/finance.js'
import { Loading } from 'element-ui'

export default {
  name: 'HandleCommissionReceivable',
  data() {
    return {
      form: {
        actual_commission: '',
        proof: '',
        remark: ''
      },
      search: '',
      totalPremium: 0,
      totalCommission: 0,
      removeIds: [],
      handleIds: [],
      dialogVisible: false,
      cols: [
        { label: '保单号', prop: 'policy.policy_no', width: '200', fixed: 'left' },
        { label: '应收平台', prop: 'receivable_platform.name' },
        {
          prop: 'type',
          label: '险种',
          scopedSlots: {
            default: (scoped) => {
              const types = {
                1: '国内货运险',
                2: '国际货运险',
                3: '单车责任险',
                4: '其他险种',
                5: '雇主责任险',
                6: '线下录入保单',
                7: '跨境电商险'
              }

              return scoped.row.policy.type === 6 ? scoped.row.policy.offline_type : types[scoped.row.policy.type]
            }
          }
        },
        { prop: 'premium', label: '客户保费' },
        { prop: 'commission', label: '应收佣金' },
        { prop: 'issued_at', label: '生效时间' },
        // JSX 插槽
        {
          label: '操作',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    type="primary"
                    onClick={() => {
                      this.remove(scoped.row)
                    }}
                  >
                    移除
                  </el-link>
                </div>
              )
            }
          }
        }
      ],
      data: [],
      searchQuery: {},
      pagingAttrs: {
        currentPage: 1,
        pageSize: 15,
        layout: 'total, prev, pager, next, jumper',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.pagingAttrs.currentPage = page

          this.fetchCommissionReceivables()
        }
      },
      rules: {
        actual_commission: [{ required: true, message: '请输入实际收取金额', trigger: 'blur' }],
        proof: [{ required: true, message: '请上传支付凭证', trigger: 'blur' }]
      }
    }
  },
  mounted() {
    this.searchQuery = { ...this.$route?.query }
    delete this.searchQuery.all_checkout
    delete this.searchQuery.id

    this.fetchData()
  },
  methods: {
    handleSearch() {
      this.searchQuery = { ...this.searchQuery, policy_no: this.search }
      this.fetchData()
    },
    fetchData() {
      this.fetchCommissionReceivables()
      this.fetchCommissionReceivablesCount()
    },
    fetchCommissionReceivables() {
      const loading = Loading.service()
      getCommissionReceivables({
        page: this.pagingAttrs.currentPage,
        ids: this.$route?.query?.id?.split(','),
        all_checkout: this.$route?.query?.all_checkout,
        filter: this.searchQuery
      })
        .then((r) => {
          this.data = r.data

          this.pagingAttrs.currentPage = r.meta.current_page
          this.pagingAttrs.pageSize = r.meta.per_page
          this.pagingAttrs.total = r.meta.total
          loading.close()
        })
        .finally(() => loading.close())
    },
    fetchCommissionReceivablesCount() {
      getCommissionReceivableCount({
        ids: this.$route?.query?.id?.split(','),
        all_checkout: this.$route?.query?.all_checkout,
        filter: this.searchQuery
      }).then((r) => {
        this.totalPremium = r.data.premium
        this.totalCommission = r.data.commission
        this.handleIds = r.data.ids
      })
    },
    remove(row) {
      this.removeIds.push(row.id)
      this.search = ''
      this.searchQuery = { ...this.searchQuery, policy_no: '' }
      this.searchQuery = { ...this.searchQuery, without: this.removeIds }
      this.fetchData()
    },
    handle() {
      this.$refs?.commissionForm?.validate((valid) => {
        if (valid) {
          const loading = Loading.service()
          handleCommissionReceivable(Object.assign({}, this.marshalData(this.form)))
            .then(() => {
              this.dialogVisible = false
              this.$router.push({
                name: 'CommissionReceivable'
              })
              this.$message({
                type: 'success',
                message: '结算成功'
              })
              loading.close()
            })
            .finally(() => loading.close())
        }
      })
    },
    draft() {
      const loading = Loading.service()
      draftCommissionReceivable(Object.assign({}, this.marshalData(this.form)))
        .then(() => {
          this.dialogVisible = false
          this.$router.push({
            name: 'CommissionReceivable'
          })
          this.$message({
            type: 'success',
            message: '暂存成功'
          })
          loading.close()
        })
        .finally(() => loading.close())
    },
    handleClose() {
      this.dialogVisible = false
    },
    marshalData(data) {
      data.premium = Number(this.totalPremium).toFixed(2)
      data.commission = Number(this.totalCommission).toFixed(2)
      data.ids = this.handleIds.filter((item) => !this.removeIds.includes(item)).join(',')
      return data
    }
  }
}
</script>

<style scoped>
.feePayment {
  padding: 0 20px;
}
</style>
