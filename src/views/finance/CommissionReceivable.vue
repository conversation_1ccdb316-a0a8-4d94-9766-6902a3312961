<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <SearchPanel
      size="small"
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
    ></SearchPanel>
    <el-alert type="error" class="m-extra-large-t" :closable="false">
      <span>
        当前保费
        <el-tag type="danger" effect="dark"> {{ countReceivable.premium }} </el-tag>，
        <span class="hover-cursor">应收佣金: </span>
        <el-tag type="danger" effect="dark"> {{ countReceivable.commission }} </el-tag>
      </span>
    </el-alert>
    <el-card shadow="never" class="box-card-options m-extra-large-t">
      <div class="d-flex">
        <el-button
          type="primary"
          v-can="{ name: 'finance.commission-receivable.bills.create' }"
          @click="handleReceivable"
          style="margin-bottom: 15px"
          >佣金收取</el-button
        >
        <el-button
          type="primary"
          v-can="{ name: 'finance.commission-receivable.bills.create' }"
          @click="allCheckout"
          style="margin-bottom: 15px"
          >全部处理</el-button
        >
        <el-button
          type="primary"
          v-can="{ name: 'finance.commission-receivable.bills.draft' }"
          @click="draftBills"
          style="margin-bottom: 15px"
          >暂存记录</el-button
        >
        <el-button
          type="primary"
          v-can="{ name: 'finance.commission-receivable.bills.index' }"
          @click="receivableBills"
          style="margin-bottom: 15px"
          >收取记录</el-button
        >
      </div>
      <DefineTable
        :cols="cols"
        :data="tableData"
        :paging="pagingAttrs"
        :pagingEvents="pagingEvents"
        :events="tableEvents"
        :rowKey="getRowKeys"
      ></DefineTable>
    </el-card>
  </div>
</template>

<script>
import { getCommissionReceivables, getCommissionReceivableCount, exportCommissionReceivables } from '@/apis/finance'
import { getPlatformProductCompanies } from '@/apis/platform_product'
import { getPlatformsDict } from '@/apis/platform'
import { mapGetters } from 'vuex'

export default {
  name: 'CommissionReceivable',
  data() {
    return {
      searchFields: [
        {
          type: 'textarea',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'select',
          valKey: 'type',
          hintText: '险种',
          options: [
            {
              label: '国内货运险',
              value: 1
            },
            {
              label: '国际货运险',
              value: 2
            },
            {
              label: '单车责任险',
              value: 3
            },
            {
              label: '其他险种',
              value: 4
            },
            {
              label: '雇主责任险',
              value: 5
            }
          ]
        },
        {
          type: 'select',
          valKey: 'company_id',
          hintText: '保险公司',
          onChangeReset: 'company_branch_id',
          options: []
        },
        {
          type: 'select',
          valKey: 'company_branch_id',
          hintText: '出单公司',
          options: []
        },
        {
          type: 'daterange',
          valKey: 'issued_at_range'
        },
        {
          type: 'input',
          valKey: 'receivable_name',
          hintText: '应收平台'
        }
        // {
        //   type: 'select',
        //   valKey: 'receivable_platform',
        //   hintText: '应收平台',
        //   options: []
        // }
      ],
      cols: [
        { align: 'center', type: 'selection', width: '50', reserveSelection: true },
        { label: '保单号', prop: 'policy.policy_no', width: '200', fixed: 'left' },
        { label: '应收平台', prop: 'receivable_name' },
        {
          label: '险种',
          scopedSlots: {
            default: (scoped) => {
              const types = {
                1: '国内货运险',
                2: '国际货运险',
                3: '单车责任险',
                4: '其他险种',
                5: '雇主责任险',
                6: '线下录入保单',
                7: '跨境电商险'
              }

              return types[scoped.row.policy.type]
            }
          }
        },
        { label: '成本保费', prop: 'premium' },
        { label: '应收佣金', prop: 'commission' },
        { label: '出单公司', prop: 'company_branch.name' },
        { label: '生效时间', prop: 'issued_at' }
      ],
      tableData: [],
      searchData: {},
      searchQuery: {},
      pagingAttrs: {
        currentPage: 1,
        pageSize: 15,
        layout: 'total, prev, pager, next, jumper',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.pagingAttrs.currentPage = page

          this.fetchCommissionReceivables()
        }
      },
      tableEvents: {
        'selection-change': (val) => {
          this.multipleSelection = val
        }
      },
      multipleSelection: [],
      rawCompanies: [],
      countReceivable: {
        premium: 0,
        commission: 0
      },
      platforms: []
    }
  },
  computed: {
    ...mapGetters('auth', ['admin'])
  },
  created() {
    getPlatformProductCompanies().then((r) => {
      this.rawCompanies = r.data

      this.loadCompanies()
      this.loadCompanyBranches()
    })
    // getPlatformsDict({
    //   is_enabled: 1
    // }).then((r) => {
    //   this.platforms = r.data
    //   this.loadPlatforms()
    // })
    this.fetchCommissionReceivables()
    this.fetchCountReceivable()
  },
  methods: {
    handleSearchPanel(command, data) {
      this.searchQuery = data
      this.pagingAttrs.currentPage = 1
      if (command === 'export') {
        this.receivablesExport()
      } else {
        this.fetchCommissionReceivables()
        this.fetchCountReceivable()
      }
    },
    getRowKeys(row) {
      return row.id
    },
    fetchCommissionReceivables() {
      getCommissionReceivables({
        page: this.pagingAttrs.currentPage,
        filter: this.searchQuery
      }).then((r) => {
        this.tableData = r.data

        this.pagingAttrs.currentPage = r.meta.current_page
        this.pagingAttrs.pageSize = r.meta.per_page
        this.pagingAttrs.total = r.meta.total
      })
    },
    fetchCountReceivable() {
      getCommissionReceivableCount({
        filter: this.searchQuery
      }).then((r) => {
        this.countReceivable.premium = r.data.premium
        this.countReceivable.commission = r.data.commission
      })
    },
    loadCompanies() {
      const options = this.rawCompanies.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('company_id', options)
    },
    loadCompanyBranches() {
      let options = []
      if (this.searchData?.company_id !== '' && this.searchData?.company_id !== undefined) {
        const company = this.rawCompanies.find((e) => e.id === this.searchData?.company_id)
        if (company) {
          options = company.branches.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      } else {
        this.rawCompanies.forEach((e) => {
          e?.branches.forEach((e) => {
            options.push({
              label: e.name,
              value: e.id
            })
          })
        })
      }

      this.assignSelectOptions('company_branch_id', options)
    },
    loadPlatforms() {
      const options = this.platforms.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('receivable_platform', options)
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    },
    handleReceivable() {
      const count = this.multipleSelection.length
      if (!count) {
        this.$message({
          type: 'warning',
          message: '请至少选择一项'
        })
        return
      }
      const receivable1 = this.multipleSelection[0]

      if (this.multipleSelection.find((item) => item.company_branch.id != receivable1.company_branch.id)) {
        this.$message({
          type: 'warning',
          message: '请选择同一个出单公司的保单'
        })
        return false
      }

      this.$router.push({
        name: 'HandleCommissionReceivable',
        query: {
          id: this.multipleSelection.map((e) => e.id).join(',')
        }
      })
    },
    receivableBills() {
      this.$router.push({
        name: 'CommissionReceivableBills'
      })
    },
    draftBills() {
      this.$router.push({
        name: 'DraftCommissionReceivableBills'
      })
    },
    receivablesExport() {
      const link = exportCommissionReceivables({ filter: this.searchQuery })
      window.open(link, '_blank')
    },
    allCheckout() {
      this.$router.push({
        name: 'HandleCommissionReceivable',
        query: {
          all_checkout: 1,
          ...this.searchQuery
        }
      })
    }
  },
  watch: {
    searchData: {
      immediate: true,
      deep: true,
      handler(value, old) {
        if (old?.company_id !== value?.company_id) {
          this.loadCompanyBranches()
        }
      }
    }
  }
}
</script>

<style scoped>
.el-select,
.el-select--small {
  width: 100%;
}
.box-card-options {
  margin-bottom: 20px;
}
</style>

<style lang="scss" scoped>
.policy-search {
  /deep/.el-select,
  /deep/.el-date-editor {
    width: 100% !important;
  }
  /deep/.el-card__body {
    display: flex;
  }
}
</style>
