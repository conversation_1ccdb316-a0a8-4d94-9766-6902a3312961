<!--
 * @Author: your name
 * @Date: 2021-03-05 09:45:47
 * @LastEditTime: 2024-01-29 10:25:06
 * @LastEditors: yanb
 * @Description: In User Settings Edit
 * @FilePath: \dashboard-fe\src\views\tickets\index.vue
-->
<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <SearchPanel
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
      :exportable="false"
    ></SearchPanel>
    <el-card shadow="never" class="box-card-options m-extra-large-t">
      <define-table :data="tickets" :attrs="tableAttrs" :cols="cols" :paging="paging" :paging-events="pagingEvents" />
    </el-card>
  </div>
</template>

<script>
import InsuranceTypes from '@/components/selectors/InsuranceTypes'
import { getTickets } from '@/apis/ticket'
import { getCompaniesDict } from '@/apis/company'
export default {
  name: 'Tickets',
  components: { InsuranceTypes },
  data() {
    return {
      rowData: undefined,
      text: '',
      dialogVisible: false,
      tickets: [],
      companiesDict: [],
      searchQuery: {},
      searchData: {},
      paging: {
        currentPage: 1,
        pageSize: 15,
        layout: ' prev, pager, next, jumper, total',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.currentPage = page
          this.fetchTickets()
        }
      },
      tableAttrs: {
        rowKey: 'id',
        border: false
      },
      searchFields: [
        {
          type: 'select',
          valKey: 'company_id',
          hintText: '保险公司',
          onChangeReset: 'company_branch_id',
          options: []
        },
        {
          type: 'select',
          valKey: 'company_branch_id',
          hintText: '出单公司',
          options: []
        },
        {
          type: 'select',
          valKey: 'type',
          hintText: '类型',
          options: [
            {
              value: 1,
              label: '修改'
            },
            {
              value: 2,
              label: '退保'
            }
          ]
        },
        {
          type: 'select',
          valKey: 'policy_type',
          hintText: '险种',
          options: [
            {
              value: 1,
              label: '国内货运险'
            },
            {
              value: 2,
              label: '国际货运险'
            },
            {
              value: 3,
              label: '单车责任险'
            },
            {
              value: 4,
              label: '其他险种'
            },
            {
              value: 7,
              label: '跨境电商货运险'
            }
          ]
        },
        {
          type: 'select',
          valKey: 'status',
          hintText: '状态',
          options: [
            {
              value: 0,
              label: '未领取'
            },
            {
              value: 1,
              label: '处理中'
            },
            {
              value: 2,
              label: '已完成'
            },
            {
              value: 3,
              label: '已退回'
            },
            {
              value: 4,
              label: '已退回(补充资料)'
            },
            {
              value: 5,
              label: '审核中'
            },
            {
              value: 6,
              label: '未支付'
            }
          ]
        },
        {
          type: 'input',
          valKey: 'order_no',
          hintText: '流水号'
        },
        {
          type: 'input',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'input',
          valKey: 'policyholder',
          hintText: '投保人'
        },
        {
          type: 'input',
          valKey: 'user_name',
          hintText: '投保用户'
        },
        {
          type: 'input',
          valKey: 'operated_name',
          hintText: '处理人'
        },
        {
          type: 'daterange',
          valKey: 'created_at_range'
        }
      ],
      cols: [
        {
          label: '投保人',
          width: 200,
          prop: 'policy.holder_name'
        },
        {
          label: '出单公司',
          width: 120,
          prop: 'company_branch.name'
        },
        {
          label: '流水号',
          width: 200,
          prop: 'policy.order_no'
        },
        {
          label: '保单号',
          width: 200,
          prop: 'policy.policy_no'
        },
        {
          label: '类型',
          prop: 'type',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              switch (scoped.row.type) {
                case 1:
                  return <span class="text-primary">修改</span>
                case 2:
                  return <span class="text-danger">退保</span>
                default:
                  return <span class="text-primary">未知</span>
              }
            }
          }
        },
        {
          label: '投保用户',
          prop: 'user.name'
        },
        {
          label: '状态',
          prop: 'status',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              switch (scoped.row.status) {
                case 0:
                  return <span class="text-info">未领取</span>
                case 1:
                  return <span class="text-warning">处理中</span>
                case 2:
                  return <span class="text-success">已完成</span>
                case 3:
                  return <span>已退回</span>
                case 4:
                  return <span>已退回(补充资料)</span>
                case 5:
                  return <span>审核中</span>
                case 6:
                  return <span>未支付</span>
                default:
                  return <span class="text-primary">未知</span>
              }
            }
          }
        },
        {
          label: '领取人',
          prop: 'operator.name'
        },
        {
          label: '创建时间',
          prop: 'created_at',
          width: 150
        },
        {
          label: '操作',
          fixed: 'right',
          width: 100,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    type="primary"
                    plain
                    size="small"
                    icon="el-icon-view"
                    onClick={() => this.toDetail(scoped.row.id)}
                  >
                    查看
                  </el-button>
                </div>
              )
            }
          }
        }
      ]
    }
  },
  created() {
    this.fetchTickets()
    getCompaniesDict().then((r) => {
      this.companiesDict = r.data
      this.loadCompanies()
      this.loadCompanyBranches()
    })
  },
  watch: {
    searchData: {
      immediate: true,
      deep: true,
      handler(value, old) {
        if (old?.company_id !== value?.company_id) {
          this.loadCompanyBranches()
        }
      }
    }
  },
  methods: {
    handleSearchPanel(command, data) {
      this.searchQuery = data
      this.paging.currentPage = 1
      if (command === 'export') {
        //
      } else {
        this.fetchTickets()
      }
    },
    fetchTickets() {
      getTickets({
        page: this.paging.currentPage,
        filter: this.searchQuery
      }).then((r) => {
        this.tickets = r.data
        this.paging.currentPage = r.meta.current_page
        this.paging.total = r.meta.total
        this.paging.pageSize = r.meta.per_page
      })
    },
    // 跳转详情页
    toDetail(id) {
      this.$open({
        name: 'TicketDetail',
        params: {
          id: id
        }
      })
    },
    loadCompanies() {
      const options = this.companiesDict.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('company_id', options)
    },
    loadCompanyBranches() {
      let options = []
      if (this.searchData?.company_id !== '' && this.searchData?.company_id !== undefined) {
        const company = this.companiesDict.find((e) => e.id === this.searchData?.company_id)
        if (company) {
          options = company.branches.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      } else {
        this.companiesDict.forEach((e) => {
          e?.branches.forEach((e) => {
            options.push({
              label: e.name,
              value: e.id
            })
          })
        })
      }

      this.assignSelectOptions('company_branch_id', options)
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
