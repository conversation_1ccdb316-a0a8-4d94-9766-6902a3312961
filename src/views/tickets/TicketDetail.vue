<template>
  <div class="ticketDetail p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <div class="row-box p-extra-large bg-white flex-fill o-hidden o-y-auto">
      <el-alert
        v-if="dataList.policy.payment_method == 2"
        title="当前保单为在线支付保单,退回或退保时将直接退款,请谨慎处理该工单"
        type="error"
        center=""
        :closable="false"
        style="margin: 20px 0; padding: 15px 0"
      >
      </el-alert>
      <div style="margin: 10px 0 200px; display: flex; justify-content: flex-end" v-if="dataList.status == 5">
        <el-button
          type="primary"
          style="margin-bottom: 6px"
          v-if="dataList?.policy_insure_type === 'API_DIC'"
          @click="handleApiDialog.visible = true"
          >提交保司</el-button
        >
        <el-button type="primary" style="margin-bottom: 6px" @click="handleDialog.visible = true">手动处理</el-button>
        <el-button type="primary" style="margin-bottom: 6px" @click="sendBackDialog.visible = true">退回工单</el-button>
      </div>
      <div style="display: flex; justify-content: flex-end" v-if="dataList.status == 1">
        <el-button
          type="primary"
          style="margin-bottom: 6px"
          v-if="dataList?.policy_insure_type === 'API_DIC'"
          @click="handleApiDialog.visible = true"
          >提交保司</el-button
        >
        <el-button type="primary" style="margin-bottom: 6px" v-else @click="handleDialog.visible = true"
          >处理工单</el-button
        >
        <el-button type="primary" style="margin-bottom: 6px" @click="sendBackDialog.visible = true">退回工单</el-button>
        <el-button
          type="primary"
          style="margin-bottom: 6px"
          v-if="dataList?.type == 1"
          @click="sendBackSupplementInfoDialog.visible = true"
          >退回工单(补充资料)
        </el-button>
      </div>
      <div style="display: flex; justify-content: flex-end" v-if="dataList.operator.id === null">
        <el-button
          v-if="dataList.status == 0"
          type="primary"
          style="margin-bottom: 6px"
          @click="receiveDialog.visible = true"
          >领取工单</el-button
        >
        <el-button
          v-if="dataList.policy.payment_method == 2 && dataList.status == 6"
          type="primary"
          style="margin-bottom: 6px"
          @click="sendBackDialog.visible = true"
          >退回工单</el-button
        >
      </div>

      <el-row v-if="dataList.status === -1">
        <el-col :span="3">退回理由</el-col>
        <el-col :span="21">{{ dataList.reason }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="3">领取人</el-col>
        <el-col :span="9">
          <span> {{ dataList.operator.name }}</span>
        </el-col>
        <el-col :span="3">创建时间</el-col>
        <el-col :span="9">{{ dataList.created_at }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="3">工单类型</el-col>
        <el-col :span="9">
          <span v-if="dataList.type === 1"> 修改 </span>
          <span v-else> 退保 </span>
        </el-col>
        <el-col :span="3">工单状态</el-col>
        <el-col :span="9">
          <span v-if="dataList.status === 0">未领取</span>
          <span v-else-if="dataList.status === 1">处理中</span>
          <span v-else-if="dataList.status === 2">已完成</span>
          <span v-else-if="dataList.status === 3">已退回</span>
          <span v-else-if="dataList.status === 4">已退回(补充资料)</span>
          <span v-else-if="dataList.status === 5">审核中</span>
          <span v-else>未知</span>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="3">保单号</el-col>
        <el-col :span="9">{{ dataList.policy.policy_no }}</el-col>
        <el-col :span="3">投保时间</el-col>
        <el-col :span="9">{{ dataList.policy.submitted_at }}</el-col>
      </el-row>
      <el-row class="detail" style="border-left: 1px solid #909399">
        <el-col :span="3" style="border-right: none; border-left: none; width: calc(12.5% - 2px)">工作内容</el-col>
        <el-col :span="21" style="border-left: 1px solid #909399; width: calc(87.5% + 2px)">
          <!-- <div v-html="dataList.content"></div> -->
          <el-table :data="dataList?.revision" border style="width: 100%" v-if="dataList.type === 1">
            <el-table-column prop="name" label="修改内容" header-align="center" align="center" width="200px">
            </el-table-column>
            <el-table-column label="修改前" header-align="center"
              ><template slot-scope="scope">
                <pre v-html="scope.row.from_label"></pre>
              </template>
            </el-table-column>
            <el-table-column label="修改后" header-align="center">
              <template slot-scope="scope">
                <pre style="color: red" v-html="scope.row.to_label"></pre>
              </template>
            </el-table-column>
          </el-table>
          <div v-else>
            {{ dataList.policy?.surrender_reason ? '退保理由: ' + dataList.policy?.surrender_reason : '' }}
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="row-box p-extra-large bg-white flex-fill o-hidden o-y-auto" style="margin-top: 20px">
      <DefinePoliciesDetails :data="policyData"></DefinePoliciesDetails>
    </div>

    <div class="bg-white flex-fill o-hidden o-y-auto m-extra-large-t">
      <h1>操作记录</h1>
      <DefineTable :data="tabelData" :cols="cols" />
    </div>

    <el-dialog title="提示" :visible.sync="receiveDialog.visible" width="30%">
      <span>是否确定领取?</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="receiveDialog.visible = false">取 消</el-button>
        <el-button type="primary" @click="receiveTicket()">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="工单退回(补充资料)" width="520px" :visible.sync="sendBackSupplementInfoDialog.visible">
      <el-alert
        v-if="dataList.policy.payment_method == 2"
        title="当前保单为在线支付保单,退回或退保时将直接退款,请谨慎处理该工单"
        type="error"
        center=""
        :closable="false"
        style="margin-bottom: 20px; padding: 15px 0"
      >
      </el-alert>
      <el-form
        ref="sendBackSupplementInfoForm"
        :model="sendBackSupplementInfoDialog.form"
        :rules="sendBackSupplementInfoDialog.rules"
        label-suffix=":"
        label-width="100px"
      >
        <el-form-item prop="reason" label="退回理由">
          <el-input v-model="sendBackSupplementInfoDialog.form.reason" placeholder="请输入退回理由"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="sendBackTicketSupplementInfo()">提交</el-button>
          <el-button @click="$refs.sendBackSupplementInfoForm.resetFields()">重置</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <ticket-handle-dialog
      :dialog-visible.sync="handleDialog.visible"
      :ticket-type="dataList.type"
      :policy="dataList.policy"
      @submit="handleTicket"
      @close="resetHandleForm"
    />

    <el-dialog title="提交保司处理" width="520px" :visible.sync="handleApiDialog.visible">
      <el-form
        ref="handleForm"
        :model="handleApiDialog.form"
        :rules="handleApiDialog.rules"
        label-suffix=":"
        label-width="100px"
      >
        <div v-if="dataList.type === 1">
          <el-form-item prop="endorse_type" label="批改类型">
            <el-select placeholder="请选择批改类型" v-model="handleApiDialog.form.endorse_type" multiple class="w-100">
              <el-option
                v-for="endorseType in dicEndorseTypes"
                :key="endorseType.value"
                :label="endorseType.label"
                :value="endorseType.value"
              />
            </el-select>
          </el-form-item>
        </div>

        <el-form-item v-else>
          <span>当前工单为退保工单,请认真确认保单信息再继续操作</span>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleApiTicket()">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog title="工单退回" width="520px" :visible.sync="sendBackDialog.visible">
      <el-alert
        v-if="dataList.policy.payment_method == 2"
        title="当前保单为在线支付保单,退回或退保时将直接退款,请谨慎处理该工单"
        type="error"
        center=""
        :closable="false"
        style="margin-bottom: 20px; padding: 15px 0"
      >
      </el-alert>
      <el-form
        ref="handleForm"
        :model="sendBackDialog.form"
        :rules="sendBackDialog.rules"
        label-suffix=":"
        label-width="100px"
      >
        <el-form-item prop="reason" label="退回理由">
          <el-input v-model="sendBackDialog.form.reason" placeholder="请输入退回理由"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="sendBackTicket()">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  getTicket,
  receiveTicket,
  handleTicket,
  handleApiTicket,
  sendBackTicket,
  sendBackSupplementInfoTicket
} from '@/apis/ticket'
import { mapGetters } from 'vuex'
import { Loading } from 'element-ui'
import dayjs from 'dayjs'
import TicketHandleDialog from '@/components/tickets/TicketHandleDialog/Index.vue'
export default {
  name: 'TicketDetail',
  components: {
    TicketHandleDialog
  },
  data() {
    return {
      dataList: {
        operator: {
          id: 0,
          name: ''
        },
        created_at: '',
        policy: {
          policy_no: '',
          created_at: ''
        },
        content: '',
        reason: '',
        type: 1,
        status: 0
      },
      handleDialog: {
        visible: false
      },
      handleApiDialog: {
        visible: false,
        form: {
          endorse_type: ''
        },
        rules: {
          endorse_type: [
            {
              required: this.dataList?.policy?.company?.identifier === 'DIC',
              message: '请选择批改类型',
              trigger: 'blur'
            }
          ]
        }
      },
      receiveDialog: {
        visible: false
      },
      sendBackSupplementInfoDialog: {
        visible: false,
        form: {
          reason: ''
        },
        rules: {
          reason: [{ required: true, message: '请输入退回理由', trigger: 'blur' }]
        }
      },
      sendBackDialog: {
        visible: false,
        form: {
          reason: ''
        },
        rules: {
          reason: [{ required: true, message: '请输入退回理由', trigger: 'blur' }]
        }
      },
      tabelData: [],
      cols: [
        { align: 'center', type: 'index' },
        { prop: 'date', label: '操作者' },
        { prop: 'name', label: '操作内容' },
        { prop: 'address', label: '操作时间' }
      ]
    }
  },
  computed: {
    ...mapGetters('auth', ['admin']),
    type() {
      return this.$route.params.type
    },
    dicEndorseTypes() {
      return [
        {
          label: '投被保人',
          value: '01'
        },
        {
          label: '批改承保风险',
          value: '08'
        },
        {
          label: '变更保险止期',
          value: '19'
        },
        {
          label: '变更承保区域/范围',
          value: '23'
        },
        {
          label: '变更司法管辖',
          value: '24'
        },
        {
          label: '变更特别约定/备注',
          value: '34'
        },
        {
          label: '批改合同争议解决方式',
          value: '91'
        }
      ]
    },
    shippingDatePrintFormatLabel() {
      return parseInt(this.dataList?.policy?.detail?.shipping_date_print_format, 10) === 1
        ? dayjs(this.dataList?.policy?.detail?.shipping_date).format('MMM.DD, YYYY')
        : 'AS PER B/L'
    },
    policyData() {
      switch (this.dataList?.policy?.source_type) {
        case 1:
          return this.domesticDetails
        case 2:
          return this.intlDetails
        case 3:
          return this.lbtDetails
        case 7:
          return this.cbecDetails
        default:
          return {}
      }
    },
    domesticDetails() {
      return {
        title: '保单详情',
        data: [
          {
            title: '系统信息',
            groups: [
              { label: '保单号', value: this.dataList.policy?.policy_no },
              { label: '出单时间', value: this.dataList.policy?.issued_at },
              { label: '投保单号', value: this.dataList.policy?.apply_no },
              { label: '流水号', value: this.dataList.policy?.order_no },
              { label: '保额(元)', value: this.dataList.policy?.coverage },
              { label: '保费(元)', value: this.dataList.policy?.premium },
              { label: '用户费率(‱)', value: this.dataList.policy?.user_rate },
              { label: '保费同步', value: this.dataList.policy?.is_premium_sync === 1 ? '是' : '否' },
              { label: '是否允许开票', value: this.dataList.policy?.is_allowed_invoice === 1 ? '是' : '否' },
              { label: '投保用户', value: this.dataList.policy?.user?.name },
              { label: '投保时间', value: this.dataList.policy?.submitted_at },
              { label: '第三方标识号', value: this.dataList.policy?.trade_order_no }
            ]
          },
          {
            title: '产品信息',
            groups: [
              { label: '标的', value: this.dataList.policy?.detail?.subject?.name },
              { label: '保险公司', value: this.dataList.policy?.company_branch?.name },
              { label: '保险产品', value: this.dataList.policy?.product?.name },
              {
                label: '产品代码',
                value: this.dataList.policy?.product?.code
                // isLink: true,
                // target: '_blank',
                // to: this.$router.push({ name: 'ProductsDomesticUpdate' })
              }
            ]
          },
          {
            title: '基本信息',
            groups: [
              { label: '投保人', value: this.dataList.policy.policyholder },
              { label: '被保人', value: this.dataList.policy?.insured },
              {
                label: '地址',
                value: this.dataList.policy?.policyholder_address
              },
              {
                label: '地址',
                value: this.dataList.policy?.insured_address
              },
              { label: '投保人电话', value: this.dataList.policy?.policyholder_phone_number },
              { label: '被保人电话', value: this.dataList.policy?.insured_phone_number },
              {
                label: '第三方标识号',
                value: this.dataList.policy?.trade_order_no,
                row: true
              }
            ]
          },
          {
            title: '货物信息',
            groups: [
              { label: '货物类别', value: this.dataList.policy?.detail?.goods_type?.name },
              { label: '装载方式', value: this.dataList.policy?.detail?.loading_method?.name },
              { label: '运输方式', value: this.dataList.policy?.detail?.transport_method?.name },
              { label: '包装方式', value: this.dataList.policy?.detail?.packing_method?.name },
              { label: '货物名称', value: `<pre>${this.dataList.policy?.detail?.goods_name}</pre>` },
              { label: '包装件数', value: `<pre>${this.dataList.policy?.detail?.goods_amount}</pre>` }
            ]
          },
          {
            title: '运输信息',
            groups: [
              { label: '运单号', value: this.dataList.policy?.detail?.waybill_no },
              { label: '发票号', value: this.dataList.policy?.detail?.invoice_no },
              { label: '车牌号', value: this.dataList.policy?.detail?.transport_no },
              { label: '起运日期', value: this.dataList.policy?.detail?.shipping_date },
              { label: '起运地', value: this.dataList.policy?.detail?.departure?.replace(':', '-') },
              { label: '目的地', value: this.dataList.policy?.detail?.destination?.replace(':', '-') },
              { label: '中转地', value: this.dataList.policy?.detail?.transmit },
              {
                label: '倒签保函',
                isLink: true,
                value: '点击查看',
                target: '_blank',
                to: this.dataList.policy?.detail?.anti_dated_file
              }
            ]
          },
          {
            title: '保险内容',
            groups: [
              { label: '主条款', value: this.dataList.policy?.detail?.main_clause, row: true },
              { label: '附加条款', value: this.dataList.policy?.detail?.additional_clause, row: true },
              {
                label: '免赔',
                value: this.dataList.policy?.detail?.deductible,
                row: true
              },
              {
                label: '特别约定',
                value: this.dataList.policy?.detail?.special,
                row: true
              }
            ]
          },
          {
            title: '其他',
            groups: [{ label: '备注', value: this.dataList.policy?.remark, row: true }]
          }
        ]
      }
    },
    intlDetails() {
      return {
        title: '保单详情',
        data: [
          {
            title: '系统信息',
            groups: [
              { label: '保单号', value: this.dataList.policy?.policy_no },
              { label: '出单时间', value: this.dataList.policy?.issued_at },
              { label: '投保单号', value: this.dataList.policy?.apply_no },
              { label: '流水号', value: this.dataList.policy?.order_no },
              {
                label: `发票金额(${this.dataList.policy?.detail?.coverage_currency?.name})`,
                value: this.dataList.policy?.detail?.invoice_amount
              },
              {
                label: `保额(${this.dataList.policy?.detail?.coverage_currency?.name})`,
                value: this.dataList.policy?.coverage
              },
              {
                label: `发票金额(${this.dataList.policy?.detail?.invoice_currency?.name})`,
                value: this.dataList.policy?.detail?.invoice_amount
              },
              { label: '保费(元)', value: this.dataList.policy?.premium },
              { label: '用户费率(‱)', value: this.dataList.policy?.user_rate },
              { label: '保费同步', value: this.dataList.policy?.is_premium_sync === 1 ? '是' : '否' },
              { label: '是否允许开票', value: this.dataList.policy?.is_allowed_invoice === 1 ? '是' : '否' },
              { label: '投保用户', value: this.dataList.policy?.user?.name },
              { label: '投保时间', value: this.dataList.policy?.submitted_at },
              { label: '第三方标识号', value: this.dataList.policy?.trade_order_no }
            ]
          },
          {
            title: '产品信息',
            groups: [
              { label: '标的', value: this.dataList.policy?.detail?.subject?.name },
              { label: '保险公司', value: this.dataList.policy?.company_branch?.name },
              { label: '保险产品', value: this.dataList.policy?.product?.name },
              {
                label: '产品代码',
                value: this.dataList.policy?.product?.code,
                isLink: true,
                target: '_blank',
                to: ''
              }
            ]
          },
          {
            title: '基本信息',
            groups: [
              { label: '投保人', value: this.dataList.policy.policyholder },
              { label: '被保人', value: this.dataList.policy?.insured },
              {
                label: '地址',
                value: this.dataList.policy?.policyholder_address
              },
              {
                label: '地址',
                value: this.dataList.policy?.insured_address
              },
              { label: '投保人电话', value: this.dataList.policy?.policyholder_phone_number },
              { label: '被保人电话', value: this.dataList.policy?.insured_phone_number },
              {
                label: '第三方标识号',
                value: this.dataList.policy?.trade_order_no,
                row: true
              }
            ]
          },
          {
            title: '货物信息',
            groups: [
              { label: '货物类别', value: this.dataList.policy?.detail?.goods_type?.name },
              { label: '装载方式', value: this.dataList.policy?.detail?.loading_method?.name },
              { label: '运输方式', value: this.dataList.policy?.detail?.transport_method?.name },
              { label: '包装方式', value: this.dataList.policy?.detail?.packing_method?.name },
              { label: '货物名称', value: `<pre>${this.dataList.policy?.detail?.goods_name}</pre>` },
              { label: '包装件数', value: `<pre>${this.dataList.policy?.detail?.goods_amount}</pre>` },
              { label: '唛头', value: `<pre>${this.dataList.policy?.detail?.shipping_mark || ''}</pre>`, row: true }
            ]
          },
          {
            title: '运输信息',
            groups: [
              { label: '提/运单号', value: this.dataList.policy?.detail?.waybill_no },
              { label: '合同号', value: this.dataList.policy?.detail?.contract_no },
              { label: '发票号', value: this.dataList.policy?.detail?.invoice_no },
              { label: '起运地(国/地区)', value: this.dataList.policy?.detail?.departure },
              { label: '目的地(国/地区)', value: this.dataList.policy?.detail?.destination },
              { label: '起运地', value: this.dataList.policy?.detail?.departure_port },
              { label: '目的地', value: this.dataList.policy?.detail?.destination_port },
              { label: '中转地', value: this.dataList.policy?.detail?.transmit },
              { label: '赔付地', value: this.dataList.policy?.detail?.payable_at },
              { label: '运输工具号', value: this.dataList.policy?.detail?.transport_no },
              { label: '起运日期', value: this.dataList.policy?.detail?.shipping_date },
              { label: '起运日期打印格式', value: this.shippingDatePrintFormatLabel },
              {
                label: '倒签保函',
                isLink: true,
                value: '点击查看',
                target: '_blank',
                to: this.dataList.policy?.detail?.anti_dated_file
              }
            ]
          },
          {
            title: '保险内容',
            groups: [
              { label: '主条款', value: this.dataList.policy?.detail?.main_clause, row: true },
              { label: '附加条款', value: this.dataList.policy?.detail?.additional_clause, row: true },
              {
                label: '免赔',
                value: this.dataList.policy?.detail?.deductible,
                row: true
              },
              {
                label: '特别约定',
                value: this.dataList.policy?.detail?.special,
                row: true
              },
              {
                label: '是否做信用凭证',
                value: this.dataList.policy?.detail?.is_credit ? '是' : '否',
                row: true
              },
              {
                label: '信用凭证号',
                value: this.dataList.policy?.detail?.credit_no
              },
              {
                label: '条款内容',
                value: `<pre>${this.dataList.policy?.detail?.clause_content}</pre>`,
                row: true
              }
            ]
          },
          {
            title: '其他',
            groups: [{ label: '备注', value: this.dataList.policy?.remark, row: true }]
          }
        ]
      }
    },
    cbecDetails() {
      return {
        title: '保单详情',
        data: [
          {
            title: '系统信息',
            groups: [
              { label: '保单号', value: this.dataList.policy?.policy_no },
              { label: '出单时间', value: this.dataList.policy?.issued_at },
              { label: '投保单号', value: this.dataList.policy?.apply_no },
              { label: '流水号', value: this.dataList.policy?.order_no },
              {
                label: `发票金额(${this.dataList.policy?.detail?.coverage_currency?.name})`,
                value: this.dataList.policy?.detail?.invoice_amount
              },
              {
                label: `保额(${this.dataList.policy?.detail?.coverage_currency?.name})`,
                value: this.dataList.policy?.coverage
              },
              {
                label: `发票金额(${this.dataList.policy?.detail?.invoice_currency?.name})`,
                value: this.dataList.policy?.detail?.invoice_amount
              },
              { label: '保费(元)', value: this.dataList.policy?.premium },
              { label: '用户费率(‱)', value: this.dataList.policy?.user_rate },
              { label: '保费同步', value: this.dataList.policy?.is_premium_sync === 1 ? '是' : '否' },
              { label: '是否允许开票', value: this.dataList.policy?.is_allowed_invoice === 1 ? '是' : '否' },
              { label: '投保用户', value: this.dataList.policy?.user?.name },
              { label: '投保时间', value: this.dataList.policy?.submitted_at },
              { label: '第三方标识号', value: this.dataList.policy?.trade_order_no }
            ]
          },
          {
            title: '产品信息',
            groups: [
              { label: '标的', value: this.dataList.policy?.detail?.subject?.name },
              { label: '保险公司', value: this.dataList.policy?.company_branch?.name },
              { label: '保险产品', value: this.dataList.policy?.product?.name },
              {
                label: '产品代码',
                value: this.dataList.policy?.product?.code,
                isLink: true,
                target: '_blank',
                to: ''
              }
            ]
          },
          {
            title: '基本信息',
            groups: [
              { label: '投保人', value: this.dataList.policy.policyholder },
              { label: '被保人', value: this.dataList.policy?.insured },
              {
                label: '地址',
                value: this.dataList.policy?.policyholder_address
              },
              {
                label: '地址',
                value: this.dataList.policy?.insured_address
              },
              { label: '投保人电话', value: this.dataList.policy?.policyholder_phone_number },
              { label: '被保人电话', value: this.dataList.policy?.insured_phone_number },
              {
                label: '第三方标识号',
                value: this.dataList.policy?.trade_order_no,
                row: true
              }
            ]
          },
          {
            title: '货物信息',
            groups: [
              { label: '货物类别', value: this.dataList.policy?.detail?.goods_type?.name },
              { label: '装载方式', value: this.dataList.policy?.detail?.loading_method?.name },
              { label: '运输方式', value: this.dataList.policy?.detail?.transport_method?.name },
              { label: '包装方式', value: this.dataList.policy?.detail?.packing_method?.name },
              { label: '货物名称', value: `<pre>${this.dataList.policy?.detail?.goods_name}</pre>` },
              { label: '包装件数', value: `<pre>${this.dataList.policy?.detail?.goods_amount}</pre>` },
              { label: '重量', value: this.dataList.policy?.detail?.weight },
              { label: '唛头', value: `<pre>${this.dataList.policy?.detail?.shipping_mark || ''}</pre>`, row: true }
            ]
          },
          {
            title: '运输信息',
            groups: [
              { label: '提/运单号', value: this.dataList.policy?.detail?.waybill_no },
              { label: '合同号', value: this.dataList.policy?.detail?.contract_no },
              { label: '发票号', value: this.dataList.policy?.detail?.invoice_no },
              { label: '起运地(国/地区)', value: this.dataList.policy?.detail?.departure },
              { label: '目的地(国/地区)', value: this.dataList.policy?.detail?.destination },
              { label: '起运地', value: this.dataList.policy?.detail?.departure_port },
              { label: '目的地', value: this.dataList.policy?.detail?.destination_port },
              { label: '中转地', value: this.dataList.policy?.detail?.transmit },
              { label: '赔付地', value: this.dataList.policy?.detail?.payable_at },
              { label: '运输工具号', value: this.dataList.policy?.detail?.transport_no },
              { label: '起运日期', value: this.dataList.policy?.detail?.shipping_date },
              { label: '起运日期打印格式', value: this.shippingDatePrintFormatLabel },
              {
                label: '倒签保函',
                isLink: true,
                value: '点击查看',
                target: '_blank',
                to: this.dataList.policy?.detail?.anti_dated_file
              },
              { label: 'SHIPMENT ID', value: this.dataList.policy?.detail?.shipment_id },
              { label: '目的地类型', value: this.dataList.policy?.detail?.cbec_destination_type?.name },
              { label: '派送方式', value: this.dataList.policy?.detail?.cbec_delivery_method?.name },
              {
                label: '快递公司',
                value: this.dataList.policy?.detail?.cbec_express_company?.name,
                hide: !this.dataList.policy?.detail?.cbec_delivery_method?.code === 'express'
              },
              {
                label: '快递单号',
                value: this.dataList.policy?.detail?.express_no,
                hide: !this.dataList.policy?.detail?.cbec_delivery_method?.code === 'express'
              }
            ]
          },
          {
            title: '保险内容',
            groups: [
              { label: '主条款', value: this.dataList.policy?.detail?.main_clause, row: true },
              { label: '保障区间', value: this.dataList.policy?.detail?.coverage_scope_text, row: true },
              { label: '附加条款', value: this.dataList.policy?.detail?.additional_clause, row: true },
              {
                label: '免赔',
                value: this.dataList.policy?.detail?.deductible,
                row: true
              },
              {
                label: '特别约定',
                value: this.dataList.policy?.detail?.special,
                row: true
              },
              {
                label: '是否做信用凭证',
                value: this.dataList.policy?.detail?.is_credit ? '是' : '否',
                row: true
              },
              {
                label: '信用凭证号',
                value: this.dataList.policy?.detail?.credit_no
              },
              {
                label: '条款内容',
                value: `<pre>${this.dataList.policy?.detail?.clause_content}</pre>`,
                row: true
              }
            ]
          },
          {
            title: '其他',
            groups: [{ label: '备注', value: this.dataList.policy?.remark, row: true }]
          }
        ]
      }
    },
    lbtDetails() {
      return {
        title: '保单详情',
        data: [
          {
            title: '系统信息',
            groups: [
              { label: '保单号', value: this.dataList.policy?.policy_no },
              { label: '出单时间', value: this.dataList.policy?.issued_at },
              { label: '投保单号', value: this.dataList.policy?.apply_no },
              { label: '流水号', value: this.dataList.policy?.order_no },
              { label: '保额(元)', value: this.dataList.policy?.actual_coverage },
              { label: '保费(元)', value: this.dataList.policy?.premium },
              { label: '运费(元)', value: this.dataList.policy?.coverage },
              { label: '投保用户', value: this.dataList.policy?.user?.name },
              { label: '投保时间', value: this.dataList.policy?.submitted_at },
              { label: '保费同步', value: this.dataList.policy?.is_premium_sync === 1 ? '是' : '否' },
              { label: '是否允许开票', value: this.dataList.policy?.is_allowed_invoice === 1 ? '是' : '否' },
              { label: '第三方标识号', value: this.dataList.policy?.trade_order_no }
            ]
          },
          {
            title: '产品信息',
            groups: [
              { label: '保险公司', value: this.dataList.policy?.company_branch?.name },
              { label: '保险产品', value: this.dataList.policy?.product?.name },
              {
                label: '产品代码',
                value: this.dataList.policy?.product?.code,
                row: true
                // isLink: true,
                // target: '_blank',
                // to: ''
              }
            ]
          },
          {
            title: '基本信息',
            groups: [
              { label: '投保人', value: this.dataList.policy.policyholder },
              { label: '被保人', value: this.dataList.policy?.insured },
              {
                label: '地址',
                value: this.dataList.policy?.policyholder_address
              },
              {
                label: '地址',
                value: this.dataList.policy?.insured_address
              },
              { label: '投保人电话', value: this.dataList.policy?.policyholder_phone_number },
              { label: '被保人电话', value: this.dataList.policy?.insured_phone_number },
              {
                label: '第三方标识号',
                value: this.dataList.policy?.trade_order_no,
                row: true
              }
            ]
          },
          {
            title: '货物信息',
            groups: [
              { label: '货物类别', value: this.dataList.policy?.detail?.goods_type?.name },
              { label: '装载方式', value: this.dataList.policy?.detail?.loading_method?.name },
              { label: '运输方式', value: this.dataList.policy?.detail?.transport_method?.name },
              { label: '包装方式', value: this.dataList.policy?.detail?.packing_method?.name },
              { label: '货物名称', value: `<pre>${this.dataList.policy?.detail?.goods_name}</pre>` },
              { label: '包装件数', value: `<pre>${this.dataList.policy?.detail?.goods_amount}</pre>` }
            ]
          },
          {
            title: '运输信息',
            groups: [
              { label: '运单号', value: this.dataList.policy?.detail?.waybill_no },
              { label: '发票号', value: this.dataList.policy?.detail?.invoice_no },
              { label: '车牌号', value: this.dataList.policy?.detail?.transport_no },
              { label: '起运日期', value: this.dataList.policy?.detail?.shipping_date },
              { label: '起运地', value: this.dataList.policy?.detail?.departure?.replace(':', '-') },
              { label: '目的地', value: this.dataList.policy?.detail?.destination?.replace(':', '-') },
              { label: '中转地', value: this.dataList.policy?.detail?.transmit },
              {
                label: '倒签保函',
                isLink: true,
                value: '点击查看',
                target: '_blank',
                to: this.dataList.policy?.detail?.anti_dated_file
              }
            ]
          },
          {
            title: '保险内容',
            groups: [
              { label: '主条款', value: this.dataList.policy?.detail?.main_clause, row: true },
              { label: '附加条款', value: this.dataList.policy?.detail?.additional_clause, row: true },
              {
                label: '免赔',
                value: this.dataList.policy?.detail?.deductible,
                row: true
              },
              {
                label: '特别约定',
                value: this.dataList.policy?.detail?.special,
                row: true
              }
            ]
          },
          {
            title: '其他',
            groups: [{ label: '备注', value: this.dataList.policy?.remark, row: true }]
          }
        ]
      }
    }
  },
  mounted() {
    this.getTicket()
  },
  methods: {
    getTicket() {
      getTicket(this.$route.params.id).then((res) => {
        this.dataList = res.data
        console.log(this.dataList)
      })
    },
    receiveTicket() {
      const loading = Loading.service()
      receiveTicket(this.$route.params.id)
        .then(() => {
          this.receiveDialog.visible = false
          this.getTicket()
        })
        .finally(() => loading.close())
    },
    sendBackTicketSupplementInfo() {
      const loading = Loading.service()
      sendBackSupplementInfoTicket(this.$route.params.id, this.sendBackSupplementInfoDialog.form)
        .then(() => {
          this.sendBackSupplementInfoDialog.visible = false
          this.getTicket()
        })
        .finally(() => loading.close())
    },
    handleTicket(formData) {
      const loading = Loading.service()
      handleTicket(this.$route.params.id, formData)
        .then(() => {
          this.handleDialog.visible = false
          this.getTicket()
        })
        .finally(() => loading.close())
    },
    resetHandleForm() {
      // 重置表单方法，由子组件调用
    },
    handleApiTicket() {
      const loading = Loading.service()
      handleApiTicket(this.$route.params.id, this.handleApiDialog.form)
        .then(() => {
          this.handleApiDialog.visible = false
          this.getTicket()
        })
        .finally(() => loading.close())
    },
    sendBackTicket() {
      const loading = Loading.service()
      sendBackTicket(this.$route.params.id, this.sendBackDialog.form)
        .then(() => {
          this.sendBackDialog.visible = false
          this.getTicket()
        })
        .finally(() => loading.close())
    }
  }
}
</script>

<style lang="scss" scoped>
.ticketDetail {
  .row-box {
    .el-row:last-child {
      border-bottom: 1px solid #909399;
    }
    .el-col {
      border-top: 1px solid #909399;
      border-right: 1px solid #909399;
      min-height: 37px;
    }
    .el-col:first-child {
      border-left: 1px solid #909399;
    }
  }

  .detail {
    .el-col {
      min-height: 250px;
    }
  }
}
.el-col {
  padding: 8px 10px;
  text-align: center;
}
// 操作记录
h1 {
  position: relative;
  padding-left: 10px;
  &::before {
    content: '';
    display: inline-block;
    width: 3px;
    height: 15px;
    background-color: $app-color-primary;
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    margin-right: 10px;
  }
}
</style>
