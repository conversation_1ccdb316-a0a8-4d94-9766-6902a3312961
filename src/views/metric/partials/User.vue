<template>
  <div>
    <search-panel v-model="query" @search="handleSearch" @download="handleDownload" should-search>
      <year-select v-model="query.year" :years="filterOptions.years" clearable />
      <el-select
        v-model="query.user_id"
        filterable
        remote
        reserve-keyword
        placeholder="请输入用户名"
        :remote-method="searchUsers"
        :loading="userSearchLoading"
      >
        <el-option v-for="item in users" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </search-panel>

    <div class="cards m-extra-large-t" v-if="query.user_id">
      <CardPie
        v-if="query.year"
        :title="`${query.year}年总保费`"
        :series-option="{
          name: '保费',
          unit: '%',
          legend: ['应收保费', '实收保费'],
          data: convertPremiumToPercentages(pick('total_premium')?.[0])
        }"
      >
        <el-descriptions class="margin-top" :column="1" border>
          <el-descriptions-item label="总保费(元)">
            {{ (pick('total_premium')?.[0]?.premium / 100)?.toFixed(2) || 0 }}
            <span :class="pick('total_premium')?.[0]?.yoy ? 'text-success' : 'text-danger'">
              同比:
              {{ pick('total_premium')?.[0]?.yoy > 0 ? '+' : '' }}
              {{ pick('total_premium')?.[0]?.yoy?.toFixed(2) || 0 }}%
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="实收保费(元)">
            {{ (pick('total_premium')?.[0]?.actual_premium / 100)?.toFixed(2) || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="应收保费(元)">
            {{ (pick('total_premium')?.[0]?.expected_premium / 100)?.toFixed(2) || 0 }}
          </el-descriptions-item>
        </el-descriptions>
      </CardPie>

      <CardPie
        v-if="!query.year"
        title="累计总保费及各险种保费占比"
        :series-option="{
          name: '保费',
          legend: pick('product_premium_summary')?.[0]?.dataset?.map((item) => item.name) || [],
          data:
            pick('product_premium_summary')?.[0]?.dataset?.map((item) => ({
              name: item.name,
              value: (item.premium / 100)?.toFixed(2) || 0
            })) || []
        }"
      >
        <el-descriptions class="margin-top" :column="1" border>
          <el-descriptions-item label="累计总保费保费(元)">
            {{ (pick('product_premium_summary')?.[0]?.premium / 100)?.toFixed(2) || 0 }}
          </el-descriptions-item>
        </el-descriptions>
      </CardPie>

      <CardBar
        v-if="query.year"
        :title="`${query.year}年月度总保费`"
        :columns="[
          { label: '月份', prop: 'month' },
          { label: '保费(元)', prop: 'premium' }
        ]"
        :data="
          pick('monthly_premium.current')?.map((item) => ({
            month: item.month + '月',
            premium: (item.premium / 100).toFixed(2)
          })) || []
        "
        :series-option="{
          legend: [query.year + '年', query.year - 1 + '年'],
          unit: '元',
          categories: pick('monthly_premium.current')?.map((item) => item.month + '月') || [],
          data: [
            {
              name: query.year + '年',
              data: pick('monthly_premium.current')?.map((item) => (item.premium / 100).toFixed(2)) || []
            },
            {
              name: query.year - 1 + '年',
              data: pick('monthly_premium.last_year')?.map((item) => (item.premium / 100).toFixed(2)) || []
            }
          ]
        }"
      />

      <CardBar
        v-if="!query.year"
        :title="`每年总保费`"
        :columns="[
          { label: '年份', prop: 'year' },
          { label: '保费(元)', prop: 'premium' }
        ]"
        :data="
          pick('yearly_premium')?.map((item) => ({
            year: item.year + '年',
            premium: (item.premium / 100).toFixed(2)
          })) || []
        "
        :series-option="{
          legend: pick('yearly_premium')?.map((item) => item.year + '年') || [],
          unit: '元',
          categories: pick('yearly_premium')?.map((item) => item.year + '年') || [],
          data:
            pick('yearly_premium')?.map((item) => ({
              name: item.year + '年',
              data: (item.premium / 100).toFixed(2)
            })) || []
        }"
      />

      <CardPie
        v-if="query.year"
        :title="`${query.year}年各险种保费`"
        :columns="[
          { label: '险种', prop: 'name' },
          { label: '保费(元)', prop: 'premium' }
        ]"
        :data="
          pick('product_premium')?.map((item) => ({
            name: item.product_type,
            premium: (item.premium / 100).toFixed(2)
          })) || []
        "
        :series-option="{
          name: '保费',
          rotate: 45,
          data: pick('product_premium')?.map((item) => ({
            name: item.product_type,
            value: (item.premium / 100).toFixed(2)
          }))
        }"
      />

      <CardPie
        v-if="query.year"
        :title="`${query.year}年各险种出单数量`"
        :columns="[
          { label: '出单公司', prop: 'product_type' },
          { label: '出单数量', prop: 'count' }
        ]"
        :data="pick('product_count')"
        :series-option="{
          name: '保单数',
          rotate: 45,
          unit: '单',
          data: pick('product_count')?.map((item) => ({
            name: item.product_type,
            value: item.count
          }))
        }"
      />

      <CardBar
        v-if="query.year"
        :title="`${query.year}年各险种简单赔付率`"
        :columns="[
          { label: '险种', prop: 'name' },
          { label: '已决金额(元)', prop: 'settled_amount' },
          { label: '未决金额(元)', prop: 'pending_amount' }
        ]"
        :data="
          pick('product_loss_ratio')?.map((item) => ({
            name: item.product_type,
            settled_amount: (item.settled_amount / 100).toFixed(2),
            pending_amount: (item.pending_amount / 100).toFixed(2)
          })) || []
        "
        :series-option="{
          legend: ['已决金额', '未决金额', '简单赔付率'],
          rotate: 45,
          categories: pick('product_loss_ratio')?.map((item) => item.product_type) || [],
          yAxis: [
            {
              type: 'value',
              name: '金额(元)',
              position: 'left',
              axisLabel: {
                formatter: '{value} 元'
              }
            },
            {
              type: 'value',
              name: '赔付率(%)',
              position: 'right',
              axisLabel: {
                formatter: '{value} %'
              }
            }
          ],
          data: [
            {
              name: '已决金额',
              stack: 'total',
              unit: '元',
              yAxisIndex: 0,
              data: pick('product_loss_ratio')?.map((item) => parseFloat(item.settled_amount / 100).toFixed(2)) || []
            },
            {
              name: '未决金额',
              stack: 'total',
              unit: '元',
              yAxisIndex: 0,
              data: pick('product_loss_ratio')?.map((item) => parseFloat(item.pending_amount / 100).toFixed(2)) || []
            },
            {
              name: '简单赔付率',
              type: 'line',
              unit: '%',
              yAxisIndex: 1,
              data: pick('product_loss_ratio')?.map((item) => parseFloat(item.loss_ratio).toFixed(2)) || []
            }
          ]
        }"
      />

      <CardBar
        v-if="query.year"
        :title="`${query.year}累计各险种赔付率及出险率`"
        :columns="[
          { label: '险种', prop: 'product_type' },
          { label: '出险率(%)', prop: 'claim_rate' },
          { label: '赔付率(%)', prop: 'loss_ratio' }
        ]"
        :data="pick('product_claim_rate')"
        :series-option="{
          legend: ['出险率', '赔付率'],
          unit: '%',
          categories: pick('product_claim_rate')?.map((item) => item.product_type) || [],
          data: [
            {
              name: '出险率',
              data: pick('product_claim_rate')?.map((item) => item.claim_rate.toFixed(2)) || []
            },
            {
              name: '赔付率',
              data: pick('product_claim_rate')?.map((item) => item.loss_ratio.toFixed(2)) || []
            }
          ]
        }"
      />
    </div>
  </div>
</template>

<script>
import YearSelect from '../components/YearSelect.vue'
import SearchPanel from '../components/SearchPanel.vue'
import CardPie from '../components/CardPie.vue'
import CardBar from '../components/CardBar.vue'
import * as userApi from '@/apis/user'
import { SimpleCache, convertToPercentages } from '../utils'

export default {
  name: 'ProductMetrics',
  components: {
    YearSelect,
    SearchPanel,
    CardPie,
    CardBar
  },
  props: {
    filterOptions: {
      type: Object,
      default: () => ({})
    },
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      query: {
        user_id: '',
        year: ''
      },
      userSearchLoading: false,
      users: [],
      // Initialize cache
      cache: new SimpleCache()
    }
  },
  watch: {
    query: {
      handler() {
        // Clear cache when query changes
        this.cache.clear()
      },
      deep: true
    }
  },
  methods: {
    pick(dataKey) {
      // Create a cache key based on the dataKey and query parameters
      const cacheKey = `${dataKey}-${this.query.user_id}-${this.query.year}`

      // Return cached result if available
      return this.cache.get(cacheKey, () => {
        if (dataKey.indexOf('.') > -1) {
          const keys = dataKey.split('.')
          return this.value?.[keys[0]]?.[keys[1]]?.filter((item) => item.user_id === this.query.user_id) || []
        } else {
          return this.value?.[dataKey]?.filter((item) => item.user_id === this.query.user_id) || []
        }
      })
    },
    async searchUsers(query) {
      if (!query) {
        this.users = []
        return
      }
      this.userSearchLoading = true
      try {
        const response = await userApi.search(query)
        this.users = response.data.map((user) => ({
          value: user.id,
          label: user.name
        }))
      } finally {
        this.userSearchLoading = false
      }
    },
    triggerEmit(event, data) {
      this.$emit(event, {
        year: this.query.year,
        ...data
      })
    },
    handleSearch() {
      // Clear cache when search is triggered
      this.cache.clear()
      this.triggerEmit('search', this.query)
    },
    handleDownload() {
      this.triggerEmit('download', this.query)
    },
    // Convert premium data to percentages
    convertPremiumToPercentages(premiumData) {
      if (!premiumData) return []

      const data = [
        { name: '应收保费', value: parseFloat(premiumData.expected_premium / 100) || 0 },
        { name: '实收保费', value: parseFloat(premiumData.actual_premium / 100) || 0 }
      ]

      return convertToPercentages(data, 'value')
    }
  }
}
</script>

<style scoped lang="scss">
.cards {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(2, 1fr);
}

@media (min-width: 1920px) {
  .cards {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
