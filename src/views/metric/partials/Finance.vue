<template>
  <div>
    <search-panel v-model="query" @download="handleDownload">
      <year-select v-model="query.year" :years="filterOptions.years" clearable />
    </search-panel>

    <div class="cards m-extra-large-t">
      <CardPie
        v-if="query.year"
        :title="`${query.year}年总保费`"
        :series-option="{
          name: '保费',
          unit: '%',
          legend: ['应收保费', '实收保费'],
          data: convertPremiumToPercentages(value.this_year_premium)
        }"
      >
        <el-descriptions class="margin-top" :column="1" border>
          <el-descriptions-item label="总保费(元)">
            {{ (value.this_year_premium?.premium / 100)?.toFixed(2) || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="实收保费(元)">
            {{ (value.this_year_premium?.actual_premium / 100)?.toFixed(2) || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="应收保费(元)">
            {{ (value.this_year_premium?.expected_premium / 100)?.toFixed(2) || 0 }}
          </el-descriptions-item>
        </el-descriptions>
      </CardPie>

      <CardPie
        v-if="query.year"
        :title="`${query.year}年总经纪费`"
        :series-option="{
          name: '经纪费',
          unit: '%',
          legend: ['应收经纪费', '实收经纪费'],
          data: convertCommissionToPercentages(value.this_year_commission)
        }"
      >
        <el-descriptions class="margin-top" :column="1" border>
          <el-descriptions-item label="总经纪费(元)">
            {{ (value.this_year_commission?.total_commission / 100)?.toFixed(2) || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="实收经纪费(元)">
            {{ (value.this_year_commission?.actual_commission / 100)?.toFixed(2) || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="应收经纪费(元)">
            {{ (value.this_year_commission?.expected_commission / 100)?.toFixed(2) || 0 }}
          </el-descriptions-item>
        </el-descriptions>
      </CardPie>

      <CardPie
        v-if="!query.year"
        title="总保费"
        :series-option="{
          name: '保费',
          legend: ['应收保费', '实收保费'],
          data: convertPremiumToPercentages(value.total_premium)
        }"
      >
        <el-descriptions class="margin-top" :column="1" border>
          <el-descriptions-item label="总保费(元)">
            {{ (value.total_premium?.premium / 100)?.toFixed(2) || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="实收保费(元)">
            {{ (value.total_premium?.actual_premium / 100)?.toFixed(2) || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="应收保费(元)">
            {{ (value.total_premium?.expected_premium / 100)?.toFixed(2) || 0 }}
          </el-descriptions-item>
        </el-descriptions>
      </CardPie>

      <CardPie
        v-if="!query.year"
        title="总经纪费"
        :series-option="{
          name: '经纪费',
          legend: ['应收经纪费', '实收经纪费'],
          data: convertCommissionToPercentages(value.total_commission)
        }"
      >
        <el-descriptions class="margin-top" :column="1" border>
          <el-descriptions-item label="总经纪费(元)">
            {{ (value.total_commission?.total_commission / 100)?.toFixed(2) || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="实收经纪费(元)">
            {{ (value.total_commission?.actual_commission / 100)?.toFixed(2) || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="应收经纪费(元)">
            {{ (value.total_commission?.expected_commission / 100)?.toFixed(2) || 0 }}
          </el-descriptions-item>
        </el-descriptions>
      </CardPie>

      <CardPie
        v-for="branch in value.company_branch"
        :key="branch.com_id"
        :title="branch.name"
        :series-option="{
          name: '保费',
          legend: ['应收保费', '实收保费', '应收经纪费', '实收经纪费'],
          data: [
            {
              name: '经纪费',
              radius: [0, '30%'],
              labelLine: { length: 60 },
              data: [
                { name: '应收经纪费', value: (branch?.expected_commission / 100)?.toFixed(2) || 0 },
                { name: '实收经纪费', value: (branch?.actual_commission / 100)?.toFixed(2) || 0 }
              ]
            },
            {
              name: '保费',
              radius: ['45%', '60%'],
              data: [
                { name: '应收保费', value: (branch?.expected_premium / 100)?.toFixed(2) || 0 },
                { name: '实收保费', value: (branch?.actual_premium / 100)?.toFixed(2) || 0 }
              ]
            }
          ]
        }"
      >
        <div class="com-data">
          <div class="com-data__heading">
            <div></div>
            <div>应收(元)</div>
            <div>实收(元)</div>
            <div>合计(元)</div>
          </div>
          <div class="com-data__row">
            <div class="com-data__items">
              <div class="com-data__label">保费</div>
              <div class="com-data__value">{{ (branch?.actual_premium / 100)?.toFixed(2) || 0 }}</div>
              <div class="com-data__value">{{ (branch?.expected_premium / 100)?.toFixed(2) || 0 }}</div>
              <div class="com-data__value">{{ (branch?.total_premium / 100)?.toFixed(2) || 0 }}</div>
            </div>
            <div class="com-data__items">
              <div class="com-data__label">经纪费</div>
              <div class="com-data__value">{{ (branch?.actual_commission / 100)?.toFixed(2) || 0 }}</div>
              <div class="com-data__value">{{ (branch?.expected_commission / 100)?.toFixed(2) || 0 }}</div>
              <div class="com-data__value">{{ (branch?.total_commission / 100)?.toFixed(2) || 0 }}</div>
            </div>
          </div>
        </div>
      </CardPie>
    </div>
  </div>
</template>

<script>
import YearSelect from '../components/YearSelect.vue'
import SearchPanel from '../components/SearchPanel.vue'
import CardPie from '../components/CardPie.vue'
import { SimpleCache, convertToPercentages } from '../utils'

export default {
  name: 'ProductMetrics',
  components: {
    YearSelect,
    SearchPanel,
    CardPie
  },
  props: {
    filterOptions: {
      type: Object,
      default: () => ({})
    },
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      query: {
        year: ''
      },
      // Initialize cache
      cache: new SimpleCache()
    }
  },
  watch: {
    'query.year'() {
      // Clear cache when year changes
      this.cache.clear()
      this.triggerEmit('search', this.query)
    }
  },
  methods: {
    triggerEmit(event, data) {
      this.$emit(event, {
        year: this.query.year,
        ...data
      })
    },
    handleDownload() {
      this.triggerEmit('download', this.query)
    },
    // Convert premium data to percentages
    convertPremiumToPercentages(premiumData) {
      if (!premiumData) return []

      const data = [
        { name: '应收保费', value: parseFloat(premiumData.expected_premium / 100) || 0 },
        { name: '实收保费', value: parseFloat(premiumData.actual_premium / 100) || 0 }
      ]

      return convertToPercentages(data, 'value')
    },

    // Convert commission data to percentages
    convertCommissionToPercentages(commissionData) {
      if (!commissionData) return []

      const data = [
        { name: '应收经纪费', value: parseFloat(commissionData.expected_commission / 100) || 0 },
        { name: '实收经纪费', value: parseFloat(commissionData.actual_commission / 100) || 0 }
      ]

      return convertToPercentages(data, 'value')
    }
  }
}
</script>

<style scoped lang="scss">
.cards {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(2, 1fr);
}

.com-data {
  display: flex;
  flex-direction: column;
  font-size: 12px;

  &__heading {
    display: flex;
    background-color: #fafafa;
    color: #909399;

    div {
      border: 1px solid #ebeef5;
      border-right: none;
      padding: 8px 10px;
      flex: 1;

      &:last-child {
        border-right: 1px solid #ebeef5;
      }
    }
  }

  &__row {
    display: flex;
    flex-direction: column;
  }

  &__items {
    display: flex;
    width: 100%;

    div {
      border: 1px solid #ebeef5;
      border-top: none;
      border-right: none;
      padding: 8px 10px;
      flex: 1;

      &:last-child {
        border-right: 1px solid #ebeef5;
      }
    }
  }
  &__label {
    background-color: #fafafa;
    color: #909399;
    padding: 8px 10px;
  }
}

@media (min-width: 1920px) {
  .cards {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
