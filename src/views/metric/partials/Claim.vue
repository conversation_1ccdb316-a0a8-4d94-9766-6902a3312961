<template>
  <div>
    <search-panel v-model="query" @search="handleSearch" @download="handleDownload">
      <year-select v-model="query.year" :years="filterOptions.years" />
      <el-select v-model="query.company_id" placeholder="选择保险公司" @change="handleSearch">
        <el-option v-for="company in companies" :key="company.id" :label="company.name" :value="company.id" />
      </el-select>
      <el-select v-model="query.company_branch_id" placeholder="选择出单公司" @change="handleSearch" clearable>
        <el-option v-for="branch in branches" :key="branch.id" :label="branch.name" :value="branch.id" />
      </el-select>
      <el-cascader v-model="query.product_type" :options="productTypeOptions" :props="{ expandTrigger: 'hover' }" />
    </search-panel>

    <div class="cards m-extra-large-t">
      <CardBar
        :title="`${query.year}年赔付率`"
        :series-option="{
          unit: '%',
          name: '占比',
          zoom: true,
          rotate: 45,
          categories: this.filtered?.map((item) => item.name) || [],
          data: this.filtered?.map((item) => item.loss_ratio) || []
        }"
      />

      <CardBar
        :title="`${query.year}年出险率`"
        :series-option="{
          unit: '%',
          name: '占比',
          zoom: true,
          rotate: 45,
          categories: this.filtered?.map((item) => item.name) || [],
          data: this.filtered?.map((item) => item.claim_rate) || []
        }"
      />

      <CardBar
        :title="`${query.year}年案件数`"
        :series-option="{
          unit: '件',
          name: '案件数',
          zoom: true,
          rotate: 45,
          legend: ['已决案件', '未决案件'],
          categories: this.filtered?.map((item) => item.name) || [],
          data: [
            {
              name: '已决案件',
              data: this.filtered?.map((item) => item.settled_count) || []
            },
            {
              name: '未决案件',
              data: this.filtered?.map((item) => item.pending_count) || []
            }
          ]
        }"
      />

      <CardBar
        :title="`${query.year}年案件金额`"
        :series-option="{
          unit: '元',
          name: '案件金额',
          zoom: true,
          rotate: 45,
          legend: ['已决案件', '未决案件'],
          categories: this.filtered?.map((item) => item.name) || [],
          data: [
            {
              name: '已决案件',
              data: this.filtered?.map((item) => item.settled_amount) || []
            },
            {
              name: '未决案件',
              data: this.filtered?.map((item) => item.pending_amount) || []
            }
          ]
        }"
      />

      <CardBar
        :title="`${query.year}年案均赔款（已决案件）`"
        :series-option="{
          unit: '元',
          name: '案件金额',
          zoom: true,
          rotate: 45,
          categories: this.filtered?.map((item) => item.name) || [],
          data: this.filtered?.map((item) => item.avg_settled_amount) || []
        }"
      />

      <CardBar
        :title="`${query.year}年案均结案时长`"
        :series-option="{
          unit: '天',
          name: '结案时长',
          zoom: true,
          rotate: 45,
          categories: this.filtered?.map((item) => item.name) || [],
          data: this.filtered?.map((item) => item.avg_days) || []
        }"
      />

      <CardBar
        :title="`${query.year}年万元以下案件数`"
        :series-option="{
          unit: '件',
          name: '案件数',
          zoom: true,
          rotate: 45,
          categories: this.filtered?.map((item) => item.name) || [],
          data: this.filtered?.map((item) => item.under_ten_thousand) || []
        }"
      />

      <CardBar
        :title="`${query.year}年5万元以上案件数`"
        :series-option="{
          unit: '件',
          name: '案件数',
          zoom: true,
          rotate: 45,
          categories: this.filtered?.map((item) => item.name) || [],
          data: this.filtered?.map((item) => item.over_than_fifty_thousand) || []
        }"
      />

      <CardBar
        :title="`${query.year}年10万元以上案件数`"
        :series-option="{
          unit: '件',
          name: '案件数',
          zoom: true,
          rotate: 45,
          categories: this.filtered?.map((item) => item.name) || [],
          data: this.filtered?.map((item) => item.over_than_one_hundred_thousand) || []
        }"
      />
    </div>
  </div>
</template>

<script>
import YearSelect from '../components/YearSelect.vue'
import SearchPanel from '../components/SearchPanel.vue'
import * as companyApi from '@/apis/company'
import CardBar from '../components/CardBar.vue'

export default {
  name: 'ProductMetrics',
  components: {
    YearSelect,
    SearchPanel,
    CardBar
  },
  props: {
    filterOptions: {
      type: Object,
      default: () => ({})
    },
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      query: {
        year: new Date().getFullYear(),
        company_id: '',
        company_branch_id: '',
        product_type: []
      },
      companies: []
    }
  },
  computed: {
    productTypeOptions() {
      const childrenOptions = [
        { label: '按货物类别', value: 'subject' },
        { label: '按目的地', value: 'destination' }
      ]
      return [
        { label: '国内货运险', value: '9001', children: childrenOptions },
        {
          label: '国际货运险',
          value: '9002',
          children: childrenOptions.concat({ label: '按贸易类型', value: 'trade_type' })
        },
        {
          label: '跨境电商责任险',
          value: '9007',
          children: childrenOptions.concat({ label: '按贸易类型', value: 'trade_type' })
        }
      ]
    },
    filtered() {
      const value = this.value || {}
      const { company_id, company_branch_id, product_type = [] } = this.query

      // 提取 product_type 长度判断
      const hasProductType = product_type.length > 1

      // 简化公司数据查找
      const findData = (source, id) => (value[source] || []).find((item) => item.id === id)?.data || []

      // 如果选择了出单公司，并选择险种
      if (company_branch_id && hasProductType) {
        return (
          value.product_type?.company_branch
            ?.find((item) => item.id === company_branch_id)
            ?.data?.find((item) => item.product_type === product_type[0])?.data?.[product_type[1]] || []
        )
      }

      // 如果选择了保险公司，并选择险种
      if (company_id && !company_branch_id && hasProductType) {
        return (
          value.product_type?.company
            ?.find((item) => item.id === company_id)
            ?.data?.find((item) => item.product_type === product_type[0])?.data?.[product_type[1]] || []
        )
      }

      // 如果只选择险种
      if (!company_id && !company_branch_id && hasProductType) {
        return (
          value.product_type?.default?.find((item) => item.product_type === product_type[0])?.data?.[product_type[1]] ||
          []
        )
      }

      // 如果只选择保险公司
      if (company_id && !company_branch_id && !hasProductType) {
        return findData('company', company_id)
      }

      // 如果只选择出单公司
      if (company_branch_id && !hasProductType) {
        return findData('company_branch', company_branch_id)
      }

      // 默认返回
      return value.index || []
    },
    branches() {
      return this.companies.find((company) => company.id === this.query.company_id)?.branches
    }
  },
  created() {
    this.fetchCompanies()
  },
  methods: {
    async fetchCompanies() {
      const data = await companyApi.getCompaniesDict()
      this.companies = data.data
    },
    triggerEmit(event, data) {
      this.$emit(event, {
        year: this.query.year,
        ...data
      })
    },
    handleSearch() {
      this.triggerEmit('search', this.query)
    },
    handleDownload() {
      this.triggerEmit('download', this.query)
    }
  }
}
</script>

<style scoped lang="scss">
.cards {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(2, 1fr);
}

@media (min-width: 1920px) {
  .cards {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
