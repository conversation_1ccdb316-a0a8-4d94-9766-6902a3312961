<template>
  <div>
    <search-panel v-model="query" @download="handleDownload">
      <year-select v-model="query.year" :years="filterOptions.years" />
    </search-panel>

    <div class="summary m-extra-large-t">
      <div class="summary-card" v-for="card in summaryCards" :key="card.key">
        <div class="summary-card__title">{{ card.title }}</div>
        <div class="summary-card__value">{{ value.summary?.[card.key] || 0 }}</div>
      </div>
    </div>

    <div class="cards m-extra-large-t">
      <CardPie
        :title="`${query.year}年总保费`"
        :series-option="{
          name: '保费',
          unit: '%',
          legend: ['应收保费', '实收保费'],
          data: convertPremiumToPercentages(value.total_premium)
        }"
      >
        <el-descriptions class="margin-top" :column="1" border>
          <el-descriptions-item label="总保费(元)">
            {{ (value.total_premium?.premium?.current / 100)?.toFixed(2) || 0 }}
            <span :class="value.total_premium?.premium?.yoy ? 'text-success' : 'text-danger'">
              同比:
              {{ value.total_premium?.premium?.yoy > 0 ? '+' : '' }}
              {{ value.total_premium?.premium?.yoy?.toFixed(2) || 0 }}%
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="应收保费(元)">
            {{ (value.total_premium?.expected_premium / 100)?.toFixed(2) || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="实收保费(元)">
            {{ (value.total_premium?.actual_premium / 100)?.toFixed(2) || 0 }}
          </el-descriptions-item>
        </el-descriptions>
      </CardPie>

      <CardBar
        :title="`${query.year}年月度总保费`"
        :columns="[
          { label: '月份', prop: 'month' },
          { label: '保费(元)', prop: 'premium' }
        ]"
        :data="
          value?.monthly_premium?.current?.map((item) => ({
            month: item.month + '月',
            premium: (item.premium / 100).toFixed(2)
          })) || []
        "
        :series-option="{
          legend: [query.year + '年', query.year - 1 + '年'],
          unit: '元',
          categories: value?.monthly_premium?.current?.map((item) => item.month + '月') || [],
          data: [
            {
              name: query.year + '年',
              data: value?.monthly_premium?.current?.map((item) => (item.premium / 100).toFixed(2)) || []
            },
            {
              name: query.year - 1 + '年',
              data: value?.monthly_premium?.last_year?.map((item) => (item.premium / 100).toFixed(2)) || []
            }
          ]
        }"
      />

      <CardPie
        :title="`${query.year}年各险种保费`"
        :show-percentage="true"
        :columns="[
          { label: '险种', prop: 'product_type' },
          { label: '保费(元)', prop: 'premium' }
        ]"
        :data="
          value?.product_premium?.map((item) => ({
            product_type: item.product_type,
            premium: (item.premium / 100).toFixed(2)
          })) || []
        "
        :series-option="{
          name: '保费',
          rotate: 45,
          data:
            value?.product_premium?.map((item) => ({
              name: item.product_type,
              value: parseFloat(item.premium / 100) || 0
            })) || []
        }"
      />

      <CardBar
        :title="`${query.year}年各险种赔付率`"
        :show-label="true"
        :series-option="{
          name: '赔付率',
          unit: '%',
          rotate: 45,
          categories: value?.product_loss_ratio?.map((item) => item.product_type) || [],
          data: value?.product_loss_ratio?.map((item) => item.loss_ratio.toFixed(2)) || []
        }"
      />

      <CardBar
        :title="`${query.year}年保司赔付率`"
        :series-option="{
          name: '赔付率',
          unit: '%',
          zoom: true,
          rotate: 45,
          categories: value?.company_loss_ratio?.map((item) => item.name) || [],
          data: value?.company_loss_ratio?.map((item) => item.loss_ratio.toFixed(2)) || []
        }"
      />
    </div>
  </div>
</template>

<script>
import YearSelect from '../components/YearSelect.vue'
import SearchPanel from '../components/SearchPanel.vue'
import CardPie from '../components/CardPie.vue'
import CardBar from '../components/CardBar.vue'
import { SimpleCache, convertToPercentages } from '../utils'

export default {
  name: 'PlatformMetrics',
  components: {
    YearSelect,
    SearchPanel,
    CardPie,
    CardBar
  },
  props: {
    filterOptions: {
      type: Object,
      default: () => ({})
    },
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      summaryCards: [
        { title: '保险公司', key: 'company_count' },
        { title: '出单公司', key: 'company_branch_count' },
        { title: '当年投保用户', key: 'insured_user_count' },
        { title: '当年新增用户', key: 'new_user_count' },
        { title: '累计用户数', key: 'total_user_count' }
      ],
      query: {
        year: new Date().getFullYear()
      },
      // Initialize cache
      cache: new SimpleCache()
    }
  },
  watch: {
    'query.year'() {
      // Clear cache when year changes
      this.cache.clear()
      this.triggerEmit('search', this.query)
    }
  },
  methods: {
    triggerEmit(event, data) {
      this.$emit(event, {
        year: this.query.year,
        ...data
      })
    },
    handleDownload() {
      this.triggerEmit('download', this.query)
    },
    // Convert premium data to percentages
    convertPremiumToPercentages(premiumData) {
      if (!premiumData) return []

      const data = [
        { name: '应收保费', value: parseFloat(premiumData.expected_premium / 100) || 0 },
        { name: '实收保费', value: parseFloat(premiumData.actual_premium / 100) || 0 }
      ]

      return convertToPercentages(data, 'value')
    }
  }
}
</script>

<style scoped lang="scss">
.summary {
  background-color: #fff;
  border-radius: 10px;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1rem;
  padding: 1rem;

  .summary-card {
    background-color: #f3f7fb;
    border-radius: 10px;
    padding: 20px;

    .summary-card__title {
      font-size: 1rem;
    }

    .summary-card__value {
      font-size: 1.6rem;
      font-weight: bold;
      color: #333;
    }
  }
}

.cards {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(2, 1fr);
}

@media (min-width: 1920px) {
  .cards {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
