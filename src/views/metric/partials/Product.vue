<template>
  <div>
    <search-panel v-model="query" @download="handleDownload">
      <year-select v-model="query.year" :years="filterOptions.years" />
      <el-select v-model="query.product_type" placeholder="选择险种" size="small">
        <el-option
          v-for="item in filterOptions.product_types"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </search-panel>

    <div class="cards m-extra-large-t">
      <CardPie
        :title="`${query.year}年总保费`"
        :series-option="{
          name: '保费',
          legend: ['应收保费', '实收保费'],
          data: convertPremiumToPercentages(pick('total_premium')?.[0])
        }"
      >
        <el-descriptions class="margin-top" :column="1" border>
          <el-descriptions-item label="总保费(元)">
            {{ (pick('total_premium')?.[0]?.premium / 100)?.toFixed(2) || 0 }}
            <span :class="pick('total_premium')?.[0]?.yoy ? 'text-success' : 'text-danger'">
              同比:
              {{ pick('total_premium')?.[0]?.yoy > 0 ? '+' : '' }}
              {{ pick('total_premium')?.[0]?.yoy?.toFixed(2) || 0 }}%
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="实收保费(元)">
            {{ (pick('total_premium')?.[0]?.actual_premium / 100)?.toFixed(2) || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="应收保费(元)">
            {{ (pick('total_premium')?.[0]?.expected_premium / 100)?.toFixed(2) || 0 }}
          </el-descriptions-item>
        </el-descriptions>
      </CardPie>

      <CardBar
        :title="`${query.year}年月度总保费`"
        :columns="[
          { label: '月份', prop: 'month' },
          { label: '保费(元)', prop: 'premium' }
        ]"
        :data="
          pick('monthly_premium.current')?.map((item) => ({
            month: item.month + '月',
            premium: (item.premium / 100).toFixed(2)
          })) || []
        "
        :series-option="{
          legend: [query.year + '年', query.year - 1 + '年'],
          unit: '元',
          categories: pick('monthly_premium.current')?.map((item) => item.month + '月') || [],
          data: [
            {
              name: query.year + '年',
              data: pick('monthly_premium.current')?.map((item) => (item.premium / 100).toFixed(2)) || []
            },
            {
              name: query.year - 1 + '年',
              data: pick('monthly_premium.last_year')?.map((item) => (item.premium / 100).toFixed(2)) || []
            }
          ]
        }"
      />

      <CardPie
        :title="`${query.year}年各出单公司保费`"
        :columns="[
          { label: '出单公司', prop: 'name' },
          { label: '保费(元)', prop: 'premium' }
        ]"
        :data="
          pick('company_premium')?.map((item) => ({
            name: item.name,
            premium: (item.premium / 100).toFixed(2)
          })) || []
        "
        :series-option="{
          name: '保费',
          rotate: 45,
          data: pick('company_premium')?.map((item) => ({
            name: item.name,
            value: (item.premium / 100).toFixed(2)
          }))
        }"
      />

      <CardBar
        :title="`${query.year}年各出单公司费率均值`"
        :series-option="{
          name: '费率均值',
          rotate: 45,
          unit: '‱',
          categories: pick('company_avg_rate')?.[0]?.company_avg_rate?.map((item) => item.name) || [],
          data: pick('company_avg_rate')?.[0]?.company_avg_rate?.map(
            (item) => parseFloat(item.avg_rate)?.toFixed(2) || 0
          )
        }"
      >
        <el-descriptions class="margin-top" :column="1" border>
          <el-descriptions-item label="费率均值">
            {{ parseFloat(pick('company_avg_rate')?.[0]?.avg_rate)?.toFixed(2) || 0 }}‱
          </el-descriptions-item>
        </el-descriptions>
      </CardBar>

      <CardPie
        :title="`${query.year}年各出单公司出单数量`"
        :columns="[
          { label: '出单公司', prop: 'name' },
          { label: '出单数量', prop: 'count' }
        ]"
        :data="pick('company_count')"
        :series-option="{
          name: '保单数',
          rotate: 45,
          unit: '单',
          data: pick('company_count')?.map((item) => ({
            name: item.name,
            value: item.count
          }))
        }"
      />

      <CardBar
        :title="`${query.year}年各出单公司赔付率`"
        :columns="[
          { label: '出单公司', prop: 'name' },
          { label: '已决金额(元)', prop: 'settled_amount' },
          { label: '未决金额(元)', prop: 'pending_amount' }
        ]"
        :data="
          pick('company_loss_ratio')?.map((item) => ({
            name: item.name,
            settled_amount: (item.settled_amount / 100).toFixed(2),
            pending_amount: (item.pending_amount / 100).toFixed(2)
          })) || []
        "
        :series-option="{
          legend: ['已决金额', '未决金额', '赔付率'],
          rotate: 45,
          categories: pick('company_loss_ratio')?.map((item) => item.name) || [],
          yAxis: [
            {
              type: 'value',
              name: '金额(元)',
              position: 'left',
              axisLabel: {
                formatter: '{value} 元'
              }
            },
            {
              type: 'value',
              name: '赔付率(%)',
              position: 'right',
              axisLabel: {
                formatter: '{value} %'
              }
            }
          ],
          data: [
            {
              name: '已决金额',
              stack: 'total',
              unit: '元',
              yAxisIndex: 0,
              data: pick('company_loss_ratio')?.map((item) => parseFloat(item.settled_amount / 100).toFixed(2)) || []
            },
            {
              name: '未决金额',
              stack: 'total',
              unit: '元',
              yAxisIndex: 0,
              data: pick('company_loss_ratio')?.map((item) => parseFloat(item.pending_amount / 100).toFixed(2)) || []
            },
            {
              name: '赔付率',
              type: 'line',
              unit: '%',
              yAxisIndex: 1,
              data: pick('company_loss_ratio')?.map((item) => parseFloat(item.loss_ratio).toFixed(2)) || []
            }
          ]
        }"
      />
    </div>
  </div>
</template>

<script>
import YearSelect from '../components/YearSelect.vue'
import SearchPanel from '../components/SearchPanel.vue'
import CardPie from '../components/CardPie.vue'
import CardBar from '../components/CardBar.vue'
import { SimpleCache, convertToPercentages } from '../utils'

export default {
  name: 'ProductMetrics',
  components: {
    YearSelect,
    SearchPanel,
    CardPie,
    CardBar
  },
  props: {
    filterOptions: {
      type: Object,
      default: () => ({})
    },
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      query: {
        product_type: '',
        year: new Date().getFullYear()
      },
      // Initialize cache
      cache: new SimpleCache()
    }
  },
  watch: {
    filterOptions: {
      immediate: true,
      handler(newVal) {
        this.query.year = newVal.years?.[0] || new Date().getFullYear()
        this.query.product_type = newVal.product_types?.[0]?.value
      }
    },
    query: {
      handler() {
        // Clear cache when query changes
        this.cache.clear()
      },
      deep: true
    }
  },
  created() {
    this.query.year = this.filterOptions.years?.[0] || new Date().getFullYear()
    this.query.product_type = this.filterOptions.product_types?.[0]?.value
  },
  methods: {
    pick(dataKey) {
      // Create a cache key based on the dataKey and query parameters
      const cacheKey = `${dataKey}-${this.query.product_type}-${this.query.year}`

      // Return cached result if available
      return this.cache.get(cacheKey, () => {
        if (dataKey.indexOf('.') > -1) {
          const keys = dataKey.split('.')
          return this.value?.[keys[0]]?.[keys[1]]?.filter((item) => item.product_type === this.query.product_type) || []
        } else {
          return this.value?.[dataKey]?.filter((item) => item.product_type === this.query.product_type) || []
        }
      })
    },
    triggerEmit(event, data) {
      this.$emit(event, {
        year: this.query.year,
        ...data
      })
    },
    handleDownload() {
      this.triggerEmit('download', this.query)
    },
    // Convert premium data to percentages
    convertPremiumToPercentages(premiumData) {
      if (!premiumData) return []

      const data = [
        { name: '应收保费', value: parseFloat(premiumData.expected_premium / 100) || 0 },
        { name: '实收保费', value: parseFloat(premiumData.actual_premium / 100) || 0 }
      ]

      return convertToPercentages(data, 'value')
    }
  }
}
</script>

<style scoped lang="scss">
.cards {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(2, 1fr);
}

@media (min-width: 1920px) {
  .cards {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
