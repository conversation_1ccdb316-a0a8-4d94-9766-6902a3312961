<template>
  <div>
    <search-panel v-model="query" @download="handleDownload">
      <year-select v-model="query.year" :years="filterOptions.years" />
      <el-select v-model="query.company_id" placeholder="选择保险公司">
        <el-option v-for="company in companies" :key="company.id" :label="company.name" :value="company.id" />
      </el-select>
      <el-select v-model="query.company_branch_id" placeholder="选择出单公司" clearable>
        <el-option v-for="branch in branches" :key="branch.id" :label="branch.name" :value="branch.id" />
      </el-select>
    </search-panel>

    <div class="cards m-extra-large-t">
      <CardPie
        v-if="!showBranch"
        :title="`${query.year}年总保费`"
        :show-percentage="true"
        :series-option="{
          name: '保费',
          legend: ['应收保费', '实收保费'],
          data: convertPremiumToPercentages(pick('total_premium')?.[0])
        }"
      >
        <el-descriptions class="margin-top" :column="1" border>
          <el-descriptions-item label="总保费(元)">
            {{ (pick('total_premium')?.[0]?.premium / 100)?.toFixed(2) || 0 }}
            <span :class="pick('total_premium')?.[0]?.yoy ? 'text-success' : 'text-danger'">
              同比:
              {{ pick('total_premium')?.[0]?.yoy > 0 ? '+' : '' }}
              {{ pick('total_premium')?.[0]?.yoy?.toFixed(2) || 0 }}%
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="实收保费(元)">
            {{ (pick('total_premium')?.[0]?.actual_premium / 100)?.toFixed(2) || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="应收保费(元)">
            {{ (pick('total_premium')?.[0]?.expected_premium / 100)?.toFixed(2) || 0 }}
          </el-descriptions-item>
        </el-descriptions>
      </CardPie>

      <CardBar
        v-if="showBranch"
        :title="`${query.year}年月度总保费`"
        :columns="[
          { label: '月份', prop: 'month' },
          { label: '保费(元)', prop: 'premium' }
        ]"
        :data="
          pick('monthly_premium.current')?.map((item) => ({
            month: item.month + '月',
            premium: (item.premium / 100).toFixed(2)
          })) || []
        "
        :series-option="{
          legend: [query.year + '年', query.year - 1 + '年'],
          unit: '元',
          categories: pick('monthly_premium.current')?.map((item) => item.month + '月') || [],
          data: [
            {
              name: query.year + '年',
              data: pick('monthly_premium.current')?.map((item) => (item.premium / 100).toFixed(2)) || []
            },
            {
              name: query.year - 1 + '年',
              data: pick('monthly_premium.last_year')?.map((item) => (item.premium / 100).toFixed(2)) || []
            }
          ]
        }"
      />

      <CardPie
        v-if="showBranch"
        :title="`${query.year}年各险种保费`"
        :columns="[
          { label: '险种', prop: 'name' },
          { label: '保费(元)', prop: 'premium' }
        ]"
        :data="
          pick('product_premium')?.map((item) => ({
            name: item.name,
            premium: (item.premium / 100).toFixed(2)
          })) || []
        "
        :series-option="{
          name: '保费',
          rotate: 45,
          data: pick('product_premium')?.map((item) => ({
            name: item.name,
            value: (item.premium / 100).toFixed(2)
          }))
        }"
      />

      <CardPie
        v-if="showBranch"
        :title="`${query.year}年各险种出单数量`"
        :columns="[
          { label: '险种', prop: 'name' },
          { label: '出单数量', prop: 'count' }
        ]"
        :data="pick('product_count')"
        :series-option="{
          name: '保单数',
          rotate: 45,
          unit: '单',
          data: pick('product_count')?.map((item) => ({
            name: item.name,
            value: item.count
          }))
        }"
      />

      <CardBar
        v-if="showBranch"
        :title="`${query.year}年各险种赔付率`"
        :columns="[
          { label: '险种', prop: 'name' },
          { label: '已决金额(元)', prop: 'settled_amount' },
          { label: '未决金额(元)', prop: 'pending_amount' }
        ]"
        :data="
          pick('product_loss_ratio')?.map((item) => ({
            name: item.product_type,
            settled_amount: (item.settled_amount / 100).toFixed(2),
            pending_amount: (item.pending_amount / 100).toFixed(2)
          })) || []
        "
        :series-option="{
          legend: ['已决金额', '未决金额', '赔付率'],
          rotate: 45,
          categories: pick('product_loss_ratio')?.map((item) => item.product_type) || [],
          yAxis: [
            {
              type: 'value',
              name: '金额(元)',
              position: 'left',
              axisLabel: {
                formatter: '{value} 元'
              }
            },
            {
              type: 'value',
              name: '赔付率(%)',
              position: 'right',
              axisLabel: {
                formatter: '{value} %'
              }
            }
          ],
          data: [
            {
              name: '已决金额',
              stack: 'total',
              unit: '元',
              yAxisIndex: 0,
              data: pick('product_loss_ratio')?.map((item) => parseFloat(item.settled_amount / 100).toFixed(2)) || []
            },
            {
              name: '未决金额',
              stack: 'total',
              unit: '元',
              yAxisIndex: 0,
              data: pick('product_loss_ratio')?.map((item) => parseFloat(item.pending_amount / 100).toFixed(2)) || []
            },
            {
              name: '赔付率',
              type: 'line',
              unit: '%',
              yAxisIndex: 1,
              data: pick('product_loss_ratio')?.map((item) => parseFloat(item.loss_ratio).toFixed(2)) || []
            }
          ]
        }"
      />

      <CardPie
        v-if="!showBranch"
        :title="`${query.year}年各出单公司保费`"
        :columns="[
          { label: '出单公司', prop: 'name' },
          { label: '保费(元)', prop: 'premium' }
        ]"
        :data="
          pick('branch_premium')?.map((item) => ({
            name: item.name,
            premium: (item.premium / 100).toFixed(2)
          })) || []
        "
        :series-option="{
          name: '保费',
          rotate: 45,
          data: pick('branch_premium')?.map((item) => ({
            name: item.name,
            value: (item.premium / 100).toFixed(2)
          }))
        }"
      />

      <CardPie
        v-if="!showBranch"
        :title="`${query.year}年各出单公司出单数量`"
        :columns="[
          { label: '出单公司', prop: 'name' },
          { label: '出单数量', prop: 'count' }
        ]"
        :data="pick('branch_count')"
        :series-option="{
          name: '保单数',
          rotate: 45,
          unit: '单',
          data: pick('branch_count')?.map((item) => ({
            name: item.name,
            value: item.count
          }))
        }"
      />

      <CardBar
        v-if="!showBranch"
        :title="`${query.year}年各出单公司赔付率`"
        :columns="[
          { label: '出单公司', prop: 'name' },
          { label: '已决金额(元)', prop: 'settled_amount' },
          { label: '未决金额(元)', prop: 'pending_amount' }
        ]"
        :data="
          pick('branch_loss_ratio')?.map((item) => ({
            name: item.name,
            settled_amount: (item.settled_amount / 100).toFixed(2),
            pending_amount: (item.pending_amount / 100).toFixed(2)
          })) || []
        "
        :series-option="{
          legend: ['已决金额', '未决金额', '赔付率'],
          rotate: 45,
          categories: pick('branch_loss_ratio')?.map((item) => item.name) || [],
          yAxis: [
            {
              type: 'value',
              name: '金额(元)',
              position: 'left',
              axisLabel: {
                formatter: '{value} 元'
              }
            },
            {
              type: 'value',
              name: '赔付率(%)',
              position: 'right',
              axisLabel: {
                formatter: '{value} %'
              }
            }
          ],
          data: [
            {
              name: '已决金额',
              stack: 'total',
              unit: '元',
              yAxisIndex: 0,
              data: pick('branch_loss_ratio')?.map((item) => parseFloat(item.settled_amount / 100).toFixed(2)) || []
            },
            {
              name: '未决金额',
              stack: 'total',
              unit: '元',
              yAxisIndex: 0,
              data: pick('branch_loss_ratio')?.map((item) => parseFloat(item.pending_amount / 100).toFixed(2)) || []
            },
            {
              name: '赔付率',
              type: 'line',
              unit: '%',
              yAxisIndex: 1,
              data: pick('branch_loss_ratio')?.map((item) => parseFloat(item.loss_ratio).toFixed(2)) || []
            }
          ]
        }"
      />
    </div>
  </div>
</template>

<script>
import SearchPanel from '../components/SearchPanel.vue'
import YearSelect from '../components/YearSelect.vue'
import CardPie from '../components/CardPie.vue'
import CardBar from '../components/CardBar.vue'
import * as companyApi from '@/apis/company'
import { SimpleCache, convertToPercentages } from '../utils'

export default {
  name: 'Company',
  components: {
    SearchPanel,
    YearSelect,
    CardPie,
    CardBar
  },
  props: {
    filterOptions: {
      type: Object,
      default: () => ({})
    },
    value: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    branches() {
      return this.companies.find((company) => company.id === this.query.company_id)?.branches
    },
    showBranch() {
      return !!this.query.company_branch_id
    }
  },
  data() {
    return {
      query: {
        company_id: '',
        company_branch_id: '',
        year: new Date().getFullYear()
      },
      companies: [],
      // Initialize cache
      cache: new SimpleCache()
    }
  },
  watch: {
    query: {
      handler() {
        // Clear cache when query changes
        this.cache.clear()
      },
      deep: true
    }
  },
  created() {
    this.fetchCompanies()
  },
  methods: {
    pick(dataKey) {
      // Create a cache key based on the dataKey and query parameters
      const cacheKey = `${dataKey}-${this.query.company_id}-${this.query.company_branch_id}-${this.query.year}`

      // Return cached result if available
      return this.cache.get(cacheKey, () => {
        const filter = (values) => {
          return values.filter((item) => {
            const matchesCompany = item.company_id === this.query.company_id
            const matchesBranch = this.query.company_branch_id
              ? item.company_branch_id === this.query.company_branch_id
              : true
            return matchesCompany && matchesBranch
          })
        }
        if (dataKey.indexOf('.') > -1) {
          const keys = dataKey.split('.')
          return filter(this.value?.[keys[0]]?.[keys[1]] || [])
        } else {
          return filter(this.value?.[dataKey] || [])
        }
      })
    },
    async fetchCompanies() {
      const data = await companyApi.getCompaniesDict()
      this.companies = data.data

      this.query.company_id = this.companies[0]?.id || ''
    },
    triggerEmit(event, data) {
      this.$emit(event, {
        year: this.query.year,
        ...data
      })
    },
    handleDownload() {
      this.triggerEmit('download', this.query)
    },
    // Convert premium data to percentages
    convertPremiumToPercentages(premiumData) {
      if (!premiumData) return []

      const data = [
        { name: '应收保费', value: parseFloat(premiumData.expected_premium / 100) || 0 },
        { name: '实收保费', value: parseFloat(premiumData.actual_premium / 100) || 0 }
      ]

      return convertToPercentages(data, 'value')
    }
  }
}
</script>

<style scoped lang="scss">
.cards {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(2, 1fr);
}

@media (min-width: 1920px) {
  .cards {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
