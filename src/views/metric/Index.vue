<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x container">
    <el-select v-model="datasource" class="m-extra-large-b" @change="fetchMetrics(true)">
      <el-option label="按自有业务" value="our_user" />
      <el-option label="按自有产品" value="our_product" />
    </el-select>

    <el-tabs v-model="activePanel" @tab-click="() => fetchMetrics()">
      <el-tab-pane v-for="panel in panels" :key="panel.value" :label="panel.label" :name="panel.value" lazy>
        <component
          :is="panel.component"
          :value="data"
          @search="handleSearch"
          @download="handleDownload"
          :filter-options="filterOptions"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import { panels, dataSources } from './constants'
import * as metricApi from '@/apis/metric'
import Product from './partials/Product.vue'
import Finance from './partials/Finance.vue'
import Platform from './partials/Platform.vue'
import Company from './partials/Company.vue'
import User from './partials/User.vue'
import Claim from './partials/Claim.vue'
import { Loading } from 'element-ui'

export default {
  name: 'MetricIndex',
  components: {
    Product,
    Finance,
    Platform,
    Company,
    User,
    Claim
  },
  data() {
    return {
      filterOptions: {},
      searchData: {},
      datasource: 'our_user',
      activePanel: 'platform',
      passivePanels: ['user'],
      data: {},
      panels,
      dataSources
    }
  },
  async created() {
    await this.fetchMetrics()
    await this.fetchFilterOptions()
  },
  methods: {
    async fetchFilterOptions() {
      try {
        this.filterOptions = await metricApi.fetchFilterOptions()
      } catch (error) {
        console.error('Error fetching filter options:', error)
      }
    },
    async fetchMetrics(ignorePassive = false) {
      // If the active panel is passive, do not fetch data
      if (this.passivePanels.includes(this.activePanel) && !ignorePassive) {
        return
      }

      const loading = Loading.service({ fullscreen: true })

      try {
        this.data = await metricApi.fetchMetrics({
          datasource: this.datasource,
          panel: this.activePanel,
          ...this.searchData
        })
      } catch (error) {
        console.error('Error fetching metrics:', error)
      } finally {
        loading.close()
      }
    },
    async handleSearch(data) {
      this.searchData = data
      await this.fetchMetrics(true)
    },
    handleDownload(data) {
      console.log(data)
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  /deep/ .el-tabs__header {
    background-color: #fff;
    padding-left: 20px;
    padding-right: 20px;
    border-radius: 10px;

    .el-tabs__item {
      height: 50px;
      line-height: 50px;
    }
  }

  /deep/ .el-tabs__nav-wrap::after {
    display: none;
  }
}
</style>
