/**
 * Calculate percentages for chart data where the sum equals 100%
 * @param {Array} data - Array of data objects
 * @param {string} valueKey - Key to access the value in each data object
 * @returns {Array} - Array of data objects with percentage values
 */
export function convertToPercentages(data, valueKey = 'value') {
  if (!data || !Array.isArray(data) || data.length === 0) {
    return []
  }

  // Calculate total
  const total = data.reduce((sum, item) => {
    const value = parseFloat(item[valueKey]) || 0
    return sum + value
  }, 0)

  // Convert to percentages
  if (total === 0) {
    return data.map((item) => ({
      ...item,
      originalValue: item[valueKey],
      [valueKey]: 0
    }))
  }

  return data.map((item) => {
    const value = parseFloat(item[valueKey]) || 0
    return {
      ...item,
      originalValue: item[valueKey],
      [valueKey]: parseFloat(((value / total) * 100).toFixed(2))
    }
  })
}

/**
 * Simple caching mechanism using a Map
 */
export class SimpleCache {
  constructor() {
    this.cache = new Map()
  }

  /**
   * Get cached value or compute and cache it
   * @param {string} key - Cache key
   * @param {Function} computeFn - Function to compute the value if not cached
   * @returns {*} Cached or computed value
   */
  get(key, computeFn) {
    if (this.cache.has(key)) {
      return this.cache.get(key)
    }

    const value = computeFn()
    this.cache.set(key, value)
    return value
  }

  /**
   * Clear the cache
   */
  clear() {
    this.cache.clear()
  }

  /**
   * Delete a specific key from cache
   * @param {string} key - Cache key to delete
   */
  delete(key) {
    this.cache.delete(key)
  }
}
