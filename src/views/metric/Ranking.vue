<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <el-select v-model="filter.year" placeholder="选择统计年份">
      <el-option :value="-1" label="所有年度"></el-option>
      <el-option v-for="year in years" :key="year" :value="year" :label="year + '年'"></el-option>
    </el-select>
    <el-radio-group v-model="filter.datasource" bordered class="m-extra-large-l">
      <el-radio label="our_user" border>按自有业务</el-radio>
      <el-radio label="our_product" border>按自有产品</el-radio>
    </el-radio-group>

    <el-card shadow="never" class="m-extra-large-t data-r-filter">
      <el-alert show-icon type="warning" :title="alertTitle" :closable="false" />

      <el-row v-for="(items, k) in conditions" :key="k" :gutter="24">
        <el-col v-for="(item, i) in items" :key="i" :span="4">
          <el-radio :label="item.value" v-model="filter.by" @change="item?.trigger?.()">
            {{ item.label }}
          </el-radio>
        </el-col>
      </el-row>
    </el-card>

    <el-button class="m-extra-large-t" icon="fas fa-download" type="primary" @click="exportModal.visible = true">
      导出
    </el-button>
    <el-dialog
      title="导出"
      :visible.sync="exportModal.visible"
      width="50%"
      :before-close="() => (exportModal.visible = false)"
    >
      <el-checkbox-group v-model="exportModal.sheets">
        <el-checkbox v-for="sheet in sheets" :key="sheet.value" :label="sheet.value">
          {{ sheet.label }}
        </el-checkbox>
      </el-checkbox-group>
      <el-button class="m-extra-large-t" type="primary" @click="handleExport">确定</el-button>
    </el-dialog>
    <ranking-view id="pdf" :dataset="dataset" :label="label" :user-label="userLabel" />
  </div>
</template>

<script>
import * as dataApi from '@/apis/metric'
import chinaAreadata from '@/utils/areadata.json'
import overseaAreadata from '@/utils/regions'
import { Loading } from 'element-ui'
import RankingView from '@/components/metric/RankingView'
import dayjs from 'dayjs'

export default {
  name: 'MetricRanking',
  components: { RankingView },
  data() {
    return {
      dataset: {},
      chinaAreadata,
      overseaAreadata,
      years: [],
      sheets: [
        { label: '按保费排名', value: 'by_premium' },
        { label: '按保单数排名', value: 'by_policy_count' },
        { label: '按费率均值排名', value: 'by_rate_avg' },
        { label: '按赔付率排名', value: 'by_claim_payment_rate' },
        { label: '按出险率排名', value: 'by_claim_rate' },
        { label: '按赔款排名（万元）', value: 'by_claim_payment_amount' },
        { label: '5万元以上理赔案件排名', value: 'by_five_ten_thousand_claim_amount_rate' },
        { label: '按结案率排名', value: 'by_claim_finished_rate' }
      ],
      exportModal: {
        visible: false,
        sheets: [
          'by_user',
          'by_premium',
          'by_policy_count',
          'by_rate_avg',
          'by_claim_payment_rate',
          'by_claim_rate',
          'by_claim_payment_amount',
          'by_five_ten_thousand_claim_amount_rate',
          'by_claim_finished_rate'
        ]
      },
      byFields: [
        { label: '被保险人', value: 'insured' },
        { label: '投保人', value: 'policyholder' },
        { label: '投保用户', value: 'user' },
        { label: '代理人', value: 'agent' },
        { label: '保险公司', value: 'company' },
        { label: '出单公司', value: 'company_branch' },
        { label: '业务员', value: 'salesman' },
        { label: '险种', value: 'type' },
        { label: '标的类别', value: 'subject' },
        { label: '贸易类型', value: 'trade_type' },
        { label: '包装方式', value: 'packing_method' },
        { label: '包装方式(理赔)', value: 'claim_packaging_method' },
        { label: '起运地(国/地区)', value: 'departure' },
        { label: '目的地(国/地区)', value: 'destination' },
        { label: '运输方式', value: 'transport_method' },
        { label: '装载方式', value: 'loading_method' },
        { label: '装载方式(理赔)', value: 'claim_loading_method' },
        { label: '出险原因', value: 'claim_loss_reason' },
        { label: '理赔员', value: 'claim_operator' },
        { label: '保司理赔员', value: 'claim_external_adjuster' },
        { label: '卖家', value: 'claim_seller' },
        { label: '买家', value: 'claim_buyer' }
      ],
      rawCompanies: [],
      filter: {
        by: 'index',
        datasource: 'our_user',
        year: -1
      }
    }
  },
  computed: {
    alertTitle() {
      return '排行榜数据每日凌晨0时更新，当前数据截止时间为：' + dayjs().format('YYYY年MM月DD日') + ' 0时'
    },
    label() {
      return this.byFields.find((item) => item.value === this.filter.by)?.label || '出单公司'
    },
    userLabel() {
      return ['index', 'salesman'].includes(this.filter.by) ? '业务员' : '代理人'
    },
    conditions() {
      // 6 items one row
      return this.byFields.reduce((acc, cur, idx) => {
        if (idx % 6 === 0) {
          acc.push([cur])
        } else {
          acc[acc.length - 1].push(cur)
        }
        return acc
      }, [])
    }
  },
  watch: {
    filter: {
      immediate: true,
      deep: true,
      handler() {
        this.fetchRankingDataset()
      }
    },
    dataset: {
      immediate: true,
      deep: true,
      handler() {
        if (this.dataset?.by_user === undefined) {
          this.sheets = this.sheets.filter((s) => s.value !== 'by_user')
          this.exportModal.sheets = this.exportModal.sheets.filter((s) => s !== 'by_user')
        } else {
          this.exportModal.sheets.push('by_user')
          this.sheets.unshift({ label: '按客户数排名', value: 'by_user' })
        }
      }
    }
  },
  created() {
    this.fetchFilterOptions()
  },
  methods: {
    async fetchFilterOptions() {
      const data = await dataApi.fetchFilterOptions()
      this.years = data?.years || []
    },
    handleExport() {
      this.exportModal.visible = false
      window.open(
        dataApi.downloadRankingXlsx({
          by: this.filter.by,
          datasource: this.filter.datasource,
          year: this.filter.year,
          sheets: this.exportModal.sheets,
          label: this.label,
          user_label: this.userLabel
        }),
        '_blank'
      )
    },
    async fetchRankingDataset() {
      const loading = Loading.service({ fullscreen: true, text: '加载中' })
      try {
        const dataset = await dataApi.fetchRanking({
          by: this.filter.by,
          year: this.filter.year,
          datasource: this.filter.datasource
        })

        let newDataset = {}
        for (const key in dataset) {
          newDataset[key] = []
          for (const item of dataset[key]) {
            let newItem = {}
            for (const k in item) {
              newItem[k] = typeof item[k] === 'number' && !k.includes('count') ? item[k].toFixed(2) : item[k]
            }
            newDataset[key].push(newItem)
          }
        }
        this.dataset = newDataset
      } finally {
        loading.close()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.data-r-box {
  margin-top: 20px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.data-r-box-one {
  grid-template-columns: repeat(1, 1fr);
}

.data-r-filter {
  & .el-row:not(:first-child) {
    margin-top: 20px;
  }
}
</style>
