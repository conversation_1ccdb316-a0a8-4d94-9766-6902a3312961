<template>
  <base-card :title="title">
    <template #content>
      <slot v-if="columns.length === 0" />
      <base-table v-else :columns="columns" :data="data" />
    </template>

    <template #chart>
      <v-chart :style="{ height: '300px', marginTop: '1rem' }" :option="option" />
    </template>
  </base-card>
</template>
<script>
import BaseCard from './BaseCard.vue'
import BaseTable from './BaseTable.vue'

import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { Bar<PERSON><PERSON>, LineChart } from 'echarts/charts'
import { TooltipComponent, LegendComponent, GridComponent, DataZoomComponent } from 'echarts/components'
import VChart from 'vue-echarts'

use([CanvasRenderer, Bar<PERSON>hart, LineChart, TooltipComponent, LegendComponent, GridComponent, DataZoomComponent])

export default {
  name: 'CardBar',
  components: {
    BaseCard,
    BaseTable,
    VC<PERSON>
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    columns: {
      type: Array,
      default: () => []
    },
    data: {
      type: Array,
      default: () => []
    },
    seriesOption: {
      type: Object,
      default: () => ({})
    },
    showLabel: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    option() {
      return {
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            let tooltip = `${params[0]?.axisValue}<br/>`
            params.forEach((item) => {
              const unit = this.seriesOption?.data?.[item.seriesIndex]?.unit || this.seriesOption?.unit || ''
              tooltip += `${item.marker}${item.seriesName}：${item.data}${unit}<br/>`
            })
            return tooltip
          }
        },
        legend: {
          data: this.seriesOption.legend || []
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        ...this.axis,
        dataZoom: this.seriesOption?.zoom ? this.dataZoom : [],
        series: this.isArray(this.seriesOption.data)
          ? this.seriesOption.data.map((item) => ({
              type: item.type || 'bar',
              barWidth: item.width || '15px',
              label: this.showLabel
                ? {
                    show: true,
                    position: 'top',
                    formatter: (params) => {
                      const unit = item.unit || this.seriesOption?.unit || ''
                      return `${params.data}${unit}`
                    }
                  }
                : { show: false },
              ...item
            }))
          : [
              {
                name: this.seriesOption.name || '数据',
                type: 'bar',
                barWidth: '15px',
                label: this.showLabel
                  ? {
                      show: true,
                      position: 'top',
                      formatter: (params) => {
                        const unit = this.seriesOption?.unit || ''
                        return `${params.data}${unit}`
                      }
                    }
                  : { show: false },
                data: this.seriesOption.data
              }
            ]
      }
    },
    axis() {
      let axis = {}
      if (this.seriesOption?.xAxis) {
        axis.xAxis = this.seriesOption.xAxis
      }

      if (this.seriesOption?.yAxis) {
        axis.yAxis = this.seriesOption.yAxis
        if (!this.seriesOption?.xAxis) {
          axis.xAxis = {
            type: 'category',
            data: this.seriesOption.categories || [],
            axisLabel: {
              rotate: this.seriesOption?.rotate || 0,
              interval: 0
            }
          }
        }
      }

      if (this.seriesOption?.xAxis || this.seriesOption?.yAxis) {
        return axis
      }

      const direction = this.seriesOption?.direction || 'vertical'
      return {
        [direction === 'vertical' ? 'yAxis' : 'xAxis']: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        [direction === 'vertical' ? 'xAxis' : 'yAxis']: {
          type: 'category',
          data: this.seriesOption.categories || [],
          axisLabel: {
            rotate: this.seriesOption?.rotate || 0,
            interval: 0
          }
        }
      }
    },
    dataZoom() {
      return [
        {
          type: 'slider',
          realtime: true,
          startValue: 0,
          endValue: this.seriesOption?.zoomSize || 10,
          height: 4,
          handleSize: 14, // 两边的按钮大小
          fillerColor: 'rgba(17, 100, 210, 0.42)', // 滚动条颜色
          borderColor: 'rgba(17, 100, 210, 0.12)',
          showDetail: false, // 拖拽时是否展示滚动条两侧的文字
          top: '96%',
          zoomLock: true, // 是否只平移不缩放
          moveOnMouseMove: true, //鼠标移动能触发数据窗口平移
          zoomOnMouseWheel: true //鼠标移动能触发数据窗口缩放
        },
        {
          type: 'inside', // 支持内部鼠标滚动平移
          startValue: 0,
          endValue: this.seriesOption?.zoomSize || 10,
          zoomOnMouseWheel: false, // 关闭滚轮缩放
          moveOnMouseWheel: true, // 开启滚轮平移
          moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
        }
      ]
    }
  },
  methods: {
    isArray(value) {
      return Array.isArray(value) && value.length > 0 && value[0] && typeof value[0] === 'object'
    }
  }
}
</script>
