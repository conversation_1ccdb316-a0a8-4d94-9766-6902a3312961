# 图表组件使用说明

## CardPie 饼图组件

### 新增功能：百分比显示控制

通过 `showPercentage` 参数控制饼图是否显示百分比格式。

#### 基本用法

```vue
<template>
  <!-- 显示百分比格式 -->
  <CardPie
    title="各险种保费分布"
    :show-percentage="true"
    :series-option="{
      name: '保费',
      data: [
        { name: '车险', value: 33.33 },
        { name: '财险', value: 66.67 }
      ]
    }"
  />

  <!-- 显示原始数值格式 -->
  <CardPie
    title="各险种保费金额"
    :show-percentage="false"
    :series-option="{
      name: '保费',
      unit: '万元',
      data: [
        { name: '车险', value: 1000 },
        { name: '财险', value: 2000 }
      ]
    }"
  />
</template>
```

#### 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| showPercentage | Boolean | false | 是否显示百分比格式 |

#### 显示效果

- `showPercentage: true`: 标签和提示框显示为 "33.33%" 格式
- `showPercentage: false`: 标签和提示框显示为 "1000万元" 格式

## CardBar 柱状图组件

### 新增功能：柱体数值显示控制

通过 `showLabel` 参数控制柱状图是否在柱体上显示数值。

#### 基本用法

```vue
<template>
  <!-- 显示柱体数值 -->
  <CardBar
    title="各险种赔付率"
    :show-label="true"
    :series-option="{
      name: '赔付率',
      unit: '%',
      categories: ['车险', '财险', '意外险'],
      data: [65.5, 45.2, 78.9]
    }"
  />

  <!-- 不显示柱体数值 -->
  <CardBar
    title="月度保费趋势"
    :show-label="false"
    :series-option="{
      name: '保费',
      unit: '万元',
      categories: ['1月', '2月', '3月'],
      data: [1000, 1200, 1500]
    }"
  />
</template>
```

#### 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| showLabel | Boolean | false | 是否在柱体上显示数值 |

#### 显示效果

- `showLabel: true`: 在每个柱体顶部显示对应的数值，如 "65.5%"
- `showLabel: false`: 不显示柱体数值，只在鼠标悬停时显示提示框

#### 多系列支持

组件支持多系列数据，每个系列可以有独立的单位：

```vue
<CardBar
  :show-label="true"
  :series-option="{
    categories: ['1月', '2月', '3月'],
    data: [
      { name: '保费', data: [1000, 1200, 1500], unit: '万元' },
      { name: '赔付率', data: [65, 70, 68], unit: '%' }
    ]
  }"
/>
```

## 数据转换工具函数

### convertToPercentages

将数据数组转换为百分比格式，确保总和为100%。

```javascript
import { convertToPercentages } from '../utils'

const data = [
  { name: '车险', value: 1000 },
  { name: '财险', value: 2000 }
]

const percentageData = convertToPercentages(data, 'value')
// 结果: [
//   { name: '车险', value: 33.33, originalValue: 1000 },
//   { name: '财险', value: 66.67, originalValue: 2000 }
// ]
```

## 最佳实践

1. **饼图百分比显示**: 当需要展示比例关系时使用 `showPercentage: true`
2. **柱状图数值显示**: 当数据点较少且需要精确数值时使用 `showLabel: true`
3. **单位一致性**: 确保同一图表中的单位保持一致
4. **数据预处理**: 使用 `convertToPercentages` 函数处理需要百分比显示的数据
