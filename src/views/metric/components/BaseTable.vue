<template>
  <div class="base-table">
    <el-table :data="newValues" style="width: 100%" border stripe size="small">
      <el-table-column v-for="column in newColumns" :key="column.prop" :label="column.label" :prop="column.prop" />
    </el-table>
  </div>
</template>
<script>
export default {
  name: 'BaseTable',
  props: {
    columns: {
      type: Array,
      default: () => []
    },
    data: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    newColumns() {
      if (this.data.length <= 3) return this.columns

      const cols = []
      this.columns.forEach((col) => {
        cols.push(col) // 左边的列
      })
      this.columns.forEach((col) => {
        cols.push({
          ...col,
          prop: col.prop + '_odd',
          label: col.label
        }) // 右边的列
      })
      return cols
    },
    newValues() {
      if (this.data.length <= 3) return this.data

      // 计算左右行数：左边是偶数索引个数，右边是奇数索引个数
      const leftData = []
      const rightData = []

      this.data.forEach((item, index) => {
        if (index % 2 === 0) {
          leftData.push(item)
        } else {
          rightData.push(item)
        }
      })

      // 取两边的最大长度作为行数
      const maxLen = Math.max(leftData.length, rightData.length)

      const dataset = []

      for (let i = 0; i < maxLen; i++) {
        const leftItem = leftData[i] || {}
        const rightItem = rightData[i] || {}

        const newItem = {}

        // 复制左边偶数行原字段
        Object.keys(leftItem).forEach((key) => {
          newItem[key] = leftItem[key]
        })

        // 复制右边奇数行字段，字段名加 _odd
        Object.keys(rightItem).forEach((key) => {
          newItem[key + '_odd'] = rightItem[key]
        })

        dataset.push(newItem)
      }

      return dataset
    }
  }
}
</script>
