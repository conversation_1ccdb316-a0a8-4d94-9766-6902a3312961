import { mount } from '@vue/test-utils'
import CardBar from '../CardBar.vue'

// Mock <PERSON>s
jest.mock('vue-echarts', () => ({
  name: 'VChart',
  template: '<div class="mock-chart"></div>',
  props: ['option', 'style']
}))

describe('CardBar.vue', () => {
  const defaultProps = {
    title: 'Test Bar Chart',
    seriesOption: {
      name: '测试数据',
      unit: '%',
      categories: ['项目A', '项目B'],
      data: [50, 75]
    }
  }

  it('renders correctly with default props', () => {
    const wrapper = mount(CardBar, {
      props: defaultProps
    })
    expect(wrapper.exists()).toBe(true)
  })

  it('shows labels when showLabel is true', () => {
    const wrapper = mount(CardBar, {
      props: {
        ...defaultProps,
        showLabel: true
      }
    })

    const option = wrapper.vm.option
    const series = option.series[0]
    expect(series.label.show).toBe(true)
    expect(series.label.position).toBe('top')
    expect(typeof series.label.formatter).toBe('function')
  })

  it('hides labels when showLabel is false', () => {
    const wrapper = mount(CardBar, {
      props: {
        ...defaultProps,
        showLabel: false
      }
    })

    const option = wrapper.vm.option
    const series = option.series[0]
    expect(series.label.show).toBe(false)
  })

  it('formats label with correct unit', () => {
    const wrapper = mount(CardBar, {
      props: {
        ...defaultProps,
        showLabel: true
      }
    })

    const option = wrapper.vm.option
    const series = option.series[0]
    const formattedLabel = series.label.formatter({ data: 50 })
    expect(formattedLabel).toBe('50%')
  })

  it('handles multiple series with labels', () => {
    const wrapper = mount(CardBar, {
      props: {
        title: 'Multi Series Chart',
        showLabel: true,
        seriesOption: {
          unit: '%',
          categories: ['项目A', '项目B'],
          data: [
            { name: '系列1', data: [50, 75], unit: '%' },
            { name: '系列2', data: [60, 80], unit: '%' }
          ]
        }
      }
    })

    const option = wrapper.vm.option
    expect(option.series).toHaveLength(2)
    option.series.forEach(series => {
      expect(series.label.show).toBe(true)
      expect(series.label.position).toBe('top')
    })
  })
})
