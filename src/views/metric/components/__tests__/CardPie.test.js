import { mount } from '@vue/test-utils'
import Card<PERSON>ie from '../CardPie.vue'

// Mock <PERSON>s
jest.mock('vue-echarts', () => ({
  name: 'VChart',
  template: '<div class="mock-chart"></div>',
  props: ['option', 'style']
}))

describe('CardPie.vue', () => {
  const defaultProps = {
    title: 'Test Pie Chart',
    seriesOption: {
      name: '测试数据',
      data: [
        { name: '项目A', value: 100 },
        { name: '项目B', value: 200 }
      ]
    }
  }

  it('renders correctly with default props', () => {
    const wrapper = mount(CardPie, {
      props: defaultProps
    })
    expect(wrapper.exists()).toBe(true)
  })

  it('shows percentage format when showPercentage is true', () => {
    const wrapper = mount(CardPie, {
      props: {
        ...defaultProps,
        showPercentage: true
      }
    })

    const option = wrapper.vm.option
    expect(option.tooltip.formatter).toContain('{c}%')
    expect(option.label.formatter).toContain('{c}%')
  })

  it('shows value format when showPercentage is false', () => {
    const wrapper = mount(CardPie, {
      props: {
        ...defaultProps,
        showPercentage: false
      }
    })

    const option = wrapper.vm.option
    expect(option.tooltip.formatter).toContain('元')
    expect(option.label.formatter).toContain('元')
  })

  it('uses custom unit when provided', () => {
    const wrapper = mount(CardPie, {
      props: {
        ...defaultProps,
        seriesOption: {
          ...defaultProps.seriesOption,
          unit: '万元'
        },
        showPercentage: false
      }
    })

    const option = wrapper.vm.option
    expect(option.tooltip.formatter).toContain('万元')
    expect(option.label.formatter).toContain('万元')
  })
})
