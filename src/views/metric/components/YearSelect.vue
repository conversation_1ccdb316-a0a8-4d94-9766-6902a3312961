<template>
  <el-select v-model="modelValue" placeholder="请选择年份" :clearable="clearable" @change="handleSelect">
    <el-option v-for="item in years" :key="item" :label="item" :value="item" />
  </el-select>
</template>
<script>
export default {
  name: 'YearSelect',
  props: {
    value: {
      type: [Number, String],
      default: new Date().getFullYear()
    },
    years: {
      type: Array,
      required: false
    },
    clearable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      modelValue: '',
      options: []
    }
  },
  watch: {
    value(newValue) {
      this.modelValue = newValue
    },
    modelValue(newValue, oldValue) {
      if (newValue === oldValue) return
      this.$emit('input', newValue)
    }
  },
  created() {
    this.modelValue = this.value
  },
  methods: {
    handleSelect(value) {
      this.$emit('input', value)
    }
  }
}
</script>
