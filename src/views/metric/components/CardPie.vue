<template>
  <base-card :title="title">
    <template #content>
      <slot v-if="columns.length === 0" />
      <base-table v-else :columns="columns" :data="data" />
    </template>

    <template #chart>
      <v-chart :style="{ height: '300px', marginTop: '1rem' }" :option="option" />
    </template>
  </base-card>
</template>
<script>
import BaseCard from './BaseCard.vue'
import BaseTable from './BaseTable.vue'

import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { PieChart } from 'echarts/charts'
import { TooltipComponent, LegendComponent } from 'echarts/components'
import VChart from 'vue-echarts'

use([Can<PERSON>Renderer, PieChart, TooltipComponent, LegendComponent])

export default {
  name: 'Card<PERSON>ie',
  components: {
    BaseCard,
    BaseTable,
    VChart
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    columns: {
      type: Array,
      default: () => []
    },
    data: {
      type: Array,
      default: () => []
    },
    seriesOption: {
      type: Object,
      default: () => ({})
    },
    showPercentage: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    processedData() {
      if (!this.seriesOption.data || !Array.isArray(this.seriesOption.data)) {
        return []
      }

      // 如果需要显示百分比，自动转换数据
      if (this.showPercentage) {
        return this.convertToPercentages(this.seriesOption.data)
      }

      return this.seriesOption.data
    },
    option() {
      return {
        tooltip: {
          trigger: 'item',
          formatter: this.showPercentage
            ? `{a} <br/>{b} : {c}% ({d}%)`
            : `{a} <br/>{b} : {c}${this.seriesOption?.unit || '元'} ({d}%)`
        },
        legend: {
          data: this.seriesOption.legend || []
        },
        label: {
          alignTo: 'edge',
          formatter: this.showPercentage
            ? `{name|{b}}\n{amount|{c}%}`
            : `{name|{b}}\n{amount|{c}${this.seriesOption?.unit || '元'}}`,
          minMargin: 5,
          edgeDistance: 10,
          lineHeight: 15,
          rich: {
            amount: {
              fontSize: 10,
              color: '#999'
            }
          }
        },
        labelLine: {
          length: 15,
          length2: 0,
          maxSurfaceAngle: 80
        },
        series: this.isArray(this.seriesOption.data)
          ? this.seriesOption.data.map((item) => ({
              type: 'pie',
              ...item
            }))
          : [
              {
                name: this.seriesOption.name || '数据',
                type: 'pie',
                radius: '55%',
                center: ['50%', '60%'],
                data: this.processedData
              }
            ]
      }
    }
  },
  methods: {
    isArray(value) {
      return Array.isArray(value) && value.length > 0 && value[0]?.data !== undefined
    },
    convertToPercentages(data) {
      if (!data || !Array.isArray(data) || data.length === 0) {
        return []
      }

      // 计算总值
      const total = data.reduce((sum, item) => {
        const value = parseFloat(item.value) || 0
        return sum + value
      }, 0)

      // 转换为百分比
      if (total === 0) {
        return data.map((item) => ({
          ...item,
          originalValue: item.value,
          value: 0
        }))
      }

      return data.map((item) => {
        const value = parseFloat(item.value) || 0
        return {
          ...item,
          originalValue: item.value,
          value: parseFloat(((value / total) * 100).toFixed(2))
        }
      })
    }
  }
}
</script>
