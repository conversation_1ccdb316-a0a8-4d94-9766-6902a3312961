<template>
  <base-card :title="title">
    <template #content>
      <slot v-if="columns.length === 0" />
      <base-table v-else :columns="columns" :data="data" />
    </template>

    <template #chart>
      <v-chart :style="{ height: '300px', marginTop: '1rem' }" :option="option" />
    </template>
  </base-card>
</template>
<script>
import BaseCard from './BaseCard.vue'
import BaseTable from './BaseTable.vue'

import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { PieChart } from 'echarts/charts'
import { TooltipComponent, LegendComponent } from 'echarts/components'
import VChart from 'vue-echarts'

use([Can<PERSON><PERSON>ender<PERSON>, PieChart, TooltipComponent, LegendComponent])

export default {
  name: '<PERSON><PERSON>ie',
  components: {
    BaseCard,
    BaseTable,
    VChart
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    columns: {
      type: Array,
      default: () => []
    },
    data: {
      type: Array,
      default: () => []
    },
    seriesOption: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    option() {
      return {
        tooltip: {
          trigger: 'item',
          formatter: `{a} <br/>{b} : {c} ({d}%)`
        },
        legend: {
          data: this.seriesOption.legend || []
        },
        label: {
          alignTo: 'edge',
          formatter: `{name|{b}}\n{amount|{c}${this.seriesOption?.unit || '元'}}`,
          minMargin: 5,
          edgeDistance: 10,
          lineHeight: 15,
          rich: {
            amount: {
              fontSize: 10,
              color: '#999'
            }
          }
        },
        labelLine: {
          length: 15,
          length2: 0,
          maxSurfaceAngle: 80
        },
        series: this.isArray(this.seriesOption.data)
          ? this.seriesOption.data.map((item) => ({
              type: 'pie',
              ...item
            }))
          : [
              {
                name: this.seriesOption.name || '数据',
                type: 'pie',
                radius: '55%',
                center: ['50%', '60%'],
                data: this.seriesOption.data || []
              }
            ]
      }
    }
  },
  methods: {
    isArray(value) {
      return Array.isArray(value) && value.length > 0 && value[0]?.data !== undefined
    }
  }
}
</script>
