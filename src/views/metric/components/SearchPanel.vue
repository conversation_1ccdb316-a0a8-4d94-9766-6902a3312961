<template>
  <div>
    <el-card shadow="never" class="search-panel">
      <div class="search-panel__content">
        <el-form ref="form" :model="searchForm" class="search-panel__form">
          <slot />
        </el-form>
      </div>
      <div class="search-panel__footer" v-if="shouldSearch">
        <el-button type="primary" size="small" @click="search">查询</el-button>
      </div>
    </el-card>

    <!-- <el-button type="primary" size="small" class="m-extra-large-t" @click="download"> 导出 </el-button> -->
  </div>
</template>

<script>
export default {
  name: 'SearchPanel',
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    shouldSearch: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      searchForm: this.modelValue
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.searchForm = Object.assign({}, newVal)
      },
      immediate: true
    }
  },
  methods: {
    search() {
      if (!this.shouldSearch) return
      const data = Object.assign({}, this.searchForm)
      this.$emit('search', data)
    },
    download() {
      const data = Object.assign({}, this.searchForm)
      this.$emit('download', data)
    }
  }
}
</script>

<style lang="scss" scoped>
.search-panel {
  border: none;
  border-radius: 10px;

  /deep/ .el-card__body {
    padding: 0;
  }

  .search-panel__content {
    padding: 20px;
  }

  .search-panel__form {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
  }

  .search-panel__footer {
    padding: 20px;
    border-top: 1px solid #ececec;
  }
}
</style>
