<!--
 * @Descripttion:
 * @version:
 * @Author: yanb
 * @Date: 2023-06-26 09:36:29
 * @LastEditors: yanb
 * @LastEditTime: 2023-06-26 10:09:02
-->
<template>
  <SimpleContainer class="bg-white p-extra-large w-100 d-flex flex-column o-hidden">
    <div>
      <el-row>
        <el-col class="btns">
          <el-popconfirm title="确定完成报价吗？" @confirm="handleEnquiry" v-if="inquiry.status == 1">
            <el-button type="primary" slot="reference"> 完成报价 </el-button>
          </el-popconfirm>
        </el-col>
      </el-row>
    </div>
    <DefinePoliciesDetails :data="details"></DefinePoliciesDetails>
  </SimpleContainer>
</template>

<script>
import { getEnquiry, handleEnquiry } from '@/apis/inquiry'

export default {
  name: 'inquiryBaoyaDetail',
  data() {
    return {
      inquiry: {}
    }
  },
  computed: {
    details() {
      const _data = {
        title: '详情',
        data: [
          {
            title: '基本信息',
            groups: [
              { label: '询价产品', value: this.inquiry?.product?.name },

              { label: '邮箱', value: this.inquiry?.email },
              {
                label: '提交时间',
                value: this.inquiry?.created_at
              },
              {
                label: '状态',
                value: this.inquiry?.status == 1 ? '未处理' : '已处理'
              }
            ]
          },
          {
            _id: 'inquiry',
            title: '询价资料',
            groups: []
          }
        ]
      }
      let _groups = []
      if (this.inquiry?.content) {
        _groups = this.inquiry?.content.map((item) => {
          const isFile = item.type === 'file'
          return {
            label: item.title,
            value: isFile ? '点击查看' : item.value,
            isLink: isFile,
            target: '_blank',
            row: true,
            to: isFile ? item.value : undefined
          }
        })
        _data.data.map((item) => {
          if (item._id && item._id === 'inquiry') {
            item.groups = _groups
          }
        })
      } else {
        _data.data = _data.data.filter((item) => !item._id)
      }
      return _data
    }
  },
  created() {
    this.fetchDetail()
  },
  methods: {
    fetchDetail() {
      getEnquiry(this.$route.params.id).then((r) => {
        r.data.content = JSON.parse(r.data.content)
        this.inquiry = r.data
      })
    },
    handleEnquiry() {
      handleEnquiry(this.inquiry.id).then(() => {
        this.$message.success('完成报价')

        this.fetchDetail()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0px !important;
}

.btns {
  text-align: right;

  /deep/ .el-button {
    margin-left: 10px;
  }
}
</style>
