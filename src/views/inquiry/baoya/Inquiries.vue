<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <SearchPanel
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :exportable="false"
      :custom="searchFields"
    ></SearchPanel>
    <el-card shadow="never" class="m-extra-large-t">
      <define-table :data="tableData" :cols="cols" :paging="paging" :paging-events="pagingEvents" />
    </el-card>
  </div>
</template>

<script>
import { getEnquiries } from '@/apis/inquiry'

export default {
  name: 'Inquiries',
  data() {
    return {
      searchFields: [
        {
          type: 'input',
          valKey: 'product_name',
          hintText: '产品名称'
        },
        {
          type: 'input',
          valKey: 'email',
          hintText: '邮箱'
        },
        {
          type: 'daterange',
          valKey: 'created_at_range',
          hintText: '提交'
        },
        {
          type: 'select',
          valKey: 'status',
          hintText: '状态',
          options: [
            { value: 1, label: '未处理' },
            { value: 2, label: '已处理' }
          ]
        }
      ],
      cols: [
        {
          label: '产品名称',
          prop: 'product.name'
        },
        {
          label: '邮箱',
          prop: 'email'
        },
        {
          label: '提交时间',
          prop: 'created_at'
        },
        {
          label: '状态',
          prop: 'status',
          scopedSlots: {
            default: (scoped) => {
              switch (parseInt(scoped.row.status, 10)) {
                case 1:
                  return <span class="text-info">未处理</span>
                case 2:
                  return <span class="text-success">已处理</span>
                default:
                  break
              }
            }
          }
        },
        {
          label: '操作',
          fixed: 'right',
          width: 100,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button type="text" size="small" onClick={() => this.hrefDetails(scoped.row)}>
                    详情
                  </el-button>
                </div>
              )
            }
          }
        }
      ],
      tableData: [],
      // 分页器 不传递 -> 没有分页
      paging: {
        currentPage: 1,
        pageSize: 0,
        layout: 'prev, pager, next, jumper, total',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page

          this.fetchInquiries()
        }
      },
      searchQuery: {},
      searchData: {},
      payees: []
    }
  },
  created() {
    this.fetchInquiries()
  },
  methods: {
    handleSearchPanel(command, data) {
      this.searchQuery = data
      this.paging.page = 1
      if (command === 'export') {
        //
      } else {
        this.fetchInquiries()
      }
    },
    fetchInquiries() {
      getEnquiries({
        page: this.paging.page,
        filter: this.searchQuery
      }).then((r) => {
        this.tableData = r.data

        this.paging.currentPage = r.meta.current_page
        this.paging.pageSize = r.meta.per_page
        this.paging.total = r.meta.total
      })
    },
    hrefDetails(row) {
      this.$open({
        name: 'InquiryBaoyaDetail',
        params: {
          id: row.id
        }
      })
    }
  },
  watch: {}
}
</script>
