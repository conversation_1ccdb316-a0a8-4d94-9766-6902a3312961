<template>
  <div class="operation-bar">
    <el-button type="primary" @click="displayCreateDialog" :disabled="!isCreatable">创建暂存单</el-button>

    <el-popconfirm
      style="margin-left: 10px; margin-right: 10px"
      confirm-button-text="确定"
      cancel-button-text="取消"
      icon="el-icon-info"
      icon-color="red"
      title="确定婉拒吗？"
      @confirm="handleReject(data)"
    >
      <el-button type="primary" slot="reference" :disabled="!isRejectable">婉拒</el-button>
    </el-popconfirm>

    <el-button
      type="primary"
      :disabled="!isEditable"
      @click="() => $router.push({ name: 'InquiryEdit', params: { id: data.id } })"
    >
      编辑
    </el-button>

    <el-popconfirm
      style="margin-left: 10px"
      confirm-button-text="确定"
      cancel-button-text="取消"
      icon="el-icon-info"
      icon-color="red"
      title="确定删除吗？"
      @confirm="handleDelete(data)"
    >
      <el-button type="danger" slot="reference" :disabled="!isDeletable"> 删除 </el-button>
    </el-popconfirm>

    <el-dialog :visible.sync="displayCreatePolicyDialog" title="创建暂存单" width="50%">
      <el-form :model="draftForm" :rules="draftRules" ref="draftForm" label-width="150px">
        <el-form-item label="产品类型" prop="product_type">
          <el-radio-group v-model="draftForm.product_type">
            <el-radio :label="1">国内货运险</el-radio>
            <el-radio :label="2">国际货运险</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="产品" prop="product_id">
          <el-select v-model="draftForm.product_id" placeholder="请选择产品" class="w-100">
            <el-option v-for="item in availableProducts" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="费率" prop="rate">
          <el-input v-model="draftForm.rate" placeholder="请输入费率">
            <template slot="append">‱</template>
          </el-input>
        </el-form-item>

        <el-form-item label="免赔额和特约条款" prop="deductible_special_terms">
          <el-input
            type="textarea"
            :rows="1"
            autosize
            v-model="draftForm.deductible_special_terms"
            placeholder="请输入免赔额和特约条款"
          ></el-input>
        </el-form-item>

        <el-form-item label="人工审核原因" prop="manual_subject_reason_ids">
          <el-select
            v-model="draftForm.manual_subject_reason_ids"
            multiple
            placeholder="请选择人工审核原因"
            class="w-100"
          >
            <el-option
              v-for="reason in manualSubjectReasons"
              :key="reason.value"
              :label="reason.label"
              :value="reason.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleCreate" :loading="createLoading" :disabled="createLoading">
          创建
        </el-button>
        <el-button @click="displayCreatePolicyDialog = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import * as inquiryApi from '@/apis/inquiry'
import * as subjectApi from '@/apis/subject'
import { Loading } from 'element-ui'

export default {
  name: 'OperationBar',
  props: {
    data: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      manualSubjectReasons: [],
      availableProducts: [],
      inquiryRequest: [],
      createLoading: false,
      displayCreatePolicyDialog: false,
      draftForm: {
        product_type: '',
        product_id: null,
        manual_subject_reason_ids: [],
        inquiry_request_id: null,
        rate: null,
        deductible_special_terms: null
      },
      draftRules: {
        product_type: [{ required: true, message: '请选择产品类型', trigger: 'change' }],
        product_id: [{ required: true, message: '请选择产品', trigger: 'change' }],
        manual_subject_reason_ids: [{ required: true, message: '请选择人工审核原因', trigger: 'change' }],
        inquiry_request_id: [{ required: true, message: '请选择询价请求', trigger: 'change' }],
        rate: [{ required: true, message: '请输入费率', trigger: 'blur' }],
        deductible_special_terms: [{ required: true, message: '请输入免赔额和特约条款', trigger: 'blur' }]
      }
    }
  },
  computed: {
    isCreatable() {
      return this.data.status === 3 && !this.data.policy_id
    },
    isRejectable() {
      return [1, 2, 3].includes(this.data.status)
    },
    isEditable() {
      return [0, 1].includes(this.data.status)
    },
    isDeletable() {
      return [0, 1, 4].includes(this.data.status)
    }
  },
  watch: {
    'draftForm.product_type': function (newVal) {
      this.fetchAvailableProducts(newVal)
    },
    'draftForm.product_id': function (newVal) {
      const product = this.availableProducts.find((item) => item.id === newVal)
      this.findRequest(product.company_branch.id)
    }
  },
  methods: {
    async handleCreate() {
      this.$refs.draftForm.validate(async (valid) => {
        if (!valid) return

        this.createLoading = true
        try {
          await inquiryApi.createPolicy(this.data.id, this.draftForm)
          this.$message.success('创建成功')
          this.displayCreatePolicyDialog = false
          this.$emit('refresh')
        } catch (error) {
          this.$message.error('创建失败')
        } finally {
          this.createLoading = false
        }
      })
    },
    async displayCreateDialog() {
      this.displayCreatePolicyDialog = true

      await this.fetchManualSubjectReasons()
    },
    async handleReject(data) {
      const loading = Loading.service()
      try {
        await inquiryApi.rejectInquiry(data.id)
        this.$message.success('婉拒成功')

        this.$emit('refresh')
      } finally {
        loading.close()
      }
    },
    async handleDelete(data) {
      const loading = Loading.service()
      try {
        await inquiryApi.deleteInquiry(data.id)
        this.$message.success('删除成功')

        this.$router.push({ name: 'InquiryNew' })
      } finally {
        loading.close()
      }
    },
    async fetchAvailableProducts(productType = 1) {
      const { data } = await inquiryApi.fetchAvailableProducts(this.data?.id, {
        filter: { type: productType, subject_id: 3 }
      })
      this.availableProducts = data
    },
    async findRequest(companyBranchId) {
      const { data } = await inquiryApi.findRequest(this.data?.id, { company_branch_id: companyBranchId })
      this.inquiryRequest = data
      this.draftForm.inquiry_request_id = data.id
      this.draftForm.rate = data.rate
      this.draftForm.deductible_special_terms = data.deductible_special_terms
    },
    async fetchManualSubjectReasons() {
      const data = await subjectApi.getSubjectCategories(3)
      data
        .filter((item) => item.is_enabled)
        .forEach((item) => {
          this.manualSubjectReasons.push({
            label: item.name,
            value: item.id
          })
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.operation-bar {
  padding-bottom: 10px;

  display: flex;
  justify-content: flex-end;
}
</style>
