<template>
  <el-dialog :visible.sync="visible" width="600px" title="发送询价单" destory-on-close :before-close="close">
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="出单公司" prop="company_branch_id">
        <el-select class="w-100" v-model="form.company_branch_id" placeholder="请选择出单公司" multiple filterable>
          <el-option v-for="item in companyBranches" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="邮件标题" prop="subject">
        <el-input v-model="form.subject" placeholder="请输入邮件标题"></el-input>
      </el-form-item>
      <el-form-item label="抄送人" prop="cc">
        <el-input v-model="form.cc" placeholder="请输入抄送人, 多个用逗号分隔"></el-input>
      </el-form-item>
      <el-form-item label="询价原因" prop="reason">
        <el-select class="w-100" v-model="form.reasons" placeholder="请选择询价原因" multiple filterable>
          <el-option v-for="item in reasons" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" :loading="sending" :disabled="sending" @click="submitRequest"> 发送 </el-button>
    </template>
  </el-dialog>
</template>

<script>
import * as companyApi from '@/apis/company'
import * as inquiryApi from '@/apis/inquiry'

export default {
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    id: {
      type: Number,
      required: true
    },
    reasons: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      sending: false,
      form: {
        company_branch_id: [],
        subject: '',
        cc: '',
        reasons: []
      },
      rules: {
        company_branch_id: [{ required: true, message: '请选择出单公司', trigger: 'blur' }],
        subject: [{ required: true, message: '请输入邮件标题', trigger: 'blur' }],
        reasons: [{ required: true, message: '请输入询价原因', trigger: 'blur' }]
      },
      companies: []
    }
  },
  computed: {
    companyBranches() {
      const branches = []
      this.companies.forEach((company) => branches.push(...company.branches))
      return branches
    }
  },
  created() {
    this.fetchCompanies()
  },
  methods: {
    async fetchCompanies() {
      const { data } = await companyApi.getCompaniesDict()
      this.companies = data
    },
    close() {
      this.$emit('update:visible', false)
      this.$refs.form.resetFields()
    },
    async submitRequest() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.sending = true
          try {
            await inquiryApi.sendInquiryRequest(this.id, this.form)
            this.$message.success('询价单已发送')
            this.$emit('done')
            this.close()
          } finally {
            this.sending = false
          }
        }
      })
    }
  }
}
</script>
