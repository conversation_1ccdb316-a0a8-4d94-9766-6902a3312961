<template>
  <el-dialog :visible.sync="visible" width="600px" title="婉拒询价单" destory-on-close :before-close="close">
    <el-form ref="form" :model="form" :rules="rules" label-width="50px">
      <el-form-item label="原因" prop="remarks">
        <el-input v-model="form.remarks" type="textarea" :rows="1" autosize placeholder="请输入原因"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" :loading="sending" :disabled="sending" @click="updateRequest"> 婉拒 </el-button>
    </template>
  </el-dialog>
</template>

<script>
import * as inquiryApi from '@/apis/inquiry'
import { Loading } from 'element-ui'

export default {
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    id: {
      type: Number,
      required: true
    },
    requestId: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      sending: false,
      form: {
        remarks: ''
      },
      rules: {
        remarks: [{ required: false, message: '请输入原因', trigger: 'blur' }]
      }
    }
  },
  methods: {
    close() {
      this.$emit('update:visible', false)
      this.$refs.form.resetFields()
    },
    async updateRequest() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          const loading = Loading.service()
          try {
            await inquiryApi.rejectInquiryRequest(this.id, this.requestId, this.form.remarks)
            this.$message.success('婉拒询价单成功')
            this.$emit('done')
            this.close()
          } finally {
            loading.close()
          }
        }
      })
    }
  }
}
</script>
