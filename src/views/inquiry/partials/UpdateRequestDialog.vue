<template>
  <el-dialog :visible.sync="visible" width="600px" title="更新询价单" destory-on-close :before-close="close">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="费率" prop="rate">
        <el-input v-model="form.rate" placeholder="请输入费率">
          <template slot="append">‱</template>
        </el-input>
      </el-form-item>
      <el-form-item label="免赔和特别约定" prop="deductible_special_terms">
        <el-input
          v-model="form.deductible_special_terms"
          type="textarea"
          :rows="1"
          autosize
          placeholder="请输入免赔和特别约定"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remarks">
        <el-input v-model="form.remarks" type="textarea" :rows="1" autosize placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" :loading="sending" :disabled="sending" @click="updateRequest"> 更新 </el-button>
    </template>
  </el-dialog>
</template>

<script>
import * as inquiryApi from '@/apis/inquiry'
import { Loading } from 'element-ui'

export default {
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    id: {
      type: Number,
      required: true
    },
    requestId: {
      type: Number,
      required: true
    },
    values: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      sending: false,
      form: {
        rate: '',
        deductible_special_terms: '',
        remarks: ''
      },
      rules: {
        rate: [{ required: true, message: '请输入费率', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.form = {
      rate: this.values.rate || '',
      deductible_special_terms: this.values.deductible_special_terms || '',
      remarks: this.values.remarks || ''
    }
  },
  methods: {
    close() {
      this.$emit('update:visible', false)
      this.$refs.form.resetFields()
    },
    async updateRequest() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          const loading = Loading.service()
          try {
            await inquiryApi.updateInquiryRequest(this.id, this.requestId, this.form)
            this.$message.success('更新询价单成功')
            this.$emit('done')
            this.close()
          } finally {
            loading.close()
          }
        }
      })
    }
  }
}
</script>
