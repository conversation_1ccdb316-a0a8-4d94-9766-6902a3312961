<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <SearchPanel
      @command="handleSearch"
      @change="(data) => (searchData = data)"
      :exportable="false"
      :custom="searchFields"
    />

    <el-button
      type="primary"
      style="margin-top: 20px !important"
      icon="fas fa-plus"
      @click="$router.push({ name: 'InquiryCreate' })"
    >
      新建询价
    </el-button>

    <el-card shadow="never" class="m-extra-large-t">
      <define-table :data="data" :cols="cols" :paging="paging" :paging-events="pagingEvents" />
    </el-card>
  </div>
</template>

<script>
import * as inquiryApi from '@/apis/inquiry'
import { Loading } from 'element-ui'

export default {
  data() {
    return {
      searchData: {},
      searchFields: [
        {
          type: 'input',
          valKey: 'inquiry_number',
          hintText: '询价单号'
        },
        {
          type: 'input',
          valKey: 'user',
          hintText: '询价用户'
        },
        {
          type: 'input',
          valKey: 'policyholder_name',
          hintText: '投保人'
        },
        {
          type: 'input',
          valKey: 'insured_name',
          hintText: '被保险人'
        },
        {
          type: 'daterange',
          valKey: 'created_at_range',
          hintText: '提交'
        },
        {
          type: 'select',
          valKey: 'status',
          hintText: '状态',
          options: [
            { value: 0, label: '暂存单' },
            { value: 1, label: '未处理' },
            { value: 2, label: '已发送' },
            { value: 3, label: '已回复' },
            { value: 4, label: '婉拒' },
            { value: 5, label: '已出单' }
          ]
        },
        {
          type: 'select',
          valKey: 'reason',
          hintText: '询价原因',
          options: [
            { value: 1, label: '精密仪器' },
            { value: 2, label: '车辆、摩托车' },
            { value: 3, label: '超限额' },
            { value: 4, label: '散货船' },
            { value: 5, label: '特殊区域' },
            { value: 7, label: '线下出单' },
            { value: 8, label: '空运冷藏' },
            { value: 9, label: '锂电池' },
            { value: 10, label: '特种集装箱' },
            { value: 99, label: '其他' }
          ]
        }
      ],
      data: [],
      cols: [
        {
          label: '编号/保单流水号',
          width: 180,
          fixed: 'left',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.inquiry_number}</label>
                  <br />
                  <small>{scoped.row.policy?.order_no}</small>
                </div>
              )
            }
          }
        },
        {
          label: '被保险人/投保人',
          width: 180,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.insured_name}</label>
                  <br />
                  <small>{scoped.row.policyholder_name}</small>
                </div>
              )
            }
          }
        },
        {
          label: '起运地/目的地',
          width: 180,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.departure}</label>
                  <br />
                  <small>{scoped.row.destination}</small>
                </div>
              )
            }
          }
        },
        { label: '货物名称', prop: 'goods_name' },
        {
          label: '货物价值',
          width: 150,
          scopedSlots: {
            default: (scoped) => {
              return (
                <span>
                  {scoped.row.goods_value} {scoped.row.goods_value_currency}
                </span>
              )
            }
          }
        },
        {
          label: '状态',
          width: 80,
          scopedSlots: {
            default: (scoped) => {
              return (
                {
                  0: '暂存单',
                  1: '待处理',
                  2: '已发送',
                  3: '已回复',
                  4: '婉拒',
                  5: '已出单'
                }[scoped.row.status] || '未知'
              )
            }
          }
        },
        {
          label: '提交/更新时间',
          width: 180,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.created_at}</label>
                  <br />
                  <small>{scoped.row.updated_at}</small>
                </div>
              )
            }
          }
        },
        {
          label: '操作',
          width: 120,
          fixed: 'right',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    type="text"
                    size="small"
                    onClick={() => this.$router.push({ name: 'InquiryDetail', params: { id: scoped.row.id } })}
                  >
                    查看
                  </el-button>
                  <el-button
                    type="text"
                    size="small"
                    disabled={![0, 1].includes(scoped.row.status)}
                    onClick={() => this.$router.push({ name: 'InquiryEdit', params: { id: scoped.row.id } })}
                  >
                    编辑
                  </el-button>
                  <el-popconfirm
                    style="margin-left: 10px"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    icon="el-icon-info"
                    icon-color="red"
                    title="确定删除吗？"
                    onConfirm={() => this.handleDelete(scoped.row)}
                  >
                    <el-button size="small" type="text" slot="reference">
                      删除
                    </el-button>
                  </el-popconfirm>
                </div>
              )
            }
          }
        }
      ],
      paging: {
        currentPage: 1,
        pageSize: 0,
        layout: 'prev, pager, next, jumper, total',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page

          this.fetchInquiries()
        }
      }
    }
  },
  created() {
    this.fetchInquiries()
  },
  methods: {
    handleSearch(_, data) {
      this.searchQuery = data
      this.paging.page = 1
      this.fetchInquiries()
    },
    async handleDelete(data) {
      const loading = Loading.service()
      try {
        await inquiryApi.deleteInquiry(data.id)
        this.$message.success('删除成功')
        await this.fetchInquiries()
      } finally {
        loading.close()
      }
    },
    async fetchInquiries() {
      const loading = Loading.service()
      try {
        const data = await inquiryApi.getInquiries({
          page: this.paging.page,
          filter: this.searchData
        })

        this.data = data.data
        this.paging.currentPage = data.meta.current_page
        this.paging.pageSize = data.meta.per_page
        this.paging.total = data.meta.total
      } finally {
        loading.close()
      }
    }
  }
}
</script>
