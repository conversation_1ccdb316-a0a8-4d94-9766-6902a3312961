<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <el-card shadow="never">
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="用户" prop="user_id">
              <el-select
                v-model="form.user_id"
                placeholder="请选择用户"
                class="w-100"
                filterable
                remote
                :remote-method="fetchUsers"
                :loading="userLoading"
              >
                <el-option v-for="item in users" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="投保人名称" prop="policyholder_name">
              <el-input v-model="form.policyholder_name" placeholder="请输入投保人名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="被保人名称" prop="insured_name">
              <el-input v-model="form.insured_name" placeholder="请输入被保险人名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="货物名称" prop="goods_name">
              <el-input v-model="form.goods_name" placeholder="请输入货物名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="货物类别" prop="goods_type">
              <el-input v-model="form.goods_type" placeholder="请输入货物类别" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="包装及数量" prop="packaging_and_quantity">
              <el-input v-model="form.packaging_and_quantity" placeholder="请输入包装及数量" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="货物价值" prop="goods_value">
              <el-input v-model="form.goods_value" placeholder="请输入货物价值" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="货币" prop="goods_value_currency">
              <el-select v-model="form.goods_value_currency" placeholder="请选择货币" class="w-100">
                <el-option v-for="item in currenies" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="起运地" prop="departure">
              <el-cascader
                class="w-100"
                v-model="form.departure"
                :options="locations"
                filterable
                :props="{
                  expandTrigger: 'hover',
                  filterable: true,
                  value: 'value',
                  label: 'value',
                  children: 'children'
                }"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="起运地地址" prop="departure_address">
              <el-input v-model="form.departure_address" placeholder="请输入起运地地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="中转地" prop="transit">
              <el-cascader
                class="w-100"
                v-model="form.transit"
                :options="locations"
                filterable
                clearable
                :props="{
                  expandTrigger: 'hover',
                  filterable: true,
                  value: 'value',
                  label: 'value',
                  children: 'children'
                }"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="中转地地址" prop="transit_address">
              <el-input v-model="form.transit_address" placeholder="请输入中转地地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="目的地" prop="destination">
              <el-cascader
                class="w-100"
                v-model="form.destination"
                :options="locations"
                filterable
                :props="{
                  expandTrigger: 'hover',
                  filterable: true,
                  value: 'value',
                  label: 'value',
                  children: 'children'
                }"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="目的地地址" prop="destination_address">
              <el-input v-model="form.destination_address" placeholder="请输入目的地地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="起运日期" prop="departure_date">
              <el-date-picker
                class="w-100"
                v-model="form.departure_date"
                type="date"
                placeholder="选择日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="险种" prop="insurance_type">
              <el-input v-model="form.insurance_type" placeholder="请输入险种" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="运输方式" prop="transport_mode">
              <el-select class="w-100" v-model="form.transport_mode" placeholder="请选择运输方式">
                <el-option label="空运" :value="1" />
                <el-option label="海运" :value="2" />
                <el-option label="陆运" :value="3" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="装载方式" prop="loading_method">
              <el-select class="w-100" v-model="form.loading_method" placeholder="请选择装载方式">
                <el-option label="集装箱运输" :value="1" />
                <el-option label="非集装箱运输" :value="2" />
                <el-option label="厢式货车" :value="3" />
                <el-option label="非厢式货车" :value="4" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="船舶信息" prop="vessel_info">
              <el-input v-model="form.vessel_info" placeholder="请输入船舶信息" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="装载位置" prop="loading_position">
              <el-select class="w-100" v-model="form.loading_position" placeholder="请选择装载位置">
                <el-option label="甲板" :value="1" />
                <el-option label="舱内" :value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="装船计划" prop="stowage_plan">
              <el-input v-model="form.stowage_plan" placeholder="请输入装船计划" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="陆运计划" prop="land_transport_plan">
              <el-input v-model="form.land_transport_plan" placeholder="请输入陆运计划" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="尺寸" prop="dimensions">
              <el-input v-model="form.dimensions" placeholder="请输入尺寸" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="是否包含装卸" prop="include_warehouse_handling">
              <el-radio-group v-model="form.include_warehouse_handling">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
              <el-alert type="warning" title="例如大件货物需要含吊装" show-icon :closable="false" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="是否有特殊运输要求" prop="has_special_transport_reqs">
              <el-radio-group v-model="form.has_special_transport_reqs">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
              <el-alert type="warning" title="例如防震防倾斜货物需要用气垫车运输" show-icon :closable="false" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="是否新货" prop="is_new_goods">
              <el-radio-group v-model="form.is_new_goods">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="照片" prop="photos">
              <el-upload
                action=""
                multiple
                :limit="10"
                :file-list="fileList"
                :auto-upload="false"
                :on-change="handlePhotosChange"
                :on-remove="handlePhotosRemove"
              >
                <el-button size="small" type="primary">点击上传</el-button>
              </el-upload>
              <el-alert
                class="m-extra-large-t"
                type="warning"
                title="文件大小请勿超过 10 MB"
                show-icon
                :closable="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remarks">
              <el-input type="textarea" :rows="5" v-model="form.remarks" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item>
          <el-button
            type="primary"
            v-if="isDraft || !isUpdating"
            :loading="loading"
            :disabled="loading"
            @click="handleSubmit(true)"
          >
            暂存
          </el-button>
          <el-button type="primary" :loading="loading" :disabled="loading" @click="handleSubmit()">提交</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import * as inquiryApi from '@/apis/inquiry'
import * as userApi from '@/apis/user'
import currenies from '@/utils/currency'
import chinaAreadata from '@/utils/areadata.json'
import overseaAreadata from '@/utils/regions'

export default {
  data() {
    return {
      overseaAreadata,
      chinaAreadata,
      isDraft: false,
      users: [],
      userLoading: false,
      currenies,
      fileList: [], // 用于显示已上传的文件列表
      form: {
        user_id: '',
        policyholder_name: '',
        insured_name: '',
        goods_type: '',
        goods_name: '',
        packaging_and_quantity: '',
        goods_value: '',
        goods_value_currency: '',
        departure: [],
        departure_address: '',
        transit: [],
        transit_address: '',
        destination: [],
        destination_address: '',
        departure_date: '',
        insurance_type: '',
        transport_mode: '',
        vessel_info: '',
        loading_position: '',
        loading_method: '',
        stowage_plan: '',
        land_transport_plan: '',
        include_warehouse_handling: '',
        has_special_transport_reqs: '',
        is_new_goods: '',
        dimensions: '',
        photos: [],
        remarks: ''
      },
      rules: {
        user_id: [{ required: true, message: '请选择用户', trigger: 'change' }],
        policyholder_name: [{ required: true, message: '请输入投保人名称', trigger: 'blur' }],
        insured_name: [{ required: true, message: '请输入被保险人名称', trigger: 'blur' }],
        goods_type: [{ required: true, message: '请输入货物类别', trigger: 'blur' }],
        goods_name: [{ required: true, message: '请输入货物名称', trigger: 'blur' }],
        packaging_and_quantity: [{ required: true, message: '请输入包装及数量', trigger: 'blur' }],
        goods_value: [
          { required: true, message: '请输入货物价值', trigger: 'blur' },
          { pattern: /^\d+(\.\d{1,2})?$/, message: '货物价值必须为数字', trigger: 'blur' }
        ],
        goods_value_currency: [{ required: true, message: '请选择货币', trigger: 'change' }],
        departure: [{ required: true, message: '请输入起运地', trigger: 'blur' }],
        destination: [{ required: true, message: '请输入目的地', trigger: 'blur' }],
        departure_date: [{ required: true, message: '请选择起运日期', trigger: 'change' }],
        insurance_type: [{ required: true, message: '请选择险种', trigger: 'change' }],
        transport_mode: [{ required: true, message: '请选择运输方式', trigger: 'change' }],
        loading_method: [{ required: true, message: '请选择装载方式', trigger: 'change' }],
        include_warehouse_handling: [{ required: true, message: '请选择是否包含仓储装卸', trigger: 'change' }],
        has_special_transport_reqs: [{ required: true, message: '请选择是否有特殊运输要求', trigger: 'change' }],
        is_new_goods: [{ required: true, message: '请选择是否新货', trigger: 'change' }]
      },
      loading: false
    }
  },
  computed: {
    isUpdating() {
      return this.$route.params.id !== undefined ? true : false
    },
    id() {
      return this.$route.params.id
    },
    locations() {
      return [
        {
          value: '中国大陆',
          children: chinaAreadata.map((item) => {
            return {
              value: item.value,
              children: item.city.map((city) => {
                return {
                  value: city.value
                }
              })
            }
          })
        },
        {
          value: '境外地区',
          children: Object.keys(this.overseaAreadata).map((k) => {
            return { value: k }
          })
        }
      ]
    }
  },
  created() {
    if (this.isUpdating) {
      this.fetchInqueryDetail()
    }
  },
  methods: {
    async fetchUsers(query = '', uid = '') {
      this.userLoading = true
      try {
        const { data } = await userApi.getUsers({ filter: { name: query, id: uid } })
        this.users = data.map((item) => ({ label: `${item.name}(${item.username})`, value: item.id }))
      } finally {
        this.userLoading = false
      }
    },
    async fetchInqueryDetail() {
      const { data } = await inquiryApi.getInquiryDetail(this.id)
      for (const key in this.form) {
        data[key] !== undefined && (this.form[key] = data[key])
      }

      this.form.departure = data.departure ? data.departure.split('-') : []
      this.form.transit = data.transit ? data.transit.split('-') : []
      this.form.destination = data.destination ? data.destination.split('-') : []

      this.form.photos = []
      this.fileList = []
      this.isDraft = !data.status

      await this.fetchUsers('', this.form.user_id)
    },
    handlePhotosChange(file, fileList) {
      // 处理文件变更，追加而不是替换
      // 检查是否有重复文件名
      const duplicateIndex = this.fileList.findIndex((item) => item.name === file.name)

      if (duplicateIndex !== -1) {
        // 如果有重复文件名，替换原有的文件
        this.fileList.splice(duplicateIndex, 1, file)
      } else {
        // 如果没有重复，直接添加到列表
        this.fileList = fileList
      }

      this.form.photos = this.fileList.map((item) => item.raw).filter((f) => f instanceof File) // 只保留合法文件
    },
    handlePhotosRemove(file, fileList) {
      // 处理文件删除
      this.fileList = fileList
      this.form.photos = fileList.map((item) => item.raw).filter((f) => f instanceof File) // 只保留合法文件
    },
    async handleSubmit(draft = false) {
      draft ? await this.submit(draft) : this.$refs.form.validate(async (valid) => valid && (await this.submit(draft)))
    },
    async submit(draft = false) {
      try {
        this.loading = true

        const data = Object.assign({}, this.form)
        data.departure = data.departure.join('-')
        data.transit = data.transit.join('-')
        data.destination = data.destination.join('-')

        const response = this.isUpdating
          ? await inquiryApi.updateInquiry(this.id, { ...data, ...{ is_draft: draft ? 1 : 0 } })
          : await inquiryApi.createInquiry({ ...data, ...{ is_draft: draft ? 1 : 0 } })

        const id = this.isUpdating ? this.id : response.data.id

        this.$message.success(this.isUpdating ? '更新成功' : '保存成功')

        this.$router.push({ name: 'InquiryDetail', params: { id } })
      } finally {
        this.loading = false
      }
    }
  }
}
</script>
