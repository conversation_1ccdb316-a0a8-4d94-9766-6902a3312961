<template>
  <div class="w-100">
    <simple-container class="bg-white p-extra-large d-flex flex-column o-hidden">
      <operation-bar :data="data" @refresh="fetchDetail" />

      <el-alert :closable="false" type="warning" show-icon class="m-extra-large-t">
        <template #title>
          <span>提醒</span>
          <small>
            <el-link icon="el-icon-edit" type="primary" class="m-mini-l" @click="handleUpdateAlert">编辑</el-link>
          </small>
        </template>
        {{ data?.alert || '无' }}
      </el-alert>

      <define-details :data.sync="detailData" />
    </simple-container>

    <div class="bg-white m-extra-large-t p-extra-large o-hidden">
      <el-button type="primary" @click="displayingSendRequestDialog">发送询价单</el-button>

      <define-table :data="data?.requests" :cols="requestCols" />
    </div>

    <send-request-dialog
      :visible.sync="requestDialog.visible"
      v-if="requestDialog.visible"
      :id="requestDialog.id"
      :reasons="reasons"
      @done="fetchDetail"
    />

    <update-request-dialog
      :visible.sync="updateDialog.visible"
      v-if="updateDialog.visible"
      :id="updateDialog.id"
      :request-id="updateDialog.requestId"
      :values="updateDialog.values"
      @done="fetchDetail"
    />

    <reject-request-dialog
      :visible.sync="rejectDialog.visible"
      v-if="rejectDialog.visible"
      :id="rejectDialog.id"
      :request-id="rejectDialog.requestId"
      @done="fetchDetail"
    />
  </div>
</template>

<script>
import * as inquiryApi from '@/apis/inquiry'
import SendRequestDialog from './partials/SendRequestDialog.vue'
import UpdateRequestDialog from './partials/UpdateRequestDialog.vue'
import RejectRequestDialog from './partials/RejectRequestDialog.vue'
import OperationBar from './partials/OperationBar.vue'
import { Loading } from 'element-ui'
import currencies from '@/utils/currency'

export default {
  components: {
    SendRequestDialog,
    UpdateRequestDialog,
    RejectRequestDialog,
    OperationBar
  },
  data() {
    return {
      currencies,
      data: {},
      companies: [],
      reasons: [
        { value: 1, label: '精密仪器' },
        { value: 2, label: '车辆、摩托车' },
        { value: 3, label: '超限额' },
        { value: 4, label: '散货船' },
        { value: 5, label: '特殊区域' },
        { value: 7, label: '线下出单' },
        { value: 8, label: '空运冷藏' },
        { value: 9, label: '锂电池' },
        { value: 10, label: '特种集装箱' },
        { value: 99, label: '其他' }
      ],
      requestDialog: {
        id: 0,
        visible: false
      },
      rejectDialog: {
        id: 0,
        requestId: 0,
        visible: false
      },
      updateDialog: {
        id: 0,
        requestId: 0,
        values: {},
        visible: false
      }
    }
  },
  computed: {
    requestCols() {
      return [
        { label: '出单公司', prop: 'company', width: 100 },
        {
          label: '询价原因',
          width: 100,
          scopedSlots: {
            default: (scoped) => {
              let text = ''
              scoped.row.reasons.forEach((reason) => {
                const found = this.reasons.find((item) => item.value === reason)
                if (found) {
                  text += `${found.label},`
                }
              })
              return text.slice(0, -1) || '无'
            }
          }
        },
        { label: '费率', prop: 'rate', width: 100 },
        { label: '免赔特约', prop: 'deductible_special_terms' },
        { label: '备注', prop: 'remarks' },
        {
          label: '创建/更新时间',
          width: 150,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.created_at}</label>
                  <br />
                  <small>{scoped.row.updated_at}</small>
                </div>
              )
            }
          }
        },
        {
          label: '状态',
          width: 80,
          scopedSlots: {
            default: (scoped) => {
              return (
                {
                  0: '待发送',
                  1: '已发送',
                  2: '发送失败',
                  3: '已回复',
                  4: '婉拒'
                }[scoped.row.status] || '未知'
              )
            }
          }
        },
        {
          label: '操作',
          width: 120,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    type="text"
                    disabled={![1, 3].includes(scoped.row.status)}
                    onClick={() => this.displayingUpdateDialog(scoped.row)}
                  >
                    更新
                  </el-button>
                  <el-button
                    type="text"
                    disabled={scoped.row.status !== 1}
                    onClick={() => this.displayingRejectDialog(scoped.row)}
                  >
                    婉拒
                  </el-button>
                  <el-popconfirm
                    style="margin-left: 10px"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    icon="el-icon-info"
                    icon-color="red"
                    title="确定删除吗？"
                    onConfirm={() => this.handleDelete(scoped.row)}
                  >
                    <el-button size="small" type="text" slot="reference">
                      删除
                    </el-button>
                  </el-popconfirm>
                </div>
              )
            }
          }
        }
      ]
    },
    detailData() {
      return {
        title: '询价单',
        data: [
          {
            title: '询价信息',
            groups: [
              { label: '询价单号', value: this.data?.inquiry_number },
              {
                label: '投保单流水号',
                value: this.data?.policy?.order_no || '无'
              },
              {
                label: '状态',
                value:
                  { 0: '暂存单', 1: '待处理', 2: '已发送', 3: '已回复', 4: '婉拒', 5: '已出单' }[this.data?.status] ||
                  ''
              },
              { label: '询价用户', value: this.data?.user?.name },
              { label: '投保人', value: this.data?.policyholder_name },
              { label: '被保险人', value: this.data?.insured_name },
              { label: '货物名称', value: this.data?.goods_name },
              { label: '包装及数量', value: this.data?.packaging_and_quantity },
              {
                label: '货物价值',
                value: `${this.data?.goods_value} ${
                  currencies.find((e) => e.value === this.data?.goods_value_currency)?.label
                }`
              },
              { label: '货物类别', value: this.data?.goods_type },
              { label: '起运地', value: this.data?.departure + ' ' + this.data?.departure_address },
              {
                label: '运输途经地',
                value: (this.data?.transit || '') + ' ' + (this.data?.transit_address || '')
              },
              { label: '目的地', value: this.data?.destination + ' ' + this.data?.destination_address },
              { label: '起运日期', value: this.data?.departure_date },
              { label: '险种', value: this.data?.insurance_type },
              { label: '运输方式', value: { 1: '空运', 2: '水运', 3: '陆运' }[this.data?.transport_mode] || '' },
              {
                label: '装载方式',
                value:
                  { 1: '集装箱运输', 2: '非集装箱运输', 3: '厢式货车', 4: '非厢式货车' }[this.data?.loading_method] ||
                  ''
              },
              { label: '船舶信息', value: this.data?.vessel_info },
              { label: '装载位置', value: { 1: '甲板', 2: '仓内' }[this.data?.loading_position] || '' },
              { label: '配载计划', value: this.data?.stowage_plan },
              { label: '陆上运输计划', value: this.data?.land_transport_plan },
              { label: '是否包含仓储装卸费', value: this.data?.include_warehouse_handling ? '是' : '否' },
              { label: '是否有特殊运输要求', value: this.data?.has_special_transport_reqs ? '是' : '否' },
              { label: '是否新货物', value: this.data?.is_new_goods ? '是' : '否' },
              { label: '尺寸（长*宽*高）', value: this.data?.dimensions },
              { label: '备注', value: this.data?.remarks }
            ]
          },
          {
            title: '照片',
            groups:
              this.data?.photos?.map((photo) => ({
                label: photo.name,
                value: `<a href="${photo.url}" target="_blank">点击查看</a>`
              })) || []
          }
        ]
      }
    }
  },
  created() {
    this.fetchDetail()
  },
  methods: {
    async fetchDetail() {
      const { data } = await inquiryApi.getInquiryDetail(this.$route.params.id)
      this.data = data
    },
    displayingUpdateDialog(data) {
      this.updateDialog.visible = true
      this.updateDialog.id = Number(this.$route.params.id)
      this.updateDialog.requestId = Number(data.id)
      this.updateDialog.values = {
        rate: data.rate,
        deductible_special_terms: data.deductible_special_terms,
        remarks: data.remarks
      }
    },
    displayingSendRequestDialog() {
      this.requestDialog.visible = true
      this.requestDialog.id = Number(this.$route.params.id)
    },
    displayingRejectDialog(data) {
      this.rejectDialog.visible = true
      this.rejectDialog.id = Number(this.$route.params.id)
      this.rejectDialog.requestId = Number(data.id)
    },
    async handleDelete(data) {
      const loading = Loading.service()
      try {
        await inquiryApi.deleteInquiryRequest(this.$route.params.id, data.id)
        this.$message.success('删除成功')
      } finally {
        loading.close()
        await this.fetchDetail()
      }
    },
    async handleUpdateAlert() {
      this.$prompt('请输入提醒内容', '提醒', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: '请输入提醒内容',
        inputValue: this.data.alert || ''
      }).then(async ({ value }) => {
        const loading = Loading.service()
        try {
          await inquiryApi.updateAlertContent(this.$route.params.id, value)
          this.$message.success('更新成功')
          await this.fetchDetail()
        } finally {
          loading.close()
        }
      })
    }
  }
}
</script>
