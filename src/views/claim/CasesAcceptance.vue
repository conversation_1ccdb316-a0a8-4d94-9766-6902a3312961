<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <SearchPanel
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :exportable="true"
      :custom="searchFields"
    ></SearchPanel>
    <el-card shadow="never" class="m-extra-large-t">
      <define-table :data="tableData" :cols="cols" :paging="paging" :paging-events="pagingEvents" />
    </el-card>
  </div>
</template>

<script>
import * as adminApi from '@/apis/admin'
import * as platformProductApi from '@/apis/platform_product'
import * as claimApi from '@/apis/claim'

export default {
  data() {
    return {
      rawCompanies: [],
      searchFields: [
        {
          type: 'input',
          valKey: 'policyholder',
          hintText: '投保人'
        },
        {
          type: 'input',
          valKey: 'insured',
          hintText: '被保险人'
        },
        {
          type: 'input',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'select',
          valKey: 'salesman_id',
          hintText: '业务员',
          options: []
        },
        {
          type: 'input',
          valKey: 'case_no',
          hintText: '理赔编号'
        },
        {
          type: 'select',
          valKey: 'company_id',
          hintText: '保险公司',
          onChangeReset: 'company_branch_id',
          options: []
        },
        {
          type: 'select',
          valKey: 'company_branch_id',
          isMultiple: true,
          hintText: '出单公司',
          options: []
        },
        {
          type: 'daterange',
          valKey: 'date_of_reporting_range',
          hintText: '报案'
        },
        {
          type: 'daterange',
          valKey: 'date_of_loss_range',
          hintText: '出险'
        }
      ],
      cols: [
        {
          label: '险种',
          width: 100,
          fixed: 'left',
          scopedSlots: {
            default: (scoped) => {
              const types = {
                1: '国内货运险',
                2: '国际货运险',
                3: '单车责任险',
                4: '其他险种',
                5: '雇主责任险',
                6: '线下录入保单',
                7: '跨境电商险'
              }

              return <div>{scoped.row.policy_type === 6 ? scoped.row.offline_type : types[scoped.row.policy_type]}</div>
            }
          }
        },
        {
          label: '保单号/理赔编号',
          fixed: 'left',
          width: 180,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.policy_no}</label>
                  <br />
                  <small>{scoped.row.case_no}</small>
                </div>
              )
            }
          }
        },
        {
          prop: 'insured',
          label: '投保人/被保人',
          width: 300,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.policyholder}</label>
                  <br />
                  <small>{scoped.row.insured ? scoped.row.insured : ''}</small>
                </div>
              )
            }
          }
        },
        {
          label: '标的',
          prop: 'subject',
          width: 180
        },
        {
          label: '出险原因',
          prop: 'loss_reason',
          width: 150
        },
        {
          label: '提交时间/出险时间',
          width: 150,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.created_at}</label>
                  <br />
                  <small>{scoped.row.loss_of_date}</small>
                </div>
              )
            }
          }
        },
        {
          label: '出险地点',
          prop: 'loss_location',
          width: 150
        },
        {
          label: '损失类型',
          width: 150,
          scopedSlots: {
            default: (scoped) => {
              const types = {
                1: '人损',
                2: '物损'
              }

              return <div>{types[scoped.row.loss_category]}</div>
            }
          }
        },
        {
          width: 100,
          label: '报损金额',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.loss_amount}</label>
                  <br />
                  <small>{scoped.row.loss_amount_currency.name}</small>
                </div>
              )
            }
          }
        },
        {
          label: '事故经过',
          prop: 'loss_detail',
          width: 180
        },
        {
          label: '报案人',
          width: 150,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.claimant}</label>
                  <br />
                  <small>{scoped.row.claimant_phone_number}</small>
                  <br />
                  <small>{scoped.row.claimant_email}</small>
                </div>
              )
            }
          }
        },
        {
          label: '操作',
          align: 'center',
          fixed: 'right',
          width: 100,
          scopedSlots: {
            default: (scope) => {
              return (
                <div>
                  <el-button
                    v-can={{ name: 'claim.cases.accept' }}
                    plain
                    size="small"
                    icon="el-icon-open"
                    onClick={() => this.acceptCase(scope.row.id)}
                  >
                    受理
                  </el-button>
                </div>
              )
            }
          }
        }
      ],
      tableData: [],
      // 分页器 不传递 -> 没有分页
      paging: {
        currentPage: 1,
        pageSize: 0,
        layout: 'prev, pager, next, jumper, total',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page

          this.fetchCases()
        }
      },
      searchQuery: {},
      searchData: {},
      payees: []
    }
  },
  watch: {
    searchData: {
      immediate: true,
      deep: true,
      handler(value, old) {
        if (old?.company_id !== value?.company_id) {
          this.loadCompanyBranches()
        }
      }
    }
  },
  async created() {
    await this.initSalesmanSelect()
    await this.initCompanySelect()
    await this.fetchCases()
  },
  methods: {
    async initSalesmanSelect() {
      adminApi.getSales().then((r) => {
        r.data.push({ name: '自然来源', id: -1 })
        this.assignSelectOptions(
          'salesman_id',
          r.data.map((e) => {
            return { label: e.name, value: e.id }
          })
        )
      })
    },
    async initCompanySelect() {
      const data = await platformProductApi.getPlatformProductCompanies()
      this.rawCompanies = data.data

      this.loadCompanies()
      this.loadCompanyBranches()
    },
    loadCompanies() {
      const options = this.rawCompanies.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('company_id', options)
    },
    loadCompanyBranches() {
      let options = []
      if (this.searchData?.company_id !== '' && this.searchData?.company_id !== undefined) {
        const company = this.rawCompanies.find((e) => e.id === this.searchData?.company_id)
        if (company) {
          options = company.branches.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      } else {
        this.rawCompanies.forEach((e) => {
          e?.branches.forEach((e) => {
            options.push({
              label: e.name,
              value: e.id
            })
          })
        })
      }

      this.assignSelectOptions('company_branch_id', options)
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    },
    handleSearchPanel(command, data) {
      this.searchQuery = data
      this.paging.page = 1
      if (command !== 'export') {
        this.fetchCases()
      } else {
        window.location.href = claimApi.buildCasesXlsxUrl(
          {
            filter: Object.assign(this.searchQuery, {
              operator_id: -1
            })
          },
          '_blank'
        )
      }
    },
    async fetchCases() {
      const data = await claimApi.fetchCases({
        page: this.paging.page,
        filter: Object.assign(this.searchQuery, {
          operator_id: -1
        })
      })

      this.tableData = data.data
      this.paging.currentPage = data.meta.current_page
      this.paging.pageSize = data.meta.per_page
      this.paging.total = data.meta.total
    },
    async acceptCase(id) {
      try {
        await this.$confirm('是否确定受理此案?', '提示', {
          confirmButtonText: '是',
          cancelButtonText: '否',
          type: 'warning'
        })

        await claimApi.acceptCase(id)
        this.$message.success('案件受理成功')
        this.$router.push({ name: 'ClaimWorkplace' })
      } catch {
        //
      }
    }
  }
}
</script>
