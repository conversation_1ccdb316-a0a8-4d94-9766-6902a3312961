<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <SearchPanel
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :exportable="true"
      :custom="searchFields"
    ></SearchPanel>
    <el-card shadow="never" class="m-extra-large-t">
      <define-table :data="tableData" :cols="cols" :paging="paging" :paging-events="pagingEvents" />
    </el-card>
  </div>
</template>

<script>
import * as adminApi from '@/apis/admin'
// import * as platformProductApi from '@/apis/platform_product'
import * as companyApi from '@/apis/company'
import * as claimApi from '@/apis/claim'
import * as platformApi from '@/apis/platform'
import * as subjectApi from '@/apis/subject'
import { getOfflineProductCategories } from '@/apis/product'

export default {
  name: 'Claims',
  data() {
    return {
      rawCompanies: [],
      platforms: [],
      offlineCategories: [],
      searchFields: [
        {
          type: 'input',
          valKey: 'policyholder',
          hintText: '投保人'
        },
        {
          type: 'input',
          valKey: 'insured',
          hintText: '被保险人'
        },
        {
          type: 'input',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'select',
          valKey: 'salesman_id',
          hintText: '业务员',
          options: []
        },
        {
          type: 'input',
          valKey: 'case_no',
          hintText: '理赔编号/报司报案号'
        },
        {
          type: 'select',
          valKey: 'types',
          hintText: '险种',
          isMultiple: true,
          options: []
        },
        {
          type: 'select',
          valKey: 'company_id',
          hintText: '保险公司',
          onChangeReset: 'company_branch_id',
          options: []
        },
        {
          type: 'select',
          valKey: 'company_branch_id',
          isMultiple: true,
          hintText: '出单公司',
          options: []
        },
        {
          type: 'select',
          valKey: 'operator_id',
          hintText: '理赔员',
          options: []
        },
        {
          type: 'daterange',
          valKey: 'date_of_reporting_range',
          hintText: '报案'
        },
        {
          type: 'daterange',
          valKey: 'date_of_loss_range',
          hintText: '出险'
        },
        {
          type: 'daterange',
          valKey: 'submitted_at_range',
          hintText: '投保'
        },
        {
          type: 'select',
          valKey: 'status',
          hintText: '状态',
          options: []
        },
        {
          type: 'select',
          valKey: 'is_typical_case',
          hintText: '是否典型案例',
          options: [
            { value: 1, label: '是' },
            { value: 0, label: '否' }
          ]
        },
        {
          type: 'select',
          valKey: 'is_archived',
          hintText: '是否已归档',
          options: [
            { value: 1, label: '是' },
            { value: 0, label: '否' }
          ]
        },
        {
          type: 'select',
          valKey: 'product_source',
          hintText: '产品来源',
          options: []
        },
        {
          type: 'select',
          valKey: 'business_source',
          hintText: '业务来源',
          options: []
        },
        {
          type: 'select',
          valKey: 'subject_id',
          hintText: '标的',
          options: []
        },
        {
          type: 'input',
          valKey: 'username',
          hintText: '投保用户'
        }
      ],
      cols: [
        {
          label: '理赔编号',
          prop: 'case_no',
          width: 150,
          fixed: 'left'
        },
        {
          label: '险种',
          width: 100,
          fixed: 'left',
          scopedSlots: {
            default: (scoped) => {
              const types = {
                1: '国内货运险',
                2: '国际货运险',
                3: '单车责任险',
                4: '其他险种',
                5: '雇主责任险',
                6: '线下录入保单',
                7: '跨境电商险'
              }

              return <div>{scoped.row.policy_type === 6 ? scoped.row.offline_type : types[scoped.row.policy_type]}</div>
            }
          }
        },
        {
          label: '保单号/保司报案号',
          fixed: 'left',
          width: 180,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.policy_no}</label>
                  <br />
                  <small>{scoped.row.external_case_no || '-'}</small>
                </div>
              )
            }
          }
        },
        {
          label: '出单公司',
          prop: 'company_branch.name',
          width: 100
        },
        {
          prop: 'insured',
          label: '投保人/被保人',
          width: 300,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.policyholder}</label>
                  <br />
                  <small>{scoped.row.insured}</small>
                </div>
              )
            }
          }
        },
        {
          label: '标的',
          prop: 'subject',
          width: 150
        },
        {
          label: '报案时间/出险时间',
          width: 150,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.date_of_reporting}</label>
                  <br />
                  <small>{scoped.row.date_of_loss}</small>
                </div>
              )
            }
          }
        },
        {
          width: 100,
          label: '获赔金额',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.settlement_payment_amount}</label>
                  <br />
                  <small>{scoped.row.settlement_payment_amount_currency.name}</small>
                </div>
              )
            }
          }
        },
        {
          label: '保司理赔员',
          width: 150,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.external_adjuster}</label>
                  <br />
                  <small>{scoped.row.external_adjuster_phone_number}</small>
                  <br />
                  <small>{scoped.row.external_adjuster_email}</small>
                </div>
              )
            }
          }
        },
        {
          prop: 'operator',
          width: 150,
          label: '理赔员',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.operator.name}</label>
                  <br />
                  <small>{scoped.row.operator.email}</small>
                  <br />
                  <small>{scoped.row.operator.phone_number}</small>
                </div>
              )
            }
          }
        },
        {
          prop: 'status',
          width: 100,
          label: '状态',
          scopedSlots: {
            default: (scoped) => {
              const lastStatus = scoped.row.is_archived
                ? '归档'
                : scoped.row.status_text.slice(scoped.row.status_text.lastIndexOf('>') + 1)

              return (
                <div>
                  <span class="text-danger">{lastStatus}</span>
                  <el-popover
                    placement="bottom"
                    title="当前进度"
                    trigger="hover"
                    content={scoped.row.is_archived ? scoped.row.status_text + '->归档' : scoped.row.status_text}
                  >
                    <i slot="reference" class="el-icon-question text-primary"></i>
                  </el-popover>
                </div>
              )
            }
          }
        },
        {
          label: '操作',
          align: 'center',
          fixed: 'right',
          width: 180,
          scopedSlots: {
            default: (scope) => {
              return (
                <div>
                  <el-button
                    v-can={{ name: 'claim.cases.show' }}
                    plain
                    size="small"
                    icon="el-icon-view"
                    onClick={() =>
                      this.$router.push({
                        name: 'ClaimCasesDetailReadonly',
                        params: { id: scope.row.id },
                        query: { is_readonly: true }
                      })
                    }
                  >
                    详情
                  </el-button>
                  <el-button
                    v-can={{ name: 'claim.cases.reopen' }}
                    plain
                    size="small"
                    icon="el-icon-open"
                    disabled={!scope.row.is_archived}
                    onClick={() => {
                      this.reopenClaim(scope.row.id)
                    }}
                  >
                    重开
                  </el-button>
                </div>
              )
            }
          }
        }
      ],
      tableData: [],
      // 分页器 不传递 -> 没有分页
      paging: {
        currentPage: 1,
        pageSize: 0,
        layout: 'prev, pager, next, jumper, total',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page

          this.fetchCases()
        }
      },
      searchQuery: {},
      searchData: {},
      payees: []
    }
  },
  watch: {
    searchData: {
      immediate: true,
      deep: true,
      handler(value, old) {
        if (old?.company_id !== value?.company_id) {
          this.loadCompanyBranches()
        }
      }
    }
  },
  async created() {
    await this.initSalesmanSelect()
    await this.initAdjustersSelect()
    await this.initCompanySelect()
    await this.initPlatforms()
    await this.initTypes()
    await this.fetchSubjecs()
    await this.fetchCases()
  },
  methods: {
    async initSalesmanSelect() {
      adminApi.getSales().then((r) => {
        r.data.push({ name: '自然来源', id: -1 })
        this.assignSelectOptions(
          'salesman_id',
          r.data.map((e) => {
            return { label: e.name, value: e.id }
          })
        )
      })
    },
    async initCompanySelect() {
      const data = await companyApi.getCompaniesDict()
      this.rawCompanies = data.data

      this.loadCompanies()
      this.loadCompanyBranches()
    },
    async initAdjustersSelect() {
      adminApi.getClaimsAdjusters().then((r) => {
        this.assignSelectOptions(
          'operator_id',
          r.data.map((e) => {
            return { label: e.name, value: e.id }
          })
        )
      })
    },
    loadCompanies() {
      const options = this.rawCompanies.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('company_id', options)
    },
    loadCompanyBranches() {
      let options = []
      if (this.searchData?.company_id !== '' && this.searchData?.company_id !== undefined) {
        const company = this.rawCompanies.find((e) => e.id === this.searchData?.company_id)
        if (company) {
          options = company.branches.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      } else {
        this.rawCompanies.forEach((e) => {
          e?.branches.forEach((e) => {
            options.push({
              label: e.name,
              value: e.id
            })
          })
        })
      }

      this.assignSelectOptions('company_branch_id', options)
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    },
    handleSearchPanel(command, data) {
      this.searchQuery = data
      this.paging.page = 1
      if (command !== 'export') {
        this.fetchCases()
      } else {
        window.location.href = claimApi.buildCasesXlsxUrl({ filter: this.searchQuery }, '_blank')
      }
    },
    async fetchCases() {
      const data = await claimApi.fetchCases({
        page: this.paging.page,
        filter: this.searchQuery
      })

      this.searchFields[this.searchFields.findIndex((item) => item.valKey === 'status')].options = data.statuses.map(
        (item) => {
          return {
            label: item.label.slice(item.label.lastIndexOf('>') + 1),
            value: item.value
          }
        }
      )

      this.tableData = data.data
      this.paging.currentPage = data.meta.current_page
      this.paging.pageSize = data.meta.per_page
      this.paging.total = data.meta.total
    },
    async reopenClaim(id) {
      try {
        await this.$confirm('是否确定重开此案?', '提示', {
          confirmButtonText: '是',
          cancelButtonText: '否',
          type: 'warning'
        })

        await claimApi.reopenCase(id)
        this.$message.success('重开案件成功')
        this.fetchCases()
      } catch {
        //
      }
    },
    async initPlatforms() {
      const platforms = await platformApi.getPlatformsDict()
      const options = platforms.data.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('product_source', options)
      this.assignSelectOptions('business_source', options)
    },
    async initTypes() {
      getOfflineProductCategories({ is_parent: 0, is_pageable: 0 }).then((r) => {
        this.offlineCategories = r.data

        this.loadCategories()
      })
    },
    loadCategories() {
      var options = [
        {
          label: '国内货运险',
          value: 1
        },
        {
          label: '国际货运险',
          value: 2
        },
        {
          label: '单车责任险',
          value: 3
        },
        {
          label: '其他险种',
          value: 4
        },
        {
          label: '雇主责任险',
          value: 5
        },
        {
          label: '跨境电商险',
          value: 7
        }
      ]
      options = options.concat(
        this.offlineCategories.map((e) => {
          return {
            label: e.name + '-线下录入',
            value: 'OF-' + e.id
          }
        })
      )
      this.assignSelectOptions('types', options)
    },
    async fetchSubjecs() {
      const data = (await subjectApi.getSubjects()).data
      this.assignSelectOptions(
        'subject_id',
        data.map((e) => {
          return { label: e.name, value: e.id }
        })
      )
    }
  }
}
</script>
