<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <SearchPanel
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :exportable="true"
      :custom="searchFields"
    ></SearchPanel>

    <el-alert
      v-if="badges?.count?.hurry_up_count > 0 || badges?.count?.apply_cancellation_count > 0"
      title="提醒"
      style="margin-top: 10px"
      type="error"
      :closable="false"
    >
      <span v-if="badges?.count?.hurry_up_count > 0">
        <el-checkbox type="danger" v-model="searchQuery.hurry_up" effect="dark" @change="reloadData" />
        <span>
          当前有 {{ badges?.count?.hurry_up_count }}
          单需要加急处理
        </span>
      </span>
      <span v-if="badges?.count?.apply_cancellation_count > 0">
        <el-checkbox type="danger" v-model="searchQuery.apply_cancellation_at" effect="dark" @change="reloadData" />
        <span>
          当前有 {{ badges?.count?.apply_cancellation_count }}
          单需要撤案
        </span>
      </span>
    </el-alert>

    <el-card shadow="never" class="m-extra-large-t">
      <template #header>
        <el-radio-group v-model="caseType">
          <el-radio-button :label="1">
            新案件区
            <span class="pending-notification-tips">
              {{ 1 | caseTypeNotificationCount(badges?.badges) }}
            </span>
          </el-radio-button>
          <el-radio-button :label="2">
            收集材料区
            <span class="pending-notification-tips">
              {{ 2 | caseTypeNotificationCount(badges?.badges) }}
            </span>
          </el-radio-button>
          <el-radio-button :label="3">
            审核阶段区
            <span class="pending-notification-tips">
              {{ 3 | caseTypeNotificationCount(badges?.badges) }}
            </span>
          </el-radio-button>
          <el-radio-button :label="4">
            支付阶段区
            <span class="pending-notification-tips">
              {{ 4 | caseTypeNotificationCount(badges?.badges) }}
            </span>
          </el-radio-button>
          <el-radio-button :label="5"> 已结案待归档区 </el-radio-button>
        </el-radio-group>
      </template>

      <define-table :data="tableData" :cols="cols" :paging="paging" :paging-events="pagingEvents" />
    </el-card>
  </div>
</template>

<script>
import * as adminApi from '@/apis/admin'
import * as platformProductApi from '@/apis/platform_product'
import * as claimApi from '@/apis/claim'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      caseType: 1,
      badges: {
        count: {
          hurry_up_count: 0,
          apply_cancellation_count: 0
        },
        badges: []
      },
      rawCompanies: [],
      searchFields: [
        {
          type: 'input',
          valKey: 'policyholder',
          hintText: '投保人'
        },
        {
          type: 'input',
          valKey: 'insured',
          hintText: '被保险人'
        },
        {
          type: 'input',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'select',
          valKey: 'salesman_id',
          hintText: '业务员',
          options: []
        },
        {
          type: 'input',
          valKey: 'case_no',
          hintText: '理赔编号/报司报案号'
        },
        {
          type: 'select',
          valKey: 'company_id',
          hintText: '保险公司',
          onChangeReset: 'company_branch_id',
          options: []
        },
        {
          type: 'select',
          valKey: 'company_branch_id',
          isMultiple: true,
          hintText: '出单公司',
          options: []
        },
        {
          type: 'select',
          valKey: 'hurry_up',
          hintText: '是否加急',
          options: [
            { label: '是', value: true },
            { label: '否', value: false }
          ]
        },
        {
          type: 'daterange',
          valKey: 'date_of_reporting_range',
          hintText: '报案'
        },
        {
          type: 'daterange',
          valKey: 'date_of_loss_range',
          hintText: '出险'
        }
      ],
      cols: [
        {
          label: '理赔编号',
          prop: 'case_no',
          width: 150,
          fixed: 'left'
        },
        {
          label: '险种',
          width: 120,
          fixed: 'left',
          scopedSlots: {
            default: (scoped) => {
              const types = {
                1: '国内货运险',
                2: '国际货运险',
                3: '单车责任险',
                4: '其他险种',
                5: '雇主责任险',
                6: '线下录入保单',
                7: '跨境电商险'
              }

              return <div>{scoped.row.policy_type === 6 ? scoped.row.offline_type : types[scoped.row.policy_type]}</div>
            }
          }
        },
        {
          label: '保单号/保司报案号',
          fixed: 'left',
          width: 180,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.policy_no}</label>
                  <br />
                  <small>{scoped.row.external_case_no}</small>
                </div>
              )
            }
          }
        },
        {
          label: '出单公司',
          prop: 'company_branch.name',
          width: 150
        },
        {
          prop: 'insured',
          label: '投保人/被保人',
          width: 300,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.policyholder}</label>
                  <br />
                  <small>{scoped.row.insured ? scoped.row.insured : ''}</small>
                </div>
              )
            }
          }
        },
        {
          width: 150,
          label: '报案时间/报案时长(天)',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.date_of_reporting}</label>
                  <br />
                  <small>{scoped.row.case_duration}</small>
                </div>
              )
            }
          }
        },
        {
          label: '标的',
          prop: 'subject',
          width: 150
        },
        {
          prop: 'status',
          width: 100,
          label: '理赔进度',
          scopedSlots: {
            default: (scoped) => {
              const lastStatus = scoped.row.status_text.slice(scoped.row.status_text.lastIndexOf('>') + 1)
              return (
                <div>
                  <span class="text-danger">{lastStatus}</span>
                  <el-popover placement="bottom" title="当前进度" trigger="hover" content={scoped.row.status_text}>
                    <i slot="reference" class="el-icon-question text-primary"></i>
                  </el-popover>
                </div>
              )
            }
          }
        },
        {
          width: 100,
          label: '获赔金额',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.settlement_payment_amount}</label>
                  <br />
                  <small>{scoped.row.settlement_payment_amount_currency.name}</small>
                </div>
              )
            }
          }
        },
        {
          prop: 'operator',
          width: 200,
          label: '保司理赔员',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.external_adjuster}</label>
                  <br />
                  <small>{scoped.row.external_adjuster_phone_number}</small>
                  <br />
                  <small>{scoped.row.external_adjuster_email}</small>
                </div>
              )
            }
          }
        },
        {
          prop: 'operator',
          width: 200,
          label: '理赔员',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.operator.name}</label>
                  <br />
                  <small>{scoped.row.operator.phone_number}</small>
                  <br />
                  <small>{scoped.row.operator.email}</small>
                </div>
              )
            }
          }
        },
        {
          label: '操作',
          align: 'center',
          fixed: 'right',
          width: 100,
          scopedSlots: {
            default: (scope) => {
              return (
                <div>
                  <el-button
                    v-can={{ name: 'claim.cases.show' }}
                    plain
                    size="small"
                    icon="el-icon-view"
                    onClick={() => this.$router.push({ name: 'ClaimCasesDetail', params: { id: scope.row.id } })}
                  >
                    详情
                  </el-button>
                </div>
              )
            }
          }
        }
      ],
      tableData: [],
      // 分页器 不传递 -> 没有分页
      paging: {
        currentPage: 1,
        pageSize: 0,
        layout: 'prev, pager, next, jumper, total',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page

          this.fetchCases()
        }
      },
      searchQuery: {},
      searchData: {},
      payees: []
    }
  },
  watch: {
    caseType() {
      this.fetchCases()
    },
    searchData: {
      immediate: true,
      deep: true,
      handler(value, old) {
        if (old?.company_id !== value?.company_id) {
          this.loadCompanyBranches()
        }
      }
    }
  },
  computed: {
    ...mapGetters('auth', ['admin'])
  },
  filters: {
    caseTypeNotificationCount(type, badges) {
      return badges?.find((e) => e.type === type)?.count || 0
    }
  },
  async created() {
    await this.initSalesmanSelect()
    await this.initCompanySelect()
    await this.fetchCases()
    await this.fetchBadges()
  },
  methods: {
    async reloadData() {
      await this.fetchCases()
      await this.fetchBadges()
    },
    async fetchBadges() {
      const data = await claimApi.fetchBadges({
        filter: Object.assign(this.searchQuery, {
          case_type: ''
        })
      })
      this.badges = data.data
    },
    async initSalesmanSelect() {
      adminApi.getSales().then((r) => {
        r.data.push({ name: '自然来源', id: -1 })
        this.assignSelectOptions(
          'salesman_id',
          r.data.map((e) => {
            return { label: e.name, value: e.id }
          })
        )
      })
    },
    async initCompanySelect() {
      const data = await platformProductApi.getPlatformProductCompanies()
      this.rawCompanies = data.data

      this.loadCompanies()
      this.loadCompanyBranches()
    },
    loadCompanies() {
      const options = this.rawCompanies.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('company_id', options)
    },
    loadCompanyBranches() {
      let options = []
      if (this.searchData?.company_id !== '' && this.searchData?.company_id !== undefined) {
        const company = this.rawCompanies.find((e) => e.id === this.searchData?.company_id)
        if (company) {
          options = company.branches.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      } else {
        this.rawCompanies.forEach((e) => {
          e?.branches.forEach((e) => {
            options.push({
              label: e.name,
              value: e.id
            })
          })
        })
      }

      this.assignSelectOptions('company_branch_id', options)
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    },
    handleSearchPanel(command, data) {
      this.searchQuery = data
      this.paging.page = 1
      if (command !== 'export') {
        this.fetchCases()
      } else {
        window.location.href = claimApi.buildCasesXlsxUrl(
          {
            filter: Object.assign(this.searchQuery, {
              case_type: this.caseType,
              operator_id: this.admin.id
            })
          },
          '_blank'
        )
      }
    },
    async fetchCases() {
      const data = await claimApi.fetchCases({
        page: this.paging.page,
        filter: Object.assign(this.searchQuery, {
          case_type: this.caseType,
          operator_id: this.admin.id
        })
      })

      this.tableData = data.data
      this.paging.currentPage = data.meta.current_page
      this.paging.pageSize = data.meta.per_page
      this.paging.total = data.meta.total
    }
  }
}
</script>

<style lang="scss">
.pending-notification-tips {
  border-radius: 99px;
  display: inline-block;
  text-align: center;
  line-height: 8px;
  min-width: 10px;
  max-height: 8px;
  padding: 2px;
  background-color: #f56c6c;
  font-size: 12px;
  z-index: 99;
  margin-left: 2px;
  color: white;
}
</style>
