<template>
  <div class="w-100">
    <simple-container class="bg-white p-extra-large d-flex flex-column o-hidden">
      <el-alert title="当前进度" :description="caseData?.status_text" type="success" :closable="false"></el-alert>
      <el-alert
        title="催办提醒"
        class="m-mini-t"
        v-if="caseData?.hurry_up_at"
        :description="`客户于 ${caseData?.hurry_up_at} 提交了加急申请`"
        type="error"
        :closable="false"
      ></el-alert>
      <el-alert
        title="撤案提醒"
        class="m-mini-t"
        v-if="caseData?.apply_cancellation_at"
        :description="`客户于 ${caseData?.apply_cancellation_at} 提交了撤案申请`"
        type="error"
        :closable="false"
      ></el-alert>
      <vue-html2pdf
        :enable-download="true"
        filename="赔案理算书"
        :pdf-quality="2"
        :manual-pagination="true"
        pdf-content-width="95%"
        width="100%"
        pdf-format="a4"
        pdf-orientation="landscape"
        ref="html2Pdf"
      >
        <section slot="pdf-content">
          <report-pdf :data.sync="caseData" />
        </section>
      </vue-html2pdf>
      <define-details :data.sync="detailData" @changed="handleCaseDataChanged" />
      <define-table :cols="attachmentCols" :data="caseData?.attachments" :attrs="{ border: true }"></define-table>
    </simple-container>

    <template v-if="!isReadonly && !caseData?.is_archived">
      <div class="fixed-operation-box bg-white p-extra-large">
        <div class="operation-buttons">
          <el-button
            type="primary"
            v-can="{ name: 'claim.cases.update-status' }"
            :disabled="![1].includes(caseData?.status)"
            @click="confirmAction('确认撤案吗?', handleCancel)"
          >
            撤案
          </el-button>
          <el-button
            type="primary"
            v-can="{ name: 'claim.cases.update-status' }"
            :disabled="![1].includes(caseData?.status)"
            @click="confirmAction('确认进入材料收集阶段吗?', handleCollect)"
          >
            收集材料
          </el-button>
          <el-button
            type="primary"
            v-can="{ name: 'claim.cases.update-status' }"
            :disabled="![2].includes(caseData?.status)"
            @click="confirmAction('确认进入保司审核阶段吗?', handleAudit)"
          >
            保司审核
          </el-button>
          <el-button
            type="primary"
            v-can="{ name: 'claim.cases.update-status' }"
            :disabled="![3].includes(caseData?.status)"
            @click="confirmAction('确认进入零赔付阶段吗?', handleZero)"
          >
            零赔付
          </el-button>
          <el-button
            type="primary"
            v-can="{ name: 'claim.cases.update-status' }"
            :disabled="![3].includes(caseData?.status)"
            @click="confirmAction('确认进入拒赔阶段吗?', handleReject)"
          >
            拒赔
          </el-button>
          <el-button
            type="primary"
            v-can="{ name: 'claim.cases.update-status' }"
            :disabled="![3].includes(caseData?.status)"
            @click="confirmAction('确认进入领赔款阶段吗?', handleReceive)"
          >
            领赔款
          </el-button>
          <el-button type="primary" v-can="{ name: 'claim.cases.transfer' }" @click="() => (dialog.transfer = true)"
            >案件转派</el-button
          >
          <el-button
            type="primary"
            v-can="{ name: 'claim.cases.send-back' }"
            :disabled="![0, 2, 3, 4, 5, 6, 7].includes(caseData?.status)"
            @click="confirmAction('确认退回吗?', handleReturn)"
          >
            退回
          </el-button>
          <el-button
            type="primary"
            v-can="{ name: 'claim.cases.update-status' }"
            :disabled="![6].includes(caseData?.status)"
            @click="confirmAction('确认结案吗?', handleFinish)"
          >
            结案
          </el-button>
          <el-button
            type="primary"
            v-can="{ name: 'claim.cases.archived' }"
            :disabled="caseData?.is_archived === 1 || ![0, 4, 5, 7].includes(caseData?.status)"
            @click="
              confirmAction(
                '是否保存为典型案例?',
                () => handleArchive(1),
                () => handleArchive(0)
              )
            "
          >
            归档
          </el-button>
          <el-button type="primary" v-can="{ name: 'claim.cases.download' }" @click="dialog.attachment = true">
            上传附件
          </el-button>
          <el-button
            type="primary"
            v-can="{ name: 'claim.cases.assessment-report-pdf' }"
            @click="confirmAction('确认导出赔案理算书吗?', handleDownloadConfirmationPdf)"
          >
            下载理算书
          </el-button>
          <el-button
            type="primary"
            :disabled="![1, 2].includes(caseData?.status)"
            v-can="{ name: 'claim.cases.send-email' }"
            @click="handleRenderEmail"
          >
            邮件报案
          </el-button>
          <el-button
            type="primary"
            v-can="{ name: 'claim.cases.save' }"
            @click="confirmAction('是否保存?', handleUpdateCase)"
          >
            保存
          </el-button>
        </div>
      </div>
    </template>

    <upload-attachment :visible.sync="dialog.attachment" :case-id="caseData?.id" @succeed="fetchCaseDetail" />
    <transfer
      :visible.sync="dialog.transfer"
      :case-id="caseData?.id || 0"
      @succeed="$router.push({ name: 'ClaimWorkplace' })"
    />

    <viewer :visible.sync="dialog.viewer.visible" :filename="dialog.viewer.filename" :href="dialog.viewer.href" />
    <el-dialog
      title="邮件预览"
      :visible.sync="dialog.email.visible"
      width="1024px"
      :before-close="() => (dialog.email.visible = false)"
      destroy-on-close
    >
      <template>
        <div style="width: 90%; margin: 0 auto">
          <h3>收件人</h3>
          {{ caseData?.external_adjuster_email }}
        </div>
        <div>
          <div style="width: 90%; margin: 0 auto">
            <h3>邮件内容</h3>
          </div>
          <div v-html="dialog.email.content"></div>
        </div>
        <div style="width: 90%; margin: 0 auto">
          <h3>附件</h3>
          <p v-for="attachment in caseData?.attachments" :key="attachment.id">
            <el-link :href="attachment.path" target="_blank">
              [{{ attachment.primary_category }}\{{ attachment.secondary_category }}] {{ attachment.name }}
            </el-link>
          </p>
        </div>
      </template>
      <template #footer>
        <el-button icon="fas fa-times" @click="dialog.email.visible = false">取消</el-button>
        <el-button type="primary" icon="fas fa-check" @click="confirmAction('确认发送至保司吗?', handleSendEmail)">
          发送
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import * as claimApi from '@/apis/claim'
import { getGoodsTypes } from '@/apis/goods_type'
import { getTransportMethods } from '@/apis/transport_method'
import { getLatestCurrencyExchange } from '@/apis/currency'
import UploadAttachment from '@/components/claim/UploadAttachment'
import Transfer from '@/components/claim/Transfer'
import ReportPdf from '@/components/claim/ReportPdf'
import { Loading } from 'element-ui'
import chinaAreadata from '@/utils/areadata.json'
import overseaAreadata from '@/utils/regions'
import Viewer from '@/components/viewer'
import VueHtml2pdf from 'vue-html2pdf'
import * as reasons from '@/utils/loss_reason'
import dayjs from 'dayjs'

export default {
  components: {
    UploadAttachment,
    Transfer,
    Viewer,
    ReportPdf,
    VueHtml2pdf
  },
  data() {
    return {
      chinaAreadata,
      overseaAreadata,
      dialog: {
        attachment: false,
        transfer: false,
        viewer: {
          visible: false,
          href: '',
          filename: ''
        },
        email: {
          visible: false,
          content: ''
        }
      },
      attachmentCols: [
        {
          prop: 'primary_category',
          label: '一级类别(点击筛选)',
          width: 150,
          filters: [],
          filterMethod: (value, row) => value === row.primary_category
        },
        {
          prop: 'secondary_category',
          label: '二级类别(点击筛选)',
          width: 200,
          filters: [],
          filterMethod: (value, row) => value === row.secondary_category
        },
        { prop: 'uploader', label: '上传人', width: 100 },
        { prop: 'date', label: '上传时间', width: 150 },
        { prop: 'name', label: '文件名' },
        {
          label: '操作',
          width: 180,
          scopedSlots: {
            default: (scoped) => {
              let deleteBtn = ''
              if (!this.isReadonly) {
                deleteBtn = (
                  <el-button
                    v-can={{ name: 'claim.cases.delete-attachment' }}
                    type="danger"
                    size="mini"
                    icon="fas fa-trash"
                    onClick={() =>
                      this.confirmAction(
                        `确认删除${scoped.row.name}吗?`,
                        async () => await claimApi.deleteAttachment(this.caseData?.id, scoped.row.id)
                      )
                    }
                  >
                    删除
                  </el-button>
                )
              }

              return (
                <div>
                  <el-button
                    type="primary"
                    size="mini"
                    icon="fas fa-eye"
                    onClick={() => {
                      this.dialog.viewer.visible = true
                      this.dialog.viewer.href = scoped.row.path
                      this.dialog.viewer.filename = scoped.row.name
                    }}
                  >
                    查看
                  </el-button>
                  {deleteBtn}
                </div>
              )
            }
          }
        }
      ],
      caseData: {},
      goodsTypes: [],
      transportTypes: [],
      formData: {},
      currencies: []
    }
  },
  computed: {
    isDomestic() {
      return ![2, 6, 7].includes(this.caseData?.policy_type) && this.caseData?.policy_type !== 7
    },
    reasons() {
      // 货运险
      switch (this.caseData?.policy_type) {
        case 1:
        case 2:
        case 3:
        case 7:
          return reasons.cargoReasons
        case 4:
          return reasons.generalReasons
        case 5:
          return reasons.groupReasons
        case 6:
          if ([5, 8, 9, 10, 11, 12, 14].includes(this.caseData?.offline_category_id)) {
            return reasons.cargoReasons
          } else if ([6].includes(this.caseData?.offline_category_id)) {
            return reasons.groupReasons
          } else {
            return reasons.generalReasons
          }
        default:
          return []
      }
    },
    lossCategories() {
      switch (this.caseData?.policy_type) {
        case 1:
        case 2:
        case 3:
        case 7:
          return [{ label: '物损', value: 2 }]
        case 5:
          return [{ label: '人伤', value: 1 }]
        default:
          return [
            { label: '人伤', value: 1 },
            { label: '物损', value: 2 },
            { label: '人伤和物损', value: 3 }
          ]
      }
    },
    locations() {
      if (this.isDomestic) {
        return chinaAreadata
      }

      return [
        {
          value: '中国大陆',
          children: chinaAreadata.map((item) => {
            return {
              value: item.value,
              children: item.city.map((city) => {
                return {
                  value: city.value
                }
              })
            }
          })
        },
        {
          value: '境外地区',
          children: Object.keys(this.overseaAreadata).map((k) => {
            return { value: k }
          })
        }
      ]
    },
    claimRecordAmount() {
      return Number(
        Number(
          this.caseData?.claim_settlement_amount *
            (this.currencies.find((e) => e.value == this.caseData?.claim_settlement_amount_currency_id)
              ? this.currencies.find((e) => e.value == this.caseData?.claim_settlement_amount_currency_id)?.rate
              : this.currencies.find((e) => e.code == this.caseData?.claim_settlement_amount_currency.code)?.rate)
        ) +
          Number(
            this.caseData?.claim_lodging_fee *
              (this.currencies.find((e) => e.value == this.caseData?.claim_lodging_fee_currency_id)
                ? this.currencies.find((e) => e.value == this.caseData?.claim_lodging_fee_currency_id)?.rate
                : this.currencies.find((e) => e.code == this.caseData?.claim_lodging_fee_currency.code)?.rate)
          )
      ).toFixed(2)
    },
    claimSettleAmount() {
      return Number(
        Number(
          this.caseData?.settlement_payment_amount *
            (this.currencies.find((e) => e.value == this.caseData?.settlement_payment_amount_currency_id)
              ? this.currencies.find((e) => e.value == this.caseData?.settlement_payment_amount_currency_id)?.rate
              : this.currencies.find((e) => e.code == this.caseData?.settlement_payment_amount_currency.code)?.rate)
        ) +
          Number(
            this.caseData?.settlement_costs *
              (this.currencies.find((e) => e.value == this.caseData?.settlement_costs_currency_id)
                ? this.currencies.find((e) => e.value == this.caseData?.settlement_costs_currency_id)?.rate
                : this.currencies.find((e) => e.code == this.caseData?.settlement_costs_currency.code)?.rate)
          )
      ).toFixed(2)
    },
    loadingTypes() {
      return [
        { label: '厢式货车', value: 1 },
        { label: '非厢式货车', value: 2 },
        { label: '集装箱（拼箱）', value: 3 },
        { label: '集装箱（整箱）', value: 4 },
        { label: '非集装箱运输', value: 5 }
      ]
    },
    packagingTypes() {
      return [
        { label: '裸装（无运输包装）', value: 1 },
        { label: '简易包装（包装材料防护力弱，如塑料膜；或不能100%全部包裹住货物的包装）', value: 2 },
        { label: '运输包装完善', value: 3 }
      ]
    },
    detailData() {
      return {
        title: '案件详情',
        data: [
          {
            title: '案件信息',
            groups: [
              { label: '理赔编号', value: this.caseData?.case_no },
              {
                label: '保司报案号',
                editable: !this.isReadonly,
                key: 'external_case_no',
                value: this.caseData?.external_case_no
              },
              { label: '产品来源', value: this.caseData?.product_source },
              { label: '业务来源', value: this.caseData?.business_source },
              { label: '险种', value: this.caseData?.policy_type_text },
              // { label: '线下录入险种', value: this.caseData?.offline_type, hide: this.caseData?.policy_type !== 6 },
              { label: '保险公司', value: this.caseData?.company?.name },
              { label: '出单公司', value: this.caseData?.company_branch?.name },
              {
                label: `保险金额(${this.caseData?.policy_coverage_currency?.name ?? '人民币'})`,
                value: this.caseData?.policy_coverage
              },
              {
                label: '保单号',
                value: this.caseData?.policy_no,
                isLink: true,
                target: '_blank',
                handler: () => this.policyUrl(this.caseData)
              },
              {
                type: 'popover',
                label: '销账流水号',
                render: () => {
                  return (
                    <div>
                      <el-table data={this.caseData?.policy?.settlements}>
                        <el-table-column width="80" property="id" label="#"></el-table-column>
                        <el-table-column width="200" label="流水号">
                          {(props) => (
                            <el-link
                              href={
                                window.location.origin + '/dashboard/finance/settlement/' + props.row?.settlement_id
                              }
                              target="_blank"
                            >
                              {props.row?.order_no}
                            </el-link>
                          )}
                        </el-table-column>
                        <el-table-column width="100" property="receivable" label="应收保费"></el-table-column>
                        <el-table-column width="150" property="created_at" label="时间"></el-table-column>
                      </el-table>
                    </div>
                  )
                }
              },
              {
                type: 'popover',
                label: '保单应付情况',
                render: () => {
                  return (
                    <div>
                      <el-table data={this.caseData?.policy?.premium_payments}>
                        <el-table-column width="80" property="id" label="#"></el-table-column>
                        <el-table-column width="200" property="order_no" label="流水号"></el-table-column>
                        <el-table-column width="100" property="user_premium" label="用户保费"></el-table-column>
                        <el-table-column width="80" property="is_payment" label="是否已付保险公司"></el-table-column>
                        <el-table-column width="150" property="created_at" label="时间"></el-table-column>
                      </el-table>
                    </div>
                  )
                }
              },
              { label: '投保人', value: this.caseData?.policyholder },
              { label: '被保人', value: this.caseData?.insured },
              {
                label: '报案人',
                editable: !this.isReadonly,
                key: 'claimant',
                value: this.caseData?.claimant
              },
              {
                label: '报案人电话',
                editable: !this.isReadonly,
                key: 'claimant_phone_number',
                value: this.caseData?.claimant_phone_number
              },
              {
                label: '报案人邮箱',
                editable: !this.isReadonly,
                key: 'claimant_email',
                value: this.caseData?.claimant_email
              },
              {
                label: '报案时间',
                editable: !this.isReadonly,
                type: 'datetime',
                key: 'date_of_reporting',
                value: this.caseData?.date_of_reporting
              },
              {
                label: '重复报案提醒',
                value: `本保单下报案次数: ${this.caseData?.report_num} <br /> ${this.caseData?.claim_records?.join(
                  '<br />'
                )}`
              },
              { label: '业务员', value: this.caseData?.salesman?.name },
              {
                label: '保司理赔员',
                editable: !this.isReadonly,
                key: 'external_adjuster',
                value: this.caseData?.external_adjuster
              },
              {
                label: '保司理赔员电话',
                editable: !this.isReadonly,
                key: 'external_adjuster_phone_number',
                value: this.caseData?.external_adjuster_phone_number
              },
              {
                label: '保司理赔员邮箱',
                editable: !this.isReadonly,
                key: 'external_adjuster_email',
                value: this.caseData?.external_adjuster_email
              }
            ]
          },
          {
            title: '案件处理',
            groups: [
              {
                label: '标的',
                editable: !this.isReadonly,
                type: 'textarea',
                key: 'subject',
                value: this.caseData?.subject
              },
              {
                label: '出险时间',
                editable: !this.isReadonly,
                type: 'date',
                key: 'date_of_loss',
                value: this.caseData?.date_of_loss
              },
              {
                label: '出险地点',
                // 保险类型国际，出险地点为海外地区
                type: 'cascader',
                props: {
                  expandTrigger: 'hover',
                  filterable: true,
                  value: 'value',
                  label: 'value',
                  children: this.isDomestic ? 'city' : 'children'
                },
                options: this.locations,
                editable: !this.isReadonly,
                key: 'loss_location',
                rawValue: Array.isArray(this.caseData?.loss_location)
                  ? this.caseData?.loss_location
                  : this.caseData?.loss_location?.split('/'),
                value: this.caseData?.loss_location
              },
              {
                label: '出险地详细地址',
                key: 'loss_address',
                value: this.caseData?.loss_address,
                editable: !this.isReadonly
              },
              {
                label: '出险原因',
                type: 'cascader',
                props: {
                  expandTrigger: 'hover',
                  filterable: true,
                  value: 'value',
                  label: 'value',
                  children: 'options'
                },
                options: this.reasons,
                key: 'loss_reason',
                rawValue: Array.isArray(this.caseData?.loss_reason)
                  ? this.caseData?.loss_reason
                  : this.caseData?.loss_reason?.split('/'),
                value: this.caseData?.loss_reason,
                editable: !this.isReadonly
              },
              {
                label: '损失类型',
                key: 'loss_category',
                value: {
                  1: '人伤',
                  2: '物损',
                  3: '人伤和物损'
                }[this.caseData?.loss_category],
                rawValue: this.caseData?.loss_category,
                editable: !this.isReadonly,
                type: 'select',
                options: this.lossCategories
              },
              {
                label: '报损金额',
                value: this.caseData?.loss_amount,
                editable: !this.isReadonly,
                key: 'loss_amount'
              },
              {
                label: '报损金额币种',
                key: 'loss_amount_currency_id',
                value: this.caseData?.loss_amount_currency?.name,
                rawValue: [0, -1].includes(this.caseData?.loss_amount_currency_id)
                  ? ''
                  : this.caseData?.loss_amount_currency_id,
                editable: !this.isReadonly,
                type: 'select',
                options: this.currencies
              },
              {
                label: '事故经过',
                value: this.caseData?.loss_detail,
                type: 'textarea',
                editable: !this.isReadonly,
                key: 'loss_detail',
                row: true
              },
              {
                label: '买方',
                key: 'buyer',
                value: this.caseData?.buyer,
                hide: !this.isCargoCase,
                editable: !this.isReadonly
              },
              {
                label: '卖方',
                key: 'seller',
                value: this.caseData?.seller,
                hide: !this.isCargoCase,
                editable: !this.isReadonly
              },
              {
                label: '货物类别',
                key: 'goods_type_id',
                hide: !this.isCargoCase,
                value: this.caseData?.goods_type?.name,
                rawValue: [0, -1].includes(this.caseData?.goods_type_id) ? '' : this.caseData?.goods_type_id,
                editable: !this.isReadonly,
                type: 'select',
                options: this.goodsTypes
              },
              {
                label: '包装方式',
                key: 'packaging_method',
                hide: !this.isCargoCase,
                value: this.caseData?.packaging_method_text,
                rawValue: [0, -1].includes(this.caseData?.packaging_method) ? '' : this.caseData?.packaging_method,
                editable: !this.isReadonly,
                type: 'select',
                options: this.packagingTypes
              },
              {
                label: '装载方式',
                key: 'loading_method',
                hide: !this.isCargoCase,
                value: this.caseData?.loading_method_text,
                rawValue: [0, -1].includes(this.caseData?.loading_method) ? '' : this.caseData?.loading_method,
                editable: !this.isReadonly,
                type: 'select',
                options: this.loadingTypes
              },
              {
                label: '装箱公司',
                key: 'packing_company',
                value: this.caseData?.packing_company,
                hide: !this.isCargoCase,
                editable: !this.isReadonly
              },
              {
                label: '承运人',
                key: 'carrier',
                value: this.caseData?.carrier,
                hide: !this.isCargoCase,
                editable: !this.isReadonly
              },
              {
                label: '运输公司',
                key: 'transportation_company',
                value: this.caseData?.transportation_company,
                hide: !this.isCargoCase,
                editable: !this.isReadonly
              },
              {
                label: '搬运公司',
                key: 'moving_company',
                value: this.caseData?.moving_company,
                hide: !this.isCargoCase,
                editable: !this.isReadonly
              },
              {
                label: '贸易类型',
                key: 'trade_type',
                hide: !this.isCargoCase,
                value: {
                  '-1': '国内运输',
                  1: '出口运输',
                  2: '进口运输',
                  3: '境外运输'
                }[this.caseData?.trade_type],
                rawValue: this.caseData?.trade_type,
                editable: !this.isReadonly,
                type: 'select',
                options: [
                  { label: '国内运输', value: -1 },
                  { label: '出口运输', value: 1 },
                  { label: '进口运输', value: 2 },
                  { label: '境外运输', value: 3 }
                ]
              },
              {
                label: '起运地',
                key: 'departure',
                value: this.caseData?.departure,
                hide: !this.isCargoCase,
                editable: !this.isReadonly
              },
              {
                label: '目的地',
                key: 'destination',
                value: this.caseData?.destination,
                hide: !this.isCargoCase,
                editable: !this.isReadonly
              },
              {
                label: '运输方式',
                key: 'transport_method_id',
                hide: !this.isCargoCase,
                value: this.caseData?.transport_method?.name,
                rawValue: [0, -1].includes(this.caseData?.transport_method_id)
                  ? ''
                  : this.caseData?.transport_method_id,
                editable: !this.isReadonly,
                type: 'select',
                options: this.transportTypes
              },
              {
                label: '运输工具',
                key: 'transport_no',
                value: this.caseData?.transport_no,
                hide: !this.isCargoCase,
                editable: !this.isReadonly
              },
              {
                label: '起运日期',
                key: 'shipping_date',
                value: this.caseData?.shipping_date,
                type: 'date',
                hide: !this.isCargoCase,
                editable: !this.isReadonly
              },
              {
                label: `发票金额(${this.caseData?.invoice_amount_currency?.name ?? '人民币'})`,
                key: 'invoice_amount',
                value: this.caseData?.invoice_amount,
                hide: !this.isCargoCase,
                editable: !this.isReadonly
              },
              {
                label: '发票号',
                key: 'invoice_no',
                value: this.caseData?.invoice_no,
                hide: !this.isCargoCase,
                editable: !this.isReadonly
              },
              {
                label: '提单/运单号',
                key: 'waybill_no',
                value: this.caseData?.waybill_no,
                hide: !this.isCargoCase,
                editable: !this.isReadonly
              },
              {
                label: '主险',
                key: 'main_clause',
                value: this.caseData?.main_clause,
                type: 'textarea',
                editable: !this.isReadonly,
                row: true
              },
              {
                label: '附加险',
                key: 'additional_clause',
                value: this.caseData?.additional_clause,
                type: 'textarea',
                editable: !this.isReadonly,
                row: true
              },
              {
                label: '免赔约定',
                key: 'special_clause',
                value: this.caseData?.special_clause,
                type: 'textarea',
                editable: !this.isReadonly,
                row: true
              },
              {
                label: '保险责任成立',
                key: 'is_established',
                value: this.caseData?.is_established === 1 ? '是' : '否',
                rawValue: this.caseData?.is_established,
                editable: !this.isReadonly,
                type: 'radio',
                options: [
                  { label: '是', value: 1 },
                  { label: '否', value: 0 }
                ]
              },
              {
                label: '立案金额(CNY)',
                value: this.claimRecordAmount
              },
              {
                label: '立案赔款',
                key: 'claim_settlement_amount',
                value: this.caseData?.claim_settlement_amount,
                editable: !this.isReadonly
              },
              {
                label: '立案赔款币种',
                key: 'claim_settlement_amount_currency_id',
                value: this.caseData?.claim_settlement_amount_currency?.name,
                rawValue: [0, -1].includes(this.caseData?.claim_settlement_amount_currency_id)
                  ? ''
                  : this.caseData?.claim_settlement_amount_currency_id,
                editable: !this.isReadonly,
                type: 'select',
                options: this.currencies
              },
              {
                label: '立案费用',
                key: 'claim_lodging_fee',
                value: this.caseData?.claim_lodging_fee,
                editable: !this.isReadonly
              },
              {
                label: '立案费用币种',
                key: 'claim_lodging_fee_currency_id',
                value: this.caseData?.claim_lodging_fee_currency?.name,
                rawValue: [0, -1].includes(this.caseData?.claim_lodging_fee_currency_id)
                  ? ''
                  : this.caseData?.claim_lodging_fee_currency_id,
                editable: !this.isReadonly,
                type: 'select',
                options: this.currencies
              },
              {
                label: '第三方责任',
                key: 'is_third_party_liability',
                value: this.caseData?.is_third_party_liability === 1 ? '是' : '否',
                rawValue: this.caseData?.is_third_party_liability,
                editable: !this.isReadonly,
                type: 'radio',
                options: [
                  { label: '是', value: 1 },
                  { label: '否', value: 0 }
                ]
              },
              {
                label: '追偿对象',
                key: 'recovery_target',
                value: this.caseData?.recovery_target,
                editable: !this.isReadonly
              },
              {
                label: '追偿金额(CNY)',
                key: 'estimated_recovery_amount',
                value: this.caseData?.estimated_recovery_amount,
                editable: !this.isReadonly
              },
              {
                label: '风险提示',
                key: 'has_risk_warning',
                value: this.caseData?.has_risk_warning === 1 ? '是' : '否',
                rawValue: this.caseData?.has_risk_warning,
                editable: !this.isReadonly,
                type: 'radio',
                options: [
                  { label: '是', value: 1 },
                  { label: '否', value: 0 }
                ]
              },
              {
                label: '风险提示内容',
                type: 'textarea',
                editable: this.caseData?.has_risk_warning && !this.isReadonly,
                hide: !this.caseData?.has_risk_warning,
                key: 'risk_warning',
                value: this.caseData?.risk_warning
              },
              {
                label: '残值处理',
                value: {
                  '-1': '无残值',
                  1: '报废处理',
                  2: '抵扣被保险人赔款',
                  3: '第三方残值回收'
                }[this.caseData.residual_value_handing_method],
                rawValue: this.caseData?.residual_value_handing_method,
                editable: !this.isReadonly,
                type: 'select',
                key: 'residual_value_handing_method',
                options: [
                  { label: '无残值', value: -1 },
                  { label: '报废处理', value: 1 },
                  { label: '抵扣被保险人赔款', value: 2 },
                  { label: '第三方残值回收', value: 3 }
                ]
              },
              {
                label: '残值金额(CNY)',
                key: 'residual_value',
                value: this.caseData?.residual_value,
                editable: !this.isReadonly
              }
            ]
          },
          {
            title: '理算报告',
            actions: [
              {
                text: '生成',
                hide: this.isReadonly,
                icon: 'fas fa-plus',
                type: 'primary',
                handler: () => {
                  this.setAssessmentReportContent()
                }
              }
            ],
            groups: [
              {
                label: '报告内容',
                type: 'textarea',
                editable: !this.isReadonly,
                key: 'claims_assessment_report',
                withTag: true,
                value: this.caseData?.claims_assessment_report
              }
            ]
          },
          {
            title: '结案信息',
            groups: [
              {
                label: '结案金额(CNY)',
                value: this.claimSettleAmount
              },
              {
                label: '结案日期',
                value: this.caseData?.settlement_date,
                key: 'settlement_date',
                editable: !this.isReadonly,
                type: 'date'
              },
              {
                label: '结案赔款',
                value: this.caseData?.settlement_payment_amount,
                key: 'settlement_payment_amount',
                editable: !this.isReadonly
              },
              {
                label: '结案赔款币种',
                key: 'settlement_payment_amount_currency_id',
                value: this.caseData?.settlement_payment_amount_currency?.name,
                rawValue: [0, -1].includes(this.caseData?.settlement_payment_amount_currency_id)
                  ? ''
                  : this.caseData?.settlement_payment_amount_currency_id,
                editable: !this.isReadonly,
                type: 'select',
                options: this.currencies
              },
              {
                label: '结案费用',
                value: this.caseData?.settlement_costs,
                key: 'settlement_costs',
                editable: !this.isReadonly
              },
              {
                label: '结案费用币种',
                key: 'settlement_costs_currency_id',
                value: this.caseData?.settlement_costs_currency?.name,
                rawValue: [0, -1].includes(this.caseData?.settlement_costs_currency_id)
                  ? ''
                  : this.caseData?.settlement_costs_currency_id,
                editable: !this.isReadonly,
                type: 'select',
                options: this.currencies
              }
            ]
          },
          {
            // hack: section only
            title: '附件',
            actions: [
              {
                text: '导入保单附件',
                hide: this.isReadonly,
                icon: 'fas fa-upload',
                type: 'primary',
                handler: () => this.importPolicyFile()
              }
            ],
            groups: []
          }
        ]
      }
    },
    isReadonly() {
      return this.$route.query?.is_readonly !== undefined
    },
    isCargoCase() {
      switch (this.caseData?.policy_type) {
        case 1:
        case 2:
        case 7:
          return true
        case 6:
          if ([5, 8, 9, 10, 11, 12, 14].includes(this.caseData?.offline_category_id)) {
            return true
          } else {
            return false
          }
        default:
          return false
      }
    }
  },
  async created() {
    await this.fetchCaseDetail()
    await this.fetchGoodsTypes()
    await this.fetchTransportTypes()
    await this.fetchCurrencyExchange()
  },
  methods: {
    handleCaseDataChanged(data) {
      this.formData = Object.assign({}, data)
      this.caseData = Object.assign({}, this.caseData || {}, data)
    },
    async fetchCaseDetail() {
      const data = await claimApi.fetchCaseDetail(this.$route.params.id)
      this.caseData = data.data

      const primaryCategories = []
      this.caseData?.attachments?.forEach(
        (item) => !primaryCategories.includes(item.primary_category) && primaryCategories.push(item.primary_category)
      )
      this.attachmentCols[this.attachmentCols.findIndex((c) => c.prop === 'primary_category')].filters =
        primaryCategories.map((name) => {
          return { text: name, value: name }
        })

      const secondaryCategories = []
      this.caseData?.attachments?.forEach(
        (item) =>
          !secondaryCategories.includes(item.secondary_category) && secondaryCategories.push(item.secondary_category)
      )
      this.attachmentCols[this.attachmentCols.findIndex((c) => c.prop === 'secondary_category')].filters =
        secondaryCategories.map((name) => {
          return { text: name, value: name }
        })
    },
    async handleCancel() {
      this.updateClaimStatus(0)
    },
    async handleCollect() {
      this.updateClaimStatus(2)
    },
    async handleAudit() {
      this.updateClaimStatus(3)
    },
    async handleZero() {
      this.updateClaimStatus(4)
    },
    async handleReject() {
      this.updateClaimStatus(5)
    },
    async handleReceive() {
      this.updateClaimStatus(6)
    },
    async handleFinish() {
      this.updateClaimStatus(7)
    },
    async handleReturn() {
      try {
        await claimApi.sendBackCase(this.$route.params.id)
        this.$message.success('退回成功')
        this.fetchCaseDetail()
      } catch {
        //
      }
    },
    async updateClaimStatus(status) {
      try {
        await claimApi.updateStatus(this.$route.params.id, status)
        this.$message.success('案件流转成功')
        this.fetchCaseDetail()
      } catch {
        //
      }
    },
    async handleArchive(isTypicalCase) {
      try {
        await claimApi.archiveCase(this.$route.params.id, isTypicalCase)
        this.$message.success('案件归档成功')
        this.fetchCaseDetail()
      } catch {
        //
      }
    },
    async handleUpdateCase() {
      const data = Object.assign({}, this.formData)
      data.date_of_loss = dayjs(data.date_of_loss).format('YYYY-MM-DD HH:mm:ss')
      data.date_of_reporting = dayjs(data.date_of_reporting).format('YYYY-MM-DD HH:mm:ss')
      data.settlement_date = dayjs(data.settlement_date).format('YYYY-MM-DD HH:mm:ss')
      data.loss_location = Array.isArray(data.loss_location) ? data.loss_location.join('/') : data.loss_location
      data.loss_reason = Array.isArray(data.loss_reason) ? data.loss_reason.join('/') : data.loss_reason
      const loading = Loading.service()
      try {
        await claimApi.updateCase(this.$route.params.id, data)
        this.$message.success('保存成功')
        this.fetchCaseDetail()
      } catch {
        //
      }

      loading.close()
    },
    async handleRenderEmail() {
      if (!this.caseData?.external_adjuster_email) {
        this.$message.error('请先填写保司理赔员邮箱')
        return
      }

      const loading = Loading.service()
      try {
        const data = await claimApi.renderEmailOfAdjuster(this.$route.params.id)
        this.dialog.email.content = data.html
        this.dialog.email.visible = true
      } catch {
        //
      }
      loading.close()
    },
    async handleSendEmail() {
      if (!this.caseData?.external_adjuster_email) {
        this.$message.error('请先填写保司理赔员邮箱')
        return
      }

      const loading = Loading.service()
      try {
        await claimApi.sendEmailToAdjuster(this.$route.params.id)
        this.dialog.email.visible = false
        this.$message.success('已发送')
      } catch {
        //
      }
      loading.close()
    },
    async confirmAction(message, action, cancelAction) {
      try {
        await this.$confirm(message, '提示', {
          confirmButtonText: '是',
          cancelButtonText: '否',
          type: 'warning'
        })
        await action()
        this.fetchCaseDetail()
      } catch {
        cancelAction?.()
      }
    },
    async fetchGoodsTypes() {
      try {
        const data = await getGoodsTypes(this.caseData?.company?.id)
        this.goodsTypes = data.data.map((item) => {
          return { label: item.name, value: item.id }
        })
      } catch {
        //
      }
    },
    async fetchTransportTypes() {
      try {
        const data = await getTransportMethods(this.caseData?.company?.id)
        this.transportTypes = data.data.map((item) => {
          return { label: item.name, value: item.id }
        })
      } catch {
        //
      }
    },
    async fetchCurrencyExchange() {
      try {
        const data = await getLatestCurrencyExchange()
        this.currencies = data.data.map((item) => {
          return { label: item.name, value: item.id, rate: item.rate, code: item.code }
        })
      } catch {
        //
      }
    },
    policyUrl(caseData) {
      const routes = {
        1: { name: 'PoliciesDomesticDetails', params: { id: caseData.policy.id } },
        2: { name: 'PoliciesIntlDetails', params: { id: caseData.policy.id } },
        3: { name: 'PoliciesLbtDetails', params: { id: caseData.policy.id } },
        4: { name: 'PoliciesOtherDetails', params: { id: caseData.policy.id } },
        5: { name: 'PoliciesGroupDetails', params: { policyGroupId: caseData.policy.group_id } },
        6: { name: 'OfflinePolicyDetails', params: { id: caseData.policy.id } },
        7: { name: 'PoliciesCbecDetails', params: { id: caseData.policy.id } }
      }
      if (caseData.policy.id === -1) {
        this.$message.error('系统内未查询到该保单号,无法跳转')
      } else {
        if (caseData.policy?.type === 6) {
          this.$open(routes[caseData.policy.type])
        } else {
          this.$open(routes[caseData.policy_type])
        }
      }
    },
    async importPolicyFile() {
      try {
        await claimApi.importPolicyFile(this.$route.params.id)
        this.$message.success('导入成功')
        this.fetchCaseDetail()
      } catch {
        //
      }
    },
    handleDownloadConfirmationPdf() {
      this.$refs.html2Pdf.generatePdf()
    },
    setAssessmentReportContent() {
      this.caseData.claims_assessment_report = `一、承保情况
被保险人(${this.caseData.insured})投保${
        {
          '-1': '国内运输',
          1: '出口运输',
          2: '进口运输',
          3: '境外运输'
        }[this.caseData.trade_type]
      }，${this.caseData.main_clause ?? ''}，${this.caseData.additional_clause ?? ''}，${
        this.caseData.special_clause ?? ''
      }。
二、事故经过
${this.caseData.loss_detail}
三、保险责任认定
${this.caseData.date_of_loss}，在${this.caseData.loss_location}标的因${this.caseData.loss_reason}发生的货损事故，${
        this.caseData.is_established ? '属保单承保风险，保险责任成立' : '不属保单承保风险，保险责任不成立'
      }。
四、核损及理算

结案金额:${Number(this.claimSettleAmount).toFixed(2)}，其中结案赔款${
        this.caseData.settlement_payment_amount
      }，结案费用${this.caseData.settlement_costs}。
五、追偿情况
${
  this.caseData.is_third_party_liability
    ? '追偿对象: ' + this.caseData.recovery_target + '，追偿预估金额: ' + this.caseData.estimated_recovery_amount
    : '无'
}
六、风险提示
${this.caseData.has_risk_warning ? this.caseData.risk_warning : '无'}
七、残值
${
  {
    '-1': '无残值',
    1: '报废处理',
    2: '抵扣被保险人赔款',
    3: '第三方残值回收'
  }[this.caseData.residual_value_handing_method]
}，${this.caseData.residual_value}
      `
    }
  }
}
</script>

<style scoped lang="scss">
.fixed-operation-box {
  box-sizing: border-box;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;

  .operation-buttons {
    padding: 10px;
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    row-gap: 10px;
    column-gap: 10px;

    .el-button {
      width: 100px;
      margin-left: 0;
    }
  }
}
</style>
