<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <el-card shadow="never">
      <el-form ref="form" :model="form" :rules="rules" label-position="top" label-width="80px">
        <section>
          <h3>保单信息</h3>
          <el-row :gutter="48">
            <el-col :span="8">
              <div class="d-flex">
                <el-input placeholder="请输入保单号以搜索保单信息" v-model="policyNo"></el-input>
                <el-button class="m-mini-l" type="primary" icon="fas fa-search" @click="handleSearchPolicy">
                  保单校验
                </el-button>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item prop="policy_type" label="险种">
                <el-select :disabled="disabledInput" v-model="form.policy_type" placeholder="请选择险种" class="w-100">
                  <el-option v-for="t in policyTypes" :key="t.value" :value="t.value" :label="t.label" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.policy_type === 6">
              <el-form-item prop="offline_category_id" label="保单录入险种">
                <el-select
                  :disabled="disabledInput"
                  v-model="form.offline_category_id"
                  placeholder="请选择保单录入险种"
                  class="w-100"
                >
                  <el-option v-for="t in offlineCategories" :key="t.value" :value="t.value" :label="t.label" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="policy_no" label="保单号">
                <el-input :disabled="disabledInput" v-model="form.policy_no" placeholder="保单号"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item prop="policyholder" label="投保人">
                <el-input :disabled="disabledInput" v-model="form.policyholder" placeholder="投保人"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="insured" label="被保险人">
                <el-input :disabled="disabledInput" v-model="form.insured" placeholder="被保险人"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item prop="company_id" label="保险公司">
                <el-select
                  :disabled="disabledInput"
                  v-model="form.company_id"
                  placeholder="请选择保险公司"
                  class="w-100"
                >
                  <el-option v-for="c in companies" :key="c.id" :value="c.id" :label="c.name" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="company_branch_id" label="出单公司">
                <el-select
                  :disabled="disabledInput"
                  v-model="form.company_branch_id"
                  placeholder="请选择出单公司"
                  class="w-100"
                >
                  <el-option
                    v-for="c in companies.find((e) => e.id == form.company_id)?.branches || []"
                    :key="c.id"
                    :value="c.id"
                    :label="c.name"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item prop="policy_coverage" label="保险金额">
                <el-input :disabled="disabledInput" v-model="form.policy_coverage" placeholder="保险金额"></el-input>
                <span>{{ form.policy_coverage | chineseAmount }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="policy_coverage_currency_id" label="保额币种">
                <el-select
                  :disabled="disabledInput"
                  v-model="form.policy_coverage_currency_id"
                  placeholder="请选择币种"
                  class="w-100"
                >
                  <el-option
                    v-for="currency in coverageCurrencies"
                    :value="currency.id"
                    :key="currency.id"
                    :label="currency.label"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item prop="salesman_id" label="业务员">
                <el-select
                  :disabled="disabledInput"
                  placeholder="请选择业务员"
                  v-model="form.salesman_id"
                  class="w-100"
                >
                  <el-option v-for="s in salesmans" :key="s.id" :value="s.id" :label="s.name"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="subject" label="标的">
                <el-input
                  :disabled="disabledInput"
                  type="textarea"
                  v-model="form.subject"
                  :rows="1"
                  autosize
                  placeholder="请输入标的"
                >
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </section>
        <section>
          <h3>报案信息</h3>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item prop="loss_reason" label="出险原因">
                <el-cascader
                  class="w-100"
                  :disabled="disabledInput"
                  v-model="form.loss_reason"
                  :options="reasons"
                  :props="{
                    expandTrigger: 'hover',
                    filterable: true,
                    label: 'value',
                    value: 'value',
                    children: 'options'
                  }"
                ></el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="date_of_loss" label="出险时间">
                <el-date-picker
                  v-model="form.date_of_loss"
                  :disabled="disabledInput"
                  type="datetime"
                  placeholder="选择日期"
                  format="yyyy-MM-dd HH:mm:ss"
                  :picker-options="pickerOptions"
                  style="width: 100% !important"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item prop="loss_location" label="出险地">
                <div class="d-flex">
                  <el-cascader
                    class="w-100"
                    :disabled="disabledInput"
                    v-model="form.loss_location"
                    :options="locations"
                    filterable
                    :props="{
                      expandTrigger: 'hover',
                      filterable: true,
                      value: 'value',
                      label: 'value',
                      children: isDomestic ? 'city' : 'children'
                    }"
                  ></el-cascader>
                  <el-input
                    :disabled="disabledInput"
                    class="m-mini-l"
                    type="textarea"
                    rows="1"
                    cols="1"
                    autosize
                    v-model="form.loss_address"
                    placeholder="出险地详细地址"
                  ></el-input>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="loss_category" label="损失类型">
                <el-select :disabled="disabledInput" class="w-100" v-model="form.loss_category" placeholder="损失类型">
                  <el-option
                    v-for="item in lossCategories"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item prop="loss_amount">
                <template #label>
                  <span>报损金额</span>
                  <small class="text-danger">（损失情况不明时请填写 1）</small>
                </template>
                <el-input
                  type="number"
                  :disabled="disabledInput"
                  clearable
                  v-model="form.loss_amount"
                  placeholder="请输入报损金额"
                >
                </el-input>
                <span>{{ form.loss_amount | chineseAmount }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="loss_amount_currency_id" label="币种">
                <el-select
                  :disabled="disabledInput"
                  v-model="form.loss_amount_currency_id"
                  placeholder="请选择币种"
                  class="w-100"
                >
                  <el-option
                    v-for="currency in currencies"
                    :value="currency.id"
                    :key="currency.value"
                    :label="currency.label"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="48">
            <el-col :span="24">
              <el-form-item prop="loss_detail" label="事故经过">
                <el-input
                  type="textarea"
                  minlength="8"
                  maxlength="2000"
                  show-word-limit
                  v-model="form.loss_detail"
                  rows="5"
                  :placeholder="lossDetailPlaceholder"
                >
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="48">
            <el-col :span="8">
              <el-form-item prop="claimant" label="报案人">
                <el-input :disabled="disabledInput" v-model="form.claimant" placeholder="报案人"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="claimant_email" label="报案人邮箱">
                <el-input :disabled="disabledInput" v-model="form.claimant_email" placeholder="报案人邮箱"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="claimant_phone_number" label="报案人电话">
                <el-input
                  :disabled="disabledInput"
                  v-model="form.claimant_phone_number"
                  placeholder="报案人电话"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </section>
        <section v-if="fileList.length > 0">
          <h3>附件信息</h3>
          <el-row :gutter="48">
            <el-col :span="24">
              <ul class="__ua-file-list">
                <li v-for="(f, idx) in fileList" :key="idx" class="__ua-file-item">
                  <span>{{ f.categoryName }} - {{ f.name }}</span>
                  <el-button type="danger" icon="fas fa-times" size="mini" @click="fileList.splice(idx, 1)">
                    删除
                  </el-button>
                </li>
              </ul>
            </el-col>
          </el-row>
        </section>
        <section class="m-mini-t">
          <el-button type="primary" icon="fas fa-upload" @click="uploadAttachmentVisible = true">添加附件</el-button>
          <el-button type="primary" icon="fas fa-check" @click="handleSubmit">提交报案</el-button>
        </section>
      </el-form>
    </el-card>

    <upload-attachment :visible.sync="uploadAttachmentVisible" @succeed="(fs) => fileList.push(...fs)" />
  </div>
</template>

<script>
import chinaAreadata from '@/utils/areadata.json'
import overseaAreadata from '@/utils/regions'
import { digitUppercase } from '@/utils'
import * as currencyApi from '@/apis/currency'
import * as policyApi from '@/apis/policy'
import * as companyApi from '@/apis/company'
import * as claimApi from '@/apis/claim'
import * as adminApi from '@/apis/admin'
import { getOfflineProductCategories } from '@/apis/product'
import dayjs from 'dayjs'
import { Loading } from 'element-ui'
import UploadAttachment from '@/components/claim/UploadAttachment'
import * as reasons from '@/utils/loss_reason'

export default {
  components: {
    UploadAttachment
  },
  data() {
    return {
      disabledInput: true,
      showOfflineCategories: false,
      uploadAttachmentVisible: false,
      policyTypes: [
        { value: 1, label: '国内货运险' },
        { value: 2, label: '国际货运险' },
        { value: 3, label: '单车货运险' },
        { value: 5, label: '雇主责任险' },
        { value: 4, label: '其他险种' },
        { value: 6, label: '线下录入保单' },
        { value: 7, label: '跨境电商险' }
      ],
      companies: [],
      offlineCategories: [],
      policyNo: '',
      chinaAreadata,
      overseaAreadata,
      rawCurrencies: [],
      coverageCurrencies: [{ id: -1, label: '人民币', value: 'CNY' }],
      salesmans: [],
      pickerOptions: {
        disabledDate(t) {
          return t.getTime() > Date.now()
        }
      },
      fileList: [],
      form: {
        // policy info
        policy_type: '',
        offline_category_id: -1,
        policy_no: '',
        company_id: '',
        company_branch_id: '',
        policyholder: '',
        insured: '',
        policy_coverage: '',
        policy_coverage_currency_id: -1,
        salesman_id: '',
        subject: '',
        // claim info
        loss_reason: '',
        date_of_loss: '',
        loss_location: '',
        loss_address: '',
        loss_category: '',
        loss_amount: '',
        loss_amount_currency_id: -1,
        loss_detail: '',
        claimant: '',
        claimant_email: '',
        claimant_phone_number: ''
      },
      rules: {
        policy_type: [{ required: true, message: '请选择险种', trigger: 'change' }],
        policy_no: [{ required: true, message: '请输入保单号', trigger: 'blur' }],
        company_id: [{ required: true, message: '请选择保险公司', trigger: 'change' }],
        company_branch_id: [{ required: true, message: '请选择出单公司', trigger: 'change' }],
        policyholder: [{ required: true, message: '请输入投保人', trigger: 'blur' }],
        insured: [{ required: true, message: '请输入被保人', trigger: 'blur' }],
        policy_coverage: [{ required: true, message: '请输入保险金额', trigger: 'blur' }],
        policy_coverage_currency_id: [{ required: true, message: '请输入保险金额币种', trigger: 'change' }],
        subject: [{ required: true, message: '请输入标的', trigger: 'blur' }],
        loss_reason: [{ required: true, message: '请选择出险原因', trigger: 'change' }],
        date_of_loss: [{ required: true, message: '请选择出险时间', trigger: 'change' }],
        loss_location: [{ required: true, message: '请选择出险地点', trigger: 'change' }],
        loss_category: [{ required: true, message: '请选择损失类别', trigger: 'change' }],
        loss_amount: [{ required: true, message: '请输入报损金额', trigger: 'blur' }],
        loss_amount_currency_id: [{ required: true, message: '请选择币种', trigger: 'change' }],
        loss_detail: [
          { required: true, message: '请输入事故经过', trigger: 'blur' },
          {
            min: 8,
            max: 2000,
            message: '长度在 8 到 2000 个字符',
            trigger: 'blur'
          }
        ],
        claimant: [{ required: true, message: '请输入报案人', trigger: 'blur' }],
        claimant_email: [{ required: true, message: '请输入报案人邮箱', trigger: 'blur' }],
        claimant_phone_number: [{ required: true, message: '请输入报案人电话', trigger: 'blur' }]
      }
    }
  },
  filters: {
    chineseAmount(value) {
      return digitUppercase(value)
    }
  },
  computed: {
    isDomestic() {
      return ![2, 6, 7].includes(this.form.policy_type) && this.form.policy_type !== ''
    },
    reasons() {
      // 货运险
      switch (this.form?.policy_type) {
        case 1:
        case 2:
        case 3:
        case 7:
          return reasons.cargoReasons
        case 4:
          return reasons.generalReasons
        case 5:
          return reasons.groupReasons
        case 6:
          if ([5, 8, 9, 10, 11, 12, 14].includes(this.form?.offline_category_id)) {
            return reasons.cargoReasons
          } else if ([6].includes(this.form?.offline_category_id)) {
            return reasons.groupReasons
          } else {
            return reasons.generalReasons
          }
        default:
          return []
      }
    },
    locations() {
      if (this.isDomestic) {
        return chinaAreadata
      }

      return [
        {
          value: '中国大陆',
          children: chinaAreadata.map((item) => {
            return {
              value: item.value,
              children: item.city.map((city) => {
                return {
                  value: city.value
                }
              })
            }
          })
        },
        {
          value: '境外地区',
          children: Object.keys(this.overseaAreadata).map((k) => {
            return { value: k }
          })
        }
      ]
    },
    isDisabledPolicyInfoSectionInputs() {
      return this.policy?.policy_no
    },
    lossCategories() {
      switch (this.form?.policy_type) {
        case 1:
        case 2:
        case 3:
        case 7:
          return [{ label: '物损', value: 2 }]
        case 5:
          return [{ label: '人伤', value: 1 }]
        default:
          return [
            { label: '人伤', value: 1 },
            { label: '物损', value: 2 },
            { label: '人伤和物损', value: 3 }
          ]
      }
    },
    currencies() {
      if (this.isDomestic) {
        return [{ id: -1, label: '人民币', value: 'CNY' }]
      }

      return this.rawCurrencies
    },
    lossDetailPlaceholder() {
      if ([1, 2, 3, 7].includes(this.form.policy_type)) {
        return 'XYZ公司从上海出口标的货物空气开关3箱到英国伦敦ABC客户，委托DDFF承运，运单号为DDFF111111，运输方式为航空运输。2023年1月1日，在伦敦卸离飞机后陆运到买家仓库途中发生交通事故，经初步清点，事故造成10只空气开关受损。'
      }

      if (this.form.policy_type === 5) {
        return '2023年9月1日10：00时左右，上海某机械有限公司员工张某在车床加工零件作业时，车床在无指令的状况下突然启动，导致张某右手受伤，事发后立即送往上海第六人民医院治疗，经诊断为手指骨折，在手术治疗中。'
      }

      return '请填写事故经过'
    }
  },
  watch: {
    'form.policy_type'(policyType) {
      // 雇主默认人伤
      if (policyType === 5) {
        this.form.loss_category = 1
      } else if ([1, 2, 3, 7].includes(policyType)) {
        this.form.loss_category = 2
      }
      if (policyType === 6) {
        this.fetchOfflineProductCategories()
        this.rules.offline_category_id = [
          { required: this.form.policy_type === 6, message: '请选择险种', trigger: 'change' }
        ]
        this.showOfflineCategories = true
      } else {
        delete this.rules.offline_category_id
        this.showOfflineCategories = false
      }
      this.form.loss_amount_currency_id = this.isDomestic ? -1 : ''
    },
    'form.company_id'(value, oldValue) {
      if (oldValue !== '') {
        this.form.company_branch_id = ''
      }
    }
  },
  async created() {
    await this.fetchCompanies()
    await this.fetchSalesmans()
    await this.fetchCurrencies()
  },
  methods: {
    async fetchSalesmans() {
      const data = await adminApi.getSales()
      this.salesmans = data.data
    },
    async fetchCompanies() {
      const companies = await companyApi.getCompaniesDict()
      this.companies = companies.data
    },
    async handleSearchPolicy() {
      if (!this.policyNo) {
        return
      }
      const policy = await policyApi.searchByPolicyNo(this.policyNo)
      this.coverageCurrencies = this.rawCurrencies
      // hack: object doesn't have length property
      if (policy.data.length === undefined) {
        this.disabledInput = false

        this.form.policy_type = policy.data.type
        this.form.offline_category_id = policy.data.offline_category_id
        this.form.policy_no = policy.data.policy_no
        this.form.company_id = policy.data.company.id
        this.form.company_branch_id = policy.data.company_branch.id
        this.form.policyholder = policy.data.policyholder
        this.form.insured = policy.data.insured
        this.form.policy_coverage = policy.data.coverage
        this.form.policy_coverage_currency_id = policy.data.coverage_currency?.id
        this.form.salesman_id = policy.data.salesman?.id === -1 ? '' : policy.data.salesman?.id
        this.form.subject = policy.data.subject

        // 货币信息
        if ([2, 7].includes(policy.data.type)) {
          this.coverageCurrencies = [
            {
              id: policy.data.coverage_currency?.id,
              label: policy.data.coverage_currency?.name,
              value: policy.data.coverage_currency?.code
            }
          ]
        } else {
          if (this.form.policy_coverage_currency_id === -1) {
            const cny = this.rawCurrencies.find((item) => item.value === 'CNY')
            this.form.policy_coverage_currency_id = cny.id
            this.form.loss_amount_currency_id = cny.id
          }
        }
      } else {
        this.$alert('未找到相关保单，不允许报案')

        this.form.policy_type = ''
        this.form.policy_no = ''
        this.form.company_id = ''
        this.form.company_branch_id = ''
        this.form.policyholder = ''
        this.form.insured = ''
        this.form.policy_coverage = ''
        // this.form.policy_coverage_currency_id = -1
        this.form.salesman_id = ''
        this.form.subject = ''
      }
    },
    async fetchCurrencies() {
      const currencies = await currencyApi.getLatestCurrencyExchange()

      this.rawCurrencies = currencies.data.map((item) => {
        return {
          id: item.id,
          label: item.name,
          value: item.code
        }
      })
      this.coverageCurrencies = this.rawCurrencies

      if (this.coverageCurrencies[0]['id'] === -1) {
        this.coverageCurrencies = this.rawCurrencies
      }

      const cny = this.rawCurrencies.find((item) => item.value === 'CNY')
      if (this.form.policy_coverage_currency_id === -1) {
        this.form.policy_coverage_currency_id = cny.id
      }

      if (this.form.loss_amount_currency_id === -1) {
        this.form.loss_amount_currency_id = cny.id
      }
    },
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          const data = Object.assign({}, this.form)
          data.date_of_loss = dayjs(data.date_of_loss).format('YYYY-MM-DD HH:mm:ss')
          data.loss_reason = data.loss_reason.join('/')
          data.loss_location = data.loss_location.join('/')
          data.salesman_id = data.salesman_id === '' ? -1 : data.salesman_id

          const loading = Loading.service()
          try {
            const caseData = await claimApi.createCase(data)
            for (const f of this.fileList) {
              try {
                await claimApi.uploadAttachment(caseData.data.id, {
                  primary_category: f.category.primary,
                  secondary_category: f.category.secondary,
                  name: f.name,
                  file: f.raw
                })
              } catch (e) {
                //
              }
            }
            loading.close()
            this.$message.success('报案成功')
            this.$router.push({ name: 'ClaimCases' })
          } catch (e) {
            loading.close()
            // this.$message.error(e.message)
          }
        }
      })
    },
    fetchOfflineProductCategories() {
      getOfflineProductCategories({
        is_parent: 0,
        is_pageable: 0
      }).then((r) => {
        this.offlineCategories = r.data.map((item) => {
          return {
            id: item.id,
            label: item.name,
            value: item.id
          }
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.__ua-file-list {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  margin: 0;
  padding: 0;
  list-style: none;

  .__ua-file-item {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &:not(:last-child) {
      margin-bottom: 10px;
    }
  }
}

.__ua-actions {
  justify-content: center;
}
</style>
