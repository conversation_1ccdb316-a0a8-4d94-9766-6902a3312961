<template>
  <div class="product-domestic p-extra-large-x p-extra-large-b w-100">
    <cargo-form :type="type" :model.sync="data" />
  </div>
</template>
<script>
import { getProduct } from '@/apis/product'
import CargoForm from '@/components/product/CargoForm'

const PRODUCT_TYPE = 1

export default {
  name: 'DomesticForm',
  components: {
    CargoForm
  },
  data() {
    return {
      type: PRODUCT_TYPE,
      data: {}
    }
  },
  created() {
    if (this.$route.params.id !== undefined) {
      getProduct(this.$route.params.id).then((r) => (this.data = r.data))
    }
  }
}
</script>
