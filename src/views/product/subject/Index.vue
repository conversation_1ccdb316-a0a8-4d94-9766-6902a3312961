<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <el-card shadow="never"> <define-table :cols="cols" :data="data"></define-table> </el-card>

    <el-dialog title="小类管理" :visible.sync="subjectCategoryDialogVisible" width="500px">
      <el-input
        v-can="{ name: 'subjects.categories.store' }"
        placeholder="请输入分类名称"
        size="small"
        v-model="subjectCategoryInput.name"
        clearable
        required
      >
        <el-button slot="append" icon="fas fa-check" @click="handleSaveSubjectCategory"> 保存 </el-button>
      </el-input>

      <define-table :cols="subjectCategoriesCols" :data="subjectCategories"> </define-table>
    </el-dialog>
  </div>
</template>

<script>
import {
  getSubjects,
  updateSubjectKeywordSuggestionStatus,
  updateSubjectCategoryStatus,
  getSubjectCategories,
  addSubjectCategory,
  updateSubjectCategory,
  deleteSubjectCategory
} from '@/apis/subject'
import { Loading } from 'element-ui'

export default {
  name: 'SubjectIndex',
  data() {
    return {
      subjectCategoryDialogVisible: false,
      subjectCategoryInput: {
        id: '',
        subject_id: '',
        name: ''
      },
      subjectCategoriesCols: [
        { label: '名称', prop: 'name' },
        {
          label: '关键词推荐',
          align: 'center',
          width: 100,
          scopedSlots: {
            default: (scoped) => {
              return (
                <el-switch
                  value={scoped.row?.is_enabled}
                  active-value={1}
                  inactive-value={0}
                  onChange={() => {
                    if (scoped.row?.is_enabled === 1) {
                      scoped.row.is_enabled = 0
                    } else {
                      scoped.row.is_enabled = 1
                    }

                    this.handleChangeSubjectCategoryStatus(scoped.row.subject_id, scoped.row.id, scoped.row.is_enabled)
                  }}
                />
              )
            }
          }
        },
        {
          label: '操作',
          width: 100,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    type="text"
                    size="small"
                    v-can={{ name: 'subjects.categories.index' }}
                    onClick={() => {
                      this.subjectCategoryInput.id = scoped.row.id
                      this.subjectCategoryInput.name = scoped.row.name
                    }}
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="text"
                    size="small"
                    v-can={{ name: 'subjects.categories.index' }}
                    onClick={() => this.handleDeleteSubjectCategory(scoped.row.subject_id, scoped.row.id)}
                  >
                    删除
                  </el-button>
                </div>
              )
            }
          }
        }
      ],
      subjectCategories: [],
      data: [],
      cols: [
        {
          label: '标识符',
          prop: 'identifier',
          width: 100
        },
        {
          label: '标的名称',
          prop: 'name',
          width: 120
        },
        {
          label: '描述',
          prop: 'description'
        },
        {
          label: '排序',
          prop: 'index',
          width: 80,
          align: 'center'
        },
        {
          label: '关键词',
          width: 200,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-tag size="small" type="success">
                    已添加: {scoped.row.added_keywords_count}
                  </el-tag>
                  <el-tag size="small" type="danger" style="margin-left: 10px;">
                    待处理: {scoped.row.pending_keywords_count}
                  </el-tag>
                </div>
              )
            }
          }
        },
        {
          label: '关键词推荐',
          align: 'center',
          width: 100,
          scopedSlots: {
            default: (scoped) => {
              return (
                <el-switch
                  value={scoped.row?.is_enabled_keywords_suggestion}
                  active-value={1}
                  inactive-value={0}
                  onChange={() => {
                    if (scoped.row?.is_enabled_keywords_suggestion === 1) {
                      scoped.row.is_enabled_keywords_suggestion = 0
                    } else {
                      scoped.row.is_enabled_keywords_suggestion = 1
                    }

                    this.handleSuggestionSwitch(scoped)
                  }}
                />
              )
            }
          }
        },
        {
          label: '操作',
          width: 120,
          fixed: 'right',
          scopedSlots: {
            default: (scoped) => {
              let button = ''
              if (scoped.row.identifier === 'MANUAL') {
                button = (
                  <el-button
                    type="text"
                    size="small"
                    v-can={{ name: 'subjects.categories.index' }}
                    onClick={() => this.handleShowSubjectCategory(scoped.row)}
                  >
                    小类
                  </el-button>
                )
              }
              return (
                <div>
                  {button}
                  <el-button
                    type="text"
                    size="small"
                    v-can={{ name: 'subjects.goods-keywords.index' }}
                    onClick={() => this.$router.push({ name: 'SubjectKeywords', params: { id: scoped.row.id } })}
                  >
                    关键词管理
                  </el-button>
                </div>
              )
            }
          }
        }
      ]
    }
  },
  created() {
    this.getData()
  },
  methods: {
    async handleShowSubjectCategory(subject) {
      this.subjectCategoryDialogVisible = true
      this.subjectCategoryInput.subject_id = subject.id
      this.subjectCategories = await getSubjectCategories(subject.id)
    },
    async handleSaveSubjectCategory() {
      const loading = Loading.service()
      if (this.subjectCategoryInput.id === '') {
        await addSubjectCategory(this.subjectCategoryInput.subject_id, this.subjectCategoryInput)
      } else {
        await updateSubjectCategory(
          this.subjectCategoryInput.subject_id,
          this.subjectCategoryInput.id,
          this.subjectCategoryInput
        )
      }

      loading.close()
      this.subjectCategories = await getSubjectCategories(this.subjectCategoryInput.subject_id)

      this.subjectCategoryInput.id = ''
      this.subjectCategoryInput.name = ''
    },
    async handleChangeSubjectCategoryStatus(subjectId, categoryId, isEnabled) {
      const loading = Loading.service()
      await updateSubjectCategoryStatus(subjectId, categoryId, {
        is_enabled: isEnabled
      })
      loading.close()
    },
    async handleDeleteSubjectCategory(categoryId) {
      this.$confirm('此操作将永久删除该分类, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const loading = Loading.service()
        await deleteSubjectCategory(categoryId)
        loading.close()
        this.subjectCategories = await getSubjectCategories(this.subjectCategoryInput.subject_id)
      })
    },
    async handleSuggestionSwitch(scoped) {
      const loading = Loading.service()
      await updateSubjectKeywordSuggestionStatus(scoped.row.id, {
        is_enabled_keywords_suggestion: scoped.row.is_enabled_keywords_suggestion
      })
      loading.close()
    },
    async getData() {
      const loading = Loading.service()
      const { data } = await getSubjects()
      this.data = data
      loading.close()
    }
  }
}
</script>
