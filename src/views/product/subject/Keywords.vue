<template>
  <div class="companies p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <el-tabs v-model="activeName">
      <el-tab-pane label="已添加" name="added">
        <el-button
          v-can="{ name: 'subjects.goods-keywords.create' }"
          class="m-extra-large-b"
          icon="el-icon-circle-plus"
          type="primary"
          @click="dialogVisible = true"
        >
          添加
        </el-button>

        <el-card shadow="never">
          <el-input v-model="searchKeyword" placeholder="输入关键字搜索" style="width: 300px">
            <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
          </el-input>
          <define-table :cols="cols" :data="data" :paging="paging" :paging-events="pagingEvents"></define-table>
        </el-card>
      </el-tab-pane>
      <el-tab-pane label="待处理" name="pending">
        <el-card shadow="never">
          <el-input v-model="searchKeyword" placeholder="输入关键字搜索" style="width: 300px">
            <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
          </el-input>
          <define-table :cols="cols" :data="data" :paging="paging" :paging-events="pagingEvents"></define-table>
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <!-- dialogs -->
    <el-dialog title="添加关键字" :visible.sync="dialogVisible" width="30%">
      <el-form
        ref="addForm"
        :model="form"
        :rules="{ keyword: [{ required: true, message: '请输入关键字', trigger: 'blur' }] }"
        label-width="80px"
      >
        <el-form-item label="关键字" prop="keyword">
          <el-input type="textarea" v-model="form.keyword" placeholder="请输入关键字"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleAdd">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getSubjectKeywords, addSubjectKeyword, convertToKeyword, deleteSubjectKeyword } from '@/apis/subject'
import { Loading } from 'element-ui'

export default {
  data() {
    return {
      activeName: 'added',
      dialogVisible: false,
      form: {
        id: null,
        keyword: ''
      },
      cols: [
        {
          prop: 'keyword',
          label: '关键字'
        },
        {
          prop: 'created_at',
          width: 200,
          label: '添加时间'
        },
        {
          fixed: 'right',
          width: 120,
          align: 'center',
          label: '操作',
          scopedSlots: {
            default: (scoped) => {
              const convertToKeywordBtn =
                this.activeName == 'pending' ? (
                  <el-button
                    type="text"
                    v-can={{ name: 'subjects.goods-keywords.create' }}
                    size="small"
                    onClick={() => this.handleConvertToKeyword(scoped.row)}
                  >
                    转为关键字
                  </el-button>
                ) : (
                  ''
                )

              return (
                <div>
                  {convertToKeywordBtn}
                  <el-button
                    type="text"
                    v-can={{ name: 'subjects.goods-keywords.delete' }}
                    size="small"
                    onClick={() => this.handleDelete(scoped.row)}
                  >
                    删除
                  </el-button>
                </div>
              )
            }
          }
        }
      ],
      data: [],
      paging: {
        currentPage: 1,
        pageSize: 15,
        layout: ' prev, pager, next, jumper, total',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page

          this.fetchKeywords()
        }
      },
      searchKeyword: ''
    }
  },
  watch: {
    activeName() {
      this.fetchKeywords()
    }
  },
  created() {
    this.fetchKeywords()
  },
  methods: {
    handleSearch() {
      this.paging.page = 1

      this.fetchKeywords()
    },
    async fetchKeywords() {
      const loading = Loading.service()
      const { data, meta } = await getSubjectKeywords(this.$route.params.id, {
        filter: {
          is_added: this.activeName === 'added' ? 1 : 0,
          keyword: this.searchKeyword
        },
        page: this.paging.page
      })

      this.data = data
      this.paging.currentPage = meta.current_page
      this.paging.pageSize = meta.per_page
      this.paging.total = meta.total

      loading.close()
    },
    async handleAdd() {
      if (this.$refs.addForm.validate()) {
        const loading = Loading.service()
        this.form.id !== null
          ? await convertToKeyword(this.$route.params.id, this.form.id, this.form)
          : await addSubjectKeyword(this.$route.params.id, this.form)
        this.fetchKeywords()

        this.$message.success('保存成功')
        loading.close()
        this.form.keyword = ''
        this.dialogVisible = false
      }
    },
    handleConvertToKeyword(row) {
      this.dialogVisible = true
      this.form.keyword = row.keyword
      this.form.id = row.id
    },
    handleDelete(row) {
      this.$confirm('确定删除该关键字吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const loading = Loading.service()
          await deleteSubjectKeyword(this.$route.params.id, row.id)
          this.fetchKeywords()

          this.$message.success('删除成功')
          loading.close()
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding: 10px !important;
}
</style>
