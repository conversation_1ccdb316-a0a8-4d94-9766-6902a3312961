<!--
 * @Descripttion: 
 * @version: 
 * @Author: yanb
 * @Date: 2022-06-01 15:53:15
 * @LastEditors: yanb
 * @LastEditTime: 2024-06-13 16:26:59
-->
<template>
  <div class="channels p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <el-header class="d-flex align-items-center p-none">
      <el-button icon="el-icon-circle-plus" type="primary" @click="handleEditor({})"> 添加字段 </el-button>
    </el-header>

    <el-card shadow="never">
      <define-table :data="data" :cols="cols" />
    </el-card>

    <fields-editor :model="fieldsEditor.model" :visible.sync="fieldsEditor.visible" @submit="handleSubmit" />
  </div>
</template>

<script>
import { getOfflineProductFields, createOfflineFields, updateOfflineFields, deleteOfflineFields } from '@/apis/product'
import FieldsEditor from '@/components/product/OfflineProduct/FieldsEditor'
import { Loading } from 'element-ui'

export default {
  name: 'OfflineProductTemplateProductFields',
  components: { FieldsEditor },
  data() {
    return {
      fieldsEditor: {
        model: {},
        visible: false
      },
      data: [],
      cols: [
        {
          label: '字段名称',
          prop: 'title'
        },
        {
          label: '排序号',
          prop: 'order'
        },
        {
          label: '类型',
          prop: 'type',
          scopedSlots: {
            default: (scoped) => {
              switch (scoped.row.type) {
                case 'file':
                  return <span>文件上传</span>
                case '_file':
                  return <span>文件上传(带附件)</span>
                case 'text':
                  return <span>单行文本</span>
                case 'date':
                  return <span>时间选择</span>
                case 'select':
                  return <span>下拉列表</span>
                case 'textarea':
                  return <span>多行文本</span>
                default:
                  break
              }
            }
          }
        },
        {
          label: '操作',
          width: 85,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button size="small" type="text" onClick={() => this.handleEditor(scoped.row)}>
                    编辑
                  </el-button>
                  <el-button size="small" type="text" onClick={() => this.deleteFields(scoped.row)}>
                    删除
                  </el-button>
                </div>
              )
            }
          }
        }
      ]
    }
  },
  created() {
    this.fetchProductFields()
  },
  methods: {
    fetchProductFields() {
      getOfflineProductFields(this.$route.params.id).then((r) => (this.data = r.data))
    },
    handleEditor(model) {
      this.fieldsEditor.model = model
      this.fieldsEditor.visible = true
    },
    handleSubmit(data) {
      const loading = Loading.service()

      const action =
        data.id !== undefined
          ? updateOfflineFields(this.$route.params.id, data.id, data)
          : createOfflineFields(this.$route.params.id, data)

      action
        .then(() => {
          this.$message.success('操作成功')

          this.fetchProductFields()
        })
        .finally(() => loading.close())
    },
    deleteFields(data) {
      const loading = Loading.service()

      deleteOfflineFields(this.$route.params.id, data.id, data)
        .then(() => {
          this.$message.success('操作成功')

          this.fetchProductFields()
        })
        .finally(() => loading.close())
    }
  }
}
</script>
