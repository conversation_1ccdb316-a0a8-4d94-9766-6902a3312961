<!--
 * @Descripttion: 
 * @version: 
 * @Author: yanb
 * @Date: 2022-06-01 15:53:15
 * @LastEditors: yanb
 * @LastEditTime: 2023-03-13 10:41:56
-->
<template>
  <div class="channels p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <SearchPanel
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
      size="small"
    ></SearchPanel>

    <el-header class="d-flex align-items-center p-none">
      <el-button type="primary" @click="productInsurance"> 险类 </el-button>
      <el-button type="primary" @click="productType"> 险种 </el-button>
    </el-header>

    <el-card shadow="never">
      <define-table :data="data" :cols="cols" :paging="paging" :paging-events="pagingEvents" />
    </el-card>
  </div>
</template>

<script>
import { getOfflineProductCategories } from '@/apis/product'
import { Loading } from 'element-ui'

export default {
  name: 'OfflineProduct',
  data() {
    return {
      searchFields: [
        {
          type: 'select',
          valKey: 'parent_id',
          hintText: '险类',
          options: []
        },
        {
          type: 'select',
          valKey: 'id',
          hintText: '险种',
          options: []
        }
      ],
      insurances: [],
      data: [],
      searchData: {},
      paging: {
        currentPage: 1,
        pageSize: 15,
        layout: ' prev, pager, next, jumper, total',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page

          this.fetchProducts()
        }
      },
      cols: [
        {
          label: '险类',
          prop: 'parent.name'
        },
        {
          label: '险种',
          prop: 'name'
        },
        {
          label: '模板管理',
          width: 85,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button size="small" type="text" onClick={() => this.productOfflineFields(scoped.row.id)}>
                    编辑
                  </el-button>
                </div>
              )
            }
          }
        }
      ]
    }
  },
  created() {
    getOfflineProductCategories({ is_parent: 1, is_pageable: 0 }).then((r) => {
      this.insurances = r.data
      this.loadInsurances()
    })
    this.fetchProducts()
  },
  watch: {
    searchData: {
      immediate: true,
      deep: true,
      handler(value, old) {
        if (old?.parent_id !== value?.parent_id) {
          this.loadCategories()
        }
      }
    }
  },
  methods: {
    handleSearchPanel(command, data) {
      this.searchData = data
      this.paging.page = 1
      if (command === 'export') {
        //
      } else {
        this.fetchProducts()
      }
    },
    fetchProducts() {
      getOfflineProductCategories({
        is_parent: 0,
        is_pageable: 1,
        filter: Object.assign(this.searchData),
        page: this.paging.page
      }).then((r) => {
        this.data = r.data

        this.paging.currentPage = r.meta.current_page
        this.paging.page = r.meta.current_page
        this.paging.pageSize = r.meta.per_page
        this.paging.total = r.meta.total
      })
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    },
    loadInsurances() {
      const options = this.insurances.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('parent_id', options)
    },
    loadCategories() {
      let options = []
      if (this.searchData?.parent_id !== '' && this.searchData?.parent_id !== undefined) {
        const insurance = this.insurances.find((e) => e.id === this.searchData?.parent_id)
        if (insurance) {
          options = insurance.children.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      }

      this.assignSelectOptions('id', options)
    },
    productInsurance() {
      this.$router.push({
        name: 'OfflineProductInsurances'
      })
    },
    productType() {
      this.$router.push({
        name: 'OfflineProductType'
      })
    },
    productOfflineFields(id) {
      this.$open({
        name: 'OfflineProductFields',
        params: {
          id: id
        }
      })
    }
  }
}
</script>
