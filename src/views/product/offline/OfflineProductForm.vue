<!--
 * @Descripttion: 
 * @version: 
 * @Author: yanb
 * @Date: 2022-06-07 16:15:49
 * @LastEditors: yanb
 * @LastEditTime: 2022-06-07 16:59:23
-->
<template>
  <div class="p-extra-large-x p-extra-large-b w-100">
    <offline-product-form :model.sync="data" />
  </div>
</template>
<script>
import { getOfflineProduct } from '@/apis/product'
import OfflineProductForm from '@/components/product/OfflineProduct/ProductForm'

export default {
  name: 'OfflineProduct',
  components: {
    OfflineProductForm
  },
  data() {
    return {
      data: {}
    }
  },
  created() {
    if (this.$route.params.id !== undefined) {
      getOfflineProduct(this.$route.params.id).then((r) => (this.data = r.data))
    }
  }
}
</script>
