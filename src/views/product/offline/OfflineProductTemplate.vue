<!--
 * @Descripttion: 
 * @version: 
 * @Author: yanb
 * @Date: 2022-06-01 15:53:15
 * @LastEditors: yanb
 * @LastEditTime: 2022-06-06 16:56:33
-->
<template>
  <div class="channels p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <el-header class="d-flex align-items-center p-none">
      <el-button icon="el-icon-circle-plus" type="primary" @click="handleEditor({})"> 添加模板 </el-button>
    </el-header>

    <el-card shadow="never">
      <define-table :data="data" :cols="cols" />
    </el-card>

    <template-editor :model="templateEditor.model" :visible.sync="templateEditor.visible" @submit="handleSubmit" />
  </div>
</template>

<script>
import { getOfflineProductTemplates, createOfflineProductTemplate, updateOfflineProductTemplate } from '@/apis/product'
import TemplateEditor from '@/components/product/OfflineProduct/TemplateEditor'
import { Loading } from 'element-ui'

export default {
  name: 'OfflineProductTemplate',
  components: { TemplateEditor },
  data() {
    return {
      templateEditor: {
        model: {},
        visible: false
      },
      data: [],
      cols: [
        {
          label: '模板名称',
          prop: 'name'
        },
        {
          label: '所属险类',
          prop: 'category.name'
        },
        {
          label: '产品字段',
          width: 85,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button size="small" type="text" onClick={() => this.templateProductFields(scoped.row.id)}>
                    设置
                  </el-button>
                </div>
              )
            }
          }
        },
        {
          label: '保单字段',
          width: 85,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button size="small" type="text" onClick={() => this.templatePolicyFields(scoped.row.id)}>
                    设置
                  </el-button>
                </div>
              )
            }
          }
        },
        {
          label: '是否可用',
          width: 80,
          prop: 'is_enabled',
          scopedSlots: {
            default: (scoped) => {
              return scoped.row.is_enabled ? <span class="text-primary">是</span> : <span class="text-info">否</span>
            }
          }
        },
        {
          label: '操作',
          width: 85,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button size="small" type="text" onClick={() => this.handleEditor(scoped.row)}>
                    编辑
                  </el-button>
                  <el-popconfirm
                    style="margin-left: 10px"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    icon="el-icon-info"
                    icon-color="red"
                    title="确定删除吗？"
                    onConfirm={() => console.log(1)}
                  ></el-popconfirm>
                </div>
              )
            }
          }
        }
      ]
    }
  },
  created() {
    this.fetchTemplates()
  },
  methods: {
    fetchTemplates() {
      getOfflineProductTemplates().then((r) => (this.data = r.data))
    },
    handleEditor(model) {
      this.templateEditor.model = model
      this.templateEditor.visible = true
    },
    handleSubmit(data) {
      const loading = Loading.service()

      const action =
        data.id !== undefined ? updateOfflineProductTemplate(data.id, data) : createOfflineProductTemplate(data)

      action
        .then(() => {
          this.$message.success('操作成功')

          this.fetchTemplates()
        })
        .finally(() => loading.close())
    },
    templateProductFields(templateId) {
      this.$router.push({
        name: 'OfflineProductTemplateProductFields',
        params: {
          id: templateId
        }
      })
    },
    templatePolicyFields(templateId) {
      this.$router.push({
        name: 'OfflineProductTemplatePolicyFields',
        params: {
          id: templateId
        }
      })
    }
  }
}
</script>
