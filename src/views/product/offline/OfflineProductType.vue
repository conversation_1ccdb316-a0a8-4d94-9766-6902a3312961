<!--
 * @Descripttion: 
 * @version: 
 * @Author: yanb
 * @Date: 2022-06-01 15:53:15
 * @LastEditors: yanb
 * @LastEditTime: 2022-06-02 10:29:14
-->
<template>
  <div class="channels p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <el-header class="d-flex align-items-center p-none">
      <el-button icon="el-icon-circle-plus" type="primary" @click="handleEditor({})"> 添加险种 </el-button>
    </el-header>

    <el-card shadow="never">
      <define-table :data="data" :cols="cols" />
    </el-card>

    <type-editor :model="typeEditor.model" :visible.sync="typeEditor.visible" @submit="handleSubmit" />
  </div>
</template>

<script>
import { getOfflineProductCategories, createOfflineProductCategory, updateOfflineProductCategory } from '@/apis/product'
import TypeEditor from '@/components/product/OfflineProduct/TypeEditor'
import { Loading } from 'element-ui'

export default {
  name: 'OfflineProductType',
  components: { TypeEditor },
  data() {
    return {
      typeEditor: {
        model: {},
        visible: false
      },
      data: [],
      cols: [
        {
          label: '险种',
          prop: 'name'
        },
        {
          label: '所属险类',
          prop: 'parent.name'
        },
        {
          label: '是否可用',
          width: 80,
          prop: 'is_enabled',
          scopedSlots: {
            default: (scoped) => {
              return scoped.row.is_enabled ? <span class="text-primary">是</span> : <span class="text-info">否</span>
            }
          }
        },
        {
          label: '操作',
          width: 85,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button size="small" type="text" onClick={() => this.handleEditor(scoped.row)}>
                    编辑
                  </el-button>
                </div>
              )
            }
          }
        }
      ]
    }
  },
  created() {
    this.fetchInsurances()
  },
  methods: {
    fetchInsurances() {
      getOfflineProductCategories({ is_parent: 0, is_pageable: 1 }).then((r) => (this.data = r.data))
    },
    handleEditor(model) {
      this.typeEditor.model = model
      this.typeEditor.visible = true
    },
    handleSubmit(data) {
      const loading = Loading.service()

      const action =
        data.id !== undefined ? updateOfflineProductCategory(data.id, data) : createOfflineProductCategory(data)

      action
        .then(() => {
          this.$message.success('操作成功')

          this.fetchInsurances()
        })
        .finally(() => loading.close())
    }
  }
}
</script>
