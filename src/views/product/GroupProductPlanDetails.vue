<template>
  <div class="p-extra-large-x p-extra-large-b">
    <el-card shadow="never" class="info-bar">
      <el-row>
        <span v-if="product" style="float: left"> 产品：{{ product.name }} </span>
        <el-button
          type="primary"
          icon="fas fa-check"
          style="float: right"
          @click="handlePlanSubmit"
          v-can="{ name: 'products.group.plans.update' }"
        >
          保存
        </el-button>
      </el-row>
    </el-card>
    <el-form label-position="top" :model="plan" class="m-extra-large-t">
      <form-block class="m-extra-large-b" title="套餐信息">
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item required label="名称">
              <el-input v-model="plan.title" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="productPlatform === 'API_GROUP_ZY'" :span="12">
            <el-form-item required label="计划/方案代码">
              <el-input v-model="plan.code" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="24">
            <el-form-item required label="意外身故/残疾">
              <editor v-model="plan.accidental_death" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="24">
            <el-form-item label="意外医疗">
              <editor v-model="plan.accidental_medical" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="24">
            <el-form-item label="附加 24 小时意外伤害">
              <editor v-model="plan.accidental_injury" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item required label="意外伤害住院津贴">
              <el-input v-model="plan.accidental_allowance" type="textarea" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="雇主法律责任">
              <el-input v-model="plan.legal_liability" type="textarea" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item label="误工费">
              <el-input v-model="plan.lost_wages" type="textarea" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="累计赔偿限额">
              <el-input v-model="plan.total_indemnity" type="textarea" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </form-block>
      <SimpleContainer title="价格信息">
        <form-block class="m-extra-large-b" title="价格信息">
          <div v-if="product">
            <el-upload
              ref="jobImport"
              :multiple="false"
              :show-file-list="false"
              class="m-extra-large-b"
              action="#"
              :auto-upload="false"
              :on-change="handleImportPlan"
            >
              <el-button size="mini" type="primary" icon="fas fa-upload">导入数据</el-button>
            </el-upload>
            <el-row>
              <el-col :span="24">
                <el-table size="mini" :data="jobRepository.list" border style="width: 100%" highlight-current-row>
                  <el-table-column type="index"></el-table-column>
                  <el-table-column
                    v-for="(item, index) in jobRepository.columns"
                    :key="index"
                    :label="item.label"
                    :prop="item.prop"
                  >
                    <template slot-scope="scope">
                      <span v-if="scope.row.selected">
                        <el-input size="mini" placeholder="请输入内容" v-model="jobRepository.current[item.prop]">
                        </el-input>
                      </span>
                      <span v-else>{{ scope.row[item.prop] }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="210px">
                    <template slot-scope="scope">
                      <el-button
                        type="success"
                        v-can="{ name: 'products.group.jobs.create' }"
                        size="mini"
                        class="el-button el-button--success el-button--small"
                        @click.stop="saveRow(scope.row, scope.$index)"
                      >
                        保存
                      </el-button>
                      <el-button
                        type="info"
                        v-can="{ name: 'products.group.jobs.update' }"
                        size="mini"
                        class="el-button el-button--primary el-button--small"
                        @click="editRow(scope.row, scope.$index)"
                      >
                        编辑
                      </el-button>
                      <el-button
                        type="danger"
                        v-can="{ name: 'products.group.jobs.delete' }"
                        size="mini"
                        class="el-button el-button--danger el-button--small"
                        @click="deleteRow(scope.$index, jobRepository.list)"
                      >
                        删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
              <el-col :span="24">
                <div class="el-table-add-row" @click="add()">
                  <span><span v-can="{ name: 'products.group.jobs.create' }" class="el-icon-circle-plus" /> 添加</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </form-block>
      </SimpleContainer>
    </el-form>
  </div>
</template>

<script>
import { getGroupProductPlanDetail, updateGroupProductPlan } from '@/apis/groups'
import { getProductPlanJobList } from '@/apis/groups'
import { createProductPlanJob } from '@/apis/groups'
import { updateProductPlanJob } from '@/apis/groups'
import { deleteProductPlanJob } from '@/apis/groups'
import { importProductPlanJobs } from '@/apis/groups'
import FormBlock from '@/components/globals/FormBlock/FormBlock'
import { Loading } from 'element-ui'
import SimpleContainer from '@/components/globals/SimpleContainer'

export default {
  name: 'GroupProductPlanDetails',
  components: { SimpleContainer, FormBlock },
  data() {
    return {
      product: null,
      productPlatform: '',
      plan: {
        title: '',
        code: '',
        accidental_death: '',
        accidental_injury: '',
        accidental_medical: '',
        lost_wages: '',
        accidental_allowance: '',
        legal_liability: '',
        total_indemnity: '',
        is_enabled: 1
      },
      jobRepository: {
        current: null, //选中行
        columns: [
          {
            prop: 'code',
            label: '职位类别'
          },
          {
            prop: 'name',
            label: '职位名称'
          },
          {
            prop: 'price',
            label: '价格'
          }
        ],
        list: []
      }
    }
  },
  created() {
    this.fetchGroupProductPlanDetail()
  },
  methods: {
    handleImportPlan(file) {
      this.$confirm('确定要导入数据吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          const loading = Loading.service()
          importProductPlanJobs(this.$route.params.id, this.$route.params.planId, {
            file: file.raw
          })
            .then(() => {
              this.$message.success('导入成功')

              this.refreshJobList()
            })
            .finally(() => loading.close())
        })
        .catch(() => {})
        .finally(() => this.$refs.jobImport.clearFiles())
    },
    initJobRepository() {
      if (this.productPlatform === 'API_GROUP_ZY') {
        this.jobRepository = {
          current: null, //选中行
          columns: [
            {
              prop: 'code',
              label: '职位类别'
            },
            {
              prop: 'grade',
              label: '职业类别'
            },
            {
              prop: 'major_class',
              label: '职业大类'
            },
            {
              prop: 'occupation_code',
              label: '职业编码'
            },
            {
              prop: 'name',
              label: '职位名称'
            },
            {
              prop: 'price',
              label: '价格'
            }
          ],
          list: []
        }
      }
    },
    fetchGroupProductPlanDetail() {
      getGroupProductPlanDetail(this.$route.params.id, this.$route.params.planId).then((response) => {
        this.plan = response.data
        this.product = this.plan?.product
        this.productPlatform = this.product.additional.third_platform

        this.setJobRepositoryList(this.plan.jobs)
      })
    },
    setJobRepositoryList(jobs) {
      this.initJobRepository()

      let served = []
      for (let job of jobs) {
        served.push({
          id: job.id,
          code: job.code,
          grade: job.grade,
          major_class: job.major_class,
          occupation_code: job.occupation_code,
          name: job.name,
          price: job.price
        })
      }

      this.jobRepository.list = served
    },
    add() {
      if (this.checkListHasSelectedOne()) {
        return this.$message.warning('请先保存当前编辑项')
      }
      let newJob = {
        code: '',
        name: '',
        price: '',
        selected: true
      }
      this.jobRepository.list.push(newJob)
      this.jobRepository.current = JSON.parse(JSON.stringify(newJob))
    },
    saveRow(row, index) {
      if (!this.checkListHasSelectedOne()) {
        return this.$message.warning('暂无更改')
      }
      if (row.id !== this.jobRepository.current.id) {
        return this.$message.warning('请先保存当前编辑项')
      }
      //保存
      let fields = JSON.parse(JSON.stringify(this.jobRepository.current))
      for (let field in fields) {
        row[field] = fields[field]
      }
      this.postJob(row)
    },
    editRow(row) {
      //编辑
      if (this.checkListHasSelectedOne()) {
        return this.$message.warning('请先保存当前编辑项')
      }
      this.jobRepository.current = row
      row.selected = true
    },
    checkListHasSelectedOne() {
      for (let job of this.jobRepository.list) {
        if (job.selected) return true
      }
      return false
    },
    deleteRow(index, rows) {
      //删除
      if (rows[index]?.id) {
        this.postJob(rows[index], true)
      } else {
        rows.splice(index, 1)
      }
    },
    refreshJobList() {
      getProductPlanJobList(this.$route.params.id, this.$route.params.planId).then((r) => {
        this.setJobRepositoryList(r.data)
      })
    },
    postJob(row, remove = false) {
      if (remove) {
        deleteProductPlanJob(this.$route.params.id, this.$route.params.planId, row.id).then((r) => {
          row.selected = false
          this.$message.success('操作成功')
          this.refreshJobList()
        })
      } else {
        if (row.id != undefined) {
          updateProductPlanJob(this.$route.params.id, this.$route.params.planId, row.id, row)
            .then((r) => {
              row.selected = false
              this.$message.success('操作成功')
              this.refreshJobList()
            })
            .finally(() => {
              row.selected = false
            })
        } else {
          createProductPlanJob(this.$route.params.id, this.$route.params.planId, row)
            .then((r) => {
              row.selected = false
              this.$message.success('操作成功')
              this.refreshJobList()
            })
            .finally(() => {
              row.selected = false
            })
        }
      }
    },
    handlePlanSubmit() {
      const loading = Loading.service()
      updateGroupProductPlan(this.plan.product_id, this.plan.id, this.plan)
        .then(() => {
          this.$message.success('操作成功')
          this.$router.push({ name: 'GroupProductPlans', id: this.plan.id })
        })
        .finally(() => loading.close())
    }
  }
}
</script>

<style scoped lang="scss">
.el-table-add-row {
  margin-top: 10px;
  width: 100%;
  height: 34px;
  border: 1px dashed #c1c1cd;
  border-radius: 3px;
  cursor: pointer;
  justify-content: center;
  display: flex;
  line-height: 34px;
}
.info-bar {
  margin-bottom: 5px;
}
</style>
