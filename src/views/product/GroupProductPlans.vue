<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <el-header class="d-flex align-items-center p-none">
      <el-button
        icon="el-icon-circle-plus"
        type="primary"
        v-can="{ name: 'products.group.plans.create' }"
        @click="handleEditorGroupPlan"
      >
        添加套餐</el-button
      >
    </el-header>
    <el-card class="m-extra-large-t" shadow="never">
      <define-table :data="plans" :cols="cols" />
    </el-card>

    <group-product-plan-editor
      :model="groupProductPlanEditor.model"
      :visible.sync="groupProductPlanEditor.visible"
      @submit="handleSubmit"
    />
  </div>
</template>

<script>
import {
  getGroupProductPlans,
  updateGroupProductPlan,
  createGroupProductPlan,
  deleteGroupProductPlan
} from '../../apis/groups'
import GroupProductPlanEditor from '../../components/product/GroupProductPlanEditor'
import { Loading } from 'element-ui'

export default {
  name: 'GroupProductPlans',
  components: { GroupProductPlanEditor },
  data() {
    return {
      groupProductPlanEditor: {
        model: {},
        visible: false
      },
      plans: [],
      cols: [
        {
          prop: 'title',
          label: '套餐名'
        },
        {
          label: '工种类别',
          scopedSlots: {
            default: (scoped) => {
              function shorten(text, count) {
                return text.slice(0, count) + (text.length > count ? '...' : '')
              }
              return shorten(Array.from(scoped.row.job_titles ?? []).join(', '), 30)
            }
          }
        },
        {
          label: '工种数',
          scopedSlots: {
            default: (scoped) => {
              return Array.from(scoped.row.job_titles ?? []).length
            }
          }
        },
        {
          prop: 'status',
          label: '状态',
          scopedSlots: {
            default: (scoped) => {
              return scoped.row.is_enabled ? (
                <span class="text-primary">已启用</span>
              ) : (
                <span class="text-danger">未启用</span>
              )
            }
          }
        },
        {
          label: '操作',
          width: 120,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button size="mini" type="text" onClick={() => this.handleEditorGroupPlan(scoped.row)}>
                    编辑
                  </el-button>
                  <el-button
                    size="small"
                    type="text"
                    v-can={{ name: 'products.group.plans.update' }}
                    onClick={() => {
                      this.$router.push({
                        name: 'GroupProductPlanDetails',
                        params: { id: this.$route.params.id, planId: scoped.row.id }
                      })
                    }}
                  >
                    配置
                  </el-button>
                  <el-popconfirm
                    style="margin-left: 7px"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    icon="el-icon-info"
                    icon-color="red"
                    title="确定删除吗？"
                    v-can={{ name: 'products.group.plans.delete' }}
                    onConfirm={() => this.handleRemoveGroupPlan(scoped.row)}
                  >
                    <el-button size="mini" type="text" slot="reference">
                      删除
                    </el-button>
                  </el-popconfirm>
                </div>
              )
            }
          }
        }
      ],
      paging: {
        currentPage: 1,
        pageSizes: [15],
        pageSize: 15,
        layout: 'prev, pager, next, jumper, total',
        total: 0,
        align: 'left',
        background: true
      }
    }
  },
  created() {
    this.fetchGroupProductPlans()
  },
  methods: {
    fetchGroupProductPlans() {
      getGroupProductPlans(this.$route.params.id).then((response) => {
        this.plans = response.data
      })
    },
    handleEditorGroupPlan(model) {
      this.groupProductPlanEditor.model = model
      this.groupProductPlanEditor.visible = true
    },
    handleSubmit(data) {
      const loading = Loading.service()
      const action =
        data.id !== undefined
          ? updateGroupProductPlan(this.$route.params.id, data.id, data)
          : createGroupProductPlan(this.$route.params.id, data)
      action
        .then(() => {
          this.$message.success('操作成功')

          this.fetchGroupProductPlans()
        })
        .finally(() => loading.close())
    },
    handleRemoveGroupPlan(model) {
      deleteGroupProductPlan(this.$route.params.id, model.id).then(() => {
        this.$message.success('删除成功')
        this.fetchGroupProductPlans()
      })
    }
  }
}
</script>
