<template>
  <div class="product-intl p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <SearchPanel
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
      show-goods-value-type
      :exportable="false"
      size="small"
    ></SearchPanel>

    <el-header class="d-flex align-items-center p-none">
      <el-button
        v-can="{ name: 'products.create' }"
        icon="el-icon-circle-plus"
        type="primary"
        @click="$router.push({ name: 'ProductsCbecCreate' })"
      >
        添加产品
      </el-button>
    </el-header>
    <el-card shadow="never">
      <define-table :data="data" :cols="cols" :paging="paging" :paging-events="pagingEvents" />
    </el-card>

    <developer-configure
      :visible.sync="developerConfigure.visible"
      :agreements.sync="developerConfigure.agreements"
      :plans.sync="developerConfigure.plans"
      :data="developerConfigure.data"
      @submit="handleDeveloperConfigureSubmit"
    />
  </div>
</template>

<script>
import DeveloperConfigure from '@/components/product/DeveloperConfigure'
import { getProducts, deleteProduct, syncDeveloperConfigurations } from '@/apis/product'
import { getPlatformProductCompanies } from '@/apis/platform_product'
import { getPlatformsDict } from '@/apis/platform'

const PRODUCT_TYPE = 7

export default {
  components: {
    DeveloperConfigure
  },
  name: 'ProductsCbec',
  data() {
    return {
      searchFields: [
        {
          type: 'input',
          valKey: 'code',
          hintText: '产品代码'
        },
        {
          type: 'select',
          valKey: 'platform_id',
          hintText: '平台',
          options: []
        },
        {
          type: 'select',
          valKey: 'company_id',
          hintText: '保险公司',
          onChangeReset: 'company_branch_id',
          options: []
        },
        {
          type: 'select',
          valKey: 'company_branch_id',
          hintText: '出单公司',
          options: []
        },
        {
          type: 'select',
          valKey: 'goods_value_type',
          hintText: '货值类型',
          options: [
            { label: '高货值', value: 1 },
            { label: '低货值', value: 2 }
          ]
        },
        {
          type: 'select',
          valKey: 'coverage_scope',
          hintText: '保障范围',
          options: [
            { label: '保上架', value: 1 },
            { label: '保签收', value: 2 }
          ]
        }
      ],
      data: [],
      developerConfigure: {
        visible: false,
        id: '',
        agreements: '',
        plans: '',
        data: {
          agreement: '',
          plan: '',
          product_code: '',
          policy_mode: '',
          template: ''
        }
      },
      paging: {
        currentPage: 1,
        pageSize: 15,
        layout: ' prev, pager, next, jumper, total',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page
          this.fetchProducts()
        }
      },
      cols: [
        {
          label: '产品代码',
          scopedSlots: {
            default: (scoped) => {
              return (
                <el-link
                  type="primary"
                  onClick={() =>
                    this.$router.push({
                      name: 'ProductsIntlUpdate',
                      params: { id: scoped.row.id },
                      query: { from: 'code' }
                    })
                  }
                >
                  {scoped.row.code}
                </el-link>
              )
            }
          }
        },
        {
          label: '产品名称',
          prop: 'name'
        },
        {
          label: '平台',
          prop: 'platform.name'
        },
        {
          label: '出单公司',
          prop: 'company_branch.name'
        },
        {
          label: '出单渠道',
          prop: 'channel.name'
        },
        {
          label: '保障范围',
          prop: 'additional.coverage_scope_text'
        },
        {
          label: '货值类型',
          prop: 'additional.goods_value_type_text'
        },
        {
          label: '是否启用',
          prop: 'is_enabled',
          width: 80,
          scopedSlots: {
            default: (scoped) => {
              return scoped.row.is_enabled ? <span class="text-primary">是</span> : <span class="text-info">否</span>
            }
          }
        },
        {
          label: '操作',
          width: 150,
          fixed: 'right',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    v-can={{ name: 'products.update' }}
                    size="small"
                    type="text"
                    onClick={() => this.showDeveloperConfigure(scoped.row)}
                  >
                    技术配置
                  </el-button>
                  <el-button
                    v-can={{ name: 'products.update' }}
                    size="small"
                    type="text"
                    onClick={() => this.$router.push({ name: 'ProductsCbecUpdate', params: { id: scoped.row.id } })}
                  >
                    编辑
                  </el-button>
                  <el-popconfirm
                    v-can={{ name: 'products.delete' }}
                    style="margin-left: 10px"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    icon="el-icon-info"
                    icon-color="red"
                    title="确定删除吗？"
                    onConfirm={() => this.handleDeleteProduct(scoped.row)}
                  >
                    <el-button size="small" type="text" slot="reference">
                      删除
                    </el-button>
                  </el-popconfirm>
                </div>
              )
            }
          }
        }
      ],
      searchData: {},
      platforms: [],
      rawCompanies: []
    }
  },
  created() {
    getPlatformsDict().then((r) => {
      this.platforms = r.data

      this.loadPlatforms()
    })
    getPlatformProductCompanies().then((r) => {
      this.rawCompanies = r.data

      this.loadCompanies()
      this.loadCompanyBranches()
    })
    this.fetchProducts()
  },
  watch: {
    searchData: {
      immediate: true,
      deep: true,
      handler(value, old) {
        if (old?.company_id !== value?.company_id) {
          this.loadCompanyBranches()
        }
      }
    }
  },
  methods: {
    showDeveloperConfigure(rowData) {
      if ((rowData?.company_branch_account?.config || []).length < 0) {
        return this.$message.error(`请先配置账号${rowData?.company_branch_account?.name}基础信息`)
      }

      this.developerConfigure = {
        visible: true,
        id: rowData.id,
        agreements: rowData?.company_branch_account?.config?.agreements,
        plans: rowData?.company_branch_account?.config?.plans,
        data: Object.assign({}, rowData.additional.config)
      }
    },
    handleDeveloperConfigureSubmit(data) {
      syncDeveloperConfigurations(this.developerConfigure.id, data).then(() => {
        this.$message.success('操作成功')
        this.fetchProducts()
      })
    },
    handleDeleteProduct(rowData) {
      deleteProduct(rowData.id).then(() => {
        this.$message.success('删除成功')

        this.fetchProducts()
      })
    },
    handleSearchPanel(command, data) {
      this.searchData = data
      this.paging.page = 1
      if (command === 'export') {
        //
      } else {
        this.fetchProducts()
      }
    },
    fetchProducts() {
      getProducts({
        filter: Object.assign(
          {
            type: PRODUCT_TYPE
          },
          this.searchData
        ),
        page: this.paging.page
      }).then((r) => {
        this.data = r.data

        this.paging.currentPage = r.meta.current_page
        this.paging.pageSize = r.meta.per_page
        this.paging.total = r.meta.total
      })
    },
    loadPlatforms() {
      const options = this.platforms.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('platform_id', options)
    },
    loadCompanies() {
      const options = this.rawCompanies.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('company_id', options)
    },
    loadCompanyBranches() {
      let options = []
      if (this.searchData?.company_id !== '' && this.searchData?.company_id !== undefined) {
        const company = this.rawCompanies.find((e) => e.id === this.searchData?.company_id)
        if (company) {
          options = company.branches.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      } else {
        this.rawCompanies.forEach((e) => {
          e?.branches.forEach((e) => {
            options.push({
              label: e.name,
              value: e.id
            })
          })
        })
      }

      this.assignSelectOptions('company_branch_id', options)
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    }
  }
}
</script>
