<template>
  <div class="currency p-extra-large-x p-extra-large-b w-100">
    <el-header class="d-flex align-items-center p-none">
      <el-button
        v-can="{ name: 'currencies.create' }"
        icon="el-icon-circle-plus"
        type="primary"
        @click="
          $router.push({
            name: 'CurrencyConfigure'
          })
        "
      >
        配置汇率
      </el-button>
    </el-header>

    <el-card shadow="never">
      <define-table :cols="cols" :data="data" />
    </el-card>
  </div>
</template>

<script>
import { getConfiguredDates } from '@/apis/currency'

export default {
  name: 'Currency',
  data() {
    return {
      cols: [
        { label: '日期', prop: 'date' },
        {
          label: '操作',
          width: 100,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    v-can={{ name: 'currencies.show' }}
                    size="small"
                    type="text"
                    onClick={() => this.$router.push({ name: 'CurrencyConfigure', params: { date: scoped.row.date } })}
                  >
                    详情/更新
                  </el-button>
                </div>
              )
            }
          }
        }
      ],
      data: []
    }
  },
  created() {
    getConfiguredDates().then((r) => {
      this.data = r.data
    })
  }
}
</script>
