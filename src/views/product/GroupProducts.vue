<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <product-search @search="handleSearch" :show-subject-field="false" :showProductCode="false" />

    <el-header class="d-flex align-items-center p-none">
      <el-button icon="el-icon-circle-plus" type="primary" @click="$router.push({ name: 'GroupProductCreate' })">
        添加产品
      </el-button>
    </el-header>

    <el-card shadow="never">
      <define-table :data="products" :cols="cols" :paging="paging" :paging-events="pagingEvents" />
    </el-card>
  </div>
</template>

<script>
import ProductSearch from '@/components/product/ProductSearch'
import { getGroupProducts } from '@/apis/groups'
import { deleteProduct } from '@/apis/product'

const GROUP_PRODUCT_TYPE = 5

export default {
  name: 'GroupProducts',
  components: {
    ProductSearch
  },
  data() {
    return {
      products: [],
      searchData: {},
      cols: [
        {
          label: '运营平台',
          prop: 'platform.name',
          width: 150
        },
        {
          label: '出单公司',
          prop: 'company_branch.name',
          width: 120
        },
        {
          label: '产品名称',
          prop: 'name',
          width: 500
        },
        {
          label: '保险渠道',
          prop: 'channel.name'
        },
        {
          label: '是否可用',
          width: 80,
          scopedSlots: {
            default: (scoped) => {
              return scoped.row?.is_enabled ? <span class="text-primary">是</span> : <span class="text-info">否</span>
            }
          }
        },
        {
          label: '操作',
          width: 100,
          fixed: 'right',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    v-can={{ name: 'products.group.update' }}
                    size="small"
                    type="text"
                    onClick={() => this.$router.push({ name: 'GroupProductUpdate', params: { id: scoped.row.id } })}
                  >
                    编辑
                  </el-button>
                  <el-popconfirm
                    v-can={{ name: 'products.delete' }}
                    style="margin-left: 10px"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    icon="el-icon-info"
                    icon-color="red"
                    title="确定删除吗？"
                    onConfirm={() => this.handleDeleteProduct(scoped.row.id)}
                  >
                    <el-button size="small" type="text" slot="reference">
                      删除
                    </el-button>
                  </el-popconfirm>
                </div>
              )
            }
          }
        }
      ],
      paging: {
        currentPage: 1,
        pageSize: 15,
        layout: ' prev, pager, next, jumper, total',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page
          this.fetchGroupProducts()
        }
      }
    }
  },
  created() {
    this.fetchGroupProducts()
  },
  methods: {
    handleSearch(data) {
      this.searchData = data
      this.paging.page = 1

      this.fetchGroupProducts()
    },
    fetchGroupProducts() {
      getGroupProducts({
        filter: Object.assign(this.searchData, {
          type: GROUP_PRODUCT_TYPE
        }),
        page: this.paging.page
      }).then((r) => {
        this.products = r.data

        this.paging.currentPage = r.meta.current_page
        this.paging.total = r.meta.total
        this.paging.pageSize = r.meta.per_page
      })
    },
    handleDeleteProduct(id) {
      deleteProduct(id).then(() => {
        this.$message.success('删除成功')

        this.fetchGroupProducts(this.filter)
      })
    }
  }
}
</script>
