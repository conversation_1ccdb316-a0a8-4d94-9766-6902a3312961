<template>
  <div class="channels p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <el-header class="d-flex align-items-center p-none">
      <el-button
        v-can="{ name: 'channels.create' }"
        icon="el-icon-circle-plus"
        type="primary"
        @click="newChannelDialog"
      >
        添加保险渠道
      </el-button>
    </el-header>

    <el-card shadow="never">
      <define-table :data="data" :cols="cols" />
    </el-card>

    <channel-editor
      :visible.sync="channelEditor.visible"
      :platforms="platforms"
      :model="channelEditor.model"
      @submit="handleSubmit"
    />
  </div>
</template>

<script>
import { getChannels, createChannel, updateChannel, deleteChannel } from '@/apis/channel'
import { getPlatforms } from '@/apis/platform'
import ChannelEditor from '@/components/product/ChannelEditor'
import { Loading } from 'element-ui'

export default {
  name: 'Channels',
  components: { ChannelEditor },
  data() {
    return {
      data: [],
      platforms: [],
      channelEditor: {
        visible: false,
        model: {}
      },
      cols: [
        {
          label: '渠道名称',
          prop: 'name'
        },
        {
          label: '联系人',
          prop: 'contact'
        },
        {
          label: '联系电话',
          prop: 'phone_number'
        },
        {
          label: '邮箱',
          prop: 'email'
        },
        {
          label: '结算比例(%)',
          width: 100,
          prop: 'settlement_ratio'
        },
        // {
        //   label: '佣金比例(%)',
        //   width: 100,
        //   prop: 'commission_rate'
        // },
        {
          label: '备注',
          width: 150,
          prop: 'remark'
        },
        {
          label: '是否可用',
          width: 80,
          prop: 'is_enabled',
          scopedSlots: {
            default: (scoped) => {
              return scoped.row.is_enabled ? <span class="text-primary">是</span> : <span class="text-info">否</span>
            }
          }
        },
        {
          label: '操作',
          width: 85,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    v-can={{ name: 'channels.update' }}
                    size="small"
                    type="text"
                    onClick={() => this.handleEditChannel(scoped.row)}
                  >
                    编辑
                  </el-button>
                  <el-popconfirm
                    v-can={{ name: 'channels.delete' }}
                    style="margin-left: 10px"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    icon="el-icon-info"
                    icon-color="red"
                    title="确定删除吗？"
                    onConfirm={() => this.handleRemoveChannel(scoped.row)}
                  >
                    <el-button size="small" type="text" slot="reference">
                      删除
                    </el-button>
                  </el-popconfirm>
                </div>
              )
            }
          }
        }
      ]
    }
  },
  created() {
    this.fetchChannels()
    this.fetchPlatforms()
  },
  methods: {
    newChannelDialog() {
      this.channelEditor.visible = true
      this.channelEditor.model = {}
    },
    fetchChannels() {
      getChannels().then((r) => (this.data = r.data))
    },
    fetchPlatforms() {
      getPlatforms().then((r) => {
        this.platforms = r.data
      })
    },
    handleSubmit(data) {
      const action = data.id === undefined ? createChannel(data) : updateChannel(data.id, data)

      const loading = Loading.service()

      action
        .then(() => {
          this.$message.success('操作成功')

          this.fetchChannels()
        })
        .finally(() => loading.close())
    },
    handleEditChannel(data) {
      this.channelEditor.visible = true
      this.channelEditor.model = data
    },
    handleRemoveChannel(data) {
      deleteChannel(data.id).then(() => {
        this.$message.success('删除成功')

        this.fetchChannels()
      })
    }
  }
}
</script>
