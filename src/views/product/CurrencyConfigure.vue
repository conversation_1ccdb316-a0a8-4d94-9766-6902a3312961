<template>
  <div class="currency p-extra-large-x p-extra-large-b w-100">
    <el-alert title="本页面所有汇率均为对人民币汇率" type="warning" :closable="false" />

    <div class="p-extra-large-b">
      <el-card shadow="never" class="m-extra-large-t">
        <el-form ref="form" :model="form" :rules="rules" label-width="150px" label-suffix="">
          <el-form-item v-for="(name, code) in currencyNames" :key="code" :prop="code" :label="name">
            <el-input v-model="form[code]" :placeholder="'请输入' + name + '汇率'" />
          </el-form-item>
          <template v-if="!$route.params.date">
            <el-form-item>
              <el-checkbox v-model="form.is_custom_date">
                选择月份
                <span style="color: red">如不选择则默认为当前月份</span>
              </el-checkbox>
            </el-form-item>
            <el-form-item v-if="form.is_custom_date" prop="date" label="日期">
              <el-date-picker class="w-100" v-model="form.date" type="month" placeholder="选择日期" />
            </el-form-item>
          </template>
          <el-form-item>
            <el-button type="primary" icon="fas fa-save" @click="handleSave">保存</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script>
import { getCurrenciesExchange, createCurrencyExchange, updateCurrencyExchange } from '@/apis/currency'
import dayjs from 'dayjs'

export default {
  name: 'CurrencyConfigure',
  data() {
    return {
      currencyNames: {
        CNY: '人民币',
        USD: '美元',
        GBP: '英镑',
        JPY: '日元',
        HKD: '港币',
        EUR: '欧元',
        AUD: '澳大利亚元',
        CAD: '加拿大元',
        SGD: '新加坡币',
        MYR: '马来西亚林吉特币',
        CHF: '瑞士法郎',
        KRW: '韩元',
        SEK: '瑞典克朗',
        NZD: '新西兰元',
        NOK: '挪威克朗',
        THB: '泰铢'
      },
      date: '',
      form: {
        is_custom_date: false,
        date: ''
      },
      rules: {}
    }
  },
  created() {
    this.initForm()
  },
  methods: {
    initForm() {
      const keys = Object.keys(this.currencyNames)
      keys.forEach((k) => this.$set(this.form, k, ''))
      if (this.$route.params.date !== undefined) {
        getCurrenciesExchange(this.$route.params.date).then((r) => {
          r.data.forEach((item) => {
            this.form[item.code] = item.rate
          })
        })
      }

      keys.forEach((k) => {
        this.$set(this.rules, k, [
          { required: true, message: '请输入' + this.currencyNames[k] + '汇率', trigger: 'blur' }
        ])
      })
    },
    handleSave() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          let action
          if (this.$route.params.date === undefined) {
            let date
            if (this.form.is_custom_date) {
              date = dayjs(this.form.date).startOf('month').format('YYYY-MM-DD')
            } else {
              date = dayjs(Date.now()).startOf('month').format('YYYY-MM-DD')
            }

            delete this.form.is_custom_date
            delete this.form.date

            action = createCurrencyExchange({
              date: date,
              currencies: this.form
            })
          } else {
            delete this.form.is_custom_date
            delete this.form.date

            action = updateCurrencyExchange(this.$route.params.date, {
              currencies: this.form
            })
          }

          action.then(() => {
            this.$message.success('保存成功')

            this.$router.push({ name: 'Currency' })
          })
        }
      })
    }
  }
}
</script>
