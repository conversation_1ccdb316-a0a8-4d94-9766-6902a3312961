<template>
  <div class="company-details p-extra-large-x p-extra-large-b w-100" style="padding: 20px">
    <el-card shadow="never">
      <div class="__box">
        <div class="header">
          <div class="title">保险公司信息</div>
          <div class="right">
            <el-button
              v-can="{ name: 'companies.clauses.index' }"
              icon="el-icon-document"
              type="primary"
              style="margin-right: 20px"
              @click="$router.push({ name: 'CompanyClauses', params: { id: $route.params.id } })"
            >
              保险条款
            </el-button>
            <el-button-group>
              <el-button
                v-can="{ name: 'companies.goods-types.index' }"
                type="primary"
                icon="el-icon-receiving"
                @click="showGoodsType"
                >货物类别</el-button
              >
              <el-button
                v-can="{ name: 'companies.packing-methods.index' }"
                type="primary"
                icon="el-icon-box"
                @click="showMethodDialog('packing-methods')"
              >
                包装方式
              </el-button>
              <el-button
                v-can="{ name: 'companies.loading-methods.index' }"
                type="primary"
                icon="fas fa-boxes"
                @click="showMethodDialog('loading-methods')"
              >
                装载方式
              </el-button>
              <el-button
                v-can="{ name: 'companies.transport-methods.index' }"
                type="primary"
                icon="el-icon-ship"
                @click="showMethodDialog('transport-methods')"
              >
                运输方式
              </el-button>
              <!-- <el-button
                v-can="{ name: 'companies.cbec-destination-types.index' }"
                type="primary"
                icon="el-icon-receiving"
                @click="showCbecMethodDialog('cbec-destination-types')"
                >目的地类型</el-button
              >
              <el-button
                v-can="{ name: 'companies.cbec-delivery-methods.index' }"
                type="primary"
                icon="el-icon-ship"
                @click="showCbecMethodDialog('cbec-delivery-methods')"
              >
                派送方式
              </el-button>
              <el-button
                v-can="{ name: 'companies.cbec-express-companies.index' }"
                type="primary"
                icon="el-icon-ship"
                @click="showCbecMethodDialog('cbec-express-companies')"
              >
                快递公司
              </el-button> -->
              <el-dropdown @command="showCbecMethodDialog">
                <el-button type="primary"> 跨境电商货运险<i class="el-icon-arrow-down el-icon--right"></i> </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    v-can="{ name: 'companies.cbec-destination-types.index' }"
                    command="cbec-destination-types"
                    >目的地类型</el-dropdown-item
                  >
                  <el-dropdown-item
                    v-can="{ name: 'companies.cbec-delivery-methods.index' }"
                    command="cbec-delivery-methods"
                    >派送方式</el-dropdown-item
                  >
                  <el-dropdown-item
                    v-can="{ name: 'companies.cbec-express-companies.index' }"
                    command="cbec-express-companies"
                    >快递公司</el-dropdown-item
                  >
                </el-dropdown-menu>
              </el-dropdown>
            </el-button-group>
          </div>
        </div>
        <main style="margin-top: 20px">
          <el-table :data="company" border>
            <el-table-column prop="name" label="公司名称" />
            <el-table-column prop="logo" label="LOGO">
              <template slot-scope="scope">
                <img :src="scope.row.logo" alt="LOGO" height="20" />
              </template>
            </el-table-column>
          </el-table>
        </main>
      </div>

      <company-goods-type
        :visible.sync="companyGoodsType.visible"
        :model="companyGoodsType.model"
        :data.sync="companyGoodsType.data"
        @submit="handleCompanyGoodsTypeSubmit"
        @delete="handleDeleteCompanyGoodsType"
      />

      <company-methods
        :attr="{
          title: '装载方式',
          create: 'companies.loading-methods.create',
          update: 'companies.loading-methods.update',
          delete: 'companies.loading-methods.delete'
        }"
        :visible.sync="loadingMethodDialogVisible"
        :data.sync="loadingMethods"
        @submit="handleLoadingMethodSubmit"
        @delete="handleDeleteLoadingMethod"
      />
      <company-methods
        :attr="{
          title: '包装方式',
          create: 'companies.packing-methods.create',
          update: 'companies.packing-methods.update',
          delete: 'companies.packing-methods.delete'
        }"
        :visible.sync="packingMethodDialogVisible"
        :data.sync="packingMethods"
        @submit="handlePackingMethodSubmit"
        @delete="handleDeletePackingMethod"
      />
      <company-transport-methods
        :attr="{
          title: '运输方式',
          create: 'companies.transport-methods.create',
          update: 'companies.transport-methods.update',
          delete: 'companies.transport-methods.delete'
        }"
        :visible.sync="transportMethodDialogVisible"
        :data.sync="transportMethods"
        :loading-methods="loadingMethods"
        @submit="handleTransportMethodSubmit"
        @delete="handleDeleteTransportMethod"
      />
      <cbec-destination-types
        :visible.sync="cbecDestinationTypeDialog.visible"
        :model="cbecDestinationTypeDialog.model"
        :data.sync="cbecDestinationTypeDialog.data"
        @submit="handleCbecDestinationTypeSubmit"
        @delete="handleDeleteCbecDestinationType"
      />
      <cbec-company-methods
        :attr="{
          title: '派送方式',
          create: 'companies.cbec-delivery-methods.create',
          update: 'companies.cbec-delivery-methods.update',
          delete: 'companies.cbec-delivery-methods.delete'
        }"
        :visible.sync="cbecDeliveryMethodDialog.visible"
        :data.sync="cbecDeliveryMethodDialog.data"
        @submit="handleCbecDeliveryMethodSubmit"
        @delete="handleDeleteCbecDeliveryMethod"
      />
      <cbec-company-methods
        :attr="{
          title: '快递公司',
          create: 'companies.cbec-express-companies.create',
          update: 'companies.cbec-express-companies.update',
          delete: 'companies.cbec-express-companies.delete'
        }"
        :visible.sync="cbecExpressCompanyDialog.visible"
        :data.sync="cbecExpressCompanyDialog.data"
        @submit="handleCbecExpressCompanySubmit"
        @delete="handleDeleteCbecExpressCompany"
      />
    </el-card>

    <el-card shadow="never" style="margin-top: 20px">
      <div class="__box">
        <div class="header">
          <div class="title">出单公司</div>
          <div class="right">
            <el-button
              v-can="{ name: 'companies.branches.create' }"
              icon="el-icon-circle-plus"
              type="primary"
              @click="
                () => {
                  companyBranchEditor.visible = true
                  companyBranchEditor.model = {}
                }
              "
            >
              添加出单公司
            </el-button>
          </div>
        </div>
        <main style="margin-top: 20px">
          <define-table :data="companyBranches" :cols="companyBranchCols" />

          <company-branch-editor
            :visible.sync="companyBranchEditor.visible"
            :model.sync="companyBranchEditor.model"
            @submit="handleCompanyBranchSubmit"
          />

          <company-branch-account
            :data="companyBranchAccount.data"
            :visible.sync="companyBranchAccount.visible"
            v-if="companyBranchAccount.visible"
            :model="companyBranchAccount.model"
            @submit="handleCompanyBranchAccountSubmit"
            @delete="handleDeleteCompanyBranchAccount"
          />
        </main>
      </div>
    </el-card>
  </div>
</template>

<script>
import CompanyMethods from '@/components/product/CompanyMethods'
import CbecCompanyMethods from '@/components/product/CbecCompanyMethods'
import CompanyTransportMethods from '@/components/product/CompanyTransportMethods'
import CompanyBranchEditor from '@/components/product/CompanyBranchEditor'
import CompanyBranchAccount from '@/components/product/CompanyBranchAccount'
import CompanyGoodsType from '@/components/product/CompanyGoodsType'
import CbecDestinationTypes from '@/components/product/CbecDestinationTypes'

import { getCompany } from '@/apis/company'
import { getLoadingMethods, createLoadingMethod, updateLoadingMethod, deleteLoadingMethod } from '@/apis/loading_method'
import { getPackingMethods, createPackingMethod, updatePackingMethod, deletePackingMethod } from '@/apis/packing_method'
import {
  getTransportMethods,
  createTransportMethod,
  updateTransportMethod,
  deleteTransportMethod
} from '@/apis/transport_method'
import {
  getCompanyBranches,
  createCompanyBranch,
  updateCompanyBranch,
  deleteCompanyBranch
} from '@/apis/company_branch'
import {
  getCompanyBranchAccounts,
  createCompanyBranchAccount,
  updateCompanyBranchAccount,
  deleteCompanyBranchAccount
} from '@/apis/company_branch_account'
import { getGoodsTypes, createGoodsType, updateGoodsType, deleteGoodsType } from '@/apis/goods_type'

import * as cbecDestinationTypeApi from '@/apis/cbec_destination_type'
import * as cbecDeliveryMethodApi from '@/apis/cbec_delivery_method'
import * as cbecExpressCompanyApi from '@/apis/cbec_express_company'

import { Loading } from 'element-ui'

export default {
  name: 'CompaniesDetails',
  components: {
    CompanyMethods,
    CompanyBranchEditor,
    CompanyBranchAccount,
    CompanyGoodsType,
    CompanyTransportMethods,
    CbecCompanyMethods,
    CbecDestinationTypes
  },
  data() {
    return {
      loadingMethodDialogVisible: false,
      packingMethodDialogVisible: false,
      transportMethodDialogVisible: false,
      loadingMethods: [],
      packingMethods: [],
      transportMethods: [],
      company: [],
      companyBranches: [],
      companyGoodsType: {
        visible: false,
        data: [],
        model: {}
      },
      companyBranchEditor: {
        visible: false,
        model: {}
      },
      companyBranchAccount: {
        data: [],
        visible: false,
        model: {}
      },
      cbecDestinationTypeDialog: {
        visible: false,
        data: [],
        model: {}
      },
      cbecDeliveryMethodDialog: {
        visible: false,
        data: [],
        model: {}
      },
      cbecExpressCompanyDialog: {
        visible: false,
        data: [],
        model: {}
      },
      companyBranchCols: [
        {
          label: '出单公司名称',
          prop: 'name'
        },
        {
          label: '联系人',
          prop: 'contact'
        },
        {
          label: '联系电话',
          prop: 'phone_number'
        },
        {
          label: '邮箱',
          prop: 'email'
        },
        {
          label: '是否可用',
          prop: 'is_enabled',
          scopedSlots: {
            default: (scoped) => {
              return scoped.row.is_enabled ? <span class="text-primary">是</span> : <span class="text-info">否</span>
            }
          }
        },
        {
          label: '操作',
          width: 150,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    v-can={{ name: 'companies.branches.update' }}
                    size="small"
                    type="text"
                    onClick={() => this.handleEditCompanyBranch(scoped.row)}
                  >
                    编辑
                  </el-button>
                  <el-button
                    v-can={{ name: 'companies.branches.accounts.index' }}
                    size="small"
                    type="text"
                    onClick={() => this.handleAccountConfig(scoped.row)}
                  >
                    账号配置
                  </el-button>
                  <el-popconfirm
                    v-can={{ name: 'companies.branches.delete' }}
                    style="margin-left: 10px"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    icon="el-icon-info"
                    icon-color="red"
                    title="确定删除吗？"
                    onConfirm={() => this.handleRemoveCompanyBranch(scoped.row)}
                  >
                    <el-button size="small" type="text" slot="reference">
                      删除
                    </el-button>
                  </el-popconfirm>
                </div>
              )
            }
          }
        }
      ]
    }
  },
  created() {
    getCompany(this.$route.params.id).then((r) => (this.company = [r.data]))

    this.fetchCompanyBranches()
  },
  methods: {
    fetchCompanyGoodsTypes() {
      getGoodsTypes(this.$route.params.id).then((r) => (this.companyGoodsType.data = r.data))
    },
    handleCompanyGoodsTypeSubmit(data) {
      const action =
        data.id === undefined
          ? createGoodsType(this.$route.params.id, data)
          : updateGoodsType(this.$route.params.id, data.id, data)

      const loading = Loading.service()
      action
        .then(() => {
          this.$message.success('操作成功')

          this.fetchCompanyGoodsTypes()
        })
        .finally(() => loading.close())
    },
    handleDeleteCompanyGoodsType(data) {
      deleteGoodsType(this.$route.params.id, data.id).then(() => {
        this.$message.success('删除成功')

        this.fetchCompanyGoodsTypes()
      })
    },
    showGoodsType() {
      this.companyGoodsType.visible = true
      this.companyGoodsType.model = {}

      this.fetchCompanyGoodsTypes()
    },
    fetchCompanyBranchAccounts(companyBranchId) {
      getCompanyBranchAccounts(this.$route.params.id, companyBranchId).then((r) => {
        this.companyBranchAccount.data = r.data
      })
    },
    handleAccountConfig(rowData) {
      this.companyBranchAccount.visible = true
      this.companyBranchAccount.model = rowData

      this.fetchCompanyBranchAccounts(rowData.id)
    },
    handleCompanyBranchAccountSubmit(data) {
      const action =
        data.id === undefined
          ? createCompanyBranchAccount(this.$route.params.id, this.companyBranchAccount.model.id, data)
          : updateCompanyBranchAccount(this.$route.params.id, this.companyBranchAccount.model.id, data.id, data)

      const loading = Loading.service()
      action
        .then(() => {
          this.$message.success('操作成功')

          this.fetchCompanyBranchAccounts(this.companyBranchAccount.model.id)
        })
        .finally(() => loading.close())
    },
    handleDeleteCompanyBranchAccount(data) {
      deleteCompanyBranchAccount(this.$route.params.id, this.companyBranchAccount.model.id, data.id).then(() => {
        this.$message.success('删除账号成功')

        this.fetchCompanyBranchAccounts(this.companyBranchAccount.model.id)
      })
    },
    fetchCompanyBranches() {
      getCompanyBranches(this.$route.params.id).then((r) => (this.companyBranches = r.data))
    },
    handleEditCompanyBranch(rowData) {
      this.companyBranchEditor.visible = true
      this.$set(this.companyBranchEditor, 'model', rowData)
    },
    handleRemoveCompanyBranch(data) {
      deleteCompanyBranch(this.$route.params.id, data.id).then(() => {
        this.$message.success('删除成功')

        this.fetchCompanyBranches()
      })
    },
    handleCompanyBranchSubmit(data) {
      const loading = Loading.service()
      const action =
        data.id === undefined
          ? createCompanyBranch(this.$route.params.id, data)
          : updateCompanyBranch(this.$route.params.id, data.id, data)

      action
        .then(() => {
          this.$message.success('操作成功')

          this.fetchCompanyBranches()
        })
        .finally(() => loading.close())
    },
    showMethodDialog(module) {
      switch (module) {
        case 'packing-methods':
          this.packingMethodDialogVisible = true
          this.fetchPackingMethods()
          break
        case 'loading-methods':
          this.loadingMethodDialogVisible = true
          this.fetchLoadingMethods()
          break
        case 'transport-methods':
          this.transportMethodDialogVisible = true
          this.fetchLoadingMethods()
          this.fetchTransportMethods()
          break
        default:
          break
      }
    },
    showCbecMethodDialog(module) {
      switch (module) {
        case 'cbec-destination-types':
          this.cbecDestinationTypeDialog.visible = true
          this.cbecDestinationTypeDialog.model = {}
          this.fetchCbecDestinationTypes()
          break
        case 'cbec-delivery-methods':
          this.cbecDeliveryMethodDialog.visible = true
          this.fetchCbecDeliveryMethods()
          break
        case 'cbec-express-companies':
          this.cbecExpressCompanyDialog.visible = true
          this.fetchCbecExpressCompanies()
          break
        default:
          break
      }
    },
    fetchLoadingMethods() {
      getLoadingMethods(this.$route.params.id).then((r) => {
        this.loadingMethods = r.data
      })
    },
    fetchPackingMethods() {
      getPackingMethods(this.$route.params.id).then((r) => {
        this.packingMethods = r.data
      })
    },
    fetchTransportMethods() {
      getTransportMethods(this.$route.params.id).then((r) => {
        this.transportMethods = r.data
      })
    },
    handleDeleteLoadingMethod(data) {
      deleteLoadingMethod(this.$route.params.id, data.id).then(() => {
        this.$message.success('删除成功')

        this.fetchLoadingMethods()
      })
    },
    handleLoadingMethodSubmit(data) {
      const action =
        data.id === undefined
          ? createLoadingMethod(this.$route.params.id, data)
          : updateLoadingMethod(this.$route.params.id, data.id, data)

      action.then(() => {
        this.$message.success('保存装载方式成功')

        this.fetchLoadingMethods()
      })
    },
    handleDeletePackingMethod(data) {
      deletePackingMethod(this.$route.params.id, data.id).then(() => {
        this.$message.success('删除成功')

        this.fetchPackingMethods()
      })
    },
    handlePackingMethodSubmit(data) {
      const action =
        data.id === undefined
          ? createPackingMethod(this.$route.params.id, data)
          : updatePackingMethod(this.$route.params.id, data.id, data)

      action.then(() => {
        this.$message.success('保存包装方式成功')

        this.fetchPackingMethods()
      })
    },
    handleDeleteTransportMethod(data) {
      deleteTransportMethod(this.$route.params.id, data.id).then(() => {
        this.$message.success('删除成功')

        this.fetchTransportMethods()
      })
    },
    handleTransportMethodSubmit(data) {
      const action =
        data.id === undefined
          ? createTransportMethod(this.$route.params.id, data)
          : updateTransportMethod(this.$route.params.id, data.id, data)

      action.then(() => {
        this.$message.success('保存运输方式成功')

        this.fetchTransportMethods()
      })
    },
    fetchCbecDestinationTypes() {
      cbecDestinationTypeApi.getCbecDestinationTypes(this.$route.params.id).then((r) => {
        this.cbecDestinationTypeDialog.data = r.data
      })
    },
    handleCbecDestinationTypeSubmit(data) {
      const action =
        data.id === undefined
          ? cbecDestinationTypeApi.createCbecDestinationType(this.$route.params.id, data)
          : cbecDestinationTypeApi.updateCbecDestinationType(this.$route.params.id, data.id, data)

      action.then(() => {
        this.$message.success('保存成功')

        this.fetchCbecDestinationTypes()
      })
    },
    handleDeleteCbecDestinationType(data) {
      cbecDestinationTypeApi.deleteCbecDestinationType(this.$route.params.id, data.id).then(() => {
        this.$message.success('删除成功')

        this.fetchCbecDestinationTypes()
      })
    },
    fetchCbecDeliveryMethods() {
      cbecDeliveryMethodApi.getCbecDeliveryMethods(this.$route.params.id).then((r) => {
        this.cbecDeliveryMethodDialog.data = r.data
      })
    },
    handleCbecDeliveryMethodSubmit(data) {
      const action =
        data.id === undefined
          ? cbecDeliveryMethodApi.createCbecDeliveryMethod(this.$route.params.id, data)
          : cbecDeliveryMethodApi.updateCbecDeliveryMethod(this.$route.params.id, data.id, data)

      action.then(() => {
        this.$message.success('保存派送方式成功')

        this.fetchCbecDeliveryMethods()
      })
    },
    handleDeleteCbecDeliveryMethod(data) {
      cbecDeliveryMethodApi.deleteCbecDeliveryMethod(this.$route.params.id, data.id).then(() => {
        this.$message.success('删除成功')

        this.fetchCbecDeliveryMethods()
      })
    },
    fetchCbecExpressCompanies() {
      cbecExpressCompanyApi.getCbecExpressCompanies(this.$route.params.id).then((r) => {
        this.cbecExpressCompanyDialog.data = r.data
      })
    },
    handleCbecExpressCompanySubmit(data) {
      const action =
        data.id === undefined
          ? cbecExpressCompanyApi.createCbecExpressCompany(this.$route.params.id, data)
          : cbecExpressCompanyApi.updateCbecExpressCompany(this.$route.params.id, data.id, data)

      action.then(() => {
        this.$message.success('保存快递公司成功')

        this.fetchCbecExpressCompanies()
      })
    },
    handleDeleteCbecExpressCompany(data) {
      cbecExpressCompanyApi.deleteCbecExpressCompany(this.$route.params.id, data.id).then(() => {
        this.$message.success('删除成功')

        this.fetchCbecExpressCompanies()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.__box {
  .header {
    display: flex;
    flex-direction: column;

    .title {
      font-size: 16px;
      line-height: 32px;
    }

    .title,
    .right {
      flex: 1;
    }

    .right {
      text-align: right;

      /deep/ .el-button {
        height: 32px;
      }
    }
  }
}
</style>
