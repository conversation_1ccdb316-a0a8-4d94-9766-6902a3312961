<template>
  <div class="p-extra-large-t p-extra-large-x p-extra-large-b w-100">
    <el-row>
      <el-col :span="24">
        <div class="companies">
          <div
            class="company"
            v-for="com in companies"
            :key="com.id"
            :class="{ active: com.id === activeCompany?.id }"
            @click="handleSwitchCompany(com)"
          >
            <img class="logo" :src="com.logo" :alt="com.name" />
          </div>
        </div>
      </el-col>
    </el-row>

    <div class="m-extra-large-t">
      <el-button v-can="{ name: 'documents.create' }" type="primary" icon="fas fa-upload" @click="dialogVisible = true">
        上传文件
      </el-button>
    </div>

    <ul class="documents">
      <li v-if="documents.length === 0">暂无资料</li>
      <li v-for="doc in documents" :key="doc.id">
        <div>{{ doc.name }}</div>
        <div class="right">
          <span class="publish_date">上传日期：{{ doc.created_at }}</span>
          <el-button-group>
            <el-button type="primary" size="mini" icon="fas fa-download" @click="handleDownload(doc)">下载</el-button>
            <el-button
              type="danger"
              size="mini"
              icon="fas fa-times"
              @click="handleRemove(doc)"
              v-can="{ name: 'documents.delete' }"
            >
              删除
            </el-button>
          </el-button-group>
        </div>
      </li>
    </ul>

    <el-dialog :title="`上传资料(${activeCompany?.name})`" :visible.sync="dialogVisible" width="520px">
      <el-form ref="form" :model="form" :rules="rules">
        <el-form-item label="文件名" prop="name">
          <el-input v-model="form.name" placeholder="请输入文件名"></el-input>
        </el-form-item>
        <el-form-item label="文件" prop="file">
          <div class="w-100">
            <el-upload
              :on-change="handleFileChanged"
              action=""
              :file-list="files"
              :show-file-list="false"
              :auto-upload="false"
            >
              <el-button size="small" type="primary">点击上传</el-button>
              <span class="m-extra-large-l" v-if="files.length > 0">{{ files[0].name }}</span>
            </el-upload>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { getCompanies } from '@/apis/company'
import { getDocuments, createDocument, deleteDocument } from '@/apis/document'
import { Loading } from 'element-ui'

export default {
  data() {
    return {
      companies: [],
      activeCompany: null,
      dialogVisible: false,
      files: [],
      documents: [],
      form: {
        name: '',
        file: ''
      },
      rules: {
        name: [{ required: true, message: '请输入文件名', trigger: 'blur' }],
        file: [{ required: true, message: '请上传文件', trigger: 'blur' }]
      }
    }
  },
  async created() {
    const companies = await getCompanies()
    this.companies = companies.data
    this.activeCompany = companies.data[0]

    this.fetchDocuments()
  },
  methods: {
    handleSwitchCompany(com) {
      this.activeCompany = com

      this.fetchDocuments()
    },
    async fetchDocuments() {
      const documents = await getDocuments({ company_id: this.activeCompany.id })

      this.documents = documents.data
    },
    handleFileChanged(file) {
      this.form.file = file.raw
      this.files = [file]
    },
    async handleRemove(document) {
      try {
        await this.$confirm('确定删除该文件吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await deleteDocument(document.id)
        this.$message.success('删除成功')
        this.fetchDocuments()
      } catch {
        // do nothing
      }
    },
    handleDownload(document) {
      window.open(document.path, '_blank')
    },
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          const loading = Loading.service()
          try {
            await createDocument({
              name: this.form.name,
              file: this.form.file,
              company_id: this.activeCompany.id
            })
            this.fetchDocuments()
          } catch (err) {
            console.log(err)
          } finally {
            loading.close()
          }

          this.handleClose()
        }
      })
    },
    handleClose() {
      this.dialogVisible = false
      this.form = {
        name: '',
        file: ''
      }
    }
  }
}
</script>

<style scoped lang="scss">
.documents {
  list-style: none;
  padding: 0px;
  padding-bottom: 10px;

  li {
    padding: 10px;
    background: #ffffff;
    border-radius: 5px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;

    .right {
      margin-left: auto;
      display: flex;
      align-items: center;

      .publish_date {
        color: #888888;
        margin-right: 20px;
      }
    }

    &:hover {
      background: #f0f0f0;
    }
  }
}
.companies {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  column-gap: 20px;
  row-gap: 20px;

  .company {
    padding: 10px;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid #f0f0f0;

    &:hover {
      background: #ffffff;
      box-shadow: 0 0 10px 0 rgba(100, 100, 100, 0.1);
    }

    .logo {
      max-height: 30px;
      max-width: 120px;
    }
  }

  .active {
    background: #ffffff;
    box-shadow: 0 0 10px 0 rgba(100, 100, 100, 0.1);
    border: 1px solid #ff9429;
  }
}
</style>
