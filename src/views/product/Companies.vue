<template>
  <div class="companies p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <el-header class="d-flex align-items-center p-none">
      <el-button
        v-can="{ name: 'companies.create' }"
        icon="el-icon-circle-plus"
        type="primary"
        @click="handleEditorCompany({})"
      >
        添加保险公司
      </el-button>
    </el-header>
    <el-card shadow="never">
      <define-table :data="companies" :cols="cols"> </define-table>
    </el-card>

    <company-editor :model="companyEditor.model" :visible.sync="companyEditor.visible" @submit="handleSubmit" />
  </div>
</template>

<script>
import { getCompanies, createCompany, updateCompany, deleteCompany } from '@/apis/company'
import CompanyEditor from '@/components/product/CompanyEditor'
import { Loading } from 'element-ui'

export default {
  name: 'ProductCompanies',
  components: { CompanyEditor },
  data() {
    return {
      companyEditor: {
        model: {},
        visible: false
      },
      companies: [],
      cols: [
        {
          label: 'LOGO',
          prop: 'logo',
          width: 200,
          scopedSlots: {
            default: (scoped) => {
              return <img src={scoped.row.logo} height="25" alt="LOGO" />
            }
          }
        },
        {
          label: '标识符',
          prop: 'identifier',
          width: 80
        },
        {
          label: '保险公司',
          prop: 'name'
        },
        {
          label: '出单公司',
          prop: 'branches_count',
          width: 80
        },
        {
          label: '产品数',
          prop: 'products_count',
          width: 80
        },
        {
          label: '是否可用',
          prop: 'is_enabled',
          width: 80,
          scopedSlots: {
            default: (scoped) => {
              return scoped.row.is_enabled ? <span class="text-primary">是</span> : <span class="text-danger">否</span>
            }
          }
        },
        {
          label: '操作',
          width: 130,
          fixed: 'right',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    v-can={{ name: 'companies.update' }}
                    size="small"
                    type="text"
                    onClick={() => this.handleEditorCompany(scoped.row)}
                  >
                    编辑
                  </el-button>
                  <el-button
                    v-can={{ name: 'companies.show' }}
                    size="small"
                    type="text"
                    onClick={() => this.$router.push({ name: 'CompanyDetails', params: { id: scoped.row.id } })}
                  >
                    配置
                  </el-button>
                  <el-popconfirm
                    v-can={{ name: 'companies.delete' }}
                    style="margin-left: 10px"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    icon="el-icon-info"
                    icon-color="red"
                    title="确定删除吗？"
                    onConfirm={() => this.handleRemoveCompany(scoped.row)}
                  >
                    <el-button size="small" type="text  " slot="reference">
                      删除
                    </el-button>
                  </el-popconfirm>
                </div>
              )
            }
          }
        }
      ]
    }
  },
  created() {
    this.fetchCompanies()
  },
  methods: {
    fetchCompanies() {
      getCompanies().then((r) => {
        this.companies = r.data
      })
    },
    handleEditorCompany(model) {
      this.companyEditor.model = model
      this.companyEditor.visible = true
    },
    handleSubmit(data) {
      const loading = Loading.service()

      const action = data.id !== undefined ? updateCompany(data.id, data) : createCompany(data)

      action
        .then(() => {
          this.$message.success('操作成功')

          this.fetchCompanies()
        })
        .finally(() => loading.close())
    },
    handleRemoveCompany(rowData) {
      deleteCompany(rowData.id).then(() => {
        this.$message.success('删除成功')

        this.fetchCompanies()
      })
    }
  }
}
</script>
