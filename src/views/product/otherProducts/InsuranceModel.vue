<template>
  <div class="p-extra-large-b w-100 o-hidden-x">
    <site-breadcrumb>
      <div class="m-mini-x">
        <el-button
          icon="el-icon-circle-plus-outline"
          type="primary"
          @click="
            () => {
              initTable()
              title = '添加保险模型'
              dialogVisible = true
            }
          "
        >
          添加保险模型
        </el-button>
      </div>
    </site-breadcrumb>
    <!--    表单-->
    <div class="p-extra-large-x">
      <el-card class="m-extra-large-t" shadow="never">
        <DefineTable :data="dataList" :cols="cols" :paging="pagingAttrs" :pagingEvents="pagingEvents"></DefineTable>
      </el-card>
    </div>
    <!--    弹窗-->
    <el-dialog :title="title" :visible.sync="dialogVisible" width="40%">
      <el-form label-position="left" label-width="100px" :model="formInfo" :rules="rules" ref="from">
        <el-form-item label="模型名称：" prop="name">
          <el-input v-model="formInfo.name" placeholder="请输入模型名称"></el-input>
        </el-form-item>
        <el-form-item label="模型标识：" prop="flag">
          <el-input v-model="formInfo.flag" placeholder="请输入模型标识"></el-input>
        </el-form-item>
        <el-form-item label="备注：">
          <el-input v-model="formInfo.marks" placeholder="请输入备注"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click=";(dialogVisible = false), (formInfo = { name: '', flag: '', marks: '' })">取 消</el-button>
        <el-button type="primary" @click="sureAdd">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import SiteBreadcrumb from '@/layouts/SiteBreadcrumb'
import { ModelList, AddModel, EditModel, DelModel } from '@/apis/otherInsurance'
// import dayjs  from "dayjs";

export default {
  name: 'modelPage',
  components: {
    SiteBreadcrumb
  },
  data() {
    return {
      //分页信息
      pagingAttrs: {},
      //分页点击事件
      pagingEvents: {
        currentChange: (page) => {
          const pageInfo = { page: page }
          this.getListData(pageInfo)
        }
      },
      is_edit: void 0,
      title: '',
      dialogVisible: false,
      formInfo: {
        name: '',
        flag: '',
        marks: ''
      },
      dataList: [],
      cols: [
        { label: '模型名', prop: 'name' },
        { label: '标识', prop: 'flag' },
        { label: '备注', prop: 'marks' },
        {
          label: '状态',
          scopedSlots: {
            default: (scoped) => {
              return scoped.row?.is_enabled ? <span class="text-primary">是</span> : <span class="text-info">否</span>
            }
          }
        },
        {
          label: '操作',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    onClick={() => this.handlerEditor(scoped.row)}
                    type="primary"
                    plain
                    size="small"
                    class="pl-10 pr-10"
                  >
                    修改
                  </el-link>
                  <el-link
                    onClick={() => this.handlerManage(scoped.row)}
                    type="primary"
                    plain
                    class="pl-10 pr-10"
                    size="small"
                  >
                    字段管理
                  </el-link>
                  <el-link
                    onClick={() => this.handlerDel(scoped.row)}
                    type="primary"
                    plain
                    class="pl-10 pr-10"
                    size="small"
                  >
                    删除
                  </el-link>
                </div>
              )
            }
          }
        }
      ],
      //  表单验证
      rules: {
        name: [{ required: true, message: '请输入模型名称', trigger: 'blur' }],
        flag: [{ required: true, message: '请输入模型标识', trigger: 'blur' }]
      }
    }
  },
  mounted() {
    const pageInfo = { page: 1 }
    this.getModelList(pageInfo)
  },
  methods: {
    //  得到模型列表
    getModelList(pageInfo) {
      ModelList(pageInfo).then((r) => {
        this.dataList = r.data
        this.pagingAttrs = {
          align: 'center',
          currentPage: r.current_page,
          pageSize: r.per_page,
          layout: 'total, prev, pager, next, jumper',
          total: r.total
        }
      })
    },
    //修改
    handlerEditor(row) {
      console.log(row)
      this.title = '修改模型'
      this.formInfo = row
      this.is_edit = row
      this.dialogVisible = true
    },
    //  字段管理
    handlerManage(row) {
      console.log(row)
      this.$router.push({
        name: 'ModelFields',
        params: {
          id: row.id
        }
      })
    },
    //  删除
    handlerDel(row) {
      this.$confirm('此操作将永久删除该产品, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          DelModel(row.id).then(() => {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })

            const pageInfo = { page: this.pagingAttrs.current_page }
            this.getModelList(pageInfo)
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    //  弹窗确认
    sureAdd() {
      this.$refs.from.validate((valid) => {
        if (valid) {
          if (this.is_edit) {
            EditModel(this.is_edit.id, this.formInfo).then(() => {
              this.$message({ type: 'success', message: '修改成功' })
              this.formInfo = {
                name: '',
                flag: '',
                marks: ''
              }
              this.dialogVisible = false
              this.is_edit = void 0
              this.initTable()
            })
            return
          }
          AddModel(this.formInfo).then(() => {
            this.$message({
              type: 'success',
              message: '添加模型成功'
            })
            this.dialogVisible = false
            this.initTable()
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    //  初始化弹窗表格
    initTable() {
      this.formInfo = {
        name: '',
        flag: '',
        marks: ''
      }
      this.is_edit = 0
      const pageInfo = { page: 1 }
      this.getModelList(pageInfo)
    }
  }
}
</script>

<style lang="scss" scoped>
.editProduct {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
