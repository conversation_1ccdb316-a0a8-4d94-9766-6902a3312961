<template>
  <div class="privilege">
    <site-breadcrumb>
      <div class="m-mini-x">
        <el-button icon="el-icon-circle-plus-outline" type="primary" @click="addType"> 添加相关资料 </el-button>
      </div>
    </site-breadcrumb>
    <!--    表单-->
    <div class="m-extra-large-x p-extra-large bg-white flex-fill o-hidden o-y-auto">
      <DefineTable :data="dataList" :cols="cols"></DefineTable>
    </div>
    <!-- 弹窗 -->
    <el-dialog :title="title" :visible.sync="dialogVisible" width="40%">
      <el-form label-position="left" label-width="120px" :model="privilegeTable" ref="form" :rules="rules">
        <el-form-item label="资料名称" prop="name">
          <el-input v-model="privilegeTable.name" placeholder="请输入资料名称"></el-input>
        </el-form-item>
        <el-form-item label="资料文件" prop="file">
          <upload-file v-model="privilegeTable.file"></upload-file>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="sureAdd">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import SiteBreadcrumb from '@/layouts/SiteBreadcrumb'
import { aboutFile, productDetail } from '@/apis/otherInsurance'
import UploadFile from '@/components/globals/UploadFile/UploadFile'

export default {
  name: 'Privilege',
  components: {
    UploadFile,
    SiteBreadcrumb
  },
  data() {
    return {
      // //表单规则
      rules: {
        name: [{ required: true, message: '请输入资料名称', trigger: 'blur' }],
        file: [{ required: true, message: '请上传资料文件', trigger: 'blur' }]
      },
      //是否修改
      is_edit: void 0,
      //本页数据
      pageInfo: this.$route.params.pageData,
      //弹窗表单
      privilegeTable: {
        type: '',
        name: '',
        file: '',
        id: void 0
      },
      //操作方式
      title: '',
      dialogVisible: false,
      dataList: [],
      cols: [
        { label: '项目名称', prop: 'name' },
        // { label: '文件', prop: 'file' },
        {
          label: '文件',
          prop: 'file',
          scopedSlots: {
            default: (scoped) => {
              return (
                <el-link plain type="primary" href={scoped.row.file} size="small" target="_blank">
                  查看文件
                </el-link>
              )
            }
          }
        },
        {
          label: '操作',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    onClick={() => this.handlerEditor(scoped.row)}
                    type="primary"
                    plain
                    size="small"
                    class="m-extra-large-x"
                  >
                    修改
                  </el-link>
                  <el-link onClick={() => this.handlerDel(scoped.row)} type="primary" plain size="small">
                    删除
                  </el-link>
                </div>
              )
            }
          }
        }
      ]
    }
  },
  mounted() {
    this.getListData()
  },
  methods: {
    //得到列表
    getListData() {
      productDetail(this.$route.params.id).then((r) => {
        const arr = []
        const jsonData = JSON.parse(r.data?.related_file)
        for (let k in jsonData) {
          arr.push(jsonData[k])
        }
        this.dataList = arr
        this.pageInfo = r.data
      })
    },
    //修改
    handlerEditor(row) {
      this.title = '修改资料信息'
      this.is_edit = row
      this.privilegeTable.name = this.is_edit.name
      this.privilegeTable.file = this.is_edit.file
      this.dialogVisible = true
    },
    //  删除
    handlerDel(row) {
      this.$confirm('此操作将永久删除该相关资料, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const _temp = Object.assign({}, this.privilegeTable)
          _temp.id = row.id
          _temp.type = 'del'
          delete _temp.name
          delete _temp.file
          aboutFile(this.pageInfo.id, _temp).then(() => {
            this.$message({
              type: 'success',
              message: '删除成功'
            })
            this.getListData()
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    //  添加分类
    addType() {
      this.title = '添加资料信息'
      this.is_edit = 0
      this.privilegeTable.name = ''
      this.privilegeTable.file = ''
      this.dialogVisible = true
    },
    //  确认添加/修改
    sureAdd() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const _temp = Object.assign({}, this.privilegeTable)
          if (this.is_edit) {
            _temp.id = this.is_edit.id
            _temp.type = 'update'
            aboutFile(this.pageInfo.id, _temp).then(() => {
              this.$message({
                type: 'success',
                message: '修改成功'
              })
              this.getListData()
            })
            this.dialogVisible = false
            this.is_edit = void 0
            return
          }
          delete _temp.id
          _temp.type = 'add'
          aboutFile(this.pageInfo.id, _temp).then(() => {
            this.$message({
              type: 'success',
              message: '添加成功'
            })
            this.getListData()
            this.dialogVisible = false
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.privilege {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
