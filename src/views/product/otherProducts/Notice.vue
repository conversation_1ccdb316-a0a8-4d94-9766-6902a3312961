<!--
 * @Descripttion:
 * @version:
 * @Author: yanb
 * @Date: 2021-06-10 15:58:26
 * @LastEditors: yanb
 * @LastEditTime: 2021-07-02 15:10:39
-->
<template>
  <div class="w-100 notice">
    <div class="m-extra-large-x p-extra-large bg-white flex-fill o-hidden o-y-auto h-100">
      <el-form :model="data">
        <editor v-model="data.notice" class="w-100" />
        <el-button @click="uploadWord" class="m-extra-large-y" type="primary">确定提交</el-button>
      </el-form>
    </div>
  </div>
</template>

<script>
import { aboutNotice, productDetail } from '@/apis/otherInsurance'

export default {
  name: 'notice',
  data() {
    return {
      //本页数据
      data: {
        notice: ''
      }
    }
  },
  mounted() {
    this.getProduct()
  },
  methods: {
    getProduct() {
      productDetail(this.$route.params.id).then((r) => {
        this.data.notice = r.data.notice
      })
    },
    uploadWord() {
      aboutNotice(this.$route.params.id, this.data).then(() => {
        this.$message({
          type: 'success',
          message: '修改成功'
        })
        this.getProduct()
      })
    }
  }
}
</script>
