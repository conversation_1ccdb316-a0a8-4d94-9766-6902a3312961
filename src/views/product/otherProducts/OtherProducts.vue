<template>
  <div class="p-extra-large-b w-100 o-hidden-x">
    <site-breadcrumb>
      <div class="m-mini-x">
        <el-button icon="el-icon-circle-plus" type="primary" @click="addProduct"> 添加其他险种 </el-button>
        <el-button icon="el-icon-menu" type="primary" @click="hrefInsuranceType"> 保险分类 </el-button>
        <el-button icon="el-icon-box" type="primary" @click="$router.push({ name: 'InsuranceModel' })">
          保险模型
        </el-button>
      </div>
    </site-breadcrumb>
    <div class="p-extra-large-x">
      <el-card shadow="never">
        <DefineTable :data="dataList" :cols="colsList" :paging="pagingAttrs" :pagingEvents="pagingEvents" />
      </el-card>
    </div>
  </div>
</template>

<script>
import SiteBreadcrumb from '@/layouts/SiteBreadcrumb'
import DefineTable from '@/components/globals/DefineTable/DefineTable'
import { productList, deleteProduct } from '@/apis/otherInsurance'

export default {
  name: 'PoliciesOtherProductDetails',
  components: {
    DefineTable,
    SiteBreadcrumb
  },
  data() {
    return {
      surrender: {
        visible: false
      },
      allDataList: {},
      //  表格数据
      colsList: [
        { prop: 'platform.name', label: '平台' },

        {
          label: '保险公司',
          prop: 'company.name',
          align: 'center'
          // scopedSlots: {
          //   default: (scoped) => {
          //     return (
          // <img src={ scoped.row.imgUrl } alt="" />
          //     )
          //   }
          // }
        },
        { prop: 'name', label: '产品名字', align: 'center' },
        { prop: 'category.name', label: '保险分类', align: 'center' },
        { prop: 'service_charge', label: '保险公司经纪费', align: 'center' },
        { prop: 'is_mail', label: '是否发送邮件', align: 'center' },
        {
          label: '是否启用',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return scoped.row?.is_enabled ? <span class="text-primary">是</span> : <span class="text-info">否</span>
            }
          }
        },
        {
          label: '操作',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    v-can={{ name: 'products.general.update' }}
                    size="small"
                    type="text"
                    onClick={() => this.handlerEditor(scoped.row)}
                  >
                    编辑
                  </el-button>
                  <el-popconfirm
                    v-can={{ name: 'products.delete' }}
                    style="margin-left: 10px"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    icon="el-icon-info"
                    icon-color="red"
                    title="确定删除吗？"
                    onConfirm={() => this.handlerDel(scoped.row)}
                  >
                    <el-button size="small" type="text" slot="reference">
                      删除
                    </el-button>
                  </el-popconfirm>
                </div>
              )
            }
          }
        }
      ],
      dataList: [],
      pagingAttrs: {},
      pagingEvents: {
        currentChange: (page) => {
          const pageInfo = { page: page }
          this.getProductList(pageInfo)
        }
      }
    }
  },
  mounted() {
    const pageInfo = { page: 1 }
    this.getProductList(pageInfo)
  },
  methods: {
    getProductList(pageInfo) {
      productList(pageInfo).then((r) => {
        if (r.data.length > 0) {
          r.data.forEach((item) => {
            item.is_mail === 1 ? (item.is_mail = '是') : (item.is_mail = '否')
            // item.service_charge = item.service_charge + '元'
          })
        }
        this.allDataList = r
        this.dataList = r.data
        this.pagingAttrs = {
          align: 'center',
          currentPage: r.meta.current_page,
          pageSizes: [10, 20, 30, 40, 50],
          pageSize: r.meta.per_page,
          layout: 'total, prev, pager, next, jumper',
          total: r.meta.total
        }
      })
    },
    //  修改
    handlerEditor(row) {
      this.$router.push({
        name: 'EditOtherProduct',
        params: {
          id: row.id,
          pageInfo: row
        }
      })
    },
    //  删除
    handlerDel(row) {
      this.$confirm('此操作将永久删除该产品, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteProduct(row.id).then((res) => {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getProductList()
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    //  添加其他险种
    addProduct() {
      this.$router.push({
        name: 'AddProduct'
      })
    },
    //  跳转保险分类
    hrefInsuranceType() {
      this.$router.push({
        name: 'TypeInsurance'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.policy-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
