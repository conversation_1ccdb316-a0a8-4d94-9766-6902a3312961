<template>
  <div class="editProduct">
    <site-breadcrumb>
      <div class="m-mini-x">
        <el-button
          icon="el-icon-circle-plus-outline"
          type="primary"
          @click="
            title = '添加字段'
            table_init()
            dialogVisible = true
          "
        >
          添加字段
        </el-button>
      </div>
    </site-breadcrumb>
    <!--    表单-->
    <div class="m-extra-large-x p-extra-large bg-white flex-fill o-hidden o-y-auto">
      <DefineTable :data="dataList" :cols="cols" :paging="pagingAttrs" :pagingEvents="pagingEvents"></DefineTable>
    </div>
    <!--    弹窗-->
    <el-dialog :title="title" :visible.sync="dialogVisible" width="40%">
      <el-form label-position="left" label-width="100px" :model="formInfo" ref="form" :rules="rules">
        <el-form-item label="名称：" prop="title">
          <el-input v-model="formInfo.title" placeholder="请输入字段名称"></el-input>
        </el-form-item>
        <el-form-item label="排序：" prop="order">
          <el-input v-model="formInfo.order" placeholder="请输入排序序号" type="number"></el-input>
        </el-form-item>
        <el-form-item label="类型：" prop="type">
          <el-select v-model="formInfo.type" placeholder="请输入字段类型">
            <el-option label="文件上传" value="file"></el-option>
            <el-option label="文件上传(带附件)" value="_file"></el-option>
            <el-option label="单行文本" value="text"></el-option>
            <el-option label="下拉列表" value="select"></el-option>
            <el-option label="多行文本" value="textarea"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="标识：" prop="name">
          <el-input v-model="formInfo.name" placeholder="请输入标识"></el-input>
        </el-form-item>
        <el-form-item label="函数：">
          <el-input v-model="formInfo.function" placeholder="请输入函数"></el-input>
        </el-form-item>
        <el-form-item label="提示：">
          <el-input type="textarea" v-model="formInfo.tips" placeholder="请输入提示" :rows="4"></el-input>
        </el-form-item>
        <el-form-item label="附件：" v-if="formInfo.type === '_file'" prop="file">
          <upload-file v-model="formInfo.file"></upload-file>
          <el-link
            v-if="formInfo.file"
            icon="el-icon-view"
            style="position: absolute; left: 120px; top: 0"
            @click="previewFile(formInfo.file)"
            >点击查看</el-link
          >
        </el-form-item>
        <el-form-item label="下拉选项: " v-if="formInfo.type === 'select'" prop="textarea">
          <el-input
            v-model="formInfo.options"
            type="textarea"
            :rows="4"
            placeholder="选项与选项请使用 ｜ 隔开"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="sureAdd">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import SiteBreadcrumb from '@/layouts/SiteBreadcrumb'
import { WordList, wordAdd, wordEdit, wordDel, addProduct } from '@/apis/otherInsurance'
// import dayjs  from "dayjs";

export default {
  name: 'ModelFields',
  components: {
    SiteBreadcrumb
  },
  data() {
    return {
      //表单验证
      rules: {
        title: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        order: [{ required: true, message: '请输入排序', trigger: 'blur' }],
        type: [{ required: true, message: '请输入类型', trigger: 'blur' }],
        name: [{ required: true, message: '请输入标识', trigger: 'blur' }],
        file: [{ required: true, message: '请上传文件', trigger: 'blur' }],
        textarea: [{ required: true, message: '请输入下拉选项', trigger: 'blur' }]
      },
      //分页信息
      pagingAttrs: {},
      //分页点击事件
      pagingEvents: {
        currentChange: (page) => {
          const pageInfo = { page: page }
          this.getListData(pageInfo)
        }
      },
      selectList: {
        file: '文件上传',
        _file: '文件上传(带附件)',
        text: '单行文本',
        select: '下拉列表',
        textarea: '多行文本'
      },
      modelOption: this.$route.params.id,
      is_edit: void 0,
      title: '',
      dialogVisible: false,
      formInfo: {
        title: '',
        order: '',
        type: '',
        name: '',
        function: '',
        tips: ''
        // file: void 0
        // options: ''
      },
      dataList: [],
      cols: [
        { label: '排序号', prop: 'order' },
        { label: '模型名', prop: 'title' },
        {
          label: '类型',
          prop: 'type',
          scopedSlots: {
            default: (scoped) => {
              switch (scoped.row.type) {
                case 'file':
                  return <span>文件上传</span>
                case '_file':
                  return <span>文件上传(带附件)</span>
                case 'text':
                  return <span>单行文本</span>
                case 'select':
                  return <span>下拉列表</span>
                case 'textarea':
                  return <span>多行文本</span>
                default:
                  break
              }
            }
          }
        },
        { label: '标识', prop: 'name' },
        { label: '描述', prop: 'tips' },
        {
          label: '操作',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    onClick={() => this.handlerEditor(scoped.row)}
                    type="primary"
                    plain
                    size="small"
                    class="m-extra-large-x"
                  >
                    修改
                  </el-link>
                  <el-link onClick={() => this.handlerDel(scoped.row)} type="primary" plain size="small">
                    删除
                  </el-link>
                </div>
              )
            }
          }
        }
      ]
    }
  },
  mounted() {
    const pageInfo = { page: 1 }
    this.getModelList(pageInfo)
  },
  methods: {
    //  得到字段列表
    getModelList(pageInfo) {
      WordList(this.$route.params.id, pageInfo).then((r) => {
        this.dataList = r.data
        this.pagingAttrs = {
          align: 'center',
          currentPage: r.current_page,
          pageSize: r.per_page,
          layout: 'total, prev, pager, next, jumper',
          total: r.total
        }
      })
    },
    //修改
    handlerEditor(row) {
      this.title = '修改字段'
      row.file ? '' : delete row.file
      row.options ? '' : delete row.options
      this.formInfo = row
      this.is_edit = row
      this.dialogVisible = true
    },
    //  删除
    handlerDel(row) {
      this.$confirm('此操作将永久删除该产品, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          wordDel(this.modelOption, row.id).then(() => {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            const pageInfo = { page: this.pagingAttrs.current_page }
            this.getModelList(pageInfo)
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    //  弹窗确认
    sureAdd() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.is_edit) {
            wordEdit(this.modelOption, this.is_edit.id, this.formInfo).then(() => {
              this.$message({ type: 'success', message: '修改成功' })
              this.dialogVisible = false
              this.is_edit = void 0
              this.table_init()
            })
            return
          }
          wordAdd(this.modelOption, this.formInfo)
            .then(() => {
              this.$message({
                type: 'success',
                message: '添加字段成功'
              })
              this.dialogVisible = false
              this.table_init()
            })
            .catch((r) => {
              this.$message({
                type: 'error',
                message: r.message
              })
            })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    //  初始化弹窗表格
    table_init() {
      this.formInfo = {
        title: '',
        order: '',
        type: '',
        name: '',
        function: '',
        tips: ''
        // file: '',
        // options: ''
      }
      this.is_edit = 0
      const pageInfo = { page: this.pagingAttrs.current_page }
      this.getModelList(pageInfo)
    },
    previewFile(path) {
      window.open(path)
    }
  }
}
</script>

<style lang="scss" scoped>
.editProduct {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
