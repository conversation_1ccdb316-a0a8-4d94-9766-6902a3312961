<template>
  <div class="privilege">
    <site-breadcrumb>
      <div class="m-mini-x">
        <el-button icon="el-icon-circle-plus-outline" type="primary" @click="addType"> 添加保障权益 </el-button>
      </div>
    </site-breadcrumb>
    <!--    表单-->
    <div class="m-extra-large-x p-extra-large bg-white flex-fill o-hidden o-y-auto">
      <DefineTable :data="dataList" :cols="cols"></DefineTable>
    </div>
    <!-- 弹窗 -->
    <el-dialog :title="title" :visible.sync="dialogVisible" width="40%">
      <el-form label-position="left" label-width="120px" :model="privilegeTable" ref="form" :rules="rules">
        <el-form-item label="保障权益" prop="name">
          <el-input v-model="privilegeTable.name" placeholder="请输入保障权益"></el-input>
        </el-form-item>
        <el-form-item label="保障金额" prop="amount">
          <el-input v-model="privilegeTable.amount" placeholder="请输入保障金额"></el-input>
        </el-form-item>
        <el-form-item label="保障权益内容" prop="content">
          <el-input :rows="4" type="textarea" placeholder="请输入保障权益内容" v-model="privilegeTable.content">
          </el-input>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="sureAdd">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import SiteBreadcrumb from '@/layouts/SiteBreadcrumb'
import { addBenefit, productDetail } from '@/apis/otherInsurance'

export default {
  name: 'Privilege',
  components: {
    SiteBreadcrumb
  },
  data() {
    return {
      //表单规则
      rules: {
        name: [{ required: true, message: '请输入保障权益', trigger: 'blur' }],
        amount: [{ required: true, message: '请输入保障金额', trigger: 'blur' }],
        content: [{ required: true, message: '请输入保障权益内容', trigger: 'blur' }]
      },
      //是否修改
      is_edit: void 0,
      //本页数据
      pageInfo: this.$route.params.pageData,
      //弹窗表单
      privilegeTable: {
        type: '',
        name: '',
        amount: '',
        content: '',
        id: void 0
      },
      //操作方式
      title: '',
      dialogVisible: false,
      dataList: [],
      cols: [
        { label: '保障权益', prop: 'name' },
        { label: '保障金额', prop: 'amount' },
        {
          label: '操作',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    onClick={() => this.handlerEditor(scoped.row)}
                    type="primary"
                    plain
                    size="small"
                    class="m-extra-large-x"
                  >
                    修改
                  </el-link>
                  <el-link onClick={() => this.handlerDel(scoped.row)} type="primary" plain size="small">
                    删除
                  </el-link>
                </div>
              )
            }
          }
        }
      ]
    }
  },
  mounted() {
    this.getListData()
  },
  methods: {
    //得到列表
    getListData() {
      try {
        productDetail(this.$route.params.id).then((r) => {
          const arr = []
          const jsonData = JSON.parse(r.data?.benefit)
          for (let k in jsonData) {
            arr.push(jsonData[k])
          }
          this.dataList = arr
          this.pageInfo = r.data
        })
      } catch (e) {
        console.log(e)
      }
    },
    //修改
    handlerEditor(row) {
      this.title = '修改保障权益'
      this.is_edit = row
      this.privilegeTable.name = this.is_edit.name
      this.privilegeTable.amount = this.is_edit.amount
      this.privilegeTable.content = this.is_edit.content
      this.dialogVisible = true
    },
    //  删除
    handlerDel(row) {
      this.$confirm('此操作将永久删除该产品, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const _temp = Object.assign({}, this.privilegeTable)
          _temp.id = row.id
          _temp.type = 'del'
          addBenefit(this.pageInfo.id, _temp).then(() => {
            this.$message({
              type: 'success',
              message: '删除成功'
            })
            this.getListData()
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    //  添加分类
    addType() {
      this.title = '添加保障权益'
      this.privilegeTable.name = ''
      this.privilegeTable.amount = ''
      this.privilegeTable.content = ''
      this.dialogVisible = true
    },
    //  确认添加/修改
    sureAdd() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const _temp = Object.assign({}, this.privilegeTable)
          if (this.is_edit) {
            _temp.id = this.is_edit.id
            _temp.type = 'update'
            addBenefit(this.pageInfo.id, _temp).then(() => {
              this.$message({
                type: 'success',
                message: '修改成功'
              })
              this.getListData()
            })
            this.dialogVisible = false
            this.is_edit = void 0
            return
          } else {
            delete _temp.id
            _temp.type = 'add'
            addBenefit(this.pageInfo.id, _temp).then(() => {
              this.$message({
                type: 'success',
                message: '添加成功'
              })
              this.getListData()
              this.dialogVisible = false
            })
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.privilege {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
