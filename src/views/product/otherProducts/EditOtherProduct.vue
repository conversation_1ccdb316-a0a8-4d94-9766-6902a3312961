<template>
  <div class="editProduct">
    <site-breadcrumb>
      <div class="m-mini-x">
        <el-dropdown @command="handleCommand">
          <el-button icon="el-icon-s-unfold" type="primary" class="el-dropdown-link m-extra-large-x">
            其他补充信息
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="a">产品标签</el-dropdown-item>
            <el-dropdown-item command="b">保障权益</el-dropdown-item>
            <el-dropdown-item command="c">相关资料</el-dropdown-item>
            <el-dropdown-item command="d">投保须知</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button icon="el-icon-document-copy" type="primary" @click="copyProduct"> 复制 </el-button>
        <el-button icon="el-icon-caret-right" type="primary" v-if="!formList.is_enabled" @click="changeStatus(1)">
          启用
        </el-button>
        <el-button icon="el-icon-circle-close" type="primary" v-else @click="changeStatus(0)"> 禁用 </el-button>
      </div>
    </site-breadcrumb>
    <!--    表单-->
    <div class="m-extra-large-x p-extra-large bg-white flex-fill o-hidden o-y-auto">
      <el-form ref="form" :model="formList" label-width="120px" label-position="top" :inline="true" :rules="rules">
        <h1>基本信息</h1>
        <el-form-item label="产品名称" prop="name">
          <el-input v-model="formList.name" placeholder="请输入产品名称"></el-input>
        </el-form-item>
        <el-form-item prop="platform_id" label="平台">
          <el-select v-model="formList.platform_id" placeholder="请选择平台" clearable filterable style="width: 100%">
            <el-option v-for="p in platforms" :key="p.id" :value="p.id" :label="p.name" />
          </el-select>
        </el-form-item>
        <h1>条款信息</h1>
        <el-form-item label="产品条款" class="row" prop="clause_file" style="position: relative">
          <upload-file v-model="formList.clause_file"></upload-file>
          <el-link
            icon="el-icon-view"
            style="position: absolute; right: 0; top: 0"
            @click="previewFile(formList.clause_file)"
            >点击查看</el-link
          >
        </el-form-item>
        <h1>产品信息</h1>
        <el-form-item label="保险公司" prop="company_id">
          <el-select v-model="formList.company_id" placeholder="选择保险公司">
            <el-option :label="item.name" :value="item.id" v-for="item in companyList" :key="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="出单公司" prop="company_branch_id">
          <el-select v-model="formList.company_branch_id" placeholder="选择出单公司">
            <el-option
              :label="item.name"
              :value="item.id"
              v-for="item in getCompanyBranchesList"
              :key="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="保险渠道" prop="channel_id">
          <el-select v-model="formList.channel_id" placeholder="选择保险渠道">
            <el-option :label="item.name" :value="item.id" v-for="item in getChannelList" :key="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="保险分类" prop="category_id">
          <el-select v-model="formList.category_id" placeholder="选择保险分类">
            <el-option :label="item.name" :value="item.id" v-for="item in insuranceTypeList" :key="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="保险模型" prop="model_id">
          <el-select v-model="formList.model_id" placeholder="选择保险模型">
            <el-option :label="item.name" :value="item.id" v-for="item in modelList" :key="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="保险期限" prop="insurance_period">
          <el-select v-model="formList.insurance_period" placeholder="选择保险期限">
            <el-option :label="item.label" :value="item.value" v-for="item in termList" :key="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="免赔">
          <el-input type="textarea" v-model="formList.deductible" :rows="6"></el-input>
        </el-form-item>
        <el-form-item label="特别约定">
          <el-input type="textarea" v-model="formList.special_agreement" :rows="6"></el-input>
        </el-form-item>
        <h1>财务信息</h1>
        <el-form-item label="保险公司经纪费（%）" prop="service_charge">
          <el-input v-model="formList.service_charge" placeholder="请输入保险公司经纪费"></el-input>
        </el-form-item>
        <el-form-item label="参考价格（单位：元）" prop="reference_price">
          <el-input v-model="formList.reference_price" placeholder="请输入参考价格"></el-input>
        </el-form-item>
        <el-form-item label="平台出单费（%）" prop="platform_service_charge">
          <el-input v-model="formList.platform_service_charge" placeholder="请输入平台出单费"></el-input>
        </el-form-item>
        <h1>其他信息</h1>
        <el-form-item label="发送邮件" class="row">
          <el-radio v-model="formList.is_mail" :label="1">是</el-radio>
          <el-radio v-model="formList.is_mail" :label="0">否</el-radio>
        </el-form-item>
        <el-form-item label="投保告知">
          <editor v-model="formList.inform" />
        </el-form-item>
        <el-form-item label="报备邮箱">
          <el-input
            type="textarea"
            v-model="formList.email"
            :rows="21"
            placeholder="有多个邮箱用英文分号隔开"
          ></el-input>
        </el-form-item>
      </el-form>
      <el-divider></el-divider>
      <el-button type="primary" @click="sureInfo">确认提交</el-button>
    </div>
    <!-- 弹窗 -->
    <el-dialog title="产品标签" :visible.sync="dialogVisible" width="40%">
      <el-form label-position="top">
        <el-form-item label="产品标签(标签之间用 ｜ 分割)" style="width: 100%">
          <el-input type="textarea" v-model="formList.label" :rows="6"></el-input>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="updateLabel">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getPlatformsDict } from '@/apis/platform'
import SiteBreadcrumb from '@/layouts/SiteBreadcrumb'
import { getCompanies } from '@/apis/company'
import { getChannels } from '@/apis/channel'
import { getCompanyBranches } from '@/apis/company_branch'
import {
  insuranceTypeList,
  ModelList,
  updateOtherProduct,
  updateLabel,
  productDetail,
  addProduct
} from '@/apis/otherInsurance'

export default {
  name: 'EditOtherProduct',
  components: {
    SiteBreadcrumb
  },
  data() {
    return {
      //  验证
      rules: {
        name: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
        company_id: [{ required: true, message: '请选择保险公司', trigger: 'blur' }],
        company_branch_id: [{ required: true, message: '请选择出单公司', trigger: 'blur' }],
        channel_id: [{ required: true, message: '请选择保险渠道', trigger: 'blur' }],
        category_id: [{ required: true, message: '请选择保险分类', trigger: 'blur' }],
        model_id: [{ required: true, message: '请选择保险模型', trigger: 'blur' }],
        insurance_period: [{ required: true, message: '请选择保险期限', trigger: 'blur' }],
        service_charge: [{ required: true, message: '请输入保险公司经纪费', trigger: 'blur' }],
        reference_price: [{ required: true, message: '请输入参考价格', trigger: 'blur' }]
      },
      initChange: 0,
      //出单公司级连标识
      company_id: void 0,
      // 平台
      platforms: [],
      //保险公司下拉
      companyList: [],
      //保险渠道
      getChannelList: [],
      //出单公司
      getCompanyBranchesList: [],
      // 保险分类列表
      insuranceTypeList: [],
      //保险模型列表
      modelList: [],
      //保险期限下拉列表
      termList: [
        { label: '一月', value: '1' },
        { label: '二月', value: '2' },
        { label: '三月', value: '3' },
        { label: '四月', value: '4' },
        { label: '五月', value: '5' },
        { label: '六月', value: '6' },
        { label: '七月', value: '7' },
        { label: '八月', value: '8' },
        { label: '九月', value: '9' },
        { label: '十月', value: '10' },
        { label: '十一月', value: '11' },
        { label: '一年', value: '12' },
        { label: '二年', value: '24' }
      ],
      //禁用启用按钮
      dialogVisible: false,
      //  修改页数据
      pageInfo: {},
      //  表单信息
      formList: {
        type: 4,
        name: '',
        deductible: '',
        special_agreement: '',
        service_charge: '',
        platform_service_charge: '',
        clause_file: '',
        insurance_period: '',
        reference_price: '',
        is_mail: 0,
        email: '',
        inform: '',
        label: '',
        is_enabled: 0,
        platform_id: '',
        company_id: 0,
        company_branch_id: 0, //id
        channel_id: 0, //id
        category_id: 0, //id
        model_id: 0 //id
      },
      //  弹窗信息
      dInfo: {
        content: ''
      },
      //  页面是否从其他产品过来
      is_from: void 0
    }
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (from.fullPath === '/otherProducts') {
        vm._data.is_from = true
      }
    })
  },
  created() {
    getPlatformsDict({
      is_enabled: 1
    }).then((r) => (this.platforms = r.data))
    this.getProductDetail()
    this.selInit()
  },
  watch: {
    'formList.company_id': {
      handler(newV) {
        //  获取出单公司
        getCompanyBranches(newV).then((r) => {
          this.getCompanyBranchesList = r.data
        })
      },
      deep: true
    }
  },
  methods: {
    //初始化下拉框
    selInit() {
      //获取保险公司列表
      getCompanies().then((r) => {
        this.companyList = r.data
      })
      //  获取出单渠道
      getChannels().then((r) => {
        this.getChannelList = r.data
      })
      //  获取保险分类下拉列表
      insuranceTypeList().then((r) => {
        this.insuranceTypeList = r.data
      })
      //  保险模型下拉列表
      ModelList().then((r) => {
        this.modelList = r.data
      })
    },
    getProductDetail() {
      productDetail(this.$route.params.id).then((res) => {
        this.formList.name = res.data.name
        this.formList.deductible = res.data.deductible
        this.formList.special_agreement = res.data.special_agreement
        this.formList.service_charge = res.data.service_charge
        this.formList.platform_service_charge = res.data.platform_service_charge
        this.formList.clause_file = res.data.clause_file
        this.formList.insurance_period = res.data.insurance_period
        this.formList.reference_price = res.data.reference_price
        this.formList.email = res.data.email
        this.formList.inform = res.data.inform
        this.formList.is_enabled = res.data.is_enabled
        this.formList.platform_id = res.data.platform.id
        this.formList.company_id = res.data.company.id
        this.formList.company_branch_id = res.data.company_branch.id
        this.formList.channel_id = res.data.channel.id
        this.formList.model_id = res.data.model.id
        this.formList.category_id = res.data.category.id
        this.formList.is_mail = res.data.is_mail
        this.formList.label = res.data.label
      })
    },
    //确认提交
    sureInfo() {
      const _temp = Object.assign({}, this.formList)
      this.submitData(_temp)
    },
    surenfoRes() {
      this.$message({
        type: 'success',
        message: '修改成功'
      })
      this.$router.push({
        name: 'OtherProducts'
      })
    },
    //  弹窗确认按钮
    updateLabel() {
      updateLabel(this.$route.params.id, { label: this.formList.label }).then(() => {
        this.$message({
          type: 'success',
          message: '操作成功'
        })
      })
      this.dialogVisible = false
    },
    //  添加补充信息
    async handleCommand(command) {
      if (command === 'a') {
        this.dialogVisible = true
      } else if (command === 'b') {
        this.$router.push({
          name: 'Privilege',
          params: {
            id: this.$route.params.id
          }
        })
      } else if (command === 'c') {
        this.$router.push({
          name: 'AboutFile',
          params: {
            id: this.$route.params.id
          }
        })
      } else {
        this.$router.push({
          name: 'Notice',
          params: {
            id: this.$route.params.id
          }
        })
      }
    },
    //  改变启用/禁用状态
    changeStatus(status) {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.btnStatus = true
          const _temp = Object.assign({}, this.formList)
          _temp.is_mail = _temp.is_mail === true ? Number(1) : Number(0)
          _temp.is_enabled = status
          this.submitData(_temp)
        } else {
          console.log('error submit!!!')
          return false
        }
      })
    },
    //提交数据
    submitData(_temp) {
      this.$refs.form.validate((valid) => {
        if (valid) {
          updateOtherProduct(this.$route.params.id, _temp).then((r) => {
            this.surenfoRes(r)
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    //复制模块
    copyProduct() {
      this.$router.push({
        name: 'CopyProduct',
        params: {
          id: this.$route.params.id
        }
      })
    },
    previewFile(path) {
      window.open(path)
    }
  }
}
</script>

<style lang="scss" scoped>
.editProduct {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
////form中item样式
.el-form-item {
  width: 49%;
}
/deep/ .el-form-item__content {
  width: 90% !important;
}
/deep/ .el-dialog .el-form-item__content {
  width: 100% !important;
}
.el-select {
  width: 100% !important;
}
.row {
  width: 100%;
  /deep/ .el-form-item__content {
    width: 44% !important;
  }
}
h1 {
  margin-top: 0;
  position: relative;
  padding-left: 10px;
  &::before {
    content: '';
    display: inline-block;
    width: 3px;
    height: 15px;
    background-color: $app-color-primary;
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    margin-right: 10px;
  }
}
</style>
