<template>
  <div class="p-extra-large-b w-100 o-hidden-x">
    <site-breadcrumb>
      <div class="m-mini-x">
        <el-button icon="el-icon-circle-plus-outline" type="primary" @click="addType"> 添加保险分类 </el-button>
      </div>
    </site-breadcrumb>
    <!--    表单-->
    <div class="p-extra-large-x">
      <el-card class="m-extra-large-t" shadow="never">
        <DefineTable :data="dataList" :cols="cols" :paging="pagingAttrs" :pagingEvents="pagingEvents"></DefineTable>
      </el-card>
    </div>
    <!-- 弹窗 -->
    <el-dialog :title="title" :visible.sync="dialogVisible" width="30%">
      <el-input placeholder="请输入保险分类" v-model="form.name"> </el-input>
      <span slot="footer" class="dialog-footer">
        <el-button
          @click="
            () => {
              dialogVisible = false
              form.name = ''
            }
          "
          >取 消</el-button
        >
        <el-button type="primary" @click="sureAdd">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import SiteBreadcrumb from '@/layouts/SiteBreadcrumb'
import { addInsuranceType, insuranceTypeList, EditInsuranceType, DelInsuranceType } from '@/apis/otherInsurance'
import dayjs from 'dayjs'

export default {
  name: 'editProduct',
  components: {
    SiteBreadcrumb
  },
  data() {
    return {
      //分页信息
      pagingAttrs: {},
      //分页点击事件
      pagingEvents: {
        currentChange: (page) => {
          const pageInfo = { page: page }
          this.getListData(pageInfo)
        }
      },
      //是否修改
      is_edit: void 0,
      //操作方式
      title: '',
      dialogVisible: false,
      form: {
        name: ''
      },
      dataList: [],
      cols: [
        { label: '分类名', prop: 'name' },
        { label: '创建时间', prop: 'created_at' },
        {
          label: '操作',
          align: 'center',
          width: 120,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    onClick={() => this.handlerEditor(scoped.row)}
                    type="primary"
                    plain
                    size="small"
                    class="m-extra-large-x"
                  >
                    修改
                  </el-link>
                  <el-link onClick={() => this.handlerDel(scoped.row)} type="primary" plain size="small">
                    删除
                  </el-link>
                </div>
              )
            }
          }
        }
      ]
    }
  },
  mounted() {
    //获取列表数据
    const pageInfo = { page: 1 }
    this.getListData(pageInfo)
  },
  methods: {
    //获取列表数据
    getListData(pageInfo) {
      insuranceTypeList(pageInfo).then((r) => {
        console.log('aaa', r)
        r.data.forEach((item) => {
          item.created_at = dayjs(item.created_at).format('YYYY-MM-DD HH:mm:ss')
        })
        this.dataList = r.data
        this.pagingAttrs = {
          align: 'center',
          currentPage: r.current_page,
          pageSize: r.per_page,
          layout: 'total, prev, pager, next, jumper',
          total: r.total
        }
      })
    },
    //修改
    handlerEditor(row) {
      this.title = '修改分类'
      this.is_edit = row.id
      this.form.name = row.name
      this.dialogVisible = true
    },
    //  删除
    handlerDel(row) {
      this.$confirm('此操作将永久删除该产品, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          DelInsuranceType(row.id).then(() => {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            const pageInfo = { page: this.pagingAttrs.current_page }
            this.getListData(pageInfo)
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    //  添加分类
    addType() {
      this.title = '添加分类'
      this.form.name = ''
      this.is_edit = 0
      this.dialogVisible = true
    },
    //  确认添加/修改
    sureAdd() {
      if (this.is_edit) {
        EditInsuranceType(this.is_edit, this.form).then((r) => {
          console.log(r)
          this.is_edit = void 0
          this.form.name = ''
          this.dialogVisible = false
          const pageInfo = { page: 1 }
          this.getListData(pageInfo)
          this.$message({
            type: 'success',
            message: '修改成功'
          })
        })
        return
      }
      addInsuranceType(this.form).then(() => {
        const pageInfo = { page: 1 }
        this.getListData(pageInfo)
        this.$message({
          type: 'success',
          message: '添加成功'
        })
      })

      this.dialogVisible = false
      this.form.name = ''
    }
  }
}
</script>

<style lang="scss" scoped>
.editProduct {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
