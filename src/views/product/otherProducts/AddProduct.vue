<template>
  <div class="editProduct">
    <!--    表单-->
    <div class="m-extra-large-x m-extra-large-b p-extra-large bg-white flex-fill o-hidden o-y-auto">
      <el-form ref="form" :model="formList" label-width="120px" label-position="top" :inline="true" :rules="rules">
        <h1>基本信息</h1>
        <el-form-item label="产品名称" prop="name">
          <el-input v-model="formList.name" placeholder="请输入产品名称"></el-input>
        </el-form-item>
        <el-form-item prop="platform_id" label="平台">
          <el-select v-model="formList.platform_id" placeholder="请选择平台" clearable filterable style="width: 100%">
            <el-option v-for="p in platforms" :key="p.id" :value="p.id" :label="p.name" />
          </el-select>
        </el-form-item>
        <h1>条款信息</h1>
        <el-form-item label="产品条款" class="row" prop="clause_file">
          <upload-file v-model="formList.clause_file"></upload-file>
        </el-form-item>
        <h1>产品信息</h1>
        <el-form-item label="保险公司" prop="company_id">
          <el-select v-model="formList.company_id" placeholder="选择保险公司">
            <el-option :label="item.name" :value="item.id" v-for="item in companyList" :key="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="出单公司" prop="company_branch_id">
          <el-select v-model="formList.company_branch_id" placeholder="选择出单公司">
            <el-option
              :label="item.name"
              :value="item.id"
              v-for="item in getCompanyBranchesList"
              :key="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="保险渠道" prop="channel_id">
          <el-select v-model="formList.channel_id" placeholder="选择保险渠道">
            <el-option :label="item.name" :value="item.id" v-for="item in getChannelList" :key="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="保险分类" prop="category_id">
          <el-select v-model="formList.category_id" placeholder="选择保险分类">
            <el-option :label="item.name" :value="item.id" v-for="item in insuranceTypeList" :key="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="保险模型" prop="model_id">
          <el-select v-model="formList.model_id" placeholder="选择保险模型">
            <el-option :label="item.name" :value="item.id" v-for="item in modelList" :key="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="保险期限" prop="insurance_period">
          <el-select v-model="formList.insurance_period" placeholder="选择保险期限">
            <el-option :label="item.label" :value="item.value" v-for="item in termList" :key="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="免赔">
          <el-input type="textarea" v-model="formList.deductible" :rows="6"></el-input>
        </el-form-item>
        <el-form-item label="特别约定">
          <el-input type="textarea" v-model="formList.special_agreement" :rows="6"></el-input>
        </el-form-item>
        <h1>财务信息</h1>
        <el-form-item label="保险公司经纪费（%）" prop="service_charge">
          <el-input v-model="formList.service_charge" placeholder="请输入保险公司经纪费"></el-input>
        </el-form-item>
        <el-form-item label="参考价格（单位：元）" prop="reference_price">
          <el-input v-model="formList.reference_price" placeholder="请输入参考价格"></el-input>
        </el-form-item>
        <el-form-item label="平台出单费（%）" prop="platform_service_charge">
          <el-input v-model="formList.platform_service_charge" placeholder="请输入平台出单费"></el-input>
        </el-form-item>
        <h1>其他信息</h1>
        <el-form-item label="发送邮件" class="row">
          <el-radio v-model="formList.is_mail" :label="true">是</el-radio>
          <el-radio v-model="formList.is_mail" :label="false">否</el-radio>
        </el-form-item>

        <el-row :gutter="48">
          <el-col :span="24">
            <el-form-item label="投保告知">
              <editor v-model="formList.inform" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="报备邮箱" class="row">
          <el-input
            type="textarea"
            v-model="formList.email"
            :rows="6"
            placeholder="有多个邮箱用英文分号隔开"
          ></el-input>
        </el-form-item>
      </el-form>
      <el-divider></el-divider>
      <el-button type="primary" @click="sureInfo">确认提交</el-button>
    </div>
  </div>
</template>

<script>
import { getPlatformsDict } from '@/apis/platform'
import { getCompanies } from '@/apis/company'
import { getChannels } from '@/apis/channel'
import { getCompanyBranches } from '@/apis/company_branch'
import { addProduct, insuranceTypeList, ModelList } from '@/apis/otherInsurance'

export default {
  name: 'editProduct',
  data() {
    return {
      //出单公司级连标识
      company_id: void 0,
      // 平台
      platforms: [],
      //保险公司下拉
      companyList: [],
      //保险渠道
      getChannelList: [],
      //出单公司
      getCompanyBranchesList: [],
      // 保险分类列表
      insuranceTypeList: [],
      //保险模型列表
      modelList: [],
      //保险期限下拉列表
      termList: [
        { label: '一月', value: '1' },
        { label: '二月', value: '2' },
        { label: '三月', value: '3' },
        { label: '四月', value: '4' },
        { label: '五月', value: '5' },
        { label: '六月', value: '6' },
        { label: '七月', value: '7' },
        { label: '八月', value: '8' },
        { label: '九月', value: '9' },
        { label: '十月', value: '10' },
        { label: '十一月', value: '11' },
        { label: '一年', value: '12' },
        { label: '二年', value: '24' }
      ],
      //  表单信息
      formList: {
        type: 4,
        name: '',
        platform_id: '',
        company_id: '',
        company_branch_id: '',
        deductible: '',
        special_agreement: '',
        service_charge: '',
        platform_service_charge: '',
        clause_file: '',
        channel_id: '',
        category_id: '',
        model_id: '',
        insurance_period: '',
        reference_price: '',
        is_mail: 0,
        email: '',
        inform: '',
        company_branch_account_id: 1
      },
      //  验证
      rules: {
        name: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
        platform_id: [{ required: true, message: '请选择运营平台', trigger: ['blur', 'change'] }],
        clause_file: [{ required: true, message: '请上传产品条例', trigger: 'blur' }],
        company_id: [{ required: true, message: '请选择保险公司', trigger: 'blur' }],
        company_branch_id: [{ required: true, message: '请选择出单公司', trigger: 'blur' }],
        channel_id: [{ required: true, message: '请选择保险渠道', trigger: 'blur' }],
        category_id: [{ required: true, message: '请选择保险分类', trigger: 'blur' }],
        model_id: [{ required: true, message: '请选择保险模型', trigger: 'blur' }],
        insurance_period: [{ required: true, message: '请选择保险期限', trigger: 'blur' }],
        service_charge: [{ required: true, message: '请输入保险公司经纪费', trigger: 'blur' }],
        reference_price: [{ required: true, message: '请输入参考价格', trigger: 'blur' }],
        platform_service_charge: [{ required: true, message: '请输入平台出单费', trigger: 'blur' }]
      }
    }
  },
  created() {
    getPlatformsDict({
      is_enabled: 1
    }).then((r) => (this.platforms = r.data))
    //获取保险公司列表
    getCompanies().then((r) => {
      this.companyList = r.data
    })
    //  获取出单渠道
    getChannels().then((r) => {
      this.getChannelList = r.data
    })
    //  获取保险分类下拉列表
    insuranceTypeList().then((r) => {
      this.insuranceTypeList = r.data
    })
    //  保险模型下拉列表
    ModelList().then((r) => {
      this.modelList = r.data
    })
  },
  watch: {
    'formList.company_id': {
      handler(newV) {
        //  获取出单公司
        getCompanyBranches(newV).then((r) => {
          this.getCompanyBranchesList = r.data
        })
      },
      deep: true
    }
  },
  methods: {
    //确认提交
    sureInfo() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const _temp = Object.assign({}, this.formList)
          _temp.is_mail = this.formList.is_mail == true ? Number(1) : Number(0)
          addProduct(_temp).then(() => {
            // console.log(r)
            this.$message({ type: 'success', message: '添加产品成功' })
            this.$router.push({ name: 'OtherProducts' })
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.editProduct {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
////form中item样式
.el-form-item {
  width: 49%;
}
/deep/ .el-form-item__content {
  width: 90% !important;
}
.el-select {
  width: 100% !important;
}
.row {
  width: 100%;
  /deep/ .el-form-item__content {
    width: 44% !important;
  }
}
h1 {
  margin-top: 0;
  position: relative;
  padding-left: 10px;
  &::before {
    content: '';
    display: inline-block;
    width: 3px;
    height: 15px;
    background-color: $app-color-primary;
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    margin-right: 10px;
  }
}
</style>
