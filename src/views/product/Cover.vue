<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <el-card shadow="never">
      <el-row :gutter="24">
        <el-col :span="6">
          <el-select v-model="searchForm.company_id" placeholder="请选择保险公司" clearable class="w-100">
            <el-option v-for="com in companies" :key="com.id" :label="com.name" :value="com.id"></el-option>
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select v-model="searchForm.company_branch_id" placeholder="出单公司" clearable class="w-100">
            <el-option
              v-for="branch in companyBranches"
              :key="branch.id"
              :label="branch.name"
              :value="branch.id"
            ></el-option>
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" icon="fas fa-search" @click="handleSearch" :disabled="isDisabled">查询</el-button>
        </el-col>
      </el-row>
    </el-card>

    <el-card shadow="never" class="m-extra-large-t">
      <template slot="header">
        保障范围
        <el-button
          :disabled="isDisabled"
          size="mini"
          type="primary"
          icon="fas fa-sync"
          v-can="{ name: 'covers.save' }"
          @click="handleSync"
        >
          同步到产品
        </el-button>
        <span class="text-danger m-mini-l">同步到产品前请确认是否已经保存，一旦同步将无法撤回。</span>
      </template>

      <el-form :model="coverForm" label-position="top" :disabled="isDisabled">
        <el-form-item label="国内禁运地区">
          <el-select
            v-model="coverForm.domestic_disabled_regions"
            multiple
            filterable
            placeholder="请选择国内禁运地区"
            class="w-100"
          >
            <el-option
              v-for="areadata in domesticAreadata"
              :key="areadata.code"
              :label="areadata.value"
              :value="areadata.code"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="国际禁运地区">
          <el-select
            v-model="coverForm.intl_disabled_regions"
            multiple
            filterable
            placeholder="请选择国际禁运地区"
            class="w-100"
          >
            <el-option v-for="(key, value) in intlRegions" :key="key" :label="key" :value="value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="保障到港地区">
          <el-select
            v-model="coverForm.to_port_regions"
            multiple
            filterable
            placeholder="请选择保障到港地区"
            class="w-100"
          >
            <el-option v-for="(key, value) in intlRegions" :key="key" :label="key" :value="value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="保障到港条款">
          <el-input v-model="coverForm.to_port_clause" placeholder="请输入保障到港条款" />
        </el-form-item>

        <el-form-item label="战争和罢工地区">
          <el-select
            v-model="coverForm.war_and_strike_regions"
            multiple
            filterable
            placeholder="请选择战争和罢工地区"
            class="w-100"
          >
            <el-option v-for="(key, value) in intlRegions" :key="key" :label="key" :value="value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="制裁地区">
          <el-select
            v-model="coverForm.sanctioned_regions"
            multiple
            filterable
            placeholder="请选择制裁地区"
            class="w-100"
          >
            <el-option v-for="(key, value) in intlRegions" :key="key" :label="key" :value="value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="制裁条款">
          <el-input v-model="coverForm.sanctioned_clause" placeholder="请输入制裁条款" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="fas fa-save" v-can="{ name: 'covers.save' }" @click="handleSaveCover">
            保存
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card shadow="never" class="m-extra-large-t">
      <template slot="header">
        <div class="d-flex justify-content-between align-items-center">
          <span>保司政策</span>
          <el-button
            type="primary"
            v-can="{ name: 'covers.save' }"
            icon="fas fa-plus"
            @click="() => (coverDetailDialogVisible = true)"
          >
            新增政策
          </el-button>
        </div>
      </template>

      <define-table :data="coverDetails" :cols="coverDetailsTableCols" />
    </el-card>

    <el-dialog :title="policyDialogTitle" :visible.sync="coverDetailDialogVisible" width="800px">
      <el-form
        ref="coverDetailForm"
        :model="coverDetailForm"
        :rules="coverDetailFormRules"
        label-position="top"
        :disabled="isDisabled"
      >
        <el-form-item prop="title" label="标题">
          <el-input v-model="coverDetailForm.title" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item prop="content" label="内容">
          <editor v-model="coverDetailForm.content" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="fas fa-save" @click="handleSaveCoverDetail">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { getCompaniesDict } from '@/apis/company'
import intlRegions from '@/utils/regions'
import domesticAreadata from '@/utils/areadata.json'
import { Loading } from 'element-ui'
import * as coverApi from '@/apis/cover'

export default {
  name: 'CoverAndPolicy',
  data() {
    return {
      companies: [],
      domesticAreadata,
      intlRegions,
      searchForm: {
        company_id: '',
        company_branch_id: ''
      },
      coverForm: {
        domestic_disabled_regions: [],
        intl_disabled_regions: [],
        to_port_regions: [],
        to_port_clause: '',
        war_and_strike_regions: [],
        sanctioned_regions: [],
        sanctioned_clause: ''
      },
      coverDetails: [],
      coverDetailsTableCols: [
        {
          label: '标题',
          prop: 'title'
        },
        {
          label: '发布日期',
          prop: 'created_at',
          width: 150
        },
        {
          label: '操作',
          width: 60,
          scopedSlots: {
            default: (scope) => {
              return (
                <div class="d-flex justify-content-between">
                  <el-button
                    type="text"
                    size="mini"
                    icon="fas fa-edit"
                    onClick={() => this.handleUpdateCoverDetail(scope.row)}
                  />
                  <el-button
                    type="text"
                    size="mini"
                    icon="fas fa-trash"
                    onClick={() => this.handleDeleteCoverDetail(scope.row)}
                  />
                </div>
              )
            }
          }
        }
      ],
      coverDetailDialogVisible: false,
      coverDetailForm: {
        title: '',
        content: ''
      },
      coverDetailFormRules: {
        title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
        content: [{ required: true, message: '请输入内容', trigger: 'blur' }]
      }
    }
  },
  computed: {
    isDisabled() {
      return !this.searchForm.company_branch_id
    },
    companyBranches() {
      const com = this.companies.find((e) => e.id === this.searchForm.company_id)

      return com?.branches || []
    },
    policyDialogTitle() {
      return this.coverDetailForm?.id ? '编辑政策' : '新增政策'
    }
  },
  created() {
    this.getCompanies()
  },
  methods: {
    async getCompanies() {
      const res = await getCompaniesDict()
      this.companies = res.data
    },
    async fetchCoverDetails() {
      const loading = Loading.service()
      try {
        const res = await coverApi.getCoverDetails(this.searchForm.company_id, this.searchForm.company_branch_id)
        this.coverDetails = res.data
      } catch (e) {
        //
      } finally {
        loading.close()
      }
    },
    async fetchCover() {
      const loading = Loading.service()
      try {
        const res = await coverApi.getCover(this.searchForm.company_id, this.searchForm.company_branch_id)
        this.coverForm = res.data
      } catch (e) {
        //
      } finally {
        loading.close()
      }
    },
    handleSearch() {
      this.fetchCover()
      this.fetchCoverDetails()
    },
    async handleSaveCover() {
      const loading = Loading.service()

      try {
        await coverApi.saveCover(this.searchForm.company_id, this.searchForm.company_branch_id, this.coverForm)
        this.$message.success('保存成功')
        await this.fetchCover()
      } catch (e) {
        //
      } finally {
        loading.close()
      }
    },
    async handleSync() {
      const loading = Loading.service()
      try {
        await this.$confirm('确认同步保障范围到产品吗？同步到产品前请确认是否已经保存，一旦同步将无法撤回。', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await coverApi.syncCover(this.searchForm.company_id, this.searchForm.company_branch_id)
        this.$message.success('同步成功')
      } catch (e) {
        return
      } finally {
        loading.close()
      }
    },
    handleSaveCoverDetail() {
      this.$refs.coverDetailForm.validate(async (valid) => {
        if (valid) {
          if (this.coverDetailForm?.id) {
            await coverApi.updateCoverDetail(
              this.searchForm.company_id,
              this.searchForm.company_branch_id,
              this.coverDetailForm.id,
              this.coverDetailForm
            )
          } else {
            await coverApi.createCoverDetail(
              this.searchForm.company_id,
              this.searchForm.company_branch_id,
              this.coverDetailForm
            )
          }

          this.$message({
            type: 'success',
            message: '保存成功!'
          })

          await this.fetchCoverDetails()
        }
      })

      this.coverDetailDialogVisible = false
    },
    async handleUpdateCoverDetail(data) {
      this.coverDetailForm = Object.assign({}, data)
      this.coverDetailDialogVisible = true
    },
    handleDeleteCoverDetail(data) {
      this.$confirm(`确认删除${data.title}吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        await coverApi.deleteCoverDetail(this.searchForm.company_id, this.searchForm.company_branch_id, data.id)
        this.$message({
          type: 'success',
          message: '删除成功!'
        })

        await this.fetchCoverDetails()
      })
    }
  }
}
</script>
