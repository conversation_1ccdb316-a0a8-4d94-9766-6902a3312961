<template>
  <div class="product-intl p-extra-large-x p-extra-large-b w-100">
    <cbec-form :type="type" :model.sync="data" />
  </div>
</template>
<script>
import { getProduct } from '@/apis/product'
import CbecForm from '@/components/product/CbecForm'

const PRODUCT_TYPE = 7

export default {
  name: 'CbecProductForm',
  components: {
    CbecForm
  },
  data() {
    return {
      type: PRODUCT_TYPE,
      data: {}
    }
  },
  created() {
    if (this.$route.params.id !== undefined) {
      getProduct(this.$route.params.id).then((r) => (this.data = r.data))
    }
  }
}
</script>
