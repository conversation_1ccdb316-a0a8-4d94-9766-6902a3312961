<template>
  <div class="p-extra-large-x p-extra-large-b w-100">
    <group-product-form :model.sync="data" @updated="handleUpdated" />
  </div>
</template>

<script>
import { getGroupProductDetails } from '../../apis/groups'
import GroupProductForm from '../../components/product/GroupProductForm'

export default {
  name: 'GroupProduct',
  components: {
    GroupProductForm
  },
  data() {
    return {
      data: {}
    }
  },
  created() {
    this.fetchProductDetail()
  },
  methods: {
    fetchProductDetail() {
      if (this.$route.params.id !== undefined) {
        getGroupProductDetails(this.$route.params.id).then((r) => {
          this.data = r.data
          if (typeof this.data.mail_attachments == 'string') {
            this.data.mail_attachments = JSON.parse(this.data.mail_attachments)
          }
        })
      }
    },
    handleUpdated() {
      this.fetchProductDetail()
    }
  }
}
</script>
