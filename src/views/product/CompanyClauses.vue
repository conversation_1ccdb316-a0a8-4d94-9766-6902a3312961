<template>
  <div class="company-clauses p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <el-header class="d-flex align-items-center p-none">
      <el-button
        v-can="{ name: 'companies.clauses.create' }"
        icon="el-icon-circle-plus"
        type="primary"
        @click="showEditor({})"
      >
        添加条款
      </el-button>
    </el-header>

    <el-card shadow="never">
      <el-tabs ref="tabs" v-model="active" @tab-click="fetchClauses">
        <el-tab-pane label="国内货运险" name="1">
          <define-table :ref="`refTable-${active}`" :data="currentData" :attrs="attributes" :cols="cols" />
        </el-tab-pane>
        <el-tab-pane label="国际货运险" name="2">
          <define-table :ref="`refTable-${active}`" :data="currentData" :attrs="attributes" :cols="cols" />
        </el-tab-pane>
        <el-tab-pane label="单车货运险" name="3">
          <define-table :ref="`refTable-${active}`" :data="currentData" :attrs="attributes" :cols="cols" />
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <domestic-clause-editor
      :visible.sync="editor.showDomestic"
      :clauses="currentData"
      :model.sync="editor.data"
      @submit="handleSubmit"
    />

    <intl-clause-editor
      :visible.sync="editor.showIntl"
      :clauses="currentData"
      :model.sync="editor.data"
      @submit="handleSubmit"
    />

    <lbt-clause-editor
      :visible.sync="editor.showLbt"
      :clauses="currentData"
      :model.sync="editor.data"
      @submit="handleSubmit"
    />
  </div>
</template>

<script>
import { getClauses, createClause, updateClause, deleteClause } from '@/apis/company_clause'
import DomesticClauseEditor from '@/components/product/CompanyClauseEditor/Domestic'
import IntlClauseEditor from '@/components/product/CompanyClauseEditor/Intl'
import LbtClauseEditor from '@/components/product/CompanyClauseEditor/Lbt'
import { Loading } from 'element-ui'

export default {
  components: {
    DomesticClauseEditor,
    IntlClauseEditor,
    LbtClauseEditor
  },
  data() {
    return {
      active: '1',
      clauses: [],
      maps: new Map(),
      editor: {
        showDomestic: false,
        showIntl: false,
        showLbt: false,
        data: {}
      },
      attributes: {
        rowKey: 'id',
        border: false,
        defaultExpandAll: false,
        lazy: true,
        load: (evt, treeNode, resolve) => {
          this.maps.set(evt.id, { evt, treeNode, resolve })

          resolve(this.clauses.filter((e) => e.parent_id === evt.id))
        },
        treeProps: { children: 'children', hasChildren: 'hasChildren' }
      },
      cols: [
        {
          label: '险种',
          prop: 'type',
          scopedSlots: {
            default: (scoped) => {
              switch (scoped.row.type) {
                case 1:
                  return '国内'
                case 2:
                  return '国际'
                case 3:
                  return '单车'
                default:
                  break
              }
            }
          }
        },
        {
          label: '条款名称',
          prop: 'name'
        },
        {
          label: '标识',
          prop: 'identifier'
        },
        {
          label: '是否启用',
          width: 90,
          prop: 'is_enabled',
          scopedSlots: {
            default: (scoped) => {
              return parseInt(scoped.row.is_enabled, 10) ? (
                <span class="text-primary">是</span>
              ) : (
                <span class="text-info">否</span>
              )
            }
          }
        },
        {
          label: '操作',
          width: 85,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    v-can={{ name: 'companies.clauses.update' }}
                    size="small"
                    type="text"
                    onClick={() => this.handleEdit(scoped.row)}
                  >
                    编辑
                  </el-button>
                  <el-popconfirm
                    v-can={{ name: 'companies.clauses.delete' }}
                    style="margin-left: 10px"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    icon="el-icon-info"
                    icon-color="red"
                    title="确定删除吗？"
                    onConfirm={() => this.handleDelete(scoped.row)}
                  >
                    <el-button size="small" type="text" slot="reference">
                      删除
                    </el-button>
                  </el-popconfirm>
                </div>
              )
            }
          }
        }
      ]
    }
  },
  computed: {
    currentData() {
      const data = this.clauses
        .filter((e) => e.parent_id === -1 && parseInt(this.active, 10) === e.type)
        .map((e) => {
          e.is_enabled = e?.is_enabled?.toString()

          e.hasChildren = this.clauses.find((f) => f.parent_id === e.id) ? true : false

          return e
        })

      return data
    }
  },
  created() {
    this.fetchClauses()
  },
  methods: {
    refresh(row) {
      this.fetchClauses().then(() => {
        const dstId = row.parent_id === undefined || row.parent_id === -1 ? row.id : row.parent_id
        if (this.maps.has(dstId)) {
          const { resolve } = this.maps.get(dstId)
          if (resolve !== undefined) {
            this.$set(this.$refs[`refTable-${this.active}`].$refs.dataTable.store.states.lazyTreeNodeMap, dstId, [])

            resolve(this.clauses.filter((e) => e.parent_id === dstId) ?? [])
          }
        }
      })
    },
    fetchClauses() {
      return new Promise((resolve) => {
        getClauses(this.$route.params.id, { type: this.active }).then((r) => {
          this.clauses = r.data

          resolve(r.data)
        })
      })
    },
    handleDelete(data) {
      deleteClause(this.$route.params.id, data.id).then(() => {
        this.$message.success('删除成功')

        this.refresh(data)
      })
    },
    handleEdit(data) {
      this.showEditor(data)
    },
    handleSubmit(data) {
      const action =
        data.id === undefined
          ? createClause(this.$route.params.id, data)
          : updateClause(this.$route.params.id, data.id, data)

      const loading = Loading.service()
      action
        .then(() => {
          this.$message.success('操作成功')

          this.refresh(data)
        })
        .finally(() => loading.close())
    },
    showEditor(data) {
      this.$nextTick(() => {
        switch (this.active) {
          case '1':
            this.editor.showDomestic = true
            break
          case '2':
            this.editor.showIntl = true
            break
          case '3':
            this.editor.showLbt = true
            break
          default:
            break
        }

        this.editor.data = data
      })
    }
  }
}
</script>
