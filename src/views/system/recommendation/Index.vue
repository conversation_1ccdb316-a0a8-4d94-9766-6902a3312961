<template>
  <div class="recommendation p-extra-large-x p-extra-large-b w-100 d-flex flex-column o-hidden">
    <el-header class="d-flex align-items-center p-none">
      <el-button
        v-can="{ name: 'recommendations.create' }"
        icon="el-icon-circle-plus"
        type="primary"
        @click="showEditor"
      >
        新增推荐
      </el-button>
    </el-header>

    <el-card shadow="never">
      <define-table :cols="cols" :data="data" />
    </el-card>

    <recommendation-editor :visible.sync="dialog.visible" :data="dialog.data" @submit="handleSubmit" />
  </div>
</template>

<script>
import RecommendationEditor from '@/components/system/RecommendationEditor'
import {
  getRecommendations,
  createRecommendation,
  updateRecommendation,
  deleteRecommendation
} from '@/apis/recommendation'

export default {
  name: 'Recommendation',
  components: { RecommendationEditor },
  data() {
    return {
      dialog: {
        visible: false,
        data: {}
      },
      cols: [
        {
          label: '产品类型',
          width: 110,
          scopedSlots: {
            default: (scoped) => {
              const types = {
                1: '国内货运险',
                2: '国际货运险',
                3: '单车责任险',
                4: '其他险种',
                5: '雇主责任险',
                6: '线下录入保单',
                7: '跨境电商险'
              }

              return types[scoped.row.product_type]
            }
          }
        },
        { label: '标题', prop: 'title' },
        { label: '推荐价格', prop: 'starting_price' },
        { label: '标签', prop: 'tags' },
        { label: '创建时间', prop: 'created_at' },
        {
          label: '操作',
          width: 110,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    type="text"
                    size="small"
                    v-can={{ name: 'recommendations.update' }}
                    onClick={() => {
                      this.dialog.visible = true
                      this.dialog.data = {
                        id: scoped.row.id,
                        starting_price: scoped.row.starting_price,
                        tags: scoped.row.tags,
                        product_type: scoped.row.product_type.toString(),
                        title: scoped.row.title,
                        image: scoped.row.image,
                        content: scoped.row.content
                      }
                    }}
                  >
                    详情/编辑
                  </el-button>
                  <el-button
                    type="text"
                    size="small"
                    v-can={{ name: 'recommendations.delete' }}
                    onClick={() => this.handleDelete(scoped.row)}
                  >
                    删除
                  </el-button>
                </div>
              )
            }
          }
        }
      ],
      data: []
    }
  },
  created() {
    this.fetchRecommendations()
  },
  methods: {
    showEditor() {
      this.dialog.visible = true
      this.dialog.data = {}
    },
    fetchRecommendations() {
      getRecommendations().then((r) => {
        this.data = r.data
      })
    },
    handleSubmit(data) {
      const action = data.id === undefined ? createRecommendation(data) : updateRecommendation(data.id, data)

      action.then(() => {
        this.$message.success('保存成功')

        this.fetchRecommendations()
      })
    },
    handleDelete(data) {
      deleteRecommendation(data.id).then(() => {
        this.$message.success('删除成功')

        this.fetchRecommendations()
      })
    }
  }
}
</script>
