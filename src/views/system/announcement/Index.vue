<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <el-header class="d-flex align-items-center p-none">
      <el-button
        v-can="{ name: 'recommendations.create' }"
        icon="el-icon-circle-plus"
        type="primary"
        @click="showEditor"
      >
        新增通知
      </el-button>
    </el-header>

    <el-card shadow="never">
      <define-table :cols="cols" :data="data" />
    </el-card>

    <el-dialog :visible.sync="dialog.visible" :before-close="handleClose" destroy-on-close title="添加通知">
      <el-form ref="form" :model="dialog.form" :rules="dialog.rules" label-position="top">
        <el-form-item prop="display_area" label="展示区域">
          <el-select v-model="dialog.form.display_area" placeholder="请选择">
            <el-option label="全站通知" :value="1" />
            <el-option label="保单打印" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item prop="content" label="通知内容">
          <el-input type="textarea" maxlength="100" minlength="1" :rows="3" v-model="dialog.form.content" />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getAnnouncements,
  createAnnouncement,
  pinAnnouncement,
  unpinAnnouncement,
  publishAnnouncement,
  unpublishAnnouncement,
  deleteAnnouncement
} from '@/apis/announcement'

export default {
  name: 'Announcements',
  data() {
    return {
      dialog: {
        visible: false,
        form: {
          display_area: 1,
          content: ''
        },
        rules: {
          display_area: [{ required: true, message: '请选择展示区域', trigger: 'change' }],
          content: [{ required: true, message: '请输入通知内容', trigger: 'blur' }]
        }
      },
      cols: [
        {
          label: '展示区域',
          width: 100,
          scopedSlots: {
            default: (scoped) => {
              const displayAreas = {
                1: '全站通知',
                2: '保单打印'
              }
              return <span>{displayAreas[scoped.row.display_area]}</span>
            }
          }
        },
        { label: '内容', prop: 'content' },
        { label: '创建时间', prop: 'created_at', width: 150 },
        {
          label: '置顶状态',
          width: 80,
          scopedSlots: {
            default: (scoped) => {
              return (
                <el-switch
                  value={scoped.row?.is_pinned}
                  active-value={1}
                  inactive-value={0}
                  onChange={() => {
                    if (scoped.row?.is_pinned === 1) {
                      this.handleUnpin(scoped.row)
                      scoped.row.is_pinned = 0
                    } else {
                      this.handlePin(scoped.row)
                      scoped.row.is_pinned = 1
                    }
                  }}
                />
              )
            }
          }
        },
        {
          label: '发布状态',
          width: 80,
          scopedSlots: {
            default: (scoped) => {
              return (
                <el-switch
                  value={scoped.row?.is_published}
                  active-value={1}
                  inactive-value={0}
                  onChange={() => {
                    if (scoped.row?.is_published === 1) {
                      this.handleUnpublish(scoped.row)
                      scoped.row.is_published = 0
                    } else {
                      this.handlePublish(scoped.row)
                      scoped.row.is_published = 1
                    }
                  }}
                />
              )
            }
          }
        },
        {
          label: '操作',
          width: 50,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    type="text"
                    size="small"
                    v-can={{ name: 'announcements.delete' }}
                    onClick={() => this.handleDelete(scoped.row)}
                  >
                    删除
                  </el-button>
                </div>
              )
            }
          }
        }
      ],
      data: []
    }
  },
  created() {
    this.fetchAnnouncements()
  },
  methods: {
    showEditor() {
      this.dialog.visible = true
    },
    handleClose() {
      this.dialog.visible = false
      this.dialog.form.content = ''
    },
    fetchAnnouncements() {
      getAnnouncements().then((r) => {
        this.data = r.data
      })
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          createAnnouncement(this.dialog.form).then(() => {
            this.$message.success('保存成功')

            this.fetchAnnouncements()

            this.handleClose()
          })
        }
      })
    },
    handlePin(data) {
      pinAnnouncement(data.id).then(() => {
        this.$message.success('置顶成功')

        this.fetchAnnouncements()
      })
    },
    handleUnpin(data) {
      unpinAnnouncement(data.id).then(() => {
        this.$message.success('取消置顶成功')

        this.fetchAnnouncements()
      })
    },
    handlePublish(data) {
      publishAnnouncement(data.id).then(() => {
        this.$message.success('撤销成功')

        this.fetchAnnouncements()
      })
    },
    handleUnpublish(data) {
      unpublishAnnouncement(data.id).then(() => {
        this.$message.success('撤销成功')

        this.fetchAnnouncements()
      })
    },
    handleDelete(data) {
      deleteAnnouncement(data.id).then(() => {
        this.$message.success('删除成功')

        this.fetchAnnouncements()
      })
    }
  }
}
</script>
