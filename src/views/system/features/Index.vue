<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <el-header class="d-flex align-items-center p-none">
      <el-button
        icon="el-icon-circle-plus"
        type="primary"
        v-can="{ name: 'features.create' }"
        @click="formVisible = true"
      >
        添加功能
      </el-button>
    </el-header>

    <el-card shadow="never">
      <define-table
        :data="dataset"
        :attrs="{
          rowKey: 'id',
          border: false,
          defaultExpandAll: false,
          treeProps: { children: 'children', hasChildren: 'hasChildren' }
        }"
        :cols="cols"
      />
    </el-card>

    <el-dialog
      :visible="formVisible"
      title="添加功能"
      width="500px"
      :before-close="handleCloseFormDialog"
      destroy-on-close
    >
      <el-form ref="form" :model="form" :rules="formRules">
        <el-form-item label="功能名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入功能名称"></el-input>
        </el-form-item>
        <el-form-item label="功能标识" prop="slug">
          <el-input v-model="form.slug" placeholder="请输入功能标识"></el-input>
        </el-form-item>
        <el-form-item label="是否启用" prop="is_enabled">
          <el-checkbox v-model="form.is_enabled" :true-label="1" :false-label="0"></el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="fas fa-check"
            @click="handleFormSubmit"
            :loading="formBtnLoading"
            :disabled="formBtnLoading"
          >
            提交
          </el-button>
          <el-button icon="fas fa-times" @click="handleCloseFormDialog">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import * as featureApi from '@/apis/feature'
import { Loading } from 'element-ui'
import { array2Tree } from '@/utils'

export default {
  data() {
    return {
      features: [],
      cols: [
        {
          label: '功能标识',
          prop: 'slug',
          width: 200
        },
        {
          label: '功能名称',
          prop: 'name'
        },
        {
          label: '操作',
          fixed: 'right',
          width: 115,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    v-can={{ name: 'features.create' }}
                    type="text"
                    size="small"
                    onClick={() => {
                      this.featureId = -1
                      this.formVisible = true
                      this.form.parent_id = scoped.row.id
                    }}
                  >
                    添加
                  </el-button>
                  <el-button
                    v-can={{ name: 'features.update' }}
                    type="text"
                    size="small"
                    onClick={() => {
                      this.featureId = scoped.row.id
                      this.formVisible = true
                      this.form = { ...scoped.row }
                    }}
                  >
                    编辑
                  </el-button>
                  <el-popconfirm
                    v-can={{ name: 'features.delete' }}
                    style="margin-left: 7px"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    icon="el-icon-info"
                    icon-color="red"
                    title="确定删除吗？"
                    onConfirm={() => this.handleDeleteFeature(scoped.row.id)}
                  >
                    <el-button type="text" size="small" slot="reference">
                      删除
                    </el-button>
                  </el-popconfirm>
                </div>
              )
            }
          }
        }
      ],
      formVisible: false,
      featureId: -1,
      formBtnLoading: false,
      form: {
        parent_id: -1,
        name: '',
        slug: '',
        is_enabled: 1
      },
      formRules: {
        name: [{ required: true, message: '请输入功能名称', trigger: 'blur' }],
        slug: [{ required: true, message: '请输入功能标识', trigger: 'blur' }],
        is_enabled: [{ required: true, message: '请选择是否启用', trigger: 'change' }]
      }
    }
  },
  computed: {
    dataset() {
      return array2Tree(this.features || [], 'id', 'parent_id', -1, 'name')
    }
  },
  created() {
    this.fetchFeatures()
  },
  methods: {
    async fetchFeatures() {
      const loading = Loading.service()
      try {
        this.features = await featureApi.fetchFeatures()
      } finally {
        loading.close()
      }
    },
    async handleFormSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.formBtnLoading = true
          try {
            if (this.featureId === -1) {
              await featureApi.createFeature(this.form)
            } else {
              await featureApi.updateFeature(this.featureId, this.form)
            }
            this.$message.success('保存成功')
            this.handleCloseFormDialog()
            this.fetchFeatures()
          } finally {
            this.formBtnLoading = false
          }
        }
      })
    },
    handleDeleteFeature(featureId) {
      const loading = Loading.service()
      try {
        featureApi.deleteFeature(featureId)
        this.fetchFeatures()
        this.$message.success('删除成功')
      } finally {
        loading.close()
      }
    },
    handleCloseFormDialog() {
      this.formVisible = false
      this.$refs.form.resetFields()
      this.featureId = -1
      this.form = {
        parent_id: -1,
        name: '',
        slug: '',
        is_enabled: 1
      }
    }
  }
}
</script>
