<!--
 * @Descripttion:
 * @Author: Mr. z<PERSON>
 * @Date: 2021-01-21 15:02:37
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-21 15:50:55
-->
<template>
  <div
    class="login d-flex justify-content-center align-items-center"
    :style="{ 'background-image': `url(${loginBg})` }"
  >
    <el-card class="container" shadow="never">
      <template slot="header">
        <img :src="logo" height="60px" />
        <p class="sub-title text-info p-extra-t m-none-b">
          {{ slogan || '' }}
        </p>
      </template>
      <el-form label-width="0" ref="login" :model="form" :rules="rules">
        <el-form-item prop="username">
          <el-input prefix-icon="el-icon-user" placeholder="请输入用户名" v-model="form.username"></el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            prefix-icon="el-icon-lock"
            type="password"
            placeholder="请输入密码"
            v-model="form.password"
            show-password
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button block class="w-100" type="primary" :loading="loading" @click="handleSubmit">登录</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { login } from '@/apis/auth'
import loginBg from '@/assets/svg/login_bg.svg'
import { mapGetters } from 'vuex'

export default {
  name: 'Login',
  data() {
    return {
      loginBg,
      loading: false,
      form: {
        username: '',
        password: ''
      },
      rules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 5, max: 32, message: '用户名长度在 5 ～ 32 位之间', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 5, max: 32, message: '密码长度在 5 ～ 32 位之间', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapGetters('platform', ['logo', 'slogan', 'favicon', 'title'])
  },
  methods: {
    handleSubmit() {
      this.loading = true

      this.$refs.login.validate((valid) => {
        if (valid) {
          login(this.form)
            .then((r) => {
              this.$store.dispatch('auth/setToken', r).then(() => {
                this.$router.push({ name: 'Home' })
              })
            })
            .finally(() => {
              this.loading = false
            })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.login {
  width: 100vw;
  height: 100vh;
  background-repeat: no-repeat;
  background-position: 50%;
  background-size: 100%;
  /deep/ .el-card.container {
    border: none;
    background-color: transparent;
    width: 388px;
    .el-card__header {
      text-align: center;
      border: none;
      .sub-title {
        font-size: $app-size-base;
      }
    }
  }
}
</style>
