<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-22 09:25:38
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-22 10:13:07
-->
<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <el-header class="d-flex align-items-center p-none">
      <el-button v-can="{ name: 'roles.create' }" icon="el-icon-circle-plus" type="primary" @click="handleEditorRole">
        添加角色
      </el-button>
    </el-header>
    <el-card shadow="never">
      <define-table :data="roles" :attrs="tableAttrs" :cols="cols" />
    </el-card>

    <role-editor
      :visible.sync="roleEditor.visible"
      v-if="roleEditor.visible"
      :model="roleEditor.model"
      @submit="handleRoleSubmit"
    />

    <role-security
      :visible.sync="security.visible"
      v-if="security.visible"
      :roleId="security.roleId"
      :roleName="security.roleName"
      :checked="security.checked"
      @submit="handleRoleSecurirySubmit"
    />
  </div>
</template>

<script>
import RoleEditor from '@/components/system/RoleEditor'
import RoleSecurity from '@/components/system/RoleSecurity'
import { Loading } from 'element-ui'
import { getRole, createRole, updateRole, syncRolePermissions, deleteRole } from '@/apis/role'
import { mapGetters } from 'vuex'

export default {
  name: 'Role',
  components: { RoleEditor, RoleSecurity },
  data() {
    return {
      roleEditor: {
        visible: false,
        model: null
      },
      security: {
        visible: false,
        roleId: null,
        roleName: null
      },
      tableAttrs: {
        border: false
      },
      cols: [
        {
          label: '角色标识',
          prop: 'name'
        },
        {
          label: '角色名称',
          prop: 'display_name'
        },
        {
          label: '平台角色',
          width: 100,
          prop: 'is_platform',
          scopedSlots: {
            default: (scoped) => {
              return scoped.row.is_platform ? <span class="text-primary">是</span> : <span class="text-info">否</span>
            }
          }
        },
        {
          label: '平台默认角色',
          width: 100,
          prop: 'is_platform_default',
          scopedSlots: {
            default: (scoped) => {
              return scoped.row.is_platform_default ? (
                <span class="text-primary">是</span>
              ) : (
                <span class="text-info">否</span>
              )
            }
          }
        },
        {
          label: '操作',
          fixed: 'right',
          width: 120,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    v-can={{ name: 'roles.permissions.sync' }}
                    type="text"
                    size="small"
                    onClick={() => this.handleRoleSecurity(scoped.row)}
                  >
                    权限
                  </el-button>
                  <el-button
                    v-can={{ name: 'roles.update' }}
                    type="text"
                    size="small"
                    onClick={() => this.handleEditorRole(scoped.row)}
                  >
                    编辑
                  </el-button>
                  <el-popconfirm
                    v-can={{ name: 'roles.delete' }}
                    style="margin-left: 10px"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    icon="el-icon-info"
                    icon-color="red"
                    title="确定删除吗？"
                    onConfirm={() => this.handleRemoveRole(scoped.row)}
                  >
                    <el-button type="text" size="small" slot="reference">
                      删除
                    </el-button>
                  </el-popconfirm>
                </div>
              )
            }
          }
        }
      ]
    }
  },
  computed: {
    ...mapGetters('app', ['roles'])
  },
  methods: {
    handleRoleSubmit(data) {
      const loading = Loading.service({ lock: true })
      const action = data.id === undefined ? createRole(data) : updateRole(data.id, data)
      action
        .then(() => {
          this.$message.success('操作完成')

          this.$store.dispatch('app/fetchRoles')
        })
        .finally(() => loading.close())
    },
    /**
     * @name: 编辑角色信息
     * @author: Mr. zhu
     */
    handleEditorRole(model) {
      this.roleEditor.visible = true
      this.roleEditor.model = model
    },
    /**
     * @name: 菜单权限
     * @author: Mr. zhu
     */
    handleRoleSecurity({ id, display_name }) {
      getRole(id).then((r) => {
        this.security = {
          visible: true,
          roleId: id,
          checked: r.data.permissions,
          roleName: display_name
        }
      })
    },
    handleRoleSecurirySubmit(id, data) {
      const loading = Loading.service({ lock: true })

      syncRolePermissions(id, {
        permissions: data
      })
        .then(() => {
          this.$message.success('更新成功')

          this.$store.dispatch('app/fetchRolePermissions')
        })
        .finally(() => loading.close())
    },
    /**
     * @name: 删除权限
     * @author: Mr. zhu
     */
    handleRemoveRole(rowData) {
      const loading = Loading.service({ lock: true })

      deleteRole(rowData.id)
        .then(() => {
          this.$message.success('删除角色成功')

          this.$store.dispatch('app/fetchRoles')
        })
        .finally(() => loading.close())
    }
  }
}
</script>

<style lang="scss" scoped></style>
