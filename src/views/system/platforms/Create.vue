<template>
  <platform-form @submit="handleSubmit" />
</template>

<script>
import { createPlatform } from '@/apis/platform'
import PlatformForm from '@/components/system/PlatformForm'

export default {
  name: 'PlatformsCreate',
  components: { PlatformForm },
  methods: {
    handleSubmit(data) {
      createPlatform(data).then(() => {
        this.$message.success('添加平台成功')

        this.$router.push({ name: 'Platforms' })
      })
    }
  }
}
</script>
