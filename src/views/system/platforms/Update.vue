<template>
  <platform-form :platform.sync="platform" @submit="handleSubmit" />
</template>

<script>
import { getPlatform, updatePlatform } from '@/apis/platform'
import PlatformForm from '@/components/system/PlatformForm'

export default {
  name: 'PlatformsUpdate',
  components: { PlatformForm },
  data() {
    return {
      platform: {}
    }
  },
  created() {
    getPlatform(this.$route.params.id).then((r) => {
      this.platform = r.data
    })
  },
  methods: {
    handleSubmit(data) {
      updatePlatform(data.id, data).then(() => {
        this.$message.success('更新成功')

        this.$router.push({ name: 'Platforms' })
      })
    }
  }
}
</script>
