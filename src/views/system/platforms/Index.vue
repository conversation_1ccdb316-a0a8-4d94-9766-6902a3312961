<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <el-header class="d-flex align-items-center p-none">
      <router-link :to="{ name: 'PlatformsCreate' }">
        <el-button v-can="{ name: 'platforms.create' }" icon="el-icon-circle-plus" type="primary"> 添加平台 </el-button>
      </router-link>
    </el-header>

    <el-card shadow="never">
      <define-table :data="platforms" :attrs="tableAttrs" :cols="tableCols" />
    </el-card>

    <platform-transaction
      :visible.sync="transactionDialog.visible"
      :data="transactionDialog.data"
      :paging="transactionDialog.paging"
      @fetch-data="fetchPlatformTransactions"
      @submit="handleChargeSubmit"
    />
  </div>
</template>
1

<script>
import PlatformTransaction from '@/components/system/PlatformTransaction'
import { getPlatforms, getPlatformTransaction, deletePlatform, platformCharge } from '@/apis/platform'
import { Loading } from 'element-ui'

export default {
  name: 'Platforms',
  components: { PlatformTransaction },
  data() {
    return {
      transactionDialog: {
        model: {},
        data: [],
        paging: {
          currentPage: 1,
          pageSize: 15,
          layout: ' prev, pager, next, jumper, total',
          total: 0
        },
        visible: false
      },
      tableAttrs: {
        border: false
      },
      platforms: [],
      tableCols: [
        {
          label: 'LOGO',
          prop: 'logo',
          scopedSlots: {
            default: (scoped) => {
              return <img src={scoped.row.logo} height="25" />
            }
          }
        },
        {
          label: '名称',
          prop: 'name'
        },
        {
          label: '绑定域名',
          prop: 'domain'
        },
        {
          label: '默认管理员',
          prop: 'admin.name'
        },
        {
          label: '余额',
          width: 80,
          prop: 'balance'
        },
        {
          label: '创建时间',
          prop: 'created_at'
        },
        {
          label: '操作',
          width: 155,
          fixed: 'right',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    v-can={{ name: 'platforms.update' }}
                    size="small"
                    type="text"
                    onClick={() => this.$router.push({ name: 'PlatformsUpdate', params: { id: scoped.row.id } })}
                  >
                    编辑
                  </el-button>
                  <el-button
                    v-can={{ name: 'platforms.transactions' }}
                    size="small"
                    type="text"
                    onClick={() => this.handleTransactions({ rowData: scoped.row })}
                  >
                    余额
                  </el-button>
                  <el-button
                    v-can={{ name: 'platforms.products.index' }}
                    size="small"
                    type="text"
                    onClick={() => this.$router.push({ name: 'PlatformsProducts', params: { id: scoped.row.id } })}
                  >
                    产品
                  </el-button>
                  <el-popconfirm
                    v-can={{ name: 'platforms.delete' }}
                    style="margin-left: 10px"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    icon="el-icon-info"
                    icon-color="red"
                    title="确定删除吗？"
                    onConfirm={() => this.handleRemovePlatform(scoped.row)}
                  >
                    <el-button size="small" type="text" slot="reference">
                      删除
                    </el-button>
                  </el-popconfirm>
                </div>
              )
            }
          }
        }
      ]
    }
  },
  created() {
    this.fetchPlatforms()
  },
  methods: {
    fetchPlatformTransactions(searchData, page = 1) {
      getPlatformTransaction(this.transactionDialog.model.id, {
        filter: searchData,
        page: page
      }).then((r) => {
        this.transactionDialog.data = r.data

        this.transactionDialog.paging.currentPage = r.meta.current_page
        this.transactionDialog.paging.pageSize = r.meta.per_page
        this.transactionDialog.paging.total = r.meta.total
      })
    },
    handleTransactions({ rowData }) {
      this.transactionDialog.visible = true
      this.transactionDialog.model = rowData

      this.fetchPlatformTransactions()
    },
    handleChargeSubmit(data) {
      const loading = Loading.service()

      platformCharge(this.transactionDialog.model.id, data)
        .then(() => {
          this.$message.success('充值成功')

          this.fetchPlatformTransactions()
        })
        .finally(() => loading.close())
    },
    handleRemovePlatform(rowData) {
      deletePlatform(rowData.id).then(() => {
        this.$message.success('删除成功')

        this.fetchPlatforms()
      })
    },
    fetchPlatforms(params) {
      getPlatforms(params).then((r) => {
        this.platforms = r.data
      })
    }
  }
}
</script>
