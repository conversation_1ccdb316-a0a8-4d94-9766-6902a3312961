<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <el-card class="m-extra-large-t" shadow="never">
      <el-tabs v-model="activeName">
        <el-tab-pane v-for="(name, key) in productTypes" :key="key" :name="key" :label="name" lazy>
          <platform-products
            :name="name"
            :data.sync="data"
            :paging.sync="paging"
            :products.sync="products"
            :is-cargo-product="cargoProducts.includes(parseInt(key))"
            @submit="handleSubmit"
            @multiple-submit="handleMultipleSubmit"
            @query-submit="handleQuerySubmit"
            @update-status="handleUpdateStatus"
          />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script>
import PlatformProducts from '@/components/system/PlatformProducts'
import {
  getConfigurableProducts,
  getPlatformProducts,
  assignPlatformProduct,
  updateStatus,
  assignPlatformProductMultiple
} from '@/apis/platform_product'
import { Loading } from 'element-ui'

export default {
  name: 'PlatformsProducts',
  components: { PlatformProducts },
  data() {
    return {
      activeName: '1',
      cargoProducts: [1, 2, 3, 7],
      paging: {
        currentPage: 1,
        pageSize: 15,
        layout: 'prev, pager, next, jumper, total',
        total: 0
      },
      productTypes: {
        1: '国内货运险',
        2: '国际货运险',
        3: '单车责任运险',
        4: '其他险种',
        5: '雇主责任险',
        7: '跨境电商货运险'
      },
      data: [],
      products: [],
      page: 1,
      queries: {}
    }
  },
  watch: {
    activeName() {
      this.page = 1
      this.queries = {}

      this.fetchProducts()
      this.fetchPlatformProducts()
    }
  },
  created() {
    this.fetchProducts()
    this.fetchPlatformProducts()
  },
  methods: {
    handleQuerySubmit(page, queries) {
      this.page = page
      this.queries = queries

      this.fetchProducts()
    },
    handleUpdateStatus(productId, status) {
      updateStatus(this.$route.params.id, productId, status)
        .then(() => {
          this.$message.success('更新状态成功')

          this.fetchProducts()
          this.fetchPlatformProducts()
        })
        .catch(() => {
          this.fetchProducts()
          this.fetchPlatformProducts()
        })
    },
    handleSubmit(form) {
      const loading = Loading.service()
      assignPlatformProduct(this.$route.params.id, form)
        .then(() => {
          this.fetchProducts()
          this.fetchPlatformProducts()

          this.$message.success('更新成功')
        })
        .finally(() => loading.close())
    },
    handleMultipleSubmit(form) {
      const loading = Loading.service()
      form.filter.type = this.activeName
      form.filter.is_enabled = 1
      assignPlatformProductMultiple(this.$route.params.id, form)
        .then(() => {
          this.fetchProducts()
          this.fetchPlatformProducts()

          this.$message.success('配置成功')
        })
        .finally(() => loading.close())
    },
    fetchProducts() {
      getConfigurableProducts(this.$route.params.id, {
        page: this.page,
        filter: Object.assign(
          {
            type: this.activeName,
            is_enabled: 1
          },
          this.queries
        )
      }).then((r) => {
        this.products = r.data

        this.paging.currentPage = r.meta.current_page
        this.paging.page = r.meta.currentPage
        this.paging.pageSize = r.meta.per_page
        this.paging.total = r.meta.total
      })
    },
    fetchPlatformProducts() {
      getPlatformProducts(this.$route.params.id, {
        filter: {
          type: this.activeName
        }
      }).then((r) => {
        this.data = r.data
      })
    }
  }
}
</script>
