<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <el-header class="d-flex align-items-center p-none">
      <el-button
        v-can="{ name: 'blacklists.create' }"
        icon="el-icon-circle-plus"
        type="primary"
        @click="blacklistEditor.visible = true"
      >
        新增黑名单
      </el-button>
    </el-header>

    <el-card shadow="never">
      <define-table :data="blacklists" :cols="blacklistCols" />
    </el-card>

    <el-dialog :visible.sync="blacklistEditor.visible" title="黑名单" width="500px" :before-close="handleCloseEditor">
      <el-form :model="blacklistEditor.form" ref="form" label-position="top" label-suffix=":" label-width="100px">
        <el-form-item>
          <template #label>
            <span>投/被保险人</span>
            <small class="text-danger">&nbsp;投/被保险人和投/被保险人证件号必填一个</small>
          </template>
          <el-input v-model="blacklistEditor.form.name" placeholder="请输入投/被保险人" />
        </el-form-item>
        <el-form-item>
          <template #label>
            <span>投/被保险人证件号</span>
            <small class="text-danger">&nbsp;投/被保险人和投/被保险人证件号必填一个</small>
          </template>
          <el-input v-model="blacklistEditor.form.idcard_no" placeholder="请输入投/被保险人证件号" />
        </el-form-item>
        <el-form-item label="禁投险种" :rules="[{ required: true, type: 'array', message: '请选择禁用险种' }]">
          <el-select
            v-model="blacklistEditor.form.disabled_products"
            placeholder="请选择禁用险种"
            class="w-100"
            multiple
          >
            <el-option v-for="product in products" :value="product.value" :label="product.label" :key="product.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="生效平台" :rules="[{ required: true, type: 'array', message: '请选择生效平台' }]">
          <el-select
            v-model="blacklistEditor.form.disabled_platforms"
            placeholder="请选择生效平台"
            class="w-100"
            multiple
          >
            <el-option
              v-for="platform in platforms"
              :value="platform.value"
              :label="platform.label"
              :key="platform.value"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <div slot="footer">
        <el-button icon="fas fa-times" @click="handleCloseEditor">取消</el-button>
        <el-button icon="fas fa-check" type="primary" @click="handleSave">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getBlacklists,
  getBlacklistPlatforms,
  createBlacklist,
  updateBlacklist,
  deleteBlacklist
} from '@/apis/blacklist'
import { Loading } from 'element-ui'

export default {
  name: 'Blacklists',
  data() {
    return {
      blacklists: [],
      platforms: [],
      blacklistEditor: {
        visible: false,
        form: {
          name: '',
          idcard_no: '',
          disabled_products: [],
          disabled_platforms: []
        }
      },
      products: [
        { value: 1, label: '国内货运险' },
        { value: 2, label: '国际货运险' },
        { value: 3, label: '单车货运险' },
        { value: 4, label: '其他险种' },
        { value: 5, label: '雇主责任险' }
      ],
      blacklistCols: [
        {
          prop: 'name',
          label: '投/被保险人',
          width: 200
        },
        {
          prop: 'idcard_no',
          label: '投/被保险人证件号',
          width: 150
        },
        {
          label: '禁投险种',
          scopedSlots: {
            default: (scoped) => {
              return scoped.row.disabled_products
                .map((product) => {
                  return this.products.find((p) => p.value === product).label
                })
                .join(',')
            }
          }
        },
        {
          label: '禁投平台',
          scopedSlots: {
            default: (scoped) => {
              return scoped.row.disabled_platforms
                .map((product) => {
                  return this.platforms.find((p) => p.value === product).label
                })
                .join(',')
            }
          }
        },
        {
          label: '操作',
          width: 100,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    v-can={{ name: 'blacklists.update' }}
                    size="mini"
                    type="text"
                    onClick={() => {
                      this.blacklistEditor.visible = true
                      this.blacklistEditor.form = scoped.row
                    }}
                  >
                    编辑
                  </el-button>
                  <el-popconfirm
                    v-can={{ name: 'blacklists.delete' }}
                    style="margin-left: 10px"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    icon="el-icon-info"
                    icon-color="red"
                    title="确定删除吗？"
                    onConfirm={() => this.handleRemove(scoped.row)}
                  >
                    <el-button size="small" type="text" slot="reference">
                      删除
                    </el-button>
                  </el-popconfirm>
                </div>
              )
            }
          }
        }
      ]
    }
  },
  created() {
    getBlacklistPlatforms().then(
      (r) =>
        (this.platforms = r.data.map((item) => ({
          value: item.id,
          label: item.name
        })))
    )

    this.fetchBlacklists()
  },
  methods: {
    fetchBlacklists() {
      getBlacklists().then((r) => (this.blacklists = r.data))
    },
    handleSave() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const loading = Loading.service()
          const method =
            this.blacklistEditor.form?.id === undefined
              ? createBlacklist(this.blacklistEditor.form)
              : updateBlacklist(this.blacklistEditor.form.id, this.blacklistEditor.form)

          method
            .then(() => {
              this.$message.success('保存成功')

              this.fetchBlacklists()
              this.handleCloseEditor()
            })
            .finally(() => loading.close())
        }
      })
    },
    handleRemove(row) {
      deleteBlacklist(row.id).then(() => {
        this.fetchBlacklists()

        this.$message.success('删除成功')
      })
    },
    handleCloseEditor() {
      this.$refs.form.resetFields()
      this.blacklistEditor.visible = false
      this.blacklistEditor.form = {
        name: '',
        idcard_no: '',
        disabled_products: [],
        disabled_platforms: []
      }
    }
  }
}
</script>
