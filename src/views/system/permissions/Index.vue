<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-21 15:53:17
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-21 18:32:13
-->
<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <el-header class="d-flex align-items-center p-none">
      <el-button
        v-can="{ name: 'permissions.create' }"
        icon="el-icon-circle-plus"
        type="primary"
        @click="handleEditorMenu({ isTop: true })"
      >
        添加顶级菜单
      </el-button>
    </el-header>
    <el-card shadow="never">
      <define-table :data="permissions" :attrs="tableAttrs" :cols="cols" />
    </el-card>

    <permissions-editor
      :visible.sync="permissionsEditor.visible"
      v-if="permissionsEditor.visible"
      :model="permissionsEditor.model"
      @submit="handleSubmitPermission"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import PermissionsEditor from '@/components/system/PermissionsEditor'
import { isObj } from '@/utils'
import { createPermission, updatePermission, deletePermission } from '@/apis/permission'
import { Loading } from 'element-ui'

export default {
  name: 'Permissions',
  components: { PermissionsEditor },
  data() {
    return {
      tableData: [],
      permissionsEditor: {
        visible: false,
        isTop: true,
        model: null
      },
      tableAttrs: {
        rowKey: 'id',
        border: false,
        defaultExpandAll: false,
        treeProps: { children: 'children', hasChildren: 'hasChildren' }
      },
      cols: [
        {
          label: '权限标识',
          prop: 'name',
          width: 200
        },
        {
          label: '显示名称',
          prop: 'display_name'
        },
        {
          label: '菜单名称',
          prop: 'menu_name'
        },
        {
          label: '路由名称',
          prop: 'route_name'
        },
        {
          label: '排序',
          prop: 'index'
        },
        {
          label: '全平台通用',
          width: 90,
          prop: 'is_universal',
          scopedSlots: {
            default: (scoped) => {
              return scoped.row.is_universal ? <span class="text-primary">是</span> : <span class="text-info">否</span>
            }
          }
        },
        {
          label: '菜单',
          width: 90,
          prop: 'is_menu',
          scopedSlots: {
            default: (scoped) => {
              return scoped.row.is_menu ? <span class="text-primary">是</span> : <span class="text-info">否</span>
            }
          }
        },
        {
          label: '操作',
          fixed: 'right',
          width: 115,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    v-can={{ name: 'permissions.create' }}
                    type="text"
                    size="small"
                    onClick={() => this.handleEditorMenu({ rowData: scoped.row })}
                  >
                    添加
                  </el-button>
                  <el-button
                    v-can={{ name: 'permissions.update' }}
                    type="text"
                    size="small"
                    onClick={() =>
                      this.handleEditorMenu({ rowData: scoped.row, isTop: scoped.parent_id === -1, isEdit: true })
                    }
                  >
                    编辑
                  </el-button>
                  <el-popconfirm
                    v-can={{ name: 'permissions.delete' }}
                    style="margin-left: 7px"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    icon="el-icon-info"
                    icon-color="red"
                    title="确定删除吗？"
                    onConfirm={() => this.handleRemovePermission(scoped.row)}
                  >
                    <el-button type="text" size="small" slot="reference">
                      删除
                    </el-button>
                  </el-popconfirm>
                </div>
              )
            }
          }
        }
      ]
    }
  },
  computed: {
    ...mapGetters('app', ['permissions'])
  },
  methods: {
    /**
     * @name: 添加菜单
     * @author: Mr. zhu
     * @param { isTop: 是否是顶级菜单, rowData: 当前行数据, isModify: 是否是修改 }
     */
    handleEditorMenu(params = { isTop: false, rowData: null, isEdit: false }) {
      this.$nextTick(() => {
        // 检测参数合法性
        if (!isObj(params)) {
          throw new Error(`请检查传入参是否正确: ${params.toString()}`)
        }
        params = Object.assign({ isTop: false, rowData: {}, isEdit: false }, params)
        this.isTop = params.isTop
        if (params.isTop) {
          this.permissionsEditor.model = Object.assign({}, {})
        }
        this.permissionsEditor.model = params.isEdit ? params.rowData : {}
        if (!params.isEdit && params.rowData) {
          this.permissionsEditor.model = Object.assign(
            {},
            {
              parent_name: params.rowData.display_name,
              parent_id: params.rowData.id
            }
          )
        }
        this.permissionsEditor.visible = true
      })
    },
    handleSubmitPermission(data) {
      const loading = Loading.service({ lock: true })

      if (data.parent_id == 1) {
        delete data.parent_id
      }
      const action = data.id !== undefined ? updatePermission(data.id, data) : createPermission(data)

      action
        .then(() => {
          this.$message.success('操作成功')

          this.$store.dispatch('app/fetchPermissions')
        })
        .finally(() => loading.close())
    },
    /**
     * @name: 删除权限
     * @author: Mr. zhu
     * @param {*} rowData 当前操作行数据
     */
    handleRemovePermission(rowData) {
      const loading = Loading.service({ lock: true })

      deletePermission(rowData.id)
        .then(() => {
          this.$message.success('删除成功')

          this.$store.dispatch('app/fetchPermissions')
        })
        .finally(() => loading.close())
    }
  }
}
</script>

<style lang="scss" scoped></style>
