<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <el-header class="d-flex align-items-center p-none">
      <el-button v-can="{ name: 'admins.create' }" icon="el-icon-circle-plus" type="primary" @click="handleEditorAdmin">
        添加用户
      </el-button>
    </el-header>

    <el-card shadow="never">
      <define-table :data="admins" :attrs="tableAttrs" :cols="cols" :paging="paging" :paging-events="pagingEvents" />
    </el-card>

    <admin-editor
      :visible.sync="adminEditor.visible"
      v-if="adminEditor.visible"
      :model="adminEditor.model"
      @submit="handleEditorSubmit"
    />
  </div>
</template>

<script>
import { getAdmins, createAdmin, updateAdmin, deleteAdmin } from '@/apis/admin'
import AdminEditor from '@/components/system/AdminEditor'
import { Loading } from 'element-ui'

export default {
  name: 'Admin',
  components: { AdminEditor },
  data() {
    return {
      admins: [],
      adminEditor: {
        visible: false,
        model: {}
      },
      paging: {
        currentPage: 1,
        pageSize: 15,
        layout: ' prev, pager, next, jumper, total',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.fetchAdmins({
            page: page
          })
        }
      },
      tableAttrs: {
        rowKey: 'id',
        border: false
      },
      cols: [
        // {
        //   label: 'ID',
        //   prop: 'id',
        //   width: 80
        // },
        {
          label: '平台',
          prop: 'platform.name'
        },
        {
          label: '名称',
          prop: 'name'
        },
        {
          label: '用户名',
          prop: 'username'
        },
        {
          label: '角色',
          prop: 'roles.0.display_name'
        },
        {
          label: '客户数',
          width: 60,
          prop: 'users_count'
        },
        {
          label: '是否可用',
          prop: 'is_enabled',
          width: 80,
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return scoped.row.is_enabled ? <span class="text-primary">是</span> : <span class="text-danger">否</span>
            }
          }
        },
        {
          label: '创建时间',
          prop: 'created_at',
          width: 150
        },
        {
          label: '操作',
          fixed: 'right',
          width: 90,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    v-can={{ name: 'admins.update' }}
                    size="small"
                    type="text"
                    onClick={() => this.handleEditAdmin({ rowData: scoped.row })}
                  >
                    编辑
                  </el-button>
                  <el-popconfirm
                    v-can={{ name: 'admins.delete' }}
                    style="margin-left: 10px"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    icon="el-icon-info"
                    icon-color="red"
                    title="确定删除吗？"
                    onConfirm={() => this.handleRemoveAdmin(scoped.row)}
                  >
                    <el-button size="small" type="text" slot="reference">
                      删除
                    </el-button>
                  </el-popconfirm>
                </div>
              )
            }
          }
        }
      ]
    }
  },
  created() {
    this.fetchAdmins()
  },
  methods: {
    fetchAdmins(params) {
      getAdmins(params).then((r) => {
        this.admins = r.data

        this.paging.currentPage = r.meta.current_page
        this.paging.total = r.meta.total
        this.paging.pageSize = r.meta.per_page
      })
    },
    handleEditorAdmin() {
      this.$nextTick(() => {
        this.adminEditor.visible = true
        this.adminEditor.model = {}
      })
    },
    handleRemoveAdmin(rowData) {
      if (rowData.users_count > 0) {
        this.$router.push({ name: 'AdminsTransfer', params: { id: rowData.id } })
      } else {
        const loading = Loading.service({ lock: true })

        deleteAdmin(rowData.id)
          .then(() => {
            this.$message.success('删除成功')

            this.fetchAdmins()
          })
          .finally(() => loading.close())
      }
    },
    handleEditAdmin({ rowData }) {
      this.$nextTick(() => {
        this.adminEditor.visible = true
        rowData.role_id = rowData?.roles[0]?.id
        this.adminEditor.model = rowData instanceof MouseEvent ? {} : rowData
      })
    },
    handleEditorSubmit(data) {
      const loading = Loading.service({ lock: true })
      const action = data.id !== undefined ? updateAdmin(data.id, data) : createAdmin(data)
      action
        .then(() => {
          this.$message.success('操作成功')
          this.fetchAdmins()
        })
        .finally(() => loading.close())
    }
  }
}
</script>
