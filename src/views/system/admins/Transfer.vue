<template>
  <div class="admins bg-white p-extra-large-x p-extra-large-y w-100 d-flex flex-column o-hidden">
    <el-alert title="当前账号有关联客户请先移除后再操作离职！" type="error" :closable="false" />

    <div class="m-extra-large-t">
      <define-table :cols="cols" :data="data" />
      <el-button type="primary" icon="fas fa-trash" style="width: 100px; float: right" @click="handleSubmit">
        删除并交接
      </el-button>
    </div>
  </div>
</template>

<script>
import { getSales, getAdminUsers, transferUsers } from '@/apis/admin'

export default {
  name: 'AdminsTransfer',
  data() {
    return {
      key: 0,
      newSales: {},
      cols: [
        { label: '客户姓名', prop: 'name' },
        { label: '手机号', prop: 'phone_number' },
        { label: '邮箱', prop: 'email' },
        { label: '地址', prop: 'address' },
        {
          label: '业务员',
          scopedSlots: {
            default: (scoped) => {
              const options = this.sales.map((item) => {
                return <el-option label={item.name} key={item.id} value={item.id} />
              })

              return (
                <div>
                  <el-select
                    placerholder="请选择业务员"
                    key={this.key}
                    vModel={this.newSales[scoped.row.id]}
                    onChange={() => {
                      this.key = new Date().getTime()
                    }}
                  >
                    {options}
                  </el-select>
                </div>
              )
            }
          }
        }
      ],
      data: [],
      sales: []
    }
  },
  created() {
    getAdminUsers(this.$route.params.id).then((r) => {
      this.data = r.data

      this.data.forEach((e) => (this.newSales[e.id] = null))
    })
    getSales().then((r) => (this.sales = r.data))
  },
  methods: {
    handleSubmit() {
      const isCheckedAll =
        Object.keys(this.newSales).filter((item) => {
          return this.newSales[item] !== null
        }).length === this.data.length

      if (isCheckedAll) {
        transferUsers(this.$route.params.id, {
          data: this.newSales
        }).then(() => {
          this.$message.success('操作成功')
          this.$router.push({ name: 'Admins' })
        })
      } else {
        this.$message.error('请确定所有用户都选择了新的业务员')
      }
    }
  }
}
</script>
