<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <el-card shadow="never">
      <define-table :cols="cols" :data="data" :paging="paging" :paging-events="pagingEvents" />
    </el-card>

    <user-editor :visible.sync="userEditor.visible" :model="userEditor.model" @submit="handleEditorSubmit" />
  </div>
</template>

<script>
import { createUser, getRegistrations } from '@/apis/user'
import { Loading } from 'element-ui'
import UserEditor from '@/components/user/UserEditor'

export default {
  name: 'UsersRegistration',
  components: {
    UserEditor
  },
  data() {
    return {
      userEditor: {
        visible: false,
        model: {}
      },
      cols: [
        { label: '姓名/企业名称', prop: 'name' },
        { label: '手机号', prop: 'phone_number' },
        { label: '邮箱', prop: 'email' },
        { label: '申请时间', prop: 'created_at' },
        {
          label: '状态',
          scopedSlots: {
            default: (scoped) => {
              return scoped.row.status === 0 ? (
                <span class="text-danger">待处理</span>
              ) : (
                <span class="text-blue">已处理</span>
              )
            }
          }
        },
        {
          label: '操作',
          width: 120,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    v-can={{ name: 'users.create' }}
                    type="text"
                    size="mini"
                    disabled={scoped.row.status === 1}
                    onClick={() => {
                      this.userEditor.visible = true
                      this.userEditor.model = {
                        name: scoped.row.name,
                        email: scoped.row.email,
                        phone_number: scoped.row.phone_number,
                        registration_id: scoped.row.id
                      }
                    }}
                  >
                    创建用户
                  </el-button>
                  <el-popconfirm
                    v-can={{ name: 'users.registrations.delete' }}
                    style="margin-left: 10px"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    icon="el-icon-info"
                    icon-color="red"
                    title="确定删除吗？"
                    onConfirm={() => this.handleRemove(scoped.row)}
                  >
                    <el-button size="small" type="text" slot="reference">
                      删除
                    </el-button>
                  </el-popconfirm>
                </div>
              )
            }
          }
        }
      ],
      data: [],
      // 分页器 不传递 -> 没有分页
      paging: {
        currentPage: 1,
        pageSize: 15,
        layout: 'prev, pager, next, jumper, total',
        total: 0,
        align: 'left',
        background: true
      },
      pagingEvents: {
        currentChange: (page) => {
          this.fetchRegistrations(page)
        }
      }
    }
  },
  created() {
    this.fetchRegistrations()
  },
  methods: {
    fetchRegistrations(page = 1) {
      getRegistrations({
        page,
        filter: {}
      }).then((r) => {
        this.data = r.data

        this.paging.currentPage = r.meta.current_page
        this.paging.pageSize = r.meta.per_page
        this.paging.total = r.meta.total
      })
    },
    handleEditorSubmit(data) {
      const loading = Loading.service({ lock: true })

      createUser(data)
        .then(() => {
          this.$message.success('操作成功')

          this.fetchRegistrations()
        })
        .finally(() => loading.close())
    },
    handleRemove(row) {
      //
    }
  }
}
</script>
