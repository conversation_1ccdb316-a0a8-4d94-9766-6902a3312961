<!--
 * @Descripttion:
 * @version:
 * @Author: yanb
 * @Date: 2021-04-06 16:54:33
 * @LastEditors: yanb
 * @LastEditTime: 2024-06-20 09:21:46
-->
<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <SearchPanel @command="handleSearchPanel" :custom="searchFields"></SearchPanel>
    <el-header class="d-flex align-items-center p-none">
      <el-button icon="el-icon-circle-plus" type="primary" v-can="{ name: 'users.create' }" @click="handleEditorUser">
        添加用户
      </el-button>
      <el-badge :value="pendingCount" class="m-mini-l" v-can="{ name: 'users.registrations' }">
        <el-button
          type="primary"
          icon="fas fa-users"
          @click="$router.push({ name: 'UserRegistration' })"
          v-can="{ name: 'users.registrations' }"
        >
          注册审核
        </el-button>
      </el-badge>
    </el-header>

    <el-card shadow="never">
      <define-table :data="users" :attrs="tableAttrs" :cols="cols" :paging="paging" :paging-events="pagingEvents" />
    </el-card>

    <user-editor :visible.sync="userEditor.visible" :model="userEditor.model" @submit="handleEditorSubmit" />
  </div>
</template>

<script>
import { getUsers, createUser, updateUser, pendingCount, userExport } from '@/apis/user'
import UserEditor from '@/components/user/UserEditor'
import { mapGetters } from 'vuex'
import { Loading } from 'element-ui'

export default {
  name: 'Users',
  components: { UserEditor },
  data() {
    return {
      pendingCount: 0,
      users: [],
      userEditor: {
        visible: false
      },
      paging: {
        currentPage: 1,
        pageSize: 15,
        layout: ' prev, pager, next, jumper, total',
        total: 0
      },
      searchQuery: {},
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page
          this.getUsers()
        }
      },
      tableAttrs: {
        rowKey: 'id',
        border: false
      },
      searchFields: [
        {
          type: 'input',
          valKey: 'username',
          hintText: '用户名'
        },
        {
          type: 'input',
          valKey: 'name',
          hintText: '客户名称'
        },
        {
          type: 'input',
          valKey: 'creator_name',
          hintText: '开通人'
        },
        {
          type: 'input',
          valKey: 'salesman_name',
          hintText: '业务员'
        },
        {
          type: 'select',
          valKey: 'is_agent',
          hintText: '角色',
          options: [
            { label: '代理', value: 1 },
            { label: '用户', value: 0 }
          ]
        },
        {
          type: 'select',
          valKey: 'is_arrears',
          hintText: '欠款',
          options: [
            { label: '有', value: 1 },
            { label: '无', value: 0 }
          ]
        },
        {
          type: 'daterange',
          valKey: 'created_at_range'
        },
        {
          type: 'select',
          valKey: 'charge_type',
          hintText: '充值类型',
          options: [
            { label: '真实充值', value: 0 },
            { label: '虚拟充值', value: 1 }
          ]
        }
      ],
      cols: [
        {
          label: '用户名',
          prop: 'username',
          width: 150,
          fixed: 'left'
        },
        {
          label: '客户名称',
          prop: 'name',
          width: 180,
          fixed: 'left'
        },
        {
          label: '角色',
          prop: 'role',
          width: 60,
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              switch (scoped.row.role) {
                case 'AGENT':
                  return <span class="text-primary">代理商</span>
                case 'USER':
                  return <span class="text-info">会员</span>
                default:
                  return <span class="text-danger">未知</span>
              }
            }
          }
        },
        {
          label: '余额',
          prop: 'balance',
          width: 80
        },
        {
          label: '欠款',
          prop: 'arrears',
          width: 80
        },
        {
          label: '开通人',
          prop: 'creator.name',
          width: 150
        },
        {
          label: '业务员',
          prop: 'salesman.name',
          width: 80
        },
        {
          label: '创建时间',
          prop: 'created_at'
          // width: 150
        },
        {
          label: '是否启用',
          prop: 'is_enabled',
          width: 80,
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return scoped.row?.is_enabled ? <span class="text-primary">是</span> : <span class="text-info">否</span>
            }
          }
        },
        {
          label: '操作',
          fixed: 'right',
          width: 150,
          alian: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    size="small"
                    type="text"
                    v-can={{ name: 'users.show' }}
                    onClick={() =>
                      this.$open({
                        name: 'User',
                        params: { id: scoped.row.id }
                      })
                    }
                  >
                    详情
                  </el-button>
                  <el-button
                    size="small"
                    type="text"
                    v-can={{ name: 'users.update' }}
                    onClick={() => this.handleEditUser({ rowData: scoped.row })}
                  >
                    编辑
                  </el-button>
                  <el-button
                    size="small"
                    type="text"
                    v-can={{ name: 'users.products' }}
                    onClick={() => this.$router.push({ name: 'UsersProducts', params: { id: scoped.row.id } })}
                  >
                    产品设置
                  </el-button>
                </div>
              )
            }
          }
        }
      ]
    }
  },
  computed: {
    ...mapGetters('auth', ['admin'])
  },
  created() {
    this.getUsers()

    pendingCount().then((r) => {
      this.pendingCount = r.data.count
    })
  },
  methods: {
    getUsers(params) {
      getUsers({
        page: this.paging.page,
        filter: Object.assign({}, this.searchQuery, params)
      }).then((r) => {
        this.users = r.data
        this.paging.currentPage = r.meta.current_page
        this.paging.total = r.meta.total
        this.paging.pageSize = r.meta.per_page
      })
    },
    handleSearchPanel(command, data) {
      this.searchQuery = data
      this.paging.page = 1
      if (command === 'export') {
        window.open(userExport({ filter: data }))
      } else {
        this.getUsers()
      }
    },
    handleEditorUser() {
      this.$nextTick(() => {
        this.userEditor.visible = true
        this.userEditor.model = {}
      })
    },
    handleEditUser({ rowData }) {
      this.$nextTick(() => {
        this.userEditor.visible = true
        this.userEditor.model = rowData
      })
    },
    handleEditorSubmit(data) {
      const loading = Loading.service({ lock: true })

      const action = data.id !== undefined ? updateUser(data.id, data) : createUser(data)

      action
        .then(() => {
          this.$message.success('操作成功')

          this.userEditor.visible = false

          this.getUsers()
        })
        .finally(() => loading.close())
    }
  }
}
</script>

<style lang="ass" scoped></style>
