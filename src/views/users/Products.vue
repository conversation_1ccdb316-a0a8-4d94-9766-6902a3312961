<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <el-tabs v-model="activeName">
      <el-tab-pane v-for="(name, key) in productTypes" :key="key" :name="key" :label="name" lazy>
        <user-products
          :name="name"
          :productType="activeName"
          :data.sync="data"
          :paging.sync="paging"
          :products.sync="products"
          :user="user"
          @query-submit="handleQuerySubmit"
          :has-rate-form="hasRateFormProducts.includes(parseInt(key), 0) ? true : false"
          @update-status="handleUpdateStatus"
          @submit="handleSubmit"
          @multiple-submit="handleMultipleSubmit"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import UserProducts from '@/components/user/UserProducts'
import {
  getUser,
  getUserProducts,
  assignUserProduct,
  updateUserProductStatus,
  assignUserProductMultiple
} from '@/apis/user'
import { getEnabledProducts } from '@/apis/product'
import { Loading } from 'element-ui'

export default {
  name: 'UsersProducts',
  components: { UserProducts },
  data() {
    return {
      activeName: '1',
      hasRateFormProducts: [1, 2, 3, 7],
      paging: {
        currentPage: 1,
        pageSize: 15,
        layout: 'prev, pager, next, jumper, total',
        total: 0
      },
      productTypes: {
        1: '国内货运险',
        2: '国际货运险',
        3: '单车责任运险',
        4: '其他险种',
        5: '雇主责任险',
        7: '跨境电商货运险'
      },
      data: [],
      products: [],
      user: {},
      page: 1,
      queries: {}
    }
  },
  watch: {
    activeName() {
      this.page = 1
      this.queries = {}

      this.fetchProducts()
      this.fetchUserProducts()
    }
  },
  created() {
    this.getUser()
    this.fetchProducts()
    this.fetchUserProducts()
  },
  methods: {
    handleQuerySubmit(page, queries) {
      this.page = page
      this.queries = queries

      this.fetchProducts()
    },
    handleUpdateStatus(productId, status) {
      updateUserProductStatus(this.$route.params.id, productId, status).then(() => {
        this.$message.success('更新状态成功')

        this.fetchProducts()
        this.fetchUserProducts()
      })
    },
    handleSubmit(form) {
      const loading = Loading.service()
      assignUserProduct(this.$route.params.id, form.product_id, form)
        .then(() => {
          this.fetchProducts()
          this.fetchUserProducts()

          this.$message.success('更新成功')
        })
        .finally(() => loading.close())
    },
    handleMultipleSubmit(form) {
      const loading = Loading.service()
      form.filter.type = this.activeName
      form.filter.is_enabled = 1
      assignUserProductMultiple(this.$route.params.id, form)
        .then(() => {
          this.fetchProducts()
          this.fetchUserProducts()

          this.$message.success('配置成功')
        })
        .finally(() => loading.close())
    },
    fetchProducts() {
      getEnabledProducts(this.$route.params.id, {
        page: this.page,
        filter: Object.assign(
          {
            type: this.activeName
          },
          this.queries
        )
      }).then((r) => {
        this.products = r.data

        this.paging.currentPage = r.meta.current_page
        this.paging.page = r.meta.currentPage
        this.paging.pageSize = r.meta.per_page
        this.paging.total = r.meta.total
      })
    },
    fetchUserProducts() {
      getUserProducts(this.$route.params.id, {
        filter: {
          type: this.activeName,
          is_enabled: true
        }
      }).then((r) => {
        this.data = r.data
      })
    },
    getUser() {
      getUser(this.$route.params.id).then((r) => {
        this.user = r.data
      })
    }
  }
}
</script>
