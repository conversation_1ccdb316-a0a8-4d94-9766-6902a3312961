<!--
 * @Descripttion:
 * @version:
 * @Author: yanb
 * @Date: 2021-04-07 17:31:47
 * @LastEditors: yanb
 * @LastEditTime: 2024-06-20 09:29:04
-->
<template>
  <div class="p-extra-large-x p-extra-large-b w-100 d-flex flex-column">
    <el-header class="d-flex align-items-center justify-content-end p-none">
      <el-button-group>
        <el-button
          type="primary"
          icon="el-icon-edit"
          v-can="{ name: 'users.products' }"
          @click="
            () => {
              this.$router.push({ name: 'UsersProducts', params: { id: user.id } })
            }
          "
          >设置产品</el-button
        >
        <el-button
          type="primary"
          icon="fas fa-code"
          v-can="{ name: 'users.key-secret' }"
          @click="handleGenerateAPIKeySecret"
        >
          {{ userData.api_key ? '重置' : '生成' }}接口密钥
        </el-button>
        <el-dropdown v-can="{ name: 'users.update' }">
          <el-button type="primary"> 账号相关设置<i class="el-icon-arrow-down el-icon--right"></i> </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-if="userData.is_enabled" @click.native="enabledUser">禁用客户</el-dropdown-item>
            <el-dropdown-item v-else @click.native="enabledUser">启用客户</el-dropdown-item>
            <el-dropdown-item v-can="{ name: 'users.reset-password' }" @click.native="resetPWD.visible = true"
              >重置密码</el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown>
        <el-dropdown v-can="{ name: 'users.update' }">
          <el-button type="primary"> 其他设置<i class="el-icon-arrow-down el-icon--right"></i> </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-can="{ name: 'users.update-invoice-data' }"
              @click.native="handleEditUserInvoiceData({ rowData: userData })"
            >
              发票信息
            </el-dropdown-item>
            <el-dropdown-item
              v-can="{ name: 'users.update-insure-preset-data' }"
              v-if="userData?.is_use_insure_preset_data"
              @click.native="handleEditUserInsurePresetData({ rowData: userData?.insure_preset_data })"
            >
              投保预设信息
            </el-dropdown-item>
            <el-dropdown-item v-can="{ name: 'users.update' }" @click.native="() => (showGoodsKeywordsDialog = true)">
              货物关键字
            </el-dropdown-item>
            <el-dropdown-item v-can="{ name: 'users.update' }" @click.native="() => (showSubjectClauseDialog = true)">
              免赔和特别约定
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-button-group>
    </el-header>
    <el-container class="p-extra-large-b">
      <el-main>
        <define-user-detail v-show="user.data && user.data.length > 0" :data="user" />
      </el-main>
      <el-aside width="300px">
        <el-card class="box-card" shadow="hover">
          <div slot="header" class="clearfix">
            <span>账户余额</span>
            <el-button
              style="float: right; padding: 3px 0"
              type="text"
              v-can="{ name: 'users.payments' }"
              @click="
                () => {
                  getPayments()
                  paymentsDialog.visible = true
                }
              "
            >
              充值记录
            </el-button>
            <h1 class="blance">
              ¥ {{ user.finance.balance }}
              <el-button type="primary" class="charge-btn" @click="chargeDialog.visible = true">充值</el-button>
            </h1>
          </div>
          <div>
            <div class="arrears text-danger">
              欠款: ¥ {{ user.finance.arrears }}
              <!-- <el-button type="text" class="payment-btn" @click="dialog = true">付款</el-button> -->
            </div>
          </div>
        </el-card>
      </el-aside>
    </el-container>
    <el-dialog
      append-to-body
      title="充值"
      :visible.sync="chargeDialog.visible"
      width="40%"
      :before-close="() => (chargeDialog.visible = false)"
      destory-on-close
    >
      <el-form ref="form" :model="form" :rules="rules" label-suffix=":" label-width="100" label-position="top">
        <el-form-item prop="type" label="类型">
          <el-radio v-model="form.type" :label="1">{{ userData.charge_type == 0 ? '充值' : '虚拟充值' }}</el-radio>
          <el-radio v-model="form.type" :label="3">扣费</el-radio>
        </el-form-item>
        <el-form-item prop="amount" :label="form.type == 1 ? '充值金额' : '扣费金额'">
          <el-input type="number" v-model="form.amount"></el-input>
        </el-form-item>
        <el-form-item prop="proof" label="支付凭证" v-if="form.type == 1 && userData.charge_type == 0">
          <upload-file v-model="form.proof"></upload-file>
        </el-form-item>
        <el-form-item>
          <el-form-item>
            <el-button @click="chargeDialog.visible = false">取 消</el-button>
            <el-button type="primary" @click="handleSubmit">确 定</el-button>
          </el-form-item>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog
      append-to-body
      title="付款"
      :visible.sync="dialog"
      width="40%"
      :before-close="() => (dialog = false)"
      destory-on-close
    >
      <el-form ref="form1" :model="form1" :rules="rules1" label-suffix=":" label-width="100" label-position="top">
        <el-form-item prop="proof" label="付款方式">
          <el-radio v-model="form1.charge_type" label="3">补缴欠款</el-radio>
          <el-radio v-model="form1.charge_type" label="4">保费支付保险公司</el-radio>
        </el-form-item>
        <el-form-item prop="amount" label="金额">
          <el-input type="number" v-model="form1.amount"></el-input>
        </el-form-item>
        <el-form-item prop="proof" label="支付凭证">
          <upload-file v-model="form1.proof"></upload-file>
        </el-form-item>
        <el-form-item>
          <el-form-item>
            <el-button @click="dialog = false">取 消</el-button>
            <el-button type="primary" @click="payment">确 定</el-button>
          </el-form-item>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog title="财务流水" :visible.sync="paymentsDialog.visible" width="1000px" destroy-on-close>
      <define-table
        :data="payments"
        :cols="paymentCols"
        :paging="paymentPaging"
        :paging-events="paymentPagingEvents"
      ></define-table>
    </el-dialog>

    <el-dialog :title="resetPWD.title" :visible.sync="resetPWD.visible" width="30%">
      <el-form label-position="left" label-width="100px" :model="resetPWD.form" ref="form" :rules="resetPWD.rules">
        <el-form-item label="新密码：" prop="password">
          <el-input v-model="resetPWD.form.password" placeholder="请输入新密码"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="resetPWD.visible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="
            () => {
              resetPassword()
            }
          "
        >
          确 定
        </el-button>
      </span>
    </el-dialog>

    <UserInvoiceEditor
      :visible.sync="userInvoiceEditor.visible"
      :model="userInvoiceEditor.model"
      @submit="handleUserInvoiceEditorSubmit"
    />

    <UserInsurePresetEditor
      :visible.sync="userInsurePresetEditor.visible"
      :model="userInsurePresetEditor.model"
      @submit="handleUserInsurePresetEditorSubmit"
    />

    <goods-keywords :visible.sync="showGoodsKeywordsDialog" :user="userData?.id || 0" />

    <subject-clause :visible.sync="showSubjectClauseDialog" :user="userData?.id || 0" />
  </div>
</template>

<script>
import {
  getUser,
  getPayments,
  resetPassword,
  recharge,
  enabledUser,
  updateUserInvoiceData,
  updateUserInsurePresetData,
  generateApiKeySecret
} from '@/apis/user'
import DefineUserDetail from '@/components/user/DefineUserDetail'
import UserInvoiceEditor from '@/components/user/UserInvoiceEditor'
import UserInsurePresetEditor from '@/components/user/UserInsurePresetEditor'
import GoodsKeywords from '@/components/user/GoodsKeywords'
import SubjectClause from '@/components/user/SubjectClause'
import { Loading } from 'element-ui'
import { tokenKey } from '@/config'

export default {
  name: 'User',
  components: {
    DefineUserDetail,
    UserInvoiceEditor,
    UserInsurePresetEditor,
    GoodsKeywords,
    SubjectClause
  },
  data() {
    return {
      showGoodsKeywordsDialog: false,
      showSubjectClauseDialog: false,
      form1: {
        amount: void 0,
        proof: void 0,
        charge_type: void 0
      },
      dialog: false,
      user: {
        title: '用户详情',
        user: {},
        finance: {},
        data: []
      },
      userInvoiceEditor: {
        visible: false
      },
      userInsurePresetEditor: {
        visible: false
      },
      userData: [],
      payments: [],
      chargeDialog: {
        visible: false
      },
      paymentsDialog: {
        visible: false
      },
      resetPWD: {
        visible: false,
        form: {
          password: ''
        },
        rules: {
          password: [{ required: true, message: '请输入重置后新密码', trigger: 'blur' }]
        }
      },
      form: {
        type: 1,
        charge_type: void 0,
        proof: void 0,
        amount: void 0
      },
      paymentPaging: {
        align: 'center',
        currentPage: 1,
        pageSize: 15,
        layout: ' prev, pager, next, jumper, total',
        total: 0
      },
      paymentPagingEvents: {
        currentChange: (page) => {
          this.paymentPaging.currentPage = page

          this.getPayments()
        }
      },
      paymentCols: [
        {
          label: 'ID',
          prop: 'id',
          width: 50
        },
        {
          label: '类型',
          prop: 'type',
          width: 80,
          scopedSlots: {
            default: (scoped) => {
              switch (scoped.row.type) {
                case 1:
                  return <span class="text-primary">充值</span>
                case 2:
                  return <span class="text-info">支付</span>
                case 3:
                  return <span class="text-warning">扣费</span>
                default:
                  return <span class="text-info">未知</span>
              }
            }
          }
        },
        {
          label: '方式',
          prop: 'charge_type',
          width: 80,
          scopedSlots: {
            default: (scoped) => {
              switch (scoped.row.charge_type) {
                case 1:
                  return <span class="text-info">真实充值</span>
                case 2:
                  return <span class="text-info">虚拟充值</span>
                case 3:
                  return <span class="text-info">补缴欠款</span>
                case 4:
                  return <span class="text-info">支付保司</span>
                case 5:
                  return <span class="text-info">系统扣费</span>
                case 6:
                  return <span class="text-info">代理充值</span>
                case 7:
                  return <span class="text-info">代理扣费</span>
                case 8:
                  return <span class="text-info">在线支付</span>
                default:
                  return <span class="text-info">未知</span>
              }
            }
          }
        },
        {
          label: '金额',
          width: 80,
          prop: 'amount'
        },
        {
          label: '欠款',
          width: 80,
          prop: 'arrears'
        },
        {
          label: '支付凭证',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.proof != '') {
                return (
                  <div>
                    <el-link
                      type="primary"
                      href={`${scoped.row.proof}?token=` + window.localStorage.getItem(tokenKey)}
                      download=""
                      target="_blank"
                    >
                      点击查看
                    </el-link>
                  </div>
                )
              } else {
                return <span>-</span>
              }
            }
          }
        },
        {
          label: '申请人',
          prop: 'apply.name'
        },
        {
          label: '申请时间',
          prop: 'apply_at'
        },
        {
          label: '处理人',
          prop: 'operated.name'
        },
        {
          label: '处理时间',
          prop: 'operated_at'
        },
        {
          label: '退回原因',
          prop: 'reason'
        },
        {
          label: '状态',
          prop: 'status',
          width: 80,
          scopedSlots: {
            default: (scoped) => {
              switch (scoped.row.status) {
                case 1:
                  return <span class="text-info">未确认</span>
                case 2:
                  return <span class="text-success">已完成</span>
                case 3:
                  return <span class="text-info">已退回</span>
                case 4:
                  return <span class="text-info">未支付</span>
                default:
                  return <span class="text-info">未知</span>
              }
            }
          }
        }
      ],
      rules: {
        amount: [{ required: true, message: '请输入充值金额', trigger: 'blur' }],
        // proof: [{ required: true, message: '请上传充值凭证', trigger: ['blur', 'change'] }],
        proof: [{ required: true, message: '请上传凭证文件', trigger: ['blur', 'change'] }],
        charge_type: [{ required: false, message: '请选择付款方式' }]
      },
      rules1: {
        amount: [{ required: true, message: '请输入付款金额', trigger: 'blur' }],
        proof: [{ required: true, message: '请上传凭证文件', trigger: ['blur', 'change'] }],
        charge_type: [{ required: true, message: '请选择付款方式', trigger: ['blur', 'change'] }]
      },
      proof: [],
      proofPreviewURL: '',
      options: [
        {
          value: 1,
          label: '充值'
        },
        {
          value: 2,
          label: '支付'
        }
      ]
    }
  },
  created() {
    this.getUser()
  },
  methods: {
    outputData(data) {
      this.user.title = '用户详情'
      this.user.id = data.id
      this.user.finance.balance = data.balance
      this.user.data = [
        {
          title: '基本信息',
          groups: [
            { label: '用户名', value: data.username },
            { label: '姓名', value: data.name },
            { label: '角色', value: data.role },
            { label: '充值类型', value: data.charge_type ? '虚拟充值' : '真实充值' },
            { label: '平台', value: data.platform.name },
            { label: '代理商', value: data.agent.name },
            { label: '开通来源', value: data.creator.name },
            { label: '联系方式', value: data.phone_number },
            { label: '邮箱', value: data.email },
            { label: '证件号码', value: data.idcard_no },
            { label: '联系地址', value: data.address },
            { label: '注册时间', value: data.created_at },
            {
              label: data.type === 1 ? '身份证' : '营业执照',
              value: data.idcard_no_file ? '点击查看' : '',
              isLink: true,
              target: '_blank',
              to: data.idcard_no_file
            },
            {
              label: '其他附件',
              value: data.attachment ? '点击查看' : '',
              isLink: true,
              target: '_blank',
              to: data.attachment
            },
            { label: '备注', value: data.remark }
          ]
        },
        {
          title: '接口信息',
          groups: [
            { label: 'API KEY', value: data.api_key || '-', row: true },
            { label: 'API SECRET', value: data.api_secret || '-', row: true }
          ]
        }
      ]
    },
    handleGenerateAPIKeySecret() {
      this.$confirm(`确认${this.userData.api_key ? '重置' : '生成'}接口信息吗？`, '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        callback: (action) => {
          if (action === 'confirm') {
            const loading = Loading.service()

            generateApiKeySecret(this.userData.id)
              .then(() => {
                this.$message.success('生成成功')

                this.getUser()
              })
              .finally(() => loading.close())
          }
        }
      })
    },
    getUser() {
      getUser(this.$route.params.id).then((r) => {
        this.outputData(r.data)
        this.userData = r.data
        this.user.finance.arrears = r.data.arrears
      })
    },
    getPayments() {
      getPayments(this.$route.params.id, {
        page: this.paymentPaging.currentPage
      }).then((r) => {
        this.payments = r.data

        this.paymentPaging.currentPage = r.meta.current_page
        this.paymentPaging.pageSize = r.meta.per_page
        this.paymentPaging.total = r.meta.total
      })
    },
    // 付款
    payment() {
      this.$refs.form1.validate((valid) => {
        if (valid) {
          const loading = Loading.service()
          const _data = Object.assign({}, this.form1, { user_id: this.user.id })
          recharge(_data)
            .then(() => {
              this.dialog = false
              this.$message.success('付款申请成功,请等待财务处理完成!')
              let _keys = Object.keys(this.form1)
              _keys.forEach((item) => {
                this.form1[item] = void 0
              })
              this.getUser()
            })
            .finally(() => loading.close())
        } else {
          return false
        }
      })
    },
    handleSubmit() {
      this.form.charge_type = this.userData.charge_type == 0 ? 1 : 2
      if (this.form.type == 3) {
        this.form.charge_type = 5
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          const loading = Loading.service()
          const _data = Object.assign({}, this.form, { user_id: this.user.id })
          recharge(_data)
            .then(() => {
              this.chargeDialog.visible = false
              this.$message.success('提交成功')
              let _keys = Object.keys(this.form)
              _keys.forEach((item) => {
                this.form[item] = void 0
              })
              this.getUser()
            })
            .finally(() => loading.close())
        } else {
          return false
        }
      })
    },
    handleProofChange(file) {
      this.proof = [file]
      this.proofPreviewURL = URL.createObjectURL(file.raw)
      this.form.proof = file.raw
    },
    resetPassword() {
      const loading = Loading.service()

      resetPassword(this.user.id, this.resetPWD.form)
        .then(() => {
          this.resetPWD.visible = false
          this.resetPWD.form.password = ''
          this.$message.success('操作成功')
          this.getUser()
        })
        .finally(() => loading.close())
    },
    enabledUser() {
      const loading = Loading.service()
      enabledUser(this.user.id)
        .then(() => {
          this.$message.success('操作成功')
          this.getUser()
        })
        .finally(() => loading.close())
    },
    handleEditUserInvoiceData({ rowData }) {
      this.$nextTick(() => {
        this.userInvoiceEditor.visible = true
        this.userInvoiceEditor.model = rowData
      })
    },
    handleUserInvoiceEditorSubmit(data) {
      const loading = Loading.service({ lock: true })
      updateUserInvoiceData(data.id, data)
        .then(() => {
          this.$message.success('操作成功')

          this.getUser()
        })
        .finally(() => loading.close())
    },
    handleEditUserInsurePresetData({ rowData }) {
      this.$nextTick(() => {
        rowData.id = this.userData.id
        this.userInsurePresetEditor.visible = true
        this.userInsurePresetEditor.model = rowData
      })
    },
    handleUserInsurePresetEditorSubmit(data) {
      const loading = Loading.service({ lock: true })
      updateUserInsurePresetData(data.id, data)
        .then(() => {
          this.$message.success('操作成功')

          this.getUser()
        })
        .finally(() => loading.close())
    }
  },
  watch: {
    'form.type'(type) {
      if (type == 1) {
        if (this.userData.charge_type == 0) {
          this.form.charge_type = 1
        } else {
          this.form.charge_type = 2
        }
      } else {
        this.form.charge_type = 5
      }
    }
  }
}
</script>

<style scoped>
.el-main {
  padding: 0 20px 0 0;
}
.item {
  padding: 18px 0;
}
.blance {
  font-size: 30px;
  margin: 10px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.arrears {
  font-size: 15px;
  display: flex;
  align-items: center;
}
.charge-btn {
  float: right;
}
.payment-btn {
  margin-left: 20px;
}
</style>
