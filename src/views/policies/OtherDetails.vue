<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2020-10-22 16:26:03
 * @LastEditors: yanb
 * @LastEditTime: 2021-07-06 14:53:18
-->
<template>
  <SimpleContainer class="bg-white p-extra-large w-100 d-flex flex-column o-hidden">
    <PolicyGeneralOperationBar
      :email="policy && policy.status === 1"
      :status="[2]"
      :model="policy"
      @operated="getDetails"
      @send-email="handleEmail"
    />
    <div class="m-extra-large-t">
      <el-alert
        center
        v-if="policy && policy.status === 1"
        title="请仔细审查用户投保信息后点击【发送邮件】发送至保险公司审核"
        type="warning"
        :closable="false"
      />
      <el-alert
        center
        v-if="policy && policy.status === 2"
        title="邮件已发送至保险公司，请于保险公司出单后点击【审核】填写保单号"
        type="warning"
        :closable="false"
      />
    </div>
    <DefinePoliciesDetails :data="details"></DefinePoliciesDetails>
  </SimpleContainer>
</template>

<script>
import PolicyGeneralOperationBar from '@/components/policy/PolicyGeneralOperationBar'
import { getPolicy, sendGeneralMail } from '@/apis/policy'

export default {
  name: 'PoliciesOtherDetails',
  components: {
    PolicyGeneralOperationBar
  },
  data() {
    return {
      policy: {}
    }
  },
  computed: {
    statusText() {
      const _map = {
        0: '未提交',
        1: '已提交',
        2: '审核中',
        3: '已支付',
        4: '已审核',
        5: '已出单',
        6: '已退保',
        7: '已作废',
        8: '批改中',
        9: '退保中',
        10: '已退回',
        11: '待确认',
        12: '待支付'
      }
      return function (status) {
        return _map[status] || ''
      }
    },
    details() {
      const _data = {
        title: '保单详情',
        data: [
          {
            title: '投保联系人',
            groups: [
              { label: '姓名', value: this.policy?.detail?.applicant_contact_name },
              { label: '联系电话', value: this.policy?.detail?.applicant_contact_phone }
            ]
          },
          {
            title: '投保信息',
            groups: [
              { label: '保单号', value: this.policy.policy_no ?? '尚未出单 ~' },

              { label: '流水号', value: this.policy?.order_no },
              {
                label: '投保人',
                value: this.policy?.policyholder
              },
              {
                label: '被保人',
                value: this.policy?.insured
              },
              {
                label: '起保日期',
                value: this.policy?.detail?.start_at
              },
              { label: '终保日期', value: this.policy?.detail?.end_at }
            ]
          },
          {
            title: '支付信息',
            groups: [
              {
                label: '支付凭证',
                value: '点击查看',
                row: true,
                isLink: true,
                target: '_blank',
                to: this.policy?.detail?.proof
              }
            ]
          },
          {
            _id: 'policy',
            title: '投保资料',
            groups: []
          },
          {
            title: '发票信息',
            groups: [
              { label: '发票类型', value: this.invoiceType() },
              {
                label: '发票抬头',
                value: this.policy?.detail?.invoice_content?.title
              },
              {
                label: '纳税人识别码',
                value: this.policy?.detail?.invoice_content?.tax_no
              },
              {
                label: '开户行',
                value: this.policy?.detail?.invoice_content?.bank
              },
              {
                label: '账户',
                value: this.policy?.detail?.invoice_content?.card_no
              },
              {
                label: '电话',
                value: this.policy?.detail?.invoice_content?.company_phone
              },
              {
                label: '地址',
                value: this.policy?.detail?.invoice_content?.company_address,
                row: true
              }
            ]
          }
        ]
      }
      let _groups = []
      if (this.policy?.detail?.addition) {
        _groups = this.policy?.detail?.addition.map((item) => {
          const isFile = item.type === 'file' || item.type === '_file'
          return {
            label: item.title,
            value: isFile ? '点击查看' : item.value,
            isLink: isFile,
            target: '_blank',
            row: true,
            to: isFile ? item.value : undefined
          }
        })
        _data.data.map((item) => {
          if (item._id && item._id === 'policy') {
            item.groups = _groups
          }
        })
      } else {
        _data.data = _data.data.filter((item) => !item._id)
      }
      return _data
    }
  },
  mounted() {
    this.getDetails()
  },
  methods: {
    getDetails() {
      getPolicy(this.$route.params.id).then((r) => {
        r.data.detail.addition = JSON.parse(r.data.detail.addition)
        r.data.detail.invoice_content = JSON.parse(r.data.detail.invoice_content)
        this.policy = r.data
      })
    },
    handleEmail() {
      this.$confirm(`该保单当前已发送邮件次数：[ ${this.policy.detail.send_mail_num} ]`, '发送邮件', {
        confirmButtonText: '发送',
        cancelButtonText: '关闭',
        type: 'warning'
      })
        .then(() => {
          const loading = this.$loading({
            lock: true,
            text: '发送中...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          sendGeneralMail(this.policy.id)
            .then((res) => {
              this.getDetails()
              this.$message({
                type: 'success',
                message: '发送成功!'
              })
            })
            .finally(() => {
              loading.close()
            })
        })
        .catch(() => {})
    },
    invoiceType() {
      switch (this.policy?.detail?.invoice_content?.invoice_type) {
        case 'plain':
          return '普票'
        case 'special':
          return '专票'
        default:
          return '无'
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
