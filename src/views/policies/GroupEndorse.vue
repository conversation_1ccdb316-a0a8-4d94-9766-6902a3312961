<template>
  <SimpleContainer title="参保人员列表" class="p-extra-large-x p-extra-large-b w-100 d-flex flex-column o-hidden">
    <el-card shadow="never">
      <div class="d-flex w-100" slot="header">
        <span
          >被保险人：<b>{{ insuredPerson }}</b
          >，保单号：<b>{{ policyNo }}</b></span
        >
        <i class="flex-fill m-mini-r" />
        <el-button
          type="primary"
          icon="fa fa-users"
          v-can="{ name: 'policies.group.employees.index' }"
          @click="$router.push({ name: 'GroupEmployee', params: { policyGroupId: $route.params.policyGroupId } })"
        >
          在保人员
        </el-button>
      </div>
      <define-table :data="endorses" :cols="cols" :paging="paging" :paging-events="pagingEvents" />
    </el-card>
    <el-dialog
      title="批单审核"
      width="520px"
      :visible.sync="audit.visible"
      v-can="{ name: 'policies.group.endorses.audit' }"
    >
      <el-form ref="auditForm" :model="audit.form" :rules="audit.rules" label-suffix=":" label-width="100px">
        <el-alert
          type="warning"
          title="提示"
          description="人工上传批单会涉及人员变更操作，请谨慎操作！"
          show-icon
          :closable="false"
          class="m-extra-large-b"
        ></el-alert>
        <el-form-item v-if="audit.action" prop="endorse_no" label="批单号">
          <el-input v-model="audit.form.endorse_no" placeholder="请输入批单号"></el-input>
        </el-form-item>
        <el-form-item v-if="audit.action" prop="endorse_file" label="批单文件">
          <upload-file v-model="audit.form.endorse_file" />
        </el-form-item>
        <el-form-item v-if="!audit.action" prop="endorse_no" label="退回原因">
          <el-input v-model="audit.form.content" placeholder="请输入退回原因"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleGroupEndorseSubmit()">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </SimpleContainer>
</template>

<script>
import SimpleContainer from '@/components/globals/SimpleContainer'
import {
  getGroupEndorseList,
  getGroupPolicyInfo,
  groupEndorseProcess,
  buildGroupEndorseDownloadHref
} from '@/apis/policy'
import { Loading } from 'element-ui'
import dayjs from 'dayjs'

export default {
  name: 'GroupEndorse',
  components: { SimpleContainer },
  props: {
    policyGroupId: {
      type: Number,
      default: () => -1
    }
  },
  data() {
    return {
      endorses: [],
      insuredPerson: '',
      policyNo: '',
      audit: {
        visible: false,
        action: true,
        form: {
          action: '',
          status: -1,
          endorse_no: '',
          endorse_file: '',
          endorse_id: -1,
          content: ''
        },
        rules: {},
        passed_rules: {
          endorse_no: [{ required: true, message: '请输入批单号', trigger: 'blur' }],
          endorse_file: [{ required: true, message: '请上传批单文件', trigger: ['blur', 'change'] }]
        },
        rejected_rules: {
          content: [{ required: true, message: '请输入退回原因', trigger: 'blur' }]
        }
      },
      policyGroup: {
        policy: {
          status: -1
        }
      },
      cols: [
        {
          label: '批次号',
          prop: 'batch_no',
          width: 180
        },
        {
          label: '支付凭证',
          width: 100,
          scopedSlots: {
            default: (scoped) => {
              if (this.isOnlinePay) {
                return '在线支付'
              }

              if (!scoped.row.transaction_file) {
                return ''
              } else {
                return (
                  <el-button
                    size="mini"
                    type="primary"
                    icon="fas fa-download"
                    onClick={() => this.handleDownlaod(scoped.row.id, 'transaction_file')}
                  >
                    下载
                  </el-button>
                )
              }
            }
          }
        },
        {
          label: '批单号',
          prop: 'endorse_no',
          width: 220
        },
        {
          label: '批单文件',
          width: 100,
          scopedSlots: {
            default: (scoped) => {
              if (!scoped.row.endorse_file || scoped.row.status === 2) {
                return '-'
              } else {
                return (
                  <el-button
                    size="mini"
                    type="primary"
                    icon="fas fa-download"
                    onClick={() => this.handleDownlaod(scoped.row.id, 'endorse_file')}
                  >
                    下载
                  </el-button>
                )
              }
            }
          }
        },
        {
          label: '退回原因',
          scopedSlots: {
            default: (scoped) => {
              return scoped.row.status === 2 ? scoped.row.content : '-'
            }
          }
        },
        {
          label: '添加时间',
          prop: 'created_at',
          width: 150,
          scopedSlots: {
            default: (scoped) => {
              return dayjs(scoped.row.created_at).format('YYYY-MM-DD HH:mm:ss').toString()
            }
          }
        },
        {
          label: '状态',
          width: 80,
          scopedSlots: {
            default: (scoped) => {
              let states = {
                0: '处理中',
                1: '已通过',
                2: '已退回',
                3: '已提交'
              }
              return states[scoped.row.status]
            }
          }
        },
        {
          label: '操作',
          width: 250,
          fixed: 'right',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    v-can={{ name: 'policies.group.endorses.show' }}
                    onClick={() =>
                      this.$router.push({
                        name: 'GroupEndorseEmployee',
                        params: { policyGroupId: this.$route.params.policyGroupId, endorseId: scoped.row.id }
                      })
                    }
                    type="primary"
                    size="mini"
                    icon="fa fa-user-check"
                  >
                    查看
                  </el-button>
                  <el-button
                    v-can={{ name: 'policies.group.endorses.audit' }}
                    onClick={() => this.setGroupEndorseForm(scoped.row, 'pass')}
                    type="primary"
                    size="mini"
                    v-show={[0, 2, 3].includes(scoped.row.status)}
                    class={!this.policyGroup.policy.is_owned ? 'hidden' : ''}
                    icon="fa fa-check-square"
                  >
                    批单
                  </el-button>
                  <el-button
                    v-can={{ name: 'policies.group.endorses.audit' }}
                    onClick={() => this.setGroupEndorseForm(scoped.row, 'reject')}
                    type="danger"
                    size="mini"
                    icon="fa fa-trash"
                    v-show={[0, 2, 3].includes(scoped.row.status)}
                    class={!this.policyGroup.policy.is_owned ? 'hidden' : ''}
                  >
                    退回
                  </el-button>
                  <el-button
                    v-can={{ name: 'policies.group.endorses.audit' }}
                    onClick={() => this.setGroupEndorseForm(scoped.row, 'replace')}
                    type="primary"
                    size="mini"
                    icon="fa fa-redo"
                    v-show={scoped.row.status === 1}
                    class={!this.policyGroup.policy.is_owned ? 'hidden' : ''}
                  >
                    重传批单
                  </el-button>
                </div>
              )
            }
          }
        }
      ],
      paging: {
        currentPage: 1,
        pageSize: 15,
        layout: ' prev, pager, next, jumper, total',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.fetchGroupInsuredEmployeeList(page)
        }
      }
    }
  },
  computed: {
    thirdPlatform() {
      return this.policyGroup?.product?.additional?.third_platform
    },
    isOnlinePay() {
      return this.policyGroup?.product?.additional?.payment_type === 2
    }
  },
  created() {
    this.fetchGroupInsuredEmployeeList()

    getGroupPolicyInfo(this.$route.params.policyGroupId).then((r) => {
      this.policyGroup = r.data

      this.policyNo = this.policyGroup.policy.policy_no
      this.insuredPerson = this.policyGroup.policy.insured
    })
  },
  methods: {
    fetchGroupInsuredEmployeeList(page = 1) {
      getGroupEndorseList(this.$route.params.policyGroupId, {
        page: page
      }).then((r) => {
        this.endorses = r.data

        this.paging.currentPage = r.meta.current_page
        this.paging.total = r.meta.total
      })
    },
    handleDownlaod(id, file) {
      window.open(buildGroupEndorseDownloadHref(id, file))
    },
    setGroupEndorseForm(endorse, action) {
      this.audit.form.endorse_no = ''
      if (action === 'reject') {
        this.audit.action = false
        this.audit.form.status = 2
        this.audit.rules = this.audit.rejected_rules
      } else if (action === 'replace') {
        this.audit.form.status = 1
        this.audit.form.endorse_no = endorse.endorse_no
        this.audit.rules = this.audit.passed_rules
      } else {
        this.audit.action = true
        this.audit.form.status = 1
        this.audit.rules = this.audit.passed_rules
      }
      this.audit.visible = true
      this.audit.form.endorse_id = endorse.id
    },
    handleGroupEndorseSubmit() {
      this.$refs.auditForm.validate((valid) => {
        if (valid) {
          this.submitGroupEndorse()
        }
      })
    },
    submitGroupEndorse() {
      const loading = Loading.service()
      groupEndorseProcess(this.audit.form.endorse_id, this.audit.form)
        .then(() => {
          this.$message.success('操作成功')
          this.audit.visible = false
          loading.close()

          this.fetchGroupInsuredEmployeeList()
        })
        .finally(() => {
          loading.close()
        })
    },
    handleFileChanged(f) {
      this.audit.form.endorse_file = f.raw
    }
  }
}
</script>

<style lang="scss" scoped>
.el-button.hidden {
  display: none !important;
}
</style>
