<!--
 * @Descripttion:
 * @Author: Mr. <PERSON><PERSON>
 * @Date: 2021-01-08 11:07:40
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-21 16:03:43
-->
<template>
  <div class="policy-list p-extra-large-x p-extra-large-b">
    <search-panel
      size="small"
      :custom="searchFields"
      :exportable="false"
      @change="(data) => (searchData = data)"
      @command="handleSearch"
    ></search-panel>

    <el-card class="m-extra-large-t" shadow="never">
      <define-table :data="tableData" :cols="cols" :paging="paging" :paging-events="pagingEvents" />
    </el-card>
  </div>
</template>

<script>
import { getHistoryPolicies } from '@/apis/policy'
import { mapGetters } from 'vuex'

const companyBranches = [
  {
    company_id: 1,
    branches: [
      { value: 8, label: '太平洋上海' },
      { value: 13, label: '太平洋广东' },
      { value: 19, label: '太平洋厦门' },
      { value: 27, label: '太平洋重庆' },
      { value: 29, label: '太平洋江阴' }
    ]
  },
  {
    company_id: 10,
    branches: [{ value: 32, label: '中华郑州' }]
  },
  {
    company_id: 2,
    branches: [
      { value: 1, label: '人保成都' },
      { value: 4, label: '人保吉林' },
      { value: 11, label: '人保静安' },
      { value: 12, label: '人保南京' },
      { value: 14, label: '人保四川' },
      { value: 21, label: '人保大连' },
      { value: 22, label: '人保广东' },
      { value: 23, label: '人保鞍分' }
    ]
  },
  {
    company_id: 3,
    branches: [
      { value: 2, label: '平安上海' },
      { value: 3, label: '平安杭州' },
      { value: 6, label: '平安杭州果蔬' },
      { value: 9, label: '平安深圳' },
      { value: 15, label: '平安苏州' },
      { value: 16, label: '平安深圳-11YA' },
      { value: 18, label: '平安深圳-YA' },
      { value: 20, label: '平安航保中心' },
      { value: 24, label: '平安江苏' },
      { value: 25, label: '平安昆明' },
      { value: 33, label: '平安福建' }
    ]
  },
  {
    company_id: 4,
    branches: [
      { value: 7, label: '阳光上海' },
      { value: 10, label: '阳光成都' }
    ]
  },
  {
    company_id: 5,
    branches: [
      { value: 17, label: '华泰东莞-YA' },
      { value: 31, label: '华泰广州' }
    ]
  },
  {
    company_id: 6,
    branches: [{ value: 26, label: '华安湖北' }]
  },
  {
    company_id: 8,
    branches: [{ value: 28, label: '太平浦东' }]
  },
  {
    company_id: 9,
    branches: [{ value: 30, label: '太平浦东' }]
  }
]

export default {
  name: 'PoliciesHistory',
  data() {
    return {
      searchData: {},
      // 表格数据
      tableData: [],
      meta: {},
      companyBranches,
      searchFields: [
        {
          type: 'select',
          valKey: 'flag',
          hintText: '险种',
          options: [
            { value: 'CARGO_CHN', label: '国内货运险' },
            { value: 'CARGO_INT', label: '国际货运险' }
          ]
        },
        {
          type: 'daterange',
          valKey: 'created_at_range'
        },
        {
          type: 'input',
          valKey: 'sys_order_no',
          hintText: '流水号'
        },
        {
          type: 'input',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'select',
          valKey: 'company_id',
          hintText: '保险公司',
          onChangeReset: 'company_branch_id',
          options: [
            { value: 1, label: '太平洋' },
            { value: 2, label: '人保' },
            { value: 3, label: '平安' },
            { value: 4, label: '阳光' },
            { value: 5, label: '华泰' },
            { value: 6, label: '华安' },
            { value: 7, label: '国寿财险' },
            { value: 8, label: '太平' },
            { value: 9, label: '中意' },
            { value: 10, label: '中华' }
          ]
        },
        {
          type: 'select',
          valKey: 'company_branch_id',
          hintText: '出单公司',
          options: []
        },
        {
          type: 'input',
          valKey: 'applicant_name',
          hintText: '投保人'
        },
        {
          type: 'input',
          valKey: 'insured_name',
          hintText: '被保险人'
        },
        {
          type: 'select',
          valKey: 'status',
          hintText: '保单状态',
          options: [
            { value: 0, label: '暂存单' },
            { value: 1, label: '已提交' },
            { value: 2, label: '审核中' },
            { value: 5, label: '已出单' },
            { value: 6, label: '已退保' },
            { value: 7, label: '已作废' },
            { value: 8, label: '批改中' },
            { value: 9, label: '退保申请中' },
            { value: 10, label: '已退回' },
            { value: 11, label: '待确认' }
          ]
        }
      ],
      // 表格列配置
      cols: [
        {
          label: '保单号/流水号',
          width: 180,
          fixed: 'left',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label class="text-blue">{scoped.row.policy_no ? scoped.row.policy_no : '尚未出单 ~'}</label>
                  <br />
                  <small>{scoped.row.order_no}</small>
                </div>
              )
            }
          }
        },
        { prop: 'type', label: '险种', width: '100' },
        { prop: 'company_branch_name', label: '出单公司', width: '100' },
        {
          prop: 'insured',
          label: '投保人/被保人',
          width: '180',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.policyholder}</label>
                  <br />
                  <small>{scoped.row.insured ? scoped.row.insured : ''}</small>
                </div>
              )
            }
          }
        },
        {
          label: '保费/保额',
          width: '180',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>¥{scoped.row.premium}</label>
                  <br />
                  <small>¥{scoped.row.coverage}</small>
                </div>
              )
            }
          }
        },
        { prop: 'user.name', label: '投保用户', width: '150' },
        { prop: 'submitted_at', width: '180', label: '投保时间' },
        {
          label: '状态',
          fixed: 'right',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.status_text}</label>
                </div>
              )
            }
          }
        },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          width: '80',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link onClick={() => this.handleTable('view', scoped.row)} class="text-blue">
                    详情
                  </el-link>
                </div>
              )
            }
          }
        }
      ],
      // 分页器 不传递 -> 没有分页
      paging: {
        currentPage: 1,
        pageSize: 15,
        layout: 'prev, pager, next, jumper, total',
        total: 0,
        align: 'left',
        background: true
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page
          this.fetchPolicies()
        }
      }
    }
  },
  computed: {
    ...mapGetters('auth', ['user'])
  },
  watch: {
    searchData: {
      deep: true,
      handler() {
        this.loadCompanyBranches()
      }
    }
  },
  created() {
    this.fetchPolicies()
    this.loadCompanyBranches()
  },
  methods: {
    handleSearch(cmd, data) {
      this.searchData = data
      this.paging.page = 1

      this.fetchPolicies()
    },
    loadCompanyBranches() {
      const branches = []

      if (this.searchData?.company_id === undefined || !this.searchData?.company_id) {
        this.companyBranches.forEach((com) => {
          branches.push(...com.branches)
        })
      } else {
        this.companyBranches
          .filter((e) => e.company_id === this.searchData.company_id)
          .forEach((com) => {
            branches.push(...com.branches)
          })
      }

      this.assignSelectOptions('company_branch_id', branches)
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    },
    fetchPolicies() {
      getHistoryPolicies({
        filter: this.searchData,
        page: this.paging.page
      }).then((r) => {
        this.tableData = r.data
        this.paging.currentPage = r.meta.current_page
        this.paging.pageSize = r.meta.per_page
        this.paging.total = r.meta.total
      })
    },
    handlePolicySearch(val) {
      this.searchData = Object.assign({}, this.searchData, val)

      this.fetchPolicies()
    },
    handleTable(type, row) {
      switch (type) {
        case 'view':
          this.$router.open({ name: 'PoliciesHistoryDetails', params: { id: row.id } })
          break
        default:
          break
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.policy-list {
  width: 100%;
  overflow: auto;
  /deep/ .table-wrap {
    flex: 1;
    overflow: hidden;
    .el-card__body {
      padding-top: 0;
      padding-bottom: 0;
      overflow: auto;
    }
  }
}
</style>
