<!--
 * @Descripttion: 
 * @version: 
 * @Author: yanb
 * @Date: 2022-06-07 16:15:49
 * @LastEditors: yanb
 * @LastEditTime: 2023-02-17 16:14:03
-->
<template>
  <div class="p-extra-large-x p-extra-large-b w-100">
    <offline-policy-create-form :model.sync="data" />
  </div>
</template>
<script>
import OfflinePolicyCreateForm from '@/components/policy/OfflinePolicyCreateForm'

export default {
  name: 'CreateOfflinePolicy',
  components: {
    OfflinePolicyCreateForm
  },
  data() {
    return {
      data: {}
    }
  },
  created() {
    this.data = {}
  }
}
</script>
