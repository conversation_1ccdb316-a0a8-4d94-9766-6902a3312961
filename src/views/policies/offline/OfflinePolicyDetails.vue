<template>
  <SimpleContainer class="bg-white p-extra-large w-100 d-flex flex-column o-hidden">
    <OfflinePolicyOperationBar :model="policy" @operated="fetchDetail" @toList="offlinePolicies" />
    <DefinePoliciesDetails :data="details"></DefinePoliciesDetails>
  </SimpleContainer>
</template>

<script>
import OfflinePolicyOperationBar from '@/components/policy/OfflinePolicyOperationBar'
import { getOfflinePolicy } from '@/apis/policy'

export default {
  name: 'OfflinePolicyDetails',
  components: {
    OfflinePolicyOperationBar
  },
  data() {
    return {
      policy: {}
    }
  },
  computed: {
    statusText() {
      const _map = {
        0: '未提交',
        1: '已提交',
        2: '审核中',
        3: '已支付',
        4: '已审核',
        5: '已出单',
        6: '已退保',
        7: '已作废',
        8: '批改中',
        9: '退保中',
        10: '已退回',
        11: '待确认',
        12: '待支付'
      }
      return function (status) {
        return _map[status] || ''
      }
    },
    details() {
      const _data = {
        title: '保单详情',
        data: [
          {
            title: '投保信息',
            groups: [
              { label: '险类', value: this.policy?.insurance?.name },

              { label: '险种', value: this.policy?.category?.name },
              {
                label: '保单号',
                value: this.policy?.policy_no
              },
              {
                label: '保单文件',
                value: '点击查看',
                row: true,
                isLink: true,
                target: '_blank',
                to: this.policy?.policy_file
              },
              {
                label: '保险公司',
                value: this.policy?.company?.name
              },
              { label: '出单公司', value: this.policy?.company_branch?.name },
              { label: '投保渠道', value: this.policy?.channel?.name },
              { label: '保单起始日期', value: this.policy?.start_at },
              { label: '保单终止日期', value: this.policy?.end_at },
              { label: '业务员', value: this.policy?.salesman?.name },
              { label: '保单状态', value: this.policy?.status_text },
              { label: '投保人', value: this.policy?.policyholder },
              { label: '被保人', value: this.policy?.insured },
              { label: '标的名称', value: this.policy?.subject },
              { label: '投保时间', value: this.policy?.insure_date },
              { label: '保险金额', value: this.policy?.coverage },
              { label: '保额币种', value: this.policy?.coverage_currency?.name },
              { label: '产品来源', value: this.policy?.product_from == 1 ? '自有产品' : '非自有产品' },
              { label: '保费', value: this.policy?.premium },
              { label: '经纪费比例', value: this.policy?.poundage_rate },
              { label: '保费支付情况', value: this.policy?.premium_pay_type == 1 ? '保费已付' : '保费未付' },
              { label: '结算方式', value: this.policy?.settlement_type == 1 ? '含税保费' : '不含税保费' },
              { label: '业务类型', value: this.policy?.business_type?.name }
            ]
          },
          {
            _id: 'policy',
            title: '其他信息',
            groups: []
          }
        ]
      }
      if (this.policy?.business_type?.value == 3) {
        _data.data[0].groups.push({
          label: '业务来源',
          value: this.policy?.business_from?.name
        })
      } else {
        _data.data[0].groups.push({
          label: this.policy?.business_type?.value == 1 ? '投保用户' : '代理商',
          value: this.policy?.user?.name
        })
      }
      if (this.policy?.business_type?.value == 2) {
        _data.data[0].groups.push({
          label: '佣金比例',
          value: this.policy?.commission_rate
        })
      }
      if (this.policy?.premium_pay_type == 1) {
        _data.data[0].groups.push(
          {
            label: '保费支付类型',
            value: this.policy?.payee_type == 1 ? '客户支付保险公司' : '客户支付平台'
          },
          {
            label: '付款水单',
            value: '点击查看',
            row: true,
            isLink: true,
            target: '_blank',
            to: this.policy?.memo_file
          }
        )
      }
      let _groups = []
      if (this.policy?.custom_columns) {
        _groups = this.policy?.custom_columns.map((item) => {
          const isFile = item.type === 'file' || item.type === '_file'
          return {
            label: item.title,
            value: isFile ? '点击查看' : item.value,
            isLink: isFile,
            target: '_blank',
            row: true,
            to: isFile ? item.value : undefined
          }
        })
        _data.data.map((item) => {
          if (item._id && item._id === 'policy') {
            item.groups = _groups
          }
        })
      } else {
        _data.data = _data.data.filter((item) => !item._id)
      }
      return _data
    }
  },
  created() {
    this.fetchDetail()
  },
  methods: {
    fetchDetail() {
      getOfflinePolicy(this.$route.params.id).then((r) => {
        r.data.custom_columns = JSON.parse(r.data.custom_columns)
        this.policy = r.data
      })
    },
    offlinePolicies() {
      this.$router.push({
        name: 'OfflinePolicies'
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
