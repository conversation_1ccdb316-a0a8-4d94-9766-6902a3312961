<!--
 * @Descripttion:
 * @version:
 * @Author: yanb
 * @Date: 2022-06-01 15:53:15
 * @LastEditors: yanb
 * @LastEditTime: 2024-06-28 13:59:59
-->
<template>
  <div class="channels p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <SearchPanel
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
      size="small"
    ></SearchPanel>

    <el-alert type="error" class="m-extra-large-t" :closable="false">
      <span>
        当前有
        <el-tag type="danger" effect="dark"> {{ submittedCount }} </el-tag>
        单<span class="hover-cursor" @click="handleFilterStatus(1)">已提交</span>，请检查保单并及时处理！
      </span>
    </el-alert>

    <el-card shadow="never" class="m-extra-large-t">
      <div class="d-flex" v-can="{ name: 'policies.offline.multiple-handle' }">
        <el-button type="primary" @click="handlePolicies" style="margin-bottom: 15px">提交处理</el-button>
        <el-button type="primary" @click="allCheckout" style="margin-bottom: 15px">全部处理</el-button>
      </div>
      <define-table
        :data="data"
        :cols="cols"
        :paging="paging"
        :paging-events="pagingEvents"
        :events="tableEvents"
        :rowKey="getRowKeys"
      />
    </el-card>
  </div>
</template>

<script>
import { getOfflinePolicies, policyOfflineExport, countPoliciesStatuses } from '@/apis/policy'
// import { getPlatformProductCompanies } from '@/apis/platform_product'
import { getCompaniesDict } from '@/apis/company'
import { Loading } from 'element-ui'

export default {
  name: 'OfflinePolicies',
  data() {
    return {
      statusesCount: [],
      statusList: [
        { label: '暂存单', value: 0 },
        { label: '已提交', value: 1 },
        { label: '审核中', value: 2 },
        { label: '已支付', value: 3 },
        { label: '已审核', value: 4 },
        { label: '已出单', value: 5 },
        { label: '已退保', value: 6 },
        { label: '已作废', value: 7 },
        { label: '批改中', value: 8 },
        { label: '退保中', value: 9 },
        { label: '已退回', value: 10 },
        { label: '待确认', value: 11 },
        { label: '待支付', value: 12 }
      ],
      searchFields: [
        {
          type: 'select',
          valKey: 'company_id',
          hintText: '保险公司',
          onChangeReset: 'company_branch_id',
          options: []
        },
        {
          type: 'select',
          valKey: 'company_branch_id',
          hintText: '出单公司',
          options: []
        },
        {
          type: 'input',
          valKey: 'policyholder',
          hintText: '投保人'
        },
        {
          type: 'input',
          valKey: 'insured',
          hintText: '被保人'
        },
        {
          type: 'textarea',
          valKey: 'keywords',
          hintText: '保单号'
        },
        {
          type: 'select',
          valKey: 'status',
          hintText: '状态',
          options: [
            { value: 1, label: '已提交' },
            { value: 5, label: '已出单' },
            { value: 6, label: '已退保' },
            { value: 7, label: '已作废' },
            { value: 8, label: '批改中' },
            { value: 9, label: '退保申请中' },
            { value: 10, label: '已退回' }
          ]
        },
        {
          type: 'input',
          valKey: 'username',
          hintText: '客户名称'
        },
        {
          type: 'daterange',
          valKey: 'submitted_at_range',
          hintText: '投保'
        }
      ],
      data: [],
      cols: [
        { align: 'center', type: 'selection', reserveSelection: true },
        {
          label: '保单号',
          prop: 'policy_no'
        },
        {
          label: '出单公司',
          prop: 'company_branch.name'
        },
        {
          label: '险类',
          prop: 'policy_offline.category.name'
        },
        {
          label: '险种',
          prop: 'policy_offline.insurance.name'
        },
        {
          label: '保费',
          prop: 'premium'
        },
        {
          label: '投保用户',
          width: 180,
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.policy_offline.business_type === 3) {
                return <span class="text-warning">非自有业务</span>
              }
              return scoped.row.user.is_agent === 1 ? (
                <span class="text-primary">{scoped.row.user.name}</span>
              ) : (
                scoped.row.user.name
              )
            }
          }
        },
        {
          label: '状态',
          width: 80,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{this.statusList[scoped.row.status]?.label}</label>
                </div>
              )
            }
          }
        },
        {
          label: '提交时间/出单时间',
          width: 140,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.submitted_at}</label>
                  <br />
                  <small>{scoped.row.issued_at}</small>
                </div>
              )
            }
          }
        },
        {
          label: '操作',
          width: 85,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    size="small"
                    type="text"
                    v-can={{ name: 'policies.offline.show' }}
                    onClick={() => this.policyDetails(scoped.row.id)}
                  >
                    详情
                  </el-button>
                </div>
              )
            }
          }
        }
      ],
      paging: {
        currentPage: 1,
        pageSizes: [15, 30, 50, 100],
        pageSize: 15,
        layout: 'sizes, prev, pager, next, jumper, total',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page
          this.fetchPolicies()
        },
        sizeChange: (size) => {
          this.paging.pageSize = size
          this.fetchPolicies()
        }
      },
      multipleSelection: [],
      tableEvents: {
        'selection-change': (val) => {
          this.multipleSelection = val
          console.log(this.multipleSelection)
        }
      },
      searchData: {}
    }
  },
  watch: {
    searchData: {
      immediate: true,
      deep: true,
      handler(value, old) {
        if (old?.company_id !== value?.company_id) {
          this.loadCompanyBranches()
        }
      }
    }
  },
  created() {
    getCompaniesDict().then((r) => {
      this.rawCompanies = r.data

      this.loadCompanies()
      this.loadCompanyBranches()
    })

    this.fetchPolicies()
    this.getPoliciesStatuses()
  },
  computed: {
    submittedCount() {
      const s = this.statusesCount.find((e) => e.status === 1)
      return s?.statusCount || 0
    }
  },
  methods: {
    getRowKeys(row) {
      return row.id
    },
    fetchPolicies(filter = {}) {
      const loading = Loading.service()
      filter = Object.assign({ type: 6 }, filter)
      getOfflinePolicies({
        per_page: this.paging.pageSize,
        page: this.paging.page,
        filter: Object.assign({}, this.searchData, filter)
      })
        .then((r) => {
          this.data = r.data

          this.paging.currentPage = r.meta.current_page
          this.paging.pageSize = r.meta.per_page
          this.paging.total = r.meta.total
        })
        .finally(() => loading.close())
    },
    handleSearchPanel(command, data) {
      this.searchData = data
      this.paging.page = 1
      if (command === 'export') {
        data = Object.assign({ type: 6 }, data)
        window.open(policyOfflineExport({ filter: data }))
      } else {
        this.fetchPolicies()
        this.getPoliciesStatuses()
      }
    },
    getPoliciesStatuses() {
      countPoliciesStatuses({
        filter: { type: 6 }
      }).then((r) => (this.statusesCount = r.data))
    },
    handleFilterStatus(status) {
      this.fetchPolicies({ status })
    },
    loadCompanies() {
      const options = this.rawCompanies.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('company_id', options)
    },
    loadCompanyBranches() {
      let options = []
      if (this.searchData?.company_id !== '' && this.searchData?.company_id !== undefined) {
        const company = this.rawCompanies.find((e) => e.id === this.searchData?.company_id)
        if (company) {
          options = company.branches.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      } else {
        this.rawCompanies.forEach((e) => {
          e?.branches.forEach((e) => {
            options.push({
              label: e.name,
              value: e.id
            })
          })
        })
      }

      this.assignSelectOptions('company_branch_id', options)
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    },
    policyDetails(id) {
      this.$open({
        name: 'OfflinePolicyDetails',
        params: {
          id: id
        }
      })
    },
    handlePolicies() {
      if (!this.multipleSelection.length) {
        this.$message({
          type: 'warning',
          message: '请至少选择一项'
        })
        return
      }
      this.$router.push({
        name: 'HandleOfflinePolicies',
        query: {
          ids: this.multipleSelection.map((e) => e.id).join(',')
        }
      })
    },
    allCheckout() {
      this.$router.push({
        name: 'HandleOfflinePolicies',
        query: {
          all_checkout: 1,
          ...this.searchData
        }
      })
    }
  }
}
</script>
