<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <el-card shadow="never" class="box-card-options" :body-style="{ padding: '20px' }">
      <div class="d-flex justify-content-between">
        <h3>已选单据：{{ paging.total }}单 保单数：{{ paging.total }}单</h3>
        <div>
          <el-button type="primary" v-can="{ name: 'policies.offline.multiple-handle' }" @click="dialogVisible = true"
            >确认处理</el-button
          >
        </div>
      </div>
      <DefineTable :cols="cols" :paging="paging" :paging-events="pagingEvents" :data="data"></DefineTable>
    </el-card>

    <el-dialog title="提示" :visible.sync="dialogVisible" width="30%">
      <el-form ref="form" label-position="left" label-width="100px" :model="form" :rules="rules">
        <el-form-item label="处理类型" prop="type">
          <el-select placeholder="请选择处理类型" v-model="form.type" class="w-100">
            <el-option v-for="type in handleTypes" :key="type.value" :label="type.label" :value="type.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          v-can="{ name: 'policies.offline.multiple-handle' }"
          :disabled="isDisabled"
          @click="handle"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getMultipleOfflinePolicies, multipleHandleOfflinePolicy } from '@/apis/policy'
import { Loading } from 'element-ui'

export default {
  name: 'HandleOfflinePolicies',
  data() {
    return {
      statusList: [
        { label: '暂存单', value: 0 },
        { label: '已提交', value: 1 },
        { label: '审核中', value: 2 },
        { label: '已支付', value: 3 },
        { label: '已审核', value: 4 },
        { label: '已出单', value: 5 },
        { label: '已退保', value: 6 },
        { label: '已作废', value: 7 },
        { label: '批改中', value: 8 },
        { label: '退保中', value: 9 },
        { label: '已退回', value: 10 },
        { label: '待确认', value: 11 },
        { label: '待支付', value: 12 }
      ],
      form: {
        type: ''
      },
      dialogVisible: false,
      isDisabled: false,
      paging: {
        currentPage: 1,
        pageSizes: [15, 30, 50, 100],
        pageSize: 15,
        layout: 'sizes, prev, pager, next, jumper, total',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page
          this.fetchPolicies()
        },
        sizeChange: (size) => {
          this.paging.pageSize = size
          this.fetchPolicies()
        }
      },

      cols: [
        {
          label: '流水号',
          width: 180,
          prop: 'order_no'
        },
        {
          label: '保单号',
          prop: 'policy_no'
        },
        {
          label: '出单公司',
          prop: 'company_branch.name'
        },
        {
          label: '险类',
          prop: 'policy_offline.category.name'
        },
        {
          label: '险种',
          prop: 'policy_offline.insurance.name'
        },
        {
          label: '保费',
          prop: 'premium'
        },
        {
          label: '投保用户',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.policy_offline.business_type === 3) {
                return <span class="text-warning">非自有业务</span>
              }
              return scoped.row.user.is_agent === 1 ? (
                <span class="text-primary">{scoped.row.user.name}</span>
              ) : (
                scoped.row.user.name
              )
            }
          }
        },
        {
          label: '状态',
          width: 80,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{this.statusList[scoped.row.status]?.label}</label>
                </div>
              )
            }
          }
        },
        {
          label: '投保时间/出单时间',
          width: 140,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.submitted_at}</label>
                  <br />
                  <small>{scoped.row.issued_at}</small>
                </div>
              )
            }
          }
        },
        // JSX 插槽
        {
          label: '操作',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-link
                    type="primary"
                    onClick={() => {
                      this.remove(scoped.row)
                    }}
                  >
                    移除
                  </el-link>
                </div>
              )
            }
          }
        }
      ],
      data: [],
      rules: {
        type: [{ required: true, message: '请选择处理类型', trigger: ['blur', 'change'] }]
      }
    }
  },
  created() {
    this.fetchPolicies()
  },
  computed: {
    handleTypes() {
      return [
        {
          label: '批量生效',
          value: 1
        },
        {
          label: '批量删除',
          value: 2
        }
      ]
    },
    ids() {
      return this.$route?.query?.ids?.split(',') ?? []
    }
  },
  methods: {
    fetchPolicies() {
      const loading = Loading.service()
      const data = { ...this.$route?.query }
      delete data.all_checkout
      delete data.ids

      getMultipleOfflinePolicies({
        ids: this.$route?.query?.ids?.split(','),
        all_checkout: this.$route?.query?.all_checkout,
        per_page: this.paging.pageSize,
        page: this.paging.page,
        filter: Object.assign({ type: 6 }, data)
      })
        .then((r) => {
          this.data = r.data

          this.paging.currentPage = r.meta.current_page
          this.paging.pageSize = r.meta.per_page
          this.paging.total = r.meta.total
          loading.close()
        })
        .finally(() => loading.close())
    },
    remove(row) {
      this.data.forEach((item, index) => {
        if (item.id === row.id) {
          this.data.splice(index, 1)
        }
      })
    },
    handle() {
      this.isDisabled = true
      const loading = Loading.service()
      var form = Object.assign(this.form, this.$route?.query)
      if (this.ids.length > 0) {
        form = Object.assign(form, { ids: this.ids })
      }
      if (form.type == 2) {
        this.$confirm('当前操作为批量删除,请确定是否进行继续？', '确认信息', {
          distinguishCancelAndClose: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        })
          .then(() => {
            this.submit(form, loading)
          })
          .catch((action) => {
            if (action === 'cancel') {
              loading.close()
              this.isDisabled = false
            }
          })
        return
      }
      this.submit(form, loading)
    },
    submit(form, loading) {
      multipleHandleOfflinePolicy(form)
        .then(() => {
          loading.close()
          this.isDisabled = false
          this.$router.push({
            name: 'OfflinePolicies'
          })
          this.$message({
            type: 'success',
            message: '数据已提交至处理队列,处理结果请查看登录账号邮箱'
          })
        })
        .finally(() => {
          loading.close()
          this.isDisabled = false
        })
    }
  }
}
</script>

<style scoped>
.feePayment {
  padding: 0 20px;
}
</style>
