<!--
 * @Descripttion: 
 * @version: 
 * @Author: yanb
 * @Date: 2022-06-07 16:15:49
 * @LastEditors: yanb
 * @LastEditTime: 2023-02-17 16:39:26
-->
<template>
  <div class="p-extra-large-x p-extra-large-b w-100">
    <offline-policy-update-form :model.sync="data" />
  </div>
</template>
<script>
import { getOfflinePolicyFormData } from '@/apis/policy'
import OfflinePolicyUpdateForm from '@/components/policy/OfflinePolicyUpdateForm'

export default {
  name: 'UpdateOfflinePolicy',
  components: {
    OfflinePolicyUpdateForm
  },
  data() {
    return {
      data: {}
    }
  },
  created() {
    getOfflinePolicyFormData(this.$route.params.id).then((r) => {
      this.data = r.data
    })
  }
}
</script>
