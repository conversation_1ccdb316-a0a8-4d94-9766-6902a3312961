<template>
  <SimpleContainer title="参保人员列表" class="p-extra-large-x m-extra-large-b w-100 d-flex flex-column o-hidden">
    <el-card shadow="never" v-if="this.policyGroup">
      <div class="d-flex w-100" slot="header">
        <el-input
          class="m-mini-r"
          :style="{ width: '300px' }"
          placeholder="关键词：如在保人身份证号、姓名等"
          v-model="filter.search"
        />
        <el-button icon="el-icon-search" type="primary" @click="fetchGroupInsuredEmployeeList(filter)">
          搜索
        </el-button>
        <i class="flex-fill m-mini-r" />
        <el-button-group>
          <el-button
            type="primary"
            icon="fa fa-list"
            v-can="{ name: 'policies.group.endorses.employees.index' }"
            @click="$router.push({ name: 'GroupEndorse', params: { policyGroupId: policyGroup.id } })"
          >
            人员批单
          </el-button>
          <el-button
            type="primary"
            v-can="{ name: 'policies.group.employees.index' }"
            icon="fa fa-download"
            @click="download"
          >
            导出人员
          </el-button>
        </el-button-group>
      </div>

      <div>
        <h3>被保险人：{{ this.policyGroup.policy.insured }} | 保单号：{{ this.policyGroup.policy.policy_no }}</h3>
        <h4>
          在保人员：{{ this.policyGroup.insured_employee_count }} 人，保费：{{ this.policyGroup.policy.user_premium }}
        </h4>
      </div>
      <el-divider />
      <div v-if="policyGroup.policy.status !== 5" class="info-box">
        <el-alert
          center
          v-if="[1, 2].includes(policyGroup.policy.status) && policyGroup.policy.is_owned"
          title="保单审批中"
          description="请进入【保单详情】点击右上方【发送邮件】确认投保已通知到产品报备邮箱"
          type="warning"
          effect="dark"
          show-icon
          :closable="false"
        >
        </el-alert>
        <el-alert
          center
          v-if="policyGroup.policy.status === 4 && policyGroup.policy.is_owned"
          title="保单审批中"
          description="请进入【保单详情】点击右上方【审核】确认保单生效"
          type="warning"
          effect="dark"
          show-icon
          :closable="false"
        >
        </el-alert>
        <el-alert
          center
          v-if="policyGroup.policy.status === 8 && policyGroup.policy.is_owned"
          title="保单批改中"
          description="请进入【人员批单】进行审批"
          type="warning"
          effect="dark"
          show-icon
          :closable="false"
        >
        </el-alert>
      </div>
      <define-table :data="employees" :cols="cols" :paging="paging" :paging-events="pagingEvents" />
    </el-card>
    <PolicyGroupEmployeeHistory
      :visible.sync="historyBox.visible"
      :history="this.historyBox.data"
    ></PolicyGroupEmployeeHistory>
  </SimpleContainer>
</template>

<script>
import SimpleContainer from '../../components/globals/SimpleContainer'
import { getGroupInsuredEmployeeList, getGroupPolicyInfo } from '../../apis/policy'
import PolicyGroupEmployeeHistory from '../../components/policy/PolicyGroupEmployeeHistory'
import { tokenKey } from '@/config'

export default {
  name: 'GroupEmployee',
  components: { PolicyGroupEmployeeHistory, SimpleContainer },
  props: {
    policyGroupId: {
      type: Number,
      default: () => -1
    }
  },
  data() {
    return {
      employees: [],
      policyGroup: undefined,
      filter: {
        search: ''
      },
      historyBox: {
        visible: false,
        data: {
          name: '',
          idcard_no: '',
          mobile: ''
        }
      },
      cols: [
        {
          label: '姓名',
          prop: 'name'
        },
        {
          width: '150',
          label: '身份证号码',
          prop: 'idcard_no'
        },
        {
          label: '手机号码',
          prop: 'mobile'
        },
        {
          label: '职位类别',
          prop: 'job_code'
        },
        {
          label: '职位名称',
          prop: 'job_name'
        },
        {
          label: '添加时间',
          prop: 'complete_time'
        },
        {
          label: '标识',
          scopedSlots: {
            default: (scoped) => {
              let states = {
                0: '新增',
                1: '替换',
                2: '批减'
              }
              return states[scoped.row.action]
            }
          }
        },
        {
          label: '状态',
          scopedSlots: {
            default: (scoped) => {
              let states = {
                0: '无效',
                1: '未提交',
                2: '已提交',
                3: '审核中',
                4: '已生效',
                5: '已替换',
                6: '已批减'
              }
              return states[scoped.row.status]
            }
          }
        },
        {
          label: '操作',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.original_employee != undefined) {
                return (
                  <el-button
                    v-can={{ name: 'policies.group.show' }}
                    onClick={() => this.displayHistory(scoped.row.original_employee)}
                    type="primary"
                    size="mini"
                    icon="fa fa-history"
                  >
                    原始人员
                  </el-button>
                )
              }
            }
          }
        }
      ],
      paging: {
        currentPage: 1,
        pageSize: 15,
        layout: ' prev, pager, next, jumper, total',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.fetchGroupInsuredEmployeeList(this.filter, page)
        }
      }
    }
  },
  created() {
    this.fetchGroupInsuredEmployeeList()

    getGroupPolicyInfo(this.$route.params.policyGroupId).then((r) => {
      this.policyGroup = r.data
    })
  },
  methods: {
    fetchGroupInsuredEmployeeList(filter = {}, page = 1) {
      getGroupInsuredEmployeeList(this.$route.params.policyGroupId, {
        filter: filter,
        page: page
      }).then((r) => {
        this.employees = r.data

        this.paging.currentPage = r.current_page
        this.paging.pageSize = r.per_page
        this.paging.total = r.total
      })
    },
    displayHistory(employee) {
      this.historyBox.visible = true
      this.historyBox.data = employee
    },
    download() {
      let baseUrl = process.env.VUE_APP_BASE_API
      if (!process.env.VUE_APP_BASE_API.startsWith('http')) {
        baseUrl = `${window.location.origin}${baseUrl}`
      }

      let link =
        `${baseUrl}policies/group/employees/download/${this.policyGroup.id}?token=` +
        window.localStorage.getItem(tokenKey)

      window.open(link, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.info-box {
  padding-bottom: 15px;
}
</style>
