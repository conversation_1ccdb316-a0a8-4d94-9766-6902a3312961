<!--
 * @Descripttion: 国内险单票管理
 * @Author: Mr. <PERSON><PERSON>
 * @Date: 2020-10-19 12:30:41
 * @LastEditors: yanb
 * @LastEditTime: 2024-05-06 14:56:51
-->

<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <SearchPanel
      size="small"
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
    ></SearchPanel>

    <el-card class="m-extra-large-t" shadow="never">
      <div class="d-flex w-100" slot="header">
        <el-button type="primary" icon="fas fa-file-download" @click="handleDownloadFiles">下载保单</el-button>
      </div>
      <DefineTable :data="tableData" :cols="cols" :paging="paging" :paging-events="pagingEvents" />
    </el-card>
  </div>
</template>

<script>
import { getMixedPolicies, buildMixedExportHref, buildDownloadFilesHref } from '@/apis/policy'
// import { getPlatformProductCompanies } from '@/apis/platform_product'
import { getCompaniesDict } from '@/apis/company'
import { getOfflineProductCategories } from '@/apis/product'
import { getSales } from '@/apis/admin'
import { mapGetters } from 'vuex'
import { Loading } from 'element-ui'

export default {
  name: 'PoliciesMixed',
  data() {
    return {
      additional: {
        statuses: {}
      },
      // 搜索字段
      searchFields: [
        {
          type: 'select',
          valKey: 'types',
          hintText: '险种',
          isMultiple: true,
          options: []
        },
        {
          type: 'daterange',
          valKey: 'submitted_at_range',
          hintText: '投保'
        },
        {
          type: 'daterange',
          valKey: 'issued_at_range',
          hintText: '出单'
        },
        {
          type: 'select',
          valKey: 'company_id',
          hintText: '保险公司',
          onChangeReset: 'company_branch_id',
          options: []
        },
        {
          type: 'select',
          valKey: 'company_branch_id',
          isMultiple: true,
          hintText: '出单公司',
          options: []
        },
        {
          type: 'textarea',
          valKey: 'keywords',
          hintText: '保单号,流水号'
        },
        {
          type: 'input',
          valKey: 'series_no',
          hintText: '发票号/运单号/提单号/发票号/货物名称'
        },
        {
          type: 'input',
          valKey: 'policyholder',
          hintText: '投保人'
        },
        {
          type: 'input',
          valKey: 'insured',
          hintText: '被保人'
        },
        {
          type: 'input',
          valKey: 'username',
          hintText: '投保用户'
        },
        {
          type: 'input',
          valKey: 'sticky_note',
          hintText: '工作编号'
        },
        {
          type: 'select',
          valKey: 'is_premium_sync',
          hintText: '保费同步',
          options: [
            { value: 0, label: '否' },
            { value: 1, label: '是' }
          ]
        },
        {
          type: 'select',
          valKey: 'is_allowed_invoice',
          hintText: '是否允许开票',
          options: [
            { value: 0, label: '否' },
            { value: 1, label: '是' }
          ]
        },
        {
          type: 'select',
          valKey: 'status',
          hintText: '状态',
          options: [
            { label: '已提交', value: 1 },
            { label: '审核中', value: 2 },
            { label: '已支付', value: 3 },
            { label: '审核完成', value: 4 },
            { label: '已出单', value: 5 },
            { label: '已退保', value: 6 },
            { label: '已作废', value: 7 },
            { label: '批改中', value: 8 },
            { label: '退保申请中', value: 9 },
            { label: '已退回', value: 10 },
            { label: '待确认', value: 11 },
            { label: '待支付', value: 12 },
            { label: '已退回(补充资料)', value: 13 }
          ]
        },
        {
          type: 'select',
          valKey: 'platform',
          hintText: '产品属性',
          options: [
            { label: '自有产品', value: 1 },
            { label: '非自有产品', value: 2 }
          ]
        },
        {
          type: 'select',
          valKey: 'salesman_id',
          hintText: '业务员',
          options: []
        }
      ],
      // 表格数据
      tableData: [],
      meta: {},
      // 表格列配置
      cols: [
        {
          label: '险种',
          width: '120',
          scopedSlots: {
            default: (scoped) => {
              const types = {
                1: '国内货运险',
                2: '国际货运险',
                3: '单车责任险',
                4: '其他险',
                5: '雇主责任险',
                6: '线下录入保单',
                7: '跨境电商险'
              }

              return scoped.row.type == 6 ? scoped.row.offline_type : types[scoped.row.type]
            }
          }
        },
        { prop: 'company_branch.name', label: '出单公司', width: '150' },
        {
          label: '被保险人/投保人',
          width: '180',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.insured}</label>
                  <br />
                  <small>{scoped.row.policyholder}</small>
                </div>
              )
            }
          }
        },
        {
          label: '保单号/流水号',
          width: '200',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.policy_no ? scoped.row.policy_no : '尚未出单 ~'}</label>
                  <br />
                  <small>{scoped.row.order_no}</small>
                </div>
              )
            }
          }
        },
        { prop: 'premium', label: '保费' },
        {
          label: '投保时间/出单时间',
          width: 150,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.submitted_at}</label>
                  <br />
                  <small>{scoped.row.issued_at}</small>
                </div>
              )
            }
          }
        },
        {
          label: '投保用户',
          width: 180,
          scopedSlots: {
            default: (scoped) => {
              return scoped.row.user.platform_id === this.admin.platform.id ? scoped.row.user.name : '总平台'
            }
          }
        },
        {
          label: '状态',
          width: 80,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{this.additional.statuses[scoped.row.status]}</label>
                </div>
              )
            }
          }
        },
        {
          label: '操作',
          align: 'center',
          fixed: 'right',
          width: '140',
          scopedSlots: {
            default: (scoped) => {
              const routes = {
                1: { name: 'PoliciesDomesticDetails', params: { id: scoped.row.id } },
                2: { name: 'PoliciesIntlDetails', params: { id: scoped.row.id } },
                3: { name: 'PoliciesLbtDetails', params: { id: scoped.row.id } },
                4: { name: 'PoliciesOtherDetails', params: { id: scoped.row.id } },
                5: { name: 'PoliciesGroupDetails', params: { policyGroupId: scoped.row.policy_group_id } },
                6: { name: 'OfflinePolicyDetails', params: { id: scoped.row.id } },
                7: { name: 'PoliciesCbecDetails', params: { id: scoped.row.id } }
              }
              return (
                <el-button
                  onClick={() => this.$open(routes[scoped.row.type])}
                  type="primary"
                  disabled={scoped.row.type === 5 && scoped.row.status === 0}
                  plain
                  size="small"
                  icon="el-icon-view"
                >
                  详情
                </el-button>
              )
            }
          }
        }
      ],
      // 分页器 不传递 -> 没有分页
      paging: {
        currentPage: 1,
        pageSizes: [15, 30, 50, 100],
        pageSize: 15,
        layout: 'sizes, prev, pager, next, jumper, total',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page
          this.getTableList()
        },
        sizeChange: (size) => {
          this.paging.pageSize = size
          this.getTableList()
        }
      },
      searchData: {},
      rawCompanies: [],
      offlineCategories: []
    }
  },
  computed: {
    ...mapGetters('auth', ['admin'])
  },
  watch: {
    searchData: {
      immediate: true,
      deep: true,
      handler(value, old) {
        if (old?.company_id !== value?.company_id) {
          this.loadCompanyBranches()
        }
      }
    }
  },
  mounted() {
    getOfflineProductCategories({ is_parent: 0, is_pageable: 0 }).then((r) => {
      this.offlineCategories = r.data

      this.loadCategories()
    })
    getCompaniesDict().then((r) => {
      this.rawCompanies = r.data

      this.loadCompanies()
      this.loadCompanyBranches()
    })

    getSales().then((r) => {
      r.data.push({ name: '自然来源', id: -1 })
      this.assignSelectOptions(
        'salesman_id',
        r.data.map((e) => {
          return { label: e.name, value: e.id }
        })
      )
    })
    this.getTableList()
  },
  methods: {
    handleDownloadFiles() {
      if (this.paging.total > 100) {
        return this.$message.error('单次最多只能下载 100 条数据')
      }

      window.open(buildDownloadFilesHref({ filter: this.searchData, from: 'mixed' }))
    },
    loadCompanies() {
      const options = this.rawCompanies.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('company_id', options)
    },
    loadCompanyBranches() {
      let options = []
      if (this.searchData?.company_id !== '' && this.searchData?.company_id !== undefined) {
        const company = this.rawCompanies.find((e) => e.id === this.searchData?.company_id)
        if (company) {
          options = company.branches.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      } else {
        this.rawCompanies.forEach((e) => {
          e?.branches.forEach((e) => {
            options.push({
              label: e.name,
              value: e.id
            })
          })
        })
      }

      this.assignSelectOptions('company_branch_id', options)
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    },
    handleSearchPanel(command, data) {
      this.searchData = data
      this.paging.page = 1
      if (command === 'export') {
        window.open(buildMixedExportHref({ filter: data }))
      } else {
        this.getTableList()
      }
    },
    getTableList(filter = {}) {
      const loading = Loading.service()
      getMixedPolicies({
        page: this.paging.page,
        per_page: this.paging.pageSize,
        filter: Object.assign({}, this.searchData, filter)
      })
        .then((r) => {
          const { meta, data, additional } = r
          this.meta = meta
          this.tableData = data
          this.additional = additional

          this.paging.currentPage = r.meta.current_page
          this.paging.pageSize = r.meta.per_page
          this.paging.total = r.meta.total
          loading.close()
        })
        .finally(() => loading.close())
    },
    loadCategories() {
      var options = [
        {
          label: '国内货运险',
          value: 1
        },
        {
          label: '国际货运险',
          value: 2
        },
        {
          label: '单车责任险',
          value: 3
        },
        {
          label: '其他险种',
          value: 4
        },
        {
          label: '雇主责任险',
          value: 5
        },
        {
          label: '跨境电商险',
          value: 7
        }
      ]
      options = options.concat(
        this.offlineCategories.map((e) => {
          return {
            label: e.name + '-线下录入',
            value: 'OF-' + e.id
          }
        })
      )
      this.assignSelectOptions('types', options)
    }
  }
}
</script>

<style lang="scss" scoped>
.table-warp {
  margin-top: 20px;
  /deep/ .el-card__body {
    padding: 0;
  }
}
</style>
