<!--
 * @Descripttion:
 * @Author: Mr. <PERSON><PERSON>
 * @Date: 2020-10-22 16:26:03
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2020-10-22 17:25:26
-->
<template>
  <SimpleContainer class="bg-white p-extra-large w-100 d-flex flex-column o-hidden">
    <PolicyGroupOperationBar
      :model="policy"
      @operated="(model) => handlePolicyOperated(model)"
      :policyGroup="policy"
      :productPlatform="this.productPlatform"
    />
    <DefinePoliciesDetails :data="details"></DefinePoliciesDetails>
  </SimpleContainer>
</template>

<script>
import PolicyGroupOperationBar from '@/components/policy/PolicyGroupOperationBar'
import { getGroupPolicy } from '@/apis/policy'
import dayjs from 'dayjs'

export default {
  name: 'GroupDetails',
  components: {
    PolicyGroupOperationBar
  },
  data() {
    return {
      policy: {},
      policyGroupId: -1,
      productPlatform: null,
      certTypes: {
        '-1': '请选择证件类型',
        '01': '身份证',
        '02': '军官证',
        '03': '学生证',
        '04': '台胞证',
        '06': '护照',
        '07': '港澳返乡证',
        '08': '出生证明（未成年人）',
        '09': '营业执照',
        10: '工商登记号',
        11: '组织机构代码',
        13: '统一社会信用代码',
        14: '港澳台居民居住证',
        99: '其他'
      }
    }
  },
  computed: {
    details() {
      if (this.productPlatform === 'API_GROUP_ZY') {
        return {
          title: '保单详情 - 已提交',
          data: [
            {
              title: '投保人信息',
              groups: [
                { label: '企业法人', value: this.policy?.policyholder },
                { label: '手机号', value: this.policy?.policyholder_phone_number },
                {
                  label: '证件类型',
                  value: this.certTypes[this.policy?.policy_group?.extra_info?.policyholder_idcard_type]
                },
                { label: '证件号', value: this.policy?.policyholder_idcard_no }
              ]
            },
            {
              title: '被保人信息',
              groups: [
                { label: '企业名称', value: this.policy?.insured },
                { label: '手机号', value: this.policy?.insured_phone_number },
                {
                  label: '证件类型',
                  value: this.certTypes[this.policy?.policy_group?.extra_info?.insured_idcard_type]
                },
                { label: '证件号', value: this.policy?.insured_idcard_no }
              ]
            },
            {
              title: '投保信息',
              groups: [
                { label: '出单公司', value: this.policy?.companyBranch?.name },
                { label: '保单号', value: this.policy?.policy_no },
                { label: '投保产品', value: this.policy?.policy_group?.product?.name },
                { label: '投保套餐', value: this.policy?.policy_group?.plan?.title },
                { label: '投保人数', value: this.policy?.policy_group?.insured_employee_count },
                { label: '保费', value: this.policy?.user_premium },
                {
                  label: '更新时间',
                  value: this.policy?.updated_at && dayjs(this.policy?.updated_at).format('YYYY-MM-DD HH:mm:ss')
                },
                {
                  label: '投保时间',
                  value: this.policy?.submitted_at && dayjs(this.policy?.submitted_at).format('YYYY-MM-DD HH:mm:ss')
                },
                { label: '起保日期', value: this.policy?.policy_group?.start_at },
                { label: '终保日期', value: this.policy?.policy_group?.end_at }
              ]
            },
            {
              title: '标的信息',
              groups: [
                { label: '省份', value: this.policy?.policy_group?.extra_info?.object_address[0] },
                { label: '市/区', value: this.policy?.policy_group?.extra_info?.object_address[1] },
                { label: '详细地址', value: this.policy?.policy_group?.extra_info?.object_address_detail },
                { label: '', value: '' }
              ]
            }
          ]
        }
      } else {
        return {
          title: '保单详情 - 已提交',
          data: [
            {
              title: '投保联系人',
              groups: [
                { label: '姓名', value: this.policy?.policy_group?.contact_name },
                { label: '联系电话', value: this.policy?.policy_group?.contact_phone }
              ]
            },
            {
              title: '投保信息',
              groups: [
                { label: '出单公司', value: this.policy?.companyBranch?.name },
                {
                  label: '保单号',
                  value: this.policy?.policy_no
                },
                { label: '投保产品', value: this.policy?.policy_group?.product?.name },
                { label: '投保套餐', value: this.policy?.policy_group?.plan?.title },
                { label: '投保单位', value: this.policy?.policyholder },
                { label: '被保单位', value: this.policy?.insured },
                { label: '统一社会信用代码', value: this.policy?.policyholder_idcard_no },
                { label: '统一社会信用代码', value: this.policy?.insured_idcard_no },
                { label: '在保人数', value: this.policy?.policy_group?.insured_employee_count },
                { label: '保费', value: this.policy?.user_premium },
                {
                  label: '更新时间',
                  value: this.policy?.updated_at && dayjs(this.policy?.updated_at).format('YYYY-MM-DD HH:mm:ss')
                },
                {
                  label: '投保时间',
                  value: this.policy?.submitted_at && dayjs(this.policy?.submitted_at).format('YYYY-MM-DD HH:mm:ss')
                },
                { label: '起保日期', value: this.policy?.policy_group?.start_at },
                { label: '终保日期', value: this.policy?.policy_group?.end_at }
              ]
            },
            {
              title: '文件信息',
              groups: [
                {
                  label: '被保单位营业执照',
                  value: this.policy?.policy_group?.attachment?.business_license_file ? '点击查看' : '暂无',
                  isLink: this.policy?.policy_group?.attachment?.business_license_file ? true : false,
                  target: '_blank',
                  to: this.policy?.policy_group?.attachment?.business_license_file
                },
                {
                  label: '人员清单',
                  value: this.policy?.policy_group?.attachment?.staff_list_file ? '点击查看' : '暂无',
                  isLink: this.policy?.policy_group?.attachment?.staff_list_file ? true : false,
                  target: '_blank',
                  to: this.policy?.policy_group?.attachment?.staff_list_file
                },
                {
                  label: '人员清单盖章文件',
                  value: this.policy?.policy_group?.attachment?.staff_stamp_list_file ? '点击查看' : '暂无',
                  isLink: this.policy?.policy_group?.attachment?.staff_stamp_list_file ? true : false,
                  target: '_blank',
                  to: this.policy?.policy_group?.attachment?.staff_stamp_list_file
                },
                {
                  label: '投保单',
                  value: this.policy?.policy_group?.attachment?.application_file ? '点击查看' : '暂无',
                  isLink: this.policy?.policy_group?.attachment?.application_file ? true : false,
                  target: '_blank',
                  to: this.policy?.policy_group?.attachment?.application_file
                },
                {
                  label: '投保单盖章文件',
                  value: this.policy?.policy_group?.attachment?.application_stamp_file ? '点击查看' : '暂无',
                  isLink: this.policy?.policy_group?.attachment?.application_stamp_file ? true : false,
                  target: '_blank',
                  to: this.policy?.policy_group?.attachment?.application_stamp_file
                },
                {
                  label: '委托书',
                  value: this.policy?.policy_group?.attachment?.authorization_file ? '点击查看' : '暂无',
                  isLink: this.policy?.policy_group?.attachment?.authorization_file ? true : false,
                  target: '_blank',
                  to: this.policy?.policy_group?.attachment?.authorization_file
                },
                {
                  label: '其它文件',
                  value: this.policy?.policy_group?.attachment?.extra_file ? '点击查看' : '暂无',
                  isLink: this.policy?.policy_group?.attachment?.extra_file ? true : false,
                  target: '_blank',
                  to: this.policy?.policy_group?.attachment?.extra_file
                },
                { label: '', value: '' }
              ]
            },
            {
              title: '发票信息',
              groups: [
                { label: '发票类型', value: this.policy?.policy_group?.invoice_info?.type },
                { label: '', value: '' },
                { label: '发票抬头', value: this.policy?.policy_group?.invoice_info?.title },
                { label: '纳税人识别码', value: this.policy?.policy_group?.invoice_info?.tax_no },
                { label: '开户行', value: this.policy?.policy_group?.invoice_info?.bank_name },
                { label: '账号', value: this.policy?.policy_group?.invoice_info?.bankcard_no },
                { label: '地址', value: this.policy?.policy_group?.invoice_info?.registered_addr },
                { label: '电话', value: this.policy?.policy_group?.invoice_info?.phone_number }
              ]
            }
          ]
        }
      }
    }
  },
  created() {
    this.fetchGroupPolicyDetail()
  },
  methods: {
    handlePolicyOperated(model) {
      this.fetchGroupPolicyDetail()
    },
    fetchGroupPolicyDetail() {
      getGroupPolicy(this.$route.params.policyGroupId).then((r) => {
        this.policy = r.data
        this.policyGroupId = this.policy.policy_group.id

        this.productPlatform = this.policy?.policy_group?.product?.additional?.third_platform

        if (!this.policy.policy_no) {
          const text = {
            0: '未提交',
            1: '已提交',
            2: '审核中',
            3: '已支付',
            4: '已审核',
            5: '已出单',
            6: '已退保',
            7: '已作废',
            8: '批改中',
            9: '退保中',
            10: '已退回',
            11: '待确认',
            12: '待支付'
          }
          this.policy.policy_no = `--${text[this.policy.status]}--`
        }

        const invoiceExplain = {
          none: '不需要发票',
          normal: '普通发票',
          special: '增值税专用发票'
        }
        this.policy.policy_group.invoice_info.type = invoiceExplain[this.policy.policy_group.invoice_info.type]
      })
    }
  }
}
</script>
