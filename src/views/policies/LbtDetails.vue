<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2020-10-22 16:26:03
 * @LastEditors: yanb
 * @LastEditTime: 2024-06-26 15:15:38
-->
<template>
  <SimpleContainer class="bg-white p-extra-large w-100 d-flex flex-column o-hidden">
    <PolicyCargoOperationBar :model="policy" @operated="fetchDetail" />
    <DefinePoliciesDetails :data="details"></DefinePoliciesDetails>
  </SimpleContainer>
</template>

<script>
import PolicyCargoOperationBar from '@/components/policy/PolicyCargoOperationBar'
import { getPolicy } from '@/apis/policy'

export default {
  name: 'DomesticLbt',
  components: {
    PolicyCargoOperationBar
  },
  data() {
    return {
      policy: {}
    }
  },
  computed: {
    viewData() {
      const viewData = JSON.parse(JSON.stringify(this.policy))
      for (const key in this.policy?.changes) {
        if (key === 'detail') {
          for (const key in this.policy?.changes?.detail) {
            if (key.endsWith('_id')) {
              viewData.detail[key.replace('_id', '')].name = `<span class="text-danger">${
                this.policy?.detail?.[key.replace('_id', '')]?.name
              }</span>`
            } else {
              viewData.detail[key] = `<span class="text-danger">${this.policy?.detail?.[key] || ''}</div>`
            }
          }
        } else {
          if (key.endsWith('_id')) {
            viewData[key.replace('_id', '')].name = `<span class="text-danger">${
              this.policy?.[key.replace('_id', '')]?.name
            }</span>`
          } else {
            viewData[key] = `<span class="text-danger">${this.policy?.[key] || ''}</div>`
          }
        }
      }

      return viewData
    },
    details() {
      return {
        title: '保单详情',
        data: [
          {
            title: '系统信息',
            groups: [
              { label: '保单号', value: this.viewData?.policy_no },
              { label: '出单时间', value: this.viewData?.issued_at },
              { label: '投保单号', value: this.viewData?.apply_no },
              { label: '流水号', value: this.viewData?.order_no },
              { label: '投保用户', value: this.viewData?.user?.name },
              { label: '投保时间', value: this.viewData?.submitted_at },
              { label: '第三方标识号', value: this.viewData?.trade_order_no }
            ]
          },
          {
            title: '产品信息',
            groups: [
              { label: '保险公司', value: this.viewData?.company_branch?.name },
              { label: '保险产品', value: this.viewData?.product?.name },
              {
                label: '产品代码',
                value: this.viewData?.product?.code,
                row: true
                // isLink: true,
                // target: '_blank',
                // to: ''
              }
            ]
          },
          {
            title: '基本信息',
            groups: [
              { label: '投保人', value: this.viewData?.policyholder },
              { label: '被保人', value: this.viewData?.insured },
              {
                label: '地址',
                value: this.viewData?.policyholder_address
              },
              {
                label: '地址',
                value: this.viewData?.insured_address
              },
              { label: '投保人电话', value: this.viewData?.policyholder_phone_number },
              { label: '被保人电话', value: this.viewData?.insured_phone_number },
              {
                label: '第三方标识号',
                value: this.viewData?.trade_order_no,
                row: true
              }
            ]
          },
          {
            title: '货物信息',
            groups: [
              { label: '货物类别', value: this.viewData?.detail?.goods_type?.name },
              { label: '装载方式', value: this.viewData?.detail?.loading_method?.name },
              { label: '运输方式', value: this.viewData?.detail?.transport_method?.name },
              { label: '包装方式', value: this.viewData?.detail?.packing_method?.name },
              { label: '货物名称', value: `<pre>${this.viewData?.detail?.goods_name}</pre>` },
              { label: '包装件数', value: `<pre>${this.viewData?.detail?.goods_amount}</pre>` }
            ]
          },
          {
            title: '运输信息',
            groups: [
              { label: '运单号', value: this.viewData?.detail?.waybill_no },
              { label: '发票号', value: this.viewData?.detail?.invoice_no },
              { label: '车牌号', value: this.viewData?.detail?.transport_no },
              { label: '起运日期', value: this.viewData?.detail?.shipping_date },
              { label: '起运地', value: this.viewData?.detail?.departure?.replace(':', '-') },
              { label: '目的地', value: this.viewData?.detail?.destination?.replace(':', '-') },
              { label: '中转地', value: this.viewData?.detail?.transmit },
              {
                label: '倒签保函',
                isLink: true,
                value: '点击查看',
                target: '_blank',
                hide: !this.policy?.detail?.anti_dated_file,
                to: this.policy?.detail?.anti_dated_file
              }
            ]
          },
          {
            title: '保险内容',
            groups: [
              { label: '主条款', value: this.viewData?.detail?.main_clause, row: true },
              { label: '附加条款', value: this.viewData?.detail?.additional_clause, row: true },
              {
                label: '免赔',
                value: this.viewData?.detail?.deductible,
                row: true
              },
              {
                label: '特别约定',
                value: this.viewData?.detail?.special,
                row: true
              }
            ]
          },
          {
            title: '费用信息',
            groups: [
              { label: '保额(元)', value: this.viewData?.actual_coverage },
              { label: '保费(元)', value: this.viewData?.premium },
              { label: '运费(元)', value: this.viewData?.coverage },
              { label: '用户费率(‱)', value: this.viewData?.user_rate },
              { label: '保费同步', value: this.viewData?.is_premium_sync === 1 ? '是' : '否' },
              { label: '是否允许开票', value: this.viewData?.is_allowed_invoice === 1 ? '是' : '否' }
            ]
          },
          {
            title: '其他',
            groups: [
              { label: '业务员', value: this.viewData?.salesman?.name },
              { label: '处理人', value: this.viewData?.auditor?.name },
              { label: '工作编号', value: this.viewData?.sticky_note },
              { label: '备注', value: this.viewData?.remark, row: true },
              {
                label: '投保附件',
                isLink: true,
                value: '点击查看',
                hide: !this.policy?.detail?.custom_file,
                to: this.policy?.detail?.custom_file
              }
            ]
          }
        ]
      }
    }
  },
  created() {
    this.fetchDetail()
  },
  methods: {
    fetchDetail() {
      getPolicy(this.$route.params.id).then((r) => (this.policy = r.data))
    }
  }
}
</script>

<style lang="scss" scoped></style>
