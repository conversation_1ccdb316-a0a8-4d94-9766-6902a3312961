<!--
 * @Descripttion:
 * @version:
 * @Author: yanb
 * @Date: 2021-11-03 14:26:17
 * @LastEditors: yanb
 * @LastEditTime: 2024-06-03 17:35:33
-->
<template>
  <SimpleContainer title="纸质保单" class="p-extra-large-x p-extra-large-b w-100 d-flex flex-column o-hidden">
    <SearchPanel
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
      size="small"
    ></SearchPanel>

    <el-card shadow="never" class="m-extra-large-t">
      <define-table :cols="cols" :data="data" :paging="paging" :paging-events="pagingEvents" />
    </el-card>

    <el-dialog
      :visible="dialogVisible"
      :title="`纸质保单申请处理 ${dialogModel.policy?.policy_no}`"
      width="700px"
      destory-on-close
      :before-close="handleClose"
    >
      <ul class="detail">
        <li>
          <div>当前保单已打印次数</div>
          <div>{{ dialogModel.print_num }}</div>
        </li>
        <li>
          <div>今日处理该用户单数</div>
          <div>
            <span style="color: red; font-weight: bold">{{ dialogModel.handle_num }}</span>
          </div>
        </li>
        <li>
          <div>流水号</div>
          <div>{{ dialogModel.policy?.order_no }}</div>
        </li>
        <li>
          <div>保单号</div>
          <div>
            {{ dialogModel.policy?.policy_no }}
            <el-button
              icon="fas fa-download"
              type="primary"
              size="mini"
              v-can="{ name: 'policies.download' }"
              @click="download"
            >
              下载
            </el-button>
          </div>
        </li>
        <li>
          <div>投保人</div>
          <div>{{ dialogModel.policy?.policyholder }}</div>
        </li>
        <li>
          <div>投保人地址</div>
          <div>{{ dialogModel.policy?.policyholder_address || '-' }}</div>
        </li>
        <li>
          <div>被保人</div>
          <div>{{ dialogModel.policy?.insured }}</div>
        </li>
        <li>
          <div>被保人地址</div>
          <div>{{ dialogModel.policy?.insured_address || '-' }}</div>
        </li>
        <li>
          <div>起运日期打印格式</div>
          <div>{{ shippingDatePrintFormatLabel }}</div>
        </li>
        <li>
          <div>收件人</div>
          <div>{{ dialogModel.recipient }}</div>
        </li>
        <li>
          <div>联系电话</div>
          <div>{{ dialogModel.phone }}</div>
        </li>
        <li>
          <div>收件地址</div>
          <div>{{ dialogModel.address }}</div>
        </li>
        <li>
          <div>快递类型</div>
          <div>{{ dialogModel.type | expressType }}</div>
        </li>
        <li>
          <div>客户备注</div>
          <div class="text-primary">{{ dialogModel.remark }}</div>
        </li>
      </ul>
      <el-row class="m-extra-large-t" v-if="[1].includes(dialogModel.status)">
        <el-col :span="24">
          <el-form ref="form" :model="form" :rules="rules">
            <el-form-item prop="express_company" label="快递公司">
              <el-input v-model="form.express_company" placeholder="快递公司" />
            </el-form-item>
            <el-form-item prop="express_no" label="快递单号">
              <el-input v-model="form.express_no" placeholder="快递单号" />
            </el-form-item>
            <el-form-item prop="order_no" label="单证流水号">
              <el-input v-model="form.order_no" placeholder="单证流水号" />
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>

      <template slot="footer">
        <el-button icon="fas fa-times" @click="handleClose">取消</el-button>
        <el-button v-if="[1].includes(dialogModel.status)" type="primary" icon="fas fa-check" @click="handleSubmit"
          >提交</el-button
        >
      </template>
    </el-dialog>

    <el-dialog title="退回" :visible.sync="sendBackDialog.visible" width="30%">
      <el-form ref="sendBackForm" label-position="left" label-width="80px" :model="sendBackDialog.form">
        <el-form-item
          label="退回理由"
          prop="reason"
          :rules="[{ required: true, message: '请输入退回理由', trigger: 'blur' }]"
        >
          <el-input placeholder="请输入退回理由" v-model="sendBackDialog.form.reason"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="sendBackDialog.visible = false">取 消</el-button>
        <el-button type="primary" @click="sendBack">确 定</el-button>
      </span>
    </el-dialog>
  </SimpleContainer>
</template>

<script>
import { buildDownloadHref } from '@/apis/policy'
import { getPolicyPapers, processPolicyPaper, buildExportHref, sendBackPolicyPaper } from '@/apis/policy_paper'
import { getPlatformProductCompanies } from '@/apis/platform_product'
import dayjs from 'dayjs'
import { Loading } from 'element-ui'

export default {
  name: 'PoliciesPaper',
  data() {
    return {
      dialogVisible: false,
      dialogModel: {},
      sendBackDialog: {
        visible: false,
        model: {},
        form: {
          reason: ''
        }
      },
      form: {
        express_company: '',
        express_no: '',
        order_no: ''
      },
      rules: {
        express_company: [{ required: true, message: '请输入快递公司', trigger: 'blur' }],
        express_no: [{ required: true, message: '请输入快递单号', trigger: 'blur' }],
        order_no: [{ required: true, message: '请输入单证流水号', trigger: 'blur' }]
      },
      searchFields: [
        {
          type: 'daterange',
          valKey: 'created_at_range'
        },
        {
          type: 'select',
          valKey: 'status',
          hintText: '状态',
          options: [
            { label: '已申请', value: 1 },
            { label: '已打印', value: 2 },
            { label: '已退回', value: 3 }
          ]
        },
        {
          type: 'input',
          valKey: 'keywords',
          hintText: '保单号、流水号、快递单号'
        },
        {
          type: 'input',
          valKey: 'policyholder',
          hintText: '投保人'
        },
        {
          type: 'input',
          valKey: 'express_company',
          hintText: '快递公司'
        },
        {
          type: 'select',
          valKey: 'company_id',
          hintText: '保险公司',
          onChangeReset: 'company_branch_id',
          options: []
        },
        {
          type: 'select',
          valKey: 'company_branch_id',
          hintText: '出单公司',
          options: []
        },
        {
          type: 'input',
          valKey: 'user',
          hintText: '投保用户'
        }
      ],
      paging: {
        currentPage: 1,
        pageSize: 0,
        layout: 'prev, pager, next, jumper, total',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page

          this.fetchPapers()
        }
      },
      cols: [
        { label: '流水号', prop: 'policy.order_no', width: 180, fixed: 'left' },
        { label: '保单号', prop: 'policy.policy_no', width: 180, fixed: 'left' },
        { label: '投保用户', prop: 'policy.user.name', width: 300 },
        { label: '投保人', prop: 'policy.policyholder', width: 300 },
        { label: '业务员', prop: 'policy.salesman', width: 100 },
        { label: '出单公司', prop: 'policy.company_branch', width: 100 },
        { label: '快递公司', prop: 'express_company', width: 100 },
        { label: '快递单号', prop: 'express_no', width: 180 },
        { label: '单证流水号', prop: 'order_no', width: 180 },
        {
          label: '申请时间/打印时间',
          width: 180,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.created_at}</label>
                  <br />
                  <small>{scoped.row.operator_at || '/'}</small>
                </div>
              )
            }
          }
        },
        {
          label: '状态',
          prop: 'status',
          width: 100,
          scopedSlots: {
            default: (scoped) => {
              const statuses = {
                1: '已申请',
                2: '已打印',
                3: '已退回'
              }

              return statuses[scoped.row.status] || '未知状态'
            }
          }
        },
        {
          label: '操作',
          align: 'center',
          width: 200,
          fixed: 'right',
          scopedSlots: {
            default: (scoped) => {
              if (!scoped.row.is_owned) {
                return ''
              }

              return (
                <div>
                  <el-button
                    v-can={{ name: 'policies.papers.apply' }}
                    onClick={() => {
                      this.dialogVisible = true
                      this.dialogModel = scoped.row
                    }}
                    type="primary"
                    plain
                    size="small"
                  >
                    {scoped.row.status === 1 ? '处理' : '详情'}
                  </el-button>
                  <el-button
                    v-can={{ name: 'policies.papers.send-back' }}
                    onClick={() => {
                      this.sendBackDialog.visible = true
                      this.sendBackDialog.model = scoped.row
                    }}
                    type="primary"
                    plain
                    size="small"
                    disabled={scoped.row.status !== 1}
                  >
                    退回
                  </el-button>
                </div>
              )
            }
          }
        }
      ],
      data: [],
      searchData: {},
      rawCompanies: []
    }
  },
  computed: {
    shippingDatePrintFormatLabel() {
      console.log(this.dialogModel)
      return parseInt(this.dialogModel?.policy?.shipping_date_print_format, 10) === 1
        ? dayjs(this.dialogModel?.policy?.shipping_date).format('MMM.DD, YYYY')
        : 'AS PER B/L'
    }
  },
  filters: {
    expressType(value) {
      switch (value) {
        case 1:
          return '普通快递'
        case 2:
          return '顺丰到付'
        case 3:
          return '自取保单'
        case 4:
          return '顺丰预付'
        default:
          break
      }
    }
  },
  watch: {
    searchData: {
      immediate: true,
      deep: true,
      handler(value, old) {
        if (old?.company_id !== value?.company_id) {
          this.loadCompanyBranches()
        }
      }
    }
  },
  created() {
    getPlatformProductCompanies().then((r) => {
      this.rawCompanies = r.data

      this.loadCompanies()
      this.loadCompanyBranches()
    })

    this.fetchPapers()
  },
  methods: {
    loadCompanies() {
      const options = this.rawCompanies.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('company_id', options)
    },
    loadCompanyBranches() {
      let options = []
      if (this.searchData?.company_id !== '' && this.searchData?.company_id !== undefined) {
        const company = this.rawCompanies.find((e) => e.id === this.searchData?.company_id)
        if (company) {
          options = company.branches.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      } else {
        this.rawCompanies.forEach((e) => {
          e?.branches.forEach((e) => {
            options.push({
              label: e.name,
              value: e.id
            })
          })
        })
      }

      this.assignSelectOptions('company_branch_id', options)
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    },
    fetchPapers() {
      getPolicyPapers({
        filter: this.searchData,
        page: this.paging.page
      }).then((r) => {
        this.data = r.data

        this.paging.currentPage = r.meta.current_page
        this.paging.pageSize = r.meta.per_page
        this.paging.total = r.meta.total
      })
    },
    download() {
      const link = buildDownloadHref(this.dialogModel?.policy?.id)

      window.open(link, '_blank')
    },
    handleClose() {
      this.dialogVisible = false

      this.$refs.form.resetFields()
    },
    handleSubmit() {
      const loading = Loading.service()
      this.$refs.form.validate((valid) => {
        if (valid) {
          processPolicyPaper(this.dialogModel.id, this.form)
            .then(() => {
              this.$message.success('处理完成')
              this.dialogVisible = false
              this.dialogModel = {}
              this.$refs.form.resetFields()

              this.fetchPapers()
              loading.close()
            })
            .finally(() => loading.close())
        }
      })
    },
    sendBack() {
      this.$refs.sendBackForm.validate((valid) => {
        if (valid) {
          sendBackPolicyPaper(this.sendBackDialog.model.id, this.sendBackDialog.form).then(() => {
            this.$message.success('处理完成')
            this.sendBackDialog.visible = false
            this.sendBackDialog.model = {}
            this.$refs.sendBackForm.resetFields()

            this.fetchPapers()
          })
        }
      })
    },
    handleSearchPanel(command, data) {
      this.searchData = data
      this.paging.page = 1
      if (command == 'submit') {
        this.fetchPapers()
      } else {
        window.open(buildExportHref(data))
      }
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-alert__content {
  width: 100%;
}

.detail {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  padding: 0;
  margin: 0 0 20px 0;
  border: 1px solid #eee;
  border-right: 0;
  border-bottom: 0;

  li {
    list-style: none;
    margin: 0;
    padding: 5px 10px 5px 10px;
    border-bottom: 1px solid #eee;
    border-right: 1px solid #eee;

    div {
      &:first-child {
        font-weight: bold;
      }
    }
  }
}
</style>
