<template>
  <SimpleContainer title="参保人员列表" class="p-extra-large-x p-extra-large-b w-100 d-flex flex-column o-hidden">
    <el-card shadow="never">
      <div class="d-flex w-100" slot="header">
        <span>
          被保险人：<b>{{ insuredPerson }}</b> 保单号：<b>{{ policyNo }}</b>
        </span>
        <i class="flex-fill m-mini-r" />
        <el-input
          class="m-mini-r"
          :style="{ width: '300px' }"
          placeholder="关键词：如身份证号等"
          v-model="filter.search"
        />
        <el-button icon="el-icon-search" type="primary" size="mini" @click="fetchGroupEndorseEmployeeList(filter)">
          搜索
        </el-button>
      </div>
      <define-table :data="employees" :cols="cols" :paging="paging" :paging-events="pagingEvents" />
    </el-card>
    <PolicyGroupEmployeeHistory
      :visible.sync="historyBox.visible"
      :history="historyBox.data"
    ></PolicyGroupEmployeeHistory>
  </SimpleContainer>
</template>

<script>
import SimpleContainer from '../../components/globals/SimpleContainer'
import { getGroupEndorseEmployeeList, getGroupPolicyInfo } from '../../apis/policy'
import PolicyGroupEmployeeHistory from '../../components/policy/PolicyGroupEmployeeHistory'
import dayjs from 'dayjs'

export default {
  name: 'GroupEndorseEmployee',
  components: { SimpleContainer, PolicyGroupEmployeeHistory },
  props: {
    policyGroupId: {
      type: Number,
      default: () => -1
    }
  },
  data() {
    return {
      employees: [],
      policyGroup: {},
      insuredPerson: '',
      policyNo: '',
      filter: {
        search: ''
      },
      historyBox: {
        visible: false,
        data: {
          name: '',
          idcard_no: '',
          mobile: ''
        }
      },
      cols: [
        {
          label: '姓名',
          prop: 'name'
        },
        {
          width: '150',
          label: '身份证号码',
          prop: 'idcard_no'
        },
        {
          label: '手机号码',
          prop: 'mobile'
        },
        {
          label: '职位类别',
          prop: 'job_code'
        },
        {
          label: '职位名称',
          prop: 'job_name'
        },
        {
          label: '添加时间',
          prop: 'created_at',
          width: '150',
          scopedSlots: {
            default: (scope) => {
              return dayjs(scope.row.created_at).format('YYYY-MM-DD HH:mm:ss')
            }
          }
        },
        {
          label: '标识',
          scopedSlots: {
            default: (scoped) => {
              let states = {
                0: '新增',
                1: '替换',
                2: '批减'
              }
              return states[scoped.row.action]
            }
          }
        },
        {
          label: '状态',
          scopedSlots: {
            default: (scoped) => {
              let states = {
                0: '无效',
                1: '未提交',
                2: '已提交',
                3: '审核中',
                4: '已生效',
                5: '已替换',
                6: '已批减'
              }
              return states[scoped.row.status]
            }
          }
        },
        {
          label: '操作',
          width: 120,
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.original_employee != undefined && scoped.row.action !== 2) {
                return (
                  <el-button
                    v-can={{ name: 'policies.group.show' }}
                    onClick={() => this.displayHistory(scoped.row.original_employee)}
                    type="primary"
                    size="mini"
                    icon="fa fa-history"
                  >
                    原始人员
                  </el-button>
                )
              }
            }
          }
        }
      ],
      paging: {
        currentPage: 1,
        pageSize: 15,
        layout: ' prev, pager, next, jumper, total',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.fetchGroupEndorseEmployeeList(this.filter, page)
        }
      }
    }
  },
  created() {
    this.fetchGroupEndorseEmployeeList()

    getGroupPolicyInfo(this.$route.params.policyGroupId).then((r) => {
      this.policyGroup = r.data

      this.policyNo = this.policyGroup.policy.policy_no
      this.insuredPerson = this.policyGroup.policy.insured
    })
  },
  methods: {
    fetchGroupEndorseEmployeeList(filter = {}, page = 1) {
      getGroupEndorseEmployeeList(this.$route.params.endorseId, {
        filter: filter,
        page: page
      }).then((r) => {
        this.employees = r.data

        this.paging.currentPage = r.current_page
        this.paging.total = r.total
        this.paging.pageSize = r.per_page
      })
    },
    displayHistory(employee) {
      this.historyBox.visible = true
      this.historyBox.data = employee
    }
  }
}
</script>
