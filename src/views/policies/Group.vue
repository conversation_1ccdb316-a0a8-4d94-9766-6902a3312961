<template>
  <SimpleContainer title="雇主责任险管理" class="p-extra-large-x p-extra-large-b w-100 d-flex flex-column o-hidden">
    <SearchPanel
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
      size="small"
    ></SearchPanel>

    <el-alert type="error" class="m-extra-large-t" :closable="false">
      <span>
        当前有
        <el-tag type="danger" effect="dark"> {{ submittedCount }} </el-tag>
        单<span class="hover-cursor" @click="handleFilterStatus(1)">已提交</span>，
        <el-tag type="danger" effect="dark"> {{ revisionCount }} </el-tag>
        单<span class="hover-cursor" @click="handleFilterStatus(8)">批改中</span>，
        <el-tag type="danger" effect="dark"> {{ auditingCount }} </el-tag>
        单<span class="hover-cursor" @click="handleFilterStatus(2)">审核中</span>，请检查保单并及时处理！
      </span>
    </el-alert>

    <el-card class="m-extra-large-t" shadow="never">
      <define-table :data="tableData" :cols="cols" :paging="paging" :paging-events="pagingEvents" />
    </el-card>
  </SimpleContainer>
</template>

<script>
import { buildExportHref, countPoliciesStatuses, getGroupPolicyList, disposeGroupPolicy } from '@/apis/policy'
import { getPlatformProductCompanies } from '@/apis/platform_product'
import { mapGetters } from 'vuex'
import { Loading } from 'element-ui'

const POLICY_TYPE = 5

export default {
  name: 'PoliciesGroup',
  data() {
    return {
      additional: {
        statuses: {}
      },
      statusesCount: [],
      // 搜索字段
      searchFields: [
        {
          type: 'daterange',
          valKey: 'submitted_at_range',
          hintText: '投保'
        },
        {
          type: 'daterange',
          valKey: 'issued_at_range',
          hintText: '出单'
        },
        {
          type: 'select',
          valKey: 'company_id',
          hintText: '保险公司',
          onChangeReset: 'company_branch_id',
          options: []
        },
        {
          type: 'select',
          valKey: 'company_branch_id',
          hintText: '出单公司',
          options: []
        },
        {
          type: 'input',
          valKey: 'keywords',
          hintText: '保单号,流水号'
        },
        {
          type: 'input',
          valKey: 'policyholder',
          hintText: '投保人'
        },
        {
          type: 'input',
          valKey: 'insured',
          hintText: '被保人'
        },
        {
          type: 'input',
          valKey: 'username',
          hintText: '投保用户'
        },
        {
          type: 'select',
          valKey: 'status',
          hintText: '状态',
          options: [
            { value: 0, label: '暂存单' },
            { value: 1, label: '已提交' },
            { value: 2, label: '审核中' },
            { value: 4, label: '审核完成' },
            { value: 5, label: '已出单' },
            { value: 7, label: '已作废' },
            { value: 8, label: '批改中' },
            { value: 10, label: '已退回' }
          ]
        }
      ],
      // 表格数据
      tableData: [],
      meta: {},
      // 表格列配置
      cols: [
        { prop: 'company_branch.name', label: '出单公司', width: 100, fixed: 'left' },
        {
          label: '保单号/流水号',
          width: 200,
          fixed: 'left',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.policy_no ? scoped.row.policy_no : '尚未出单 ~'}</label>
                  <br />
                  <small>{scoped.row.order_no}</small>
                </div>
              )
            }
          }
        },
        { prop: 'product.name', label: '保险产品', width: 350 },
        {
          label: '被保单位/投保单位',
          width: 180,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.insured}</label>
                  <br />
                  <small>{scoped.row.policyholder}</small>
                </div>
              )
            }
          }
        },
        {
          prop: 'insured_employee_count',
          label: '在保人数',
          width: 80,
          align: 'center'
        },
        { prop: 'submitted_at', label: '投保时间', width: 150 },
        {
          label: '投保用户',
          width: 180,
          scopedSlots: {
            default: (scoped) => {
              return scoped.row.user.platform_id === this.admin.platform.id ? (
                scoped.row.user.name
              ) : (
                <span class="text-primary">{scoped.row.user.platform.name}</span>
              )
            }
          }
        },
        {
          label: '状态',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{this.additional.statuses[scoped.row.status]}</label>
                </div>
              )
            }
          }
        },
        {
          label: '操作',
          align: 'center',
          fixed: 'right',
          width: 120,
          scopedSlots: {
            default: (scoped) => {
              return (
                <el-button
                  v-can={{ name: 'policies.group.show' }}
                  onClick={() => this.pageToDetail(scoped.row)}
                  type="primary"
                  plain
                  size="small"
                  icon="fa fa-check"
                >
                  处理
                </el-button>
              )
            }
          }
        }
      ],
      // 分页器 不传递 -> 没有分页
      paging: {
        currentPage: 1,
        pageSizes: [15, 30, 50, 100],
        pageSize: 15,
        layout: 'sizes, prev, pager, next, jumper, total',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page
          this.getTableList()
        },
        sizeChange: (size) => {
          this.paging.pageSize = size
          this.getTableList()
        }
      },
      searchData: {},
      rawCompanies: []
    }
  },
  mounted() {
    getPlatformProductCompanies().then((r) => {
      this.rawCompanies = r.data

      this.loadCompanies()
      this.loadCompanyBranches()
    })
    this.getTableList()
    this.getPoliciesStatuses()
  },
  computed: {
    submittedCount() {
      const s = this.statusesCount.find((e) => e.status === 1)
      return s?.statusCount || 0
    },
    revisionCount() {
      const s = this.statusesCount.find((e) => e.status === 8)
      return s?.statusCount || 0
    },
    auditingCount() {
      const s = this.statusesCount.find((e) => e.status === 2)
      return s?.statusCount || 0
    },
    ...mapGetters('auth', ['admin'])
  },
  watch: {
    searchData: {
      immediate: true,
      deep: true,
      handler(value, old) {
        if (old?.company_id !== value?.company_id) {
          this.loadCompanyBranches()
        }
      }
    }
  },
  methods: {
    loadCompanies() {
      const options = this.rawCompanies.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('company_id', options)
    },
    loadCompanyBranches() {
      let options = []
      if (this.searchData?.company_id !== '' && this.searchData?.company_id !== undefined) {
        const company = this.rawCompanies.find((e) => e.id === this.searchData?.company_id)
        if (company) {
          options = company.branches.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      } else {
        this.rawCompanies.forEach((e) => {
          e?.branches.forEach((e) => {
            options.push({
              label: e.name,
              value: e.id
            })
          })
        })
      }

      this.assignSelectOptions('company_branch_id', options)
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    },
    handleFilterStatus(status) {
      this.getTableList({ status })
    },
    getPoliciesStatuses(filter) {
      filter = Object.assign({ type: POLICY_TYPE }, filter)
      countPoliciesStatuses({
        filter: Object.assign({}, this.searchData, filter)
      }).then((r) => (this.statusesCount = r.data))
    },
    handleSearchPanel(command, data) {
      this.searchData = data
      this.paging.page = 1
      if (command === 'export') {
        data = Object.assign({ type: POLICY_TYPE }, data)

        window.open(buildExportHref({ filter: data }))
      } else {
        this.getTableList()
        this.getPoliciesStatuses()
      }
    },
    getTableList(filter = {}) {
      const loading = Loading.service()
      filter = Object.assign({ type: POLICY_TYPE }, filter)

      getGroupPolicyList({
        page: this.paging.page,
        per_page: this.paging.pageSize,
        filter: Object.assign({}, this.searchData, filter)
      })
        .then((r) => {
          const { meta, data, additional } = r
          this.meta = meta
          this.tableData = data
          this.additional = additional

          this.paging.currentPage = r.meta.current_page
          this.paging.pageSize = r.meta.per_page
          this.paging.total = r.meta.total
        })
        .finally(() => loading.close())
    },
    pageToDetail(policy) {
      if (policy.status !== 0) {
        disposeGroupPolicy(policy.policy_group_id).then(() => {
          this.$open({ name: 'PoliciesGroupDetails', params: { policyGroupId: policy.policy_group_id } })
        })
      } else {
        this.$message.warning('保单 未提交 无法处理')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.table-warp {
  margin-top: 20px;
  /deep/ .el-card__body {
    padding: 0;
  }
}
</style>
