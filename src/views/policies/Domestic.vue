<!--
 * @Descripttion: 国内险单票管理
 * @Author: Mr. zhu
 * @Date: 2020-10-19 12:30:41
 * @LastEditors: yanb
 * @LastEditTime: 2024-05-14 16:57:16
-->

<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <SearchPanel
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
      size="small"
    ></SearchPanel>
    <el-alert type="error" class="m-extra-large-t" :closable="false">
      <span>
        当前有
        <el-tag type="danger" effect="dark"> {{ submittedCount }} </el-tag>
        单<span class="hover-cursor" @click="handleFilterStatus(1)">已提交</span>，
        <el-tag type="danger" effect="dark"> {{ revisionCount }} </el-tag>
        单<span class="hover-cursor" @click="handleFilterStatus(8)">批改中</span>，
        <el-tag type="danger" effect="dark"> {{ auditingCount }} </el-tag>
        单<span class="hover-cursor" @click="handleFilterStatus(2)">审核中</span>，请检查保单并及时处理！
      </span>
    </el-alert>
    <el-card class="m-extra-large-t" shadow="never">
      <div class="d-flex w-100" slot="header">
        <el-button type="primary" icon="fas fa-file-download" @click="handleDownloadFiles">下载保单</el-button>
      </div>
      <DefineTable :data="tableData" :cols="cols" :paging="paging" :paging-events="pagingEvents" />
    </el-card>
  </div>
</template>

<script>
import { getPolicies, countPoliciesStatuses, buildExportHref, buildDownloadFilesHref } from '@/apis/policy'
import { getSubjects } from '@/apis/subject'
import { getPlatformProductCompanies } from '@/apis/platform_product'
import { mapGetters } from 'vuex'
import { Loading } from 'element-ui'

const POLICY_TYPE = 1

export default {
  name: 'PoliciesDomestic',
  data() {
    return {
      additional: {
        statuses: {}
      },
      statusesCount: [],
      // 搜索字段
      searchFields: [
        {
          type: 'daterange',
          valKey: 'submitted_at_range',
          hintText: '投保'
        },
        {
          type: 'daterange',
          valKey: 'issued_at_range',
          hintText: '出单'
        },
        {
          type: 'select',
          valKey: 'company_id',
          hintText: '保险公司',
          onChangeReset: 'company_branch_id',
          options: []
        },
        {
          type: 'select',
          valKey: 'company_branch_id',
          hintText: '出单公司',
          options: []
        },
        {
          type: 'select',
          valKey: 'subject_id',
          hintText: '标的',
          options: []
        },
        {
          type: 'textarea',
          valKey: 'keywords',
          hintText: '保单号,流水号'
        },
        {
          type: 'input',
          valKey: 'salesman',
          hintText: '业务员'
        },
        {
          type: 'input',
          valKey: 'auditor',
          hintText: '处理人'
        },
        {
          type: 'input',
          valKey: 'policyholder',
          hintText: '投保人'
        },
        {
          type: 'input',
          valKey: 'insured',
          hintText: '被保人'
        },
        {
          type: 'input',
          valKey: 'username',
          hintText: '投保用户'
        },
        {
          type: 'input',
          valKey: 'series_no',
          hintText: '发票号/运单号/提单号/发票号'
        },
        {
          type: 'input',
          valKey: 'sticky_note',
          hintText: '工作编号'
        },
        {
          type: 'select',
          valKey: 'is_premium_sync',
          hintText: '保费同步',
          options: [
            { value: 0, label: '否' },
            { value: 1, label: '是' }
          ]
        },
        {
          type: 'select',
          valKey: 'is_allowed_invoice',
          hintText: '是否允许开票',
          options: [
            { value: 0, label: '否' },
            { value: 1, label: '是' }
          ]
        },
        {
          type: 'select',
          valKey: 'status',
          hintText: '状态',
          options: [
            { value: 0, label: '暂存单' },
            { value: 1, label: '已提交' },
            { value: 5, label: '已出单' },
            { value: 6, label: '已退保' },
            { value: 7, label: '已作废' },
            { value: 8, label: '批改中' },
            { value: 9, label: '退保申请中' },
            { value: 10, label: '已退回' },
            { value: 12, label: '待支付' },
            { value: 13, label: '保单退回(补充资料)' },
            { value: 14, label: '工单退回(补充资料)' }
          ]
        }
      ],
      // 表格数据
      tableData: [],
      meta: {},
      // 表格列配置
      cols: [
        { prop: 'company_branch.name', label: '出单公司', width: '100', fixed: 'left' },
        {
          label: '保单号/流水号',
          width: '180',
          fixed: 'left',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.policy_no ? scoped.row.policy_no : '尚未出单 ~'}</label>
                  <br />
                  <small class={scoped.row.status === 1 && scoped.row.is_send_back ? 'text-primary' : ''}>
                    {scoped.row.order_no}
                  </small>
                </div>
              )
            }
          }
        },
        {
          label: '被保险人/投保人',
          width: '180',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.insured}</label>
                  <br />
                  <small>{scoped.row.policyholder}</small>
                </div>
              )
            }
          }
        },
        {
          label: '起运地/目的地',
          width: '180',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.details?.departure}</label>
                  <br />
                  <small>{scoped.row.details?.destination}</small>
                </div>
              )
            }
          }
        },
        {
          label: '标的/货物名称',
          width: '100',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.subject?.name}</label>
                  <br />
                  <small>{scoped.row.details?.goods_name}</small>
                </div>
              )
            }
          }
        },
        {
          label: '保费/保额',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.premium}</label>
                  <br />
                  <small>{scoped.row.coverage}</small>
                </div>
              )
            }
          }
        },
        {
          label: '投保时间/出单时间',
          width: 140,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.submitted_at}</label>
                  <br />
                  <small>{scoped.row.issued_at}</small>
                </div>
              )
            }
          }
        },
        {
          label: '投保用户',
          width: 180,
          scopedSlots: {
            default: (scoped) => {
              return scoped.row.user.platform_id === this.admin.platform.id ? (
                scoped.row.user.name
              ) : (
                <span class="text-primary">{scoped.row.user.platform.name}</span>
              )
            }
          }
        },
        {
          label: '状态',
          width: 80,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{this.additional.statuses[scoped.row.status]}</label>
                </div>
              )
            }
          }
        },
        {
          label: '操作',
          align: 'center',
          width: '140',
          fixed: 'right',
          scopedSlots: {
            default: (scoped) => {
              let btnText = '详情'
              if (scoped.row.is_manual_policy && scoped.row.status !== 5) {
                btnText = scoped.row.is_auditing ? '处理中' : '处理'
              }

              return (
                <el-button
                  v-can={{ name: 'policies.show' }}
                  onClick={() => this.$open({ name: 'PoliciesDomesticDetails', params: { id: scoped.row.id } })}
                  type="primary"
                  plain
                  size="small"
                  icon="el-icon-view"
                >
                  {btnText}
                </el-button>
              )
            }
          }
        }
      ],
      // 分页器 不传递 -> 没有分页
      paging: {
        currentPage: 1,
        pageSizes: [15, 30, 50, 100],
        pageSize: 15,
        layout: 'sizes, prev, pager, next, jumper, total',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page
          this.getTableList()
        },
        sizeChange: (size) => {
          this.paging.pageSize = size
          this.getTableList()
        }
      },
      searchData: {},
      rawCompanies: []
    }
  },
  computed: {
    submittedCount() {
      const s = this.statusesCount.find((e) => e.status === 1)
      return s?.statusCount || 0
    },
    revisionCount() {
      const s = this.statusesCount.find((e) => e.status === 8)
      return s?.statusCount || 0
    },
    auditingCount() {
      const s = this.statusesCount.find((e) => e.status === 2)
      return s?.statusCount || 0
    },
    ...mapGetters('auth', ['admin'])
  },
  watch: {
    searchData: {
      immediate: true,
      deep: true,
      handler(value, old) {
        if (old?.company_id !== value?.company_id) {
          this.loadCompanyBranches()
        }
      }
    }
  },
  mounted() {
    getPlatformProductCompanies().then((r) => {
      this.rawCompanies = r.data

      this.loadCompanies()
      this.loadCompanyBranches()
    })

    getSubjects().then((r) => {
      this.assignSelectOptions(
        'subject_id',
        r.data.map((e) => ({ value: e.id, label: e.name }))
      )
    })

    this.getTableList()
    this.getPoliciesStatuses()
  },
  methods: {
    handleDownloadFiles() {
      if (this.paging.total > 100) {
        return this.$message.error('单次最多只能下载 100 条数据')
      }

      const data = Object.assign({}, { type: POLICY_TYPE }, this.searchData)

      window.open(buildDownloadFilesHref({ filter: data }))
    },
    loadCompanies() {
      const options = this.rawCompanies.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('company_id', options)
    },
    loadCompanyBranches() {
      let options = []
      if (this.searchData?.company_id !== '' && this.searchData?.company_id !== undefined) {
        const company = this.rawCompanies.find((e) => e.id === this.searchData?.company_id)
        if (company) {
          options = company.branches.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      } else {
        this.rawCompanies.forEach((e) => {
          e?.branches.forEach((e) => {
            options.push({
              label: e.name,
              value: e.id
            })
          })
        })
      }

      this.assignSelectOptions('company_branch_id', options)
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    },
    handleFilterStatus(status) {
      this.getTableList({ status })
    },
    getPoliciesStatuses(filter) {
      filter = Object.assign({ type: POLICY_TYPE }, filter)
      countPoliciesStatuses({
        filter: Object.assign({}, this.searchData, filter)
      }).then((r) => (this.statusesCount = r.data))
    },
    handleSearchPanel(command, data) {
      this.searchData = data
      this.paging.page = 1
      if (command === 'export') {
        data = Object.assign({ type: POLICY_TYPE }, data)

        window.open(buildExportHref({ filter: data }))
      } else {
        this.getTableList()
        this.getPoliciesStatuses()
      }
    },
    getTableList(filter = {}) {
      const loading = Loading.service()
      filter = Object.assign({ type: POLICY_TYPE }, filter)

      getPolicies({
        page: this.paging.page,
        per_page: this.paging.pageSize,
        filter: Object.assign({}, this.searchData, filter)
      })
        .then((r) => {
          const { meta, data, additional } = r
          this.meta = meta
          this.tableData = data
          this.additional = additional

          this.paging.currentPage = r.meta.current_page
          this.paging.pageSize = r.meta.per_page
          this.paging.total = r.meta.total
        })
        .finally(() => loading.close())
    }
  }
}
</script>

<style lang="scss" scoped>
.table-warp {
  margin-top: 20px;
  /deep/ .el-card__body {
    padding: 0;
  }
}
</style>
