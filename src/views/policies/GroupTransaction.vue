<template>
  <div class="p-extra-large-x p-extra-large-b w-100 d-flex flex-column o-hidden">
    <el-card shadow="never">
      <DefineTable :cols="cols" :data="tableData" :paging="paging" :pagingEvents="pagingEvents"></DefineTable>
    </el-card>
  </div>
</template>

<script>
import DefineTable from '@/components/globals/DefineTable/DefineTable'
import { getGroupTransactions } from '@/apis/policy'
import dayjs from 'dayjs'

export default {
  name: 'GroupTransaction',
  components: {
    DefineTable
  },
  data() {
    return {
      cols: [
        { label: '流水号', prop: 'transaction_no' },
        { label: '充值类型', prop: 'remark' },
        { label: '充值金额', prop: 'amount' },
        {
          label: '支付凭证',
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.proof !== null) {
                return (
                  <div>
                    <el-link type="primary" href={scoped.row.proof} download="" target="_blank">
                      点击查看
                    </el-link>
                  </div>
                )
              } else {
                return <span>-</span>
              }
            }
          }
        },
        {
          label: '创建时间',
          prop: 'created_at',
          scopedSlots: {
            default: (scoped) => {
              return dayjs(scoped.row.created_at).format('YYYY/MM/DD HH:mm:ss')
            }
          }
        },
        {
          label: '状态',
          prop: 'status',
          scopedSlots: {
            default: (scoped) => {
              const text = {
                1: '已支付',
                2: '已到账',
                3: '已拒绝',
                4: '已提交'
              }
              return <span>{text[scoped.row.status]}</span>
            }
          }
        }
      ],
      tableData: [],
      paging: {
        align: 'center',
        currentPage: 1,
        pageSize: 15,
        layout: 'total, prev, pager, next, jumper',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          const data = {
            page: page
          }
          this.fetchTransactionList(data)
        }
      }
    }
  },
  mounted() {
    this.fetchTransactionList()
  },
  methods: {
    fetchTransactionList(data) {
      getGroupTransactions(this.$route.params.policyGroupId, data).then((r) => {
        this.tableData = r.data

        this.paging.currentPage = r.meta.current_page
        this.paging.total = r.meta.total
      })
    }
  }
}
</script>
