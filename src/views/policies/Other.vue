<style lang="scss" scoped></style>
<template>
  <SimpleContainer title="单车责任险管理" class="p-extra-large-x p-extra-large-b w-100 d-flex flex-column o-hidden">
    <SearchPanel
      @command="handleSearchPanel"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
      size="small"
    ></SearchPanel>
    <el-alert type="error" class="m-extra-large-t" :closable="false">
      <span>
        当前有
        <el-tag type="danger" effect="dark"> {{ submittedCount }} </el-tag>
        单<span class="hover-cursor" @click="handleFilterStatus(1)">已提交</span>，
        <el-tag type="danger" effect="dark"> {{ revisionCount }} </el-tag>
        单<span class="hover-cursor" @click="handleFilterStatus(8)">批改中</span>，
        <el-tag type="danger" effect="dark"> {{ auditingCount }} </el-tag>
        单<span class="hover-cursor" @click="handleFilterStatus(2)">审核中</span>，请检查保单并及时处理！
      </span>
    </el-alert>
    <el-card class="m-extra-large-t" shadow="never">
      <DefineTable :data="tableData" :cols="cols" :paging="paging" :paging-events="pagingEvents" />
    </el-card>
  </SimpleContainer>
</template>

<script>
import { getPolicies, countPoliciesStatuses, buildExportHref } from '@/apis/policy'
import { getPlatformProductCompanies } from '@/apis/platform_product'
import { mapGetters } from 'vuex'
import { Loading } from 'element-ui'

const POLICY_TYPE = 4

export default {
  name: 'PoliciesOther',
  data() {
    return {
      additional: {
        statuses: {}
      },
      statusesCount: [],
      // 搜索字段
      searchFields: [
        {
          type: 'input',
          valKey: 'keywords',
          hintText: '关键词'
        },
        {
          type: 'input',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'daterange',
          valKey: 'submitted_at_range',
          hintText: '投保'
        },
        {
          type: 'daterange',
          valKey: 'issued_at_range',
          hintText: '出单'
        },
        {
          type: 'select',
          valKey: 'company_id',
          hintText: '保险公司',
          onChangeReset: 'company_branch_id',
          options: []
        },
        {
          type: 'select',
          valKey: 'status',
          hintText: '状态',
          options: [
            { value: 0, label: '暂存单' },
            { value: 1, label: '已提交' },
            { value: 2, label: '审核中' },
            { value: 5, label: '已出单' },
            { value: 6, label: '已退保' },
            { value: 8, label: '批改中' },
            { value: 10, label: '已退回' }
          ]
        }
      ],
      // 表格数据
      tableData: [],
      meta: {},
      // 表格列配置
      cols: [
        { prop: 'company.name', label: '保险公司', width: '180' },
        { prop: 'company_branch.name', label: '出单公司', width: '180' },
        { prop: 'product.name', label: '保险产品', width: '180' },
        {
          label: '保单号/流水号',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.policy_no ? scoped.row.policy_no : '尚未出单 ~'}</label>
                  <br />
                  <small>{scoped.row.order_no}</small>
                </div>
              )
            }
          }
        },
        {
          label: '被保险人/投保人',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{scoped.row.insured}</label>
                  <br />
                  <small>{scoped.row.policyholder}</small>
                </div>
              )
            }
          }
        },
        { prop: 'created_at', label: '投保时间' },
        {
          label: '投保用户',
          scopedSlots: {
            default: (scoped) => {
              return scoped.row.user.platform_id === this.admin.platform.id ? (
                scoped.row.user.name
              ) : (
                <span class="text-primary">{scoped.row.user.platform.name}</span>
              )
            }
          }
        },
        {
          label: '状态',
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <label>{this.statusText(scoped.row.status)}</label>
                </div>
              )
            }
          }
        },
        {
          label: '操作',
          align: 'center',
          width: '140',
          scopedSlots: {
            default: (scoped) => {
              return (
                <el-button
                  v-can="{ name: 'policies.general.show' }"
                  onClick={() => this.$open({ name: 'PoliciesOtherDetails', params: { id: scoped.row.id } })}
                  type="primary"
                  plain
                  size="small"
                  icon="el-icon-view"
                >
                  详情
                </el-button>
              )
            }
          }
        }
      ],
      // 分页器 不传递 -> 没有分页
      paging: {
        currentPage: 1,
        pageSizes: [15, 30, 50, 100],
        pageSize: 15,
        layout: 'sizes, prev, pager, next, jumper, total',
        total: 0
      },
      pagingEvents: {
        currentChange: (page) => {
          this.paging.page = page
          this.getTableList()
        },
        sizeChange: (size) => {
          this.paging.pageSize = size
          this.getTableList()
        }
      },
      searchData: {},
      rawCompanies: []
    }
  },
  computed: {
    ...mapGetters('auth', ['admin']),
    submittedCount() {
      const s = this.statusesCount.find((e) => e.status === 1)
      return s?.statusCount || 0
    },
    revisionCount() {
      const s = this.statusesCount.find((e) => e.status === 8)
      return s?.statusCount || 0
    },
    auditingCount() {
      const s = this.statusesCount.find((e) => e.status === 2)
      return s?.statusCount || 0
    },
    statusText() {
      const _map = {
        0: '未提交',
        1: '已提交',
        2: '审核中',
        3: '已支付',
        4: '已审核',
        5: '已出单',
        6: '已退保',
        7: '已作废',
        8: '批改中',
        9: '退保中',
        10: '已退回',
        11: '待确认',
        12: '待支付'
      }
      return function (status) {
        return _map[status] || ''
      }
    }
  },
  watch: {
    searchData: {
      immediate: true,
      deep: true,
      handler(value, old) {
        if (old?.company_id !== value?.company_id) {
          this.loadCompanyBranches()
        }
      }
    }
  },
  mounted() {
    getPlatformProductCompanies().then((r) => {
      this.rawCompanies = r.data

      this.loadCompanies()
      this.loadCompanyBranches()
    })
    this.getTableList()
    this.getPoliciesStatuses()
  },
  methods: {
    loadCompanies() {
      const options = this.rawCompanies.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('company_id', options)
    },
    loadCompanyBranches() {
      let options = []
      if (this.searchData?.company_id !== '' && this.searchData?.company_id !== undefined) {
        const company = this.rawCompanies.find((e) => e.id === this.searchData?.company_id)
        if (company) {
          options = company.branches.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      } else {
        this.rawCompanies.forEach((e) => {
          e?.branches.forEach((e) => {
            options.push({
              label: e.name,
              value: e.id
            })
          })
        })
      }

      this.assignSelectOptions('company_branch_id', options)
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    },
    handleFilterStatus(status) {
      this.getTableList({ status })
    },
    getPoliciesStatuses(filter) {
      filter = Object.assign({ type: POLICY_TYPE }, filter)
      countPoliciesStatuses({
        filter: Object.assign({}, this.searchData, filter)
      }).then((r) => (this.statusesCount = r.data))
    },
    handleSearchPanel(command, data) {
      this.searchData = data
      this.paging.page = 1
      if (command === 'export') {
        data = Object.assign({ type: POLICY_TYPE }, data)

        window.open(buildExportHref({ filter: data }))
      } else {
        this.getTableList()
        this.getPoliciesStatuses()
      }
    },
    getTableList(filter = {}) {
      const loading = Loading.service()
      filter = Object.assign({ type: POLICY_TYPE }, filter)

      getPolicies({
        page: this.paging.page,
        per_page: this.paging.pageSize,
        filter: Object.assign({}, this.searchData, filter)
      })
        .then((r) => {
          const { meta, data, additional } = r
          this.meta = meta
          this.tableData = data
          this.additional = additional

          this.paging.currentPage = r.meta.current_page
          this.paging.pageSize = r.meta.per_page
          this.paging.total = r.meta.total
        })
        .finally(() => loading.close())
    }
  }
}
</script>

<style lang="scss" scoped>
.table-warp {
  margin-top: 20px;
  /deep/ .el-card__body {
    padding: 0;
  }
}
</style>
