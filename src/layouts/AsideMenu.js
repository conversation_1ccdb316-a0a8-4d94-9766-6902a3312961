/*
 * @Descripttion:生成菜单
 * @Author: Mr. zhu
 * @Date: 2021-01-08 00:04:31
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-21 17:25:23
 */
export default {
  name: 'AsideMenu',
  props: {
    menus: {
      type: Array,
      default: () => []
    },
    badges: {
      type: Object,
      default: () => {}
    },
    defaultActive: {
      type: String,
      default: ''
    },
    collapse: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      defaultOpen: []
    }
  },
  methods: {
    handleSelect(key, keyPath) {
      this.$store.dispatch('cache/flush')

      this.defaultOpen = [keyPath[0]]
      this.$router.push({
        name: key
      })
    }
  },
  render() {
    function renderMenu(arr = [], badges) {
      return arr.map((item) => {
        let badge = ''
        if (badges?.[item.route_name] !== undefined && badges[item.route_name] > 0) {
          badge = <el-badge value={badges[item.route_name]} style="position: absolute; right: 40px;"></el-badge>
        }

        if (item.children && item.children.length > 0) {
          return (
            <el-submenu index={item.route_name}>
              <template slot="title">
                <i class={item.icon || 'el-icon-location-outline'}></i>
                <span>{item.menu_name}</span>
                {badge}
              </template>
              {renderMenu(item.children, badges)}
            </el-submenu>
          )
        } else {
          return (
            <el-menu-item key={item.id} index={item.route_name}>
              <i class={item.icon}></i>
              <span slot="title">{item.menu_name}</span>
              <i class="border"></i>
              {badge}
            </el-menu-item>
          )
        }
      })
    }
    return (
      <el-menu
        collapse={this.collapse}
        default-active={this.defaultActive}
        default-openeds={this.defaultOpen}
        unique-opened={true}
        class="site-container__asid-menu"
        background-color="#364666"
        text-color="#ccc"
        active-text-color="#fff"
        style="border-right: 0;"
        vOn:select={this.handleSelect}
      >
        {renderMenu(this.menus, this.badges)}
      </el-menu>
    )
  }
}
