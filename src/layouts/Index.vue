<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-07 20:24:41
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-21 15:15:28
-->
<template>
  <el-container class="site-container">
    <el-aside
      :collapse-transition="false"
      unique-opened
      class="site-container__aside"
      :class="{ collapsed: asideIsCollapse }"
    >
      <div class="site-logo">
        <img :src="logoSrc" alt="保呀" />
      </div>
      <div class="flex-fill o-hidden">
        <el-scrollbar style="height: 100%">
          <AsideMenu :menus="menus" :badges="badges" :collapse="asideIsCollapse" :defaultActive="defaultActive" />
        </el-scrollbar>
      </div>
    </el-aside>
    <el-container class="site-container__main">
      <site-header
        :isCollapse.sync="asideIsCollapse"
        :userName="admin.name"
        :options="admin.options"
        :balance="balance"
        :hide-balance="admin?.platform?.id === -1"
        @signOut="handleSignOut"
        @resetPassword="handleResetPassword"
        @updateOptions="handleUpdateOptions"
      ></site-header>
      <site-breadcrumb v-if="!$route.meta.hideBreadcrumb" />
      <el-main class="site-container__content">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script>
import { mapGetters } from 'vuex'
import AsideMenu from './AsideMenu'
import SiteHeader from './SiteHeader'
import SiteBreadcrumb from './SiteBreadcrumb'
import logoImg from '@/assets/images/logo.png'
import logoCollapsedImg from '@/assets/images/logo_collapsed.png'
import './style.scss'
import { tokenKey } from '../config'
import { updatePassword, updateOptions } from '@/apis/admin'
import { Loading } from 'element-ui'

export default {
  name: 'SiteLayout',
  components: { AsideMenu, SiteHeader, SiteBreadcrumb },
  data() {
    return {
      asideIsCollapse: false
    }
  },
  computed: {
    ...mapGetters('app', ['menus', 'badges']),
    ...mapGetters('platform', ['logo', 'favicon', 'title', 'balance']),
    ...mapGetters('auth', ['admin']),
    logoSrc() {
      return this.asideIsCollapse ? this.favicon || logoCollapsedImg : this.logo || logoImg
    },
    collapseIcon() {
      return this.asideIsCollapse ? 'el-icon-s-unfold' : 'el-icon-s-fold'
    },
    defaultActive() {
      return this.$route.meta.active || this.$route.name
    }
  },
  created() {
    this.$store.dispatch('app/fetchBadges')
    this.refreshPlatform()

    setInterval(() => {
      this.refreshPlatform()
      this.$store.dispatch('app/fetchBadges').catch(() => {})
    }, 1000 * 60 * 5)
  },
  methods: {
    refreshPlatform() {
      if (window.localStorage.getItem(tokenKey) && this.admin?.platform?.id !== undefined) {
        this.$store.dispatch('platform/refresh', this.admin?.platform?.id)
      }
    },
    handleUpdateOptions(form) {
      const loading = Loading.service()
      updateOptions({
        options: form
      })
        .then(() => {
          this.$message.success('修改成功')
          this.$store.dispatch('auth/refreshAdmin')
        })
        .finally(() => {
          loading.close()
        })
    },
    handleResetPassword(form) {
      const loading = Loading.service()
      updatePassword(form)
        .then(() => {
          this.$message.success('修改成功，请重新登录')
          this.$store.dispatch('auth/removeToken')
          this.$store.dispatch('app/removeAll')

          this.$router.push({ name: 'Login' })
          window.location.reload()
        })
        .finally(() => {
          loading.close()
        })
    },
    /** 退出处理函数 */
    handleSignOut() {
      this.$confirm('是否确认退出登录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$store.dispatch('auth/removeToken')
          this.$store.dispatch('app/removeAll')

          this.$message.success('退出成功')

          this.$router.push({ name: 'Login' })
          window.location.reload()
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-menu-item,
/deep/ .el-submenu {
  .svg-inline--fa {
    vertical-align: middle;
    margin-right: 5px;
    width: 24px;
    text-align: center;
  }

  .el-badge__content {
    border: none !important;
  }
}
</style>
