<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-08 11:21:21
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-08 13:52:23
-->
<template>
  <div class="breadcrumb-wrap p-extra-large">
    <el-breadcrumb separator="/">
      <el-breadcrumb-item :to="{ path: '/' }"> 首页 </el-breadcrumb-item>
      <template v-for="(title, index) in $route.meta.titles">
        <el-breadcrumb-item
          :class="{ current: title.current }"
          v-if="title.name"
          :key="index"
          :to="{ name: title.name }"
        >
          {{ title.label }}
        </el-breadcrumb-item>
        <el-breadcrumb-item v-else :class="{ current: title.current }" :key="index">
          {{ title.label }}
        </el-breadcrumb-item>
      </template>
    </el-breadcrumb>
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'SiteBreadcrumb'
}
</script>

<style lang="scss" scoped>
.breadcrumb-wrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: $app-size-base;
  /deep/ .el-breadcrumb__item.current .el-breadcrumb__inner {
    color: $app-color-primary;
  }
}
</style>
