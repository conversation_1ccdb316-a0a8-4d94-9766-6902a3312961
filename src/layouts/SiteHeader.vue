<!--
 * @Descripttion:
 * @Author: Mr. <PERSON><PERSON>
 * @Date: 2021-01-08 10:05:33
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-08 15:58:10
-->
<template>
  <el-header class="site-container__header">
    <i class="site-collapsed font-20" @click="handleChangeCollapse" :class="collapseIcon"></i>
    <span class="title">{{ title }}</span>
    <i class="flex-fill"></i>
    <el-popover placement="bottom" width="400" trigger="hover">
      <el-collapse v-model="activeCollapse" accordion>
        <el-collapse-item title="个性化设置" name="options">
          <el-form ref="optionsForm" :form="optionsForm">
            <el-form-item>
              <el-checkbox v-model="optionsForm.is_new_tab" label="新窗口打开页面"></el-checkbox>
              <el-alert
                type="warning"
                show-icon
                :closable="false"
                description="设置后二级页面将由新窗口打开"
              ></el-alert>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleUpdateOptions" icon="fas fa-check" class="w-100">
                确定
              </el-button>
            </el-form-item>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="修改密码" name="resetPasswordForm">
          <el-form ref="resetPasswordForm" :model="resetPasswordForm" :rules="resetPasswordRules">
            <el-form-item label="旧密码" prop="old_password">
              <el-input v-model="resetPasswordForm.old_password" type="password" placeholder="请输入旧密码"></el-input>
            </el-form-item>
            <el-form-item label="新密码" prop="password">
              <el-input v-model="resetPasswordForm.password" type="password" placeholder="请输入新密码"></el-input>
            </el-form-item>
            <el-form-item label="确认密码" prop="password_confirmation">
              <el-input
                v-model="resetPasswordForm.password_confirmation"
                type="password"
                placeholder="请再次输入新密码"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleResetPassword" icon="fas fa-check" class="w-100">确定</el-button>
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>

      <span class="nick-name not-space" style="cursor: pointer" slot="reference">
        {{ userName }}
        <i class="el-icon-arrow-down el-icon--right"></i>
      </span>
    </el-popover>
    <el-divider direction="vertical"></el-divider>
    <el-link type="primary" :underline="false" @click="handleSignOut">退出</el-link>
    <div class="balance" v-if="!hideBalance">
      <div class="icon">
        <i class="fas fa-yen-sign" />
      </div>
      <label class="label"> {{ balance }} </label>
    </div>
  </el-header>
</template>

<script>
export default {
  name: 'SiteHeader',
  props: {
    // 余额
    balance: {
      type: [String, Number],
      default: 0.0
    },
    hideBalance: {
      type: Boolean,
      default: false
    },
    // 用户名称
    userName: {
      type: String,
      default: ''
    },
    // 标题
    title: {
      type: String,
      default: ''
    },
    // 菜单是否折叠
    isCollapse: {
      type: Boolean,
      deault: false
    },
    options: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    collapseIcon() {
      return this.isCollapse ? 'el-icon-s-unfold' : 'el-icon-s-fold'
    }
  },
  data() {
    return {
      activeCollapse: 'options',
      resetPasswordForm: {
        old_password: '',
        password: '',
        password_confirmation: ''
      },
      resetPasswordRules: {
        old_password: [{ required: true, message: '请输入旧密码', trigger: 'blur' }],
        password: [{ required: true, message: '请输入新密码', trigger: 'blur' }],
        password_confirmation: [{ required: true, message: '请再次输入新密码', trigger: 'blur' }]
      },
      optionsForm: {
        is_new_tab: false
      }
    }
  },
  watch: {
    options(value) {
      this.optionsForm = Object.assign({}, this.optionsForm, value)
    }
  },
  created() {
    this.optionsForm = Object.assign({}, this.optionsForm, this.options)
  },
  methods: {
    handleResetPassword() {
      this.$refs.resetPasswordForm.validate((valid) => {
        if (valid) {
          this.$emit('resetPassword', this.resetPasswordForm)
        }
      })
    },
    handleUpdateOptions() {
      this.$emit('updateOptions', this.optionsForm)
    },
    handleChangeCollapse() {
      this.$emit('update:isCollapse', !this.isCollapse)
    },
    handleSignOut() {
      this.$emit('signOut')
    }
  }
}
</script>
