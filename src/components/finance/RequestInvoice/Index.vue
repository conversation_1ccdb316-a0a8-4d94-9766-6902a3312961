<template>
  <el-dialog :visible.sync="visible" title="申请开票" :before-close="handleBeforeClose" destroy-on-close width="800px">
    <el-alert v-if="amount > 0" :title="'开票金额: ¥' + amount + ' 元'" type="error" effect="dark" :closable="false" />
    <el-form class="m-extra-large-t" ref="form" :model="form" :rules="rules" label-suffix=":">
      <el-row :gutter="48">
        <el-col :span="12">
          <el-form-item prop="company_name" label="发票抬头">
            <el-input v-model="form.company_name" placeholder="请输入发票抬头" :disabled="!isAllApply"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="tax_no" label="纳税人识别号">
            <el-input v-model="form.tax_no" placeholder="请输入纳税人识别号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="tax_type" label="纳税人身份">
            <el-select v-model="form.tax_type" class="w-100" placeholder="请选择纳税人身份">
              <el-option :value="1" label="个人"></el-option>
              <el-option :value="2" label="企业"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="48">
        <el-col :span="24">
          <el-form-item prop="is_mail" label="是否邮寄">
            <el-radio v-model="form.is_mail" :label="true">是</el-radio>
            <el-radio v-model="form.is_mail" :label="false" :disabled="form.type === 2 && isZhongyi">否</el-radio>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <template v-if="form.is_mail">
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="recipient" label="收件人姓名">
              <el-input v-model="form.recipient" placeholder="请输入收件人姓名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="recipient_phone_number" label="收件人电话">
              <el-input v-model="form.recipient_phone_number" placeholder="请输入收件人电话"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="24">
            <el-form-item prop="delivery_address" label="收件人地址">
              <el-input
                type="textarea"
                :rows="3"
                v-model="form.delivery_address"
                placeholder="请输入收件人地址"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </template> -->
      <el-row :gutter="48">
        <el-col :span="24">
          <el-form-item prop="type" label="发票类型(默认普票)">
            <el-radio v-model="form.type" :label="1">普票</el-radio>
            <el-radio v-model="form.type" :label="2">专票</el-radio>
          </el-form-item>
        </el-col>
      </el-row>
      <template v-if="form.type === 1 && isZhongyi">
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="email" label="邮箱地址">
              <el-input v-model="form.email" placeholder="请输入邮箱地址"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="customer_role_type" label="开票角色">
              <el-select v-model="form.customer_role_type" disabled class="w-100" placeholder="请选择开票角色">
                <el-option :value="1" label="投保人"></el-option>
                <el-option :value="2" label="被保人"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <template>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="registered_addr" label="注册地址">
              <el-input v-model="form.registered_addr" placeholder="请输入注册地址"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="registered_phone_number" label="注册电话">
              <el-input v-model="form.registered_phone_number" placeholder="请输入注册电话"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item prop="bank_name" label="开户银行">
              <el-input v-model="form.bank_name" placeholder="请输入开户银行"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="bankcard_no" label="银行账户">
              <el-input v-model="form.bankcard_no" placeholder="请输入银行账户"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <template>
        <el-row :gutter="48">
          <el-col :span="24">
            <el-form-item prop="remark" label="备注">
              <el-input type="textarea" v-model="form.remark" placeholder="请填写备注"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <el-form-item>
        <el-button @click="handleBeforeClose">取消</el-button>
        <el-button type="primary" @click="allApplyHandleSubmit" v-if="isAllApply">提交</el-button>
        <el-button type="primary" @click="handleSubmit" v-else>提交</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
export default {
  name: 'FinanceRequestInvoice',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    fillData: {
      type: Object,
      default: () => {}
    },
    amount: {
      type: [Number, String],
      default: 0
    },
    isZhongyi: {
      type: Boolean,
      default: false
    },
    isAllApply: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        type: 1,
        is_zhongyi: this.isZhongyi,
        company_name: '',
        bankcard_no: '',
        bank_name: '',
        tax_no: '',
        tax_type: 2,
        registered_addr: '',
        registered_phone_number: '',
        is_mail: false,
        recipient: '',
        recipient_phone_number: '',
        // delivery_address: '',
        email: '',
        customer_role_type: ''
      }
    }
  },
  computed: {
    rules() {
      let rules = {
        is_mail: [{ required: true, message: '请选择是否邮寄', trigger: ['blur', 'change'] }],
        type: [{ required: true, message: '请选择发票类型', trigger: ['blur', 'change'] }],
        company_name: [{ required: true, message: '请输入单位名称', trigger: 'blur' }],
        tax_no: [{ required: true, message: '请输入纳税人识别号', trigger: 'blur' }]
      }

      if (this.isZhongyi) {
        rules = Object.assign({}, rules, {
          email: [{ required: true, type: 'email', message: '请输入邮箱地址', trigger: 'blur' }],
          customer_role_type: [{ required: true, message: '请选择角色', trigger: ['blur', 'change'] }],
          tax_type: [{ required: true, message: '请选择纳税人身份', trigger: ['blur', 'change'] }]
        })
      }

      if (this.form.type === 2) {
        rules = Object.assign({}, rules, {
          bankcard_no: [{ required: true, message: '请输入银行卡号', trigger: 'blur' }],
          bank_name: [{ required: true, message: '请输入开户行', trigger: 'blur' }],
          registered_addr: [{ required: true, message: '请输入注册地址', trigger: 'blur' }],
          registered_phone_number: [{ required: true, message: '请输入注册电话', trigger: 'blur' }]
        })
      }

      return rules
    }
  },
  watch: {
    fillData: {
      deep: true,
      immediate: true,
      handler(value) {
        this.form = Object.assign({}, this.form, value)
      }
    },
    // 'form.is_mail'(value) {
    //   if (value) {
    //     this.rules = Object.assign({}, this.rules, {
    //       recipient: [{ required: true, message: '请输入收件人', trigger: 'blur' }],
    //       recipient_phone_number: [{ required: true, message: '请输入收件人联系电话', trigger: 'blur' }],
    //       delivery_address: [{ required: true, message: '请输入配送地址', trigger: 'blur' }]
    //     })
    //   } else {
    //     delete this.rules.recipient
    //     delete this.rules.recipient_phone_number
    //     delete this.rules.delivery_address
    //   }
    // },
    'form.type'(value) {
      if (value === 2 && this.isZhongyi) {
        this.form.is_mail = true
      }

      this.$refs.form.clearValidate()
    }
  },
  methods: {
    handleBeforeClose() {
      this.$emit('update:visible', false)
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('submit', this.form)

          this.$emit('update:visible', false)

          this.$refs.form.resetFields()
        }
      })
    },
    allApplyHandleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('all-apply-submit', this.form)

          this.$emit('update:visible', false)

          this.$refs.form.resetFields()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0;
}
</style>
