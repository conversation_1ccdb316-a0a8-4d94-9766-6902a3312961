<!--
 * @Descripttion:
 * @version:
 * @Author: yanb
 * @Date: 2022-06-14 16:03:27
 * @LastEditors: yanb
 * @LastEditTime: 2024-06-19 17:57:12
-->
<template>
  <div>
    <el-row>
      <el-col class="btns">
        <el-button
          type="primary"
          v-can="{ name: 'policies.offline.update' }"
          @click="updatePolicy"
          v-if="[1, 5].includes(model.status)"
        >
          修改保单
        </el-button>
        <el-popconfirm
          title="生效后保费数据不可修改! 是否确认生效该保单?"
          @confirm="issuePolicy"
          v-if="model.status == 1"
        >
          <el-button type="primary" v-can="{ name: 'policies.offline.identify-issue' }" slot="reference">
            确认生效
          </el-button>
        </el-popconfirm>
        <el-popconfirm title="确定删除吗？" @confirm="deletePolicy" v-if="model.status == 1">
          <el-button type="primary" v-can="{ name: 'policies.offline.delete' }" slot="reference"> 删除保单 </el-button>
        </el-popconfirm>
        <el-button
          type="primary"
          v-can="{ name: 'policies.offline.cancel' }"
          @click="cancel.visible = true"
          v-if="model.status == 5"
        >
          退保
        </el-button>
      </el-col>
    </el-row>

    <el-row v-if="model.status == 6" style="margin-top: 20px">
      <el-col>
        <el-alert show-icon title="该保单已退保" type="warning" :closable="false">
          <template>
            <div v-html="model.surrender_reason"></div>
          </template>
        </el-alert>
      </el-col>
    </el-row>

    <el-dialog title="退保" width="520px" :visible.sync="cancel.visible">
      <el-form ref="cancelForm" :model="cancel.form" :rules="cancel.rules" label-suffix=":" label-width="100px">
        <el-form-item prop="surrender_reason" label="理由">
          <el-input v-model="cancel.form.surrender_reason" placeholder="请输入理由" type="textarea"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleCancelPolicySubmit()">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { cancelOfflinePolicy, identifyIssuePolicy, deleteOfflinePolicy } from '@/apis/policy'
import { mapGetters } from 'vuex'

export default {
  name: 'OfflinePolicyOperationBar',
  props: {
    model: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    ...mapGetters('auth', ['admin'])
  },
  data() {
    return {
      cancel: {
        visible: false,
        form: {
          surrender_reason: ''
        },
        rules: {
          surrender_reason: [{ required: true, message: '请输入退回原因', trigger: 'blur' }]
        }
      }
    }
  },
  methods: {
    updatePolicy() {
      this.$router.push({
        name: 'UpdateOfflinePolicy',
        params: {
          id: this.$route.params.id
        }
      })
    },
    issuePolicy() {
      identifyIssuePolicy(this.model.id).then(() => {
        this.$message.success('确认生效完成')

        this.$emit('operated', 'identify_issue', this.model)
      })
    },
    deletePolicy() {
      deleteOfflinePolicy(this.model.id).then(() => {
        this.$message.success('删除成功')

        this.$emit('toList')
      })
    },
    handleCancelPolicySubmit() {
      this.$refs.cancelForm.validate((valid) => {
        if (valid) {
          cancelOfflinePolicy(this.model.id, this.cancel.form).then(() => {
            this.$message.success('退保成功')

            this.cancel.visible = false

            this.$emit('operated', 'cancel', this.model)
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0px !important;
}

.btns {
  text-align: right;

  /deep/ .el-button {
    margin-left: 10px;
  }
}
</style>
