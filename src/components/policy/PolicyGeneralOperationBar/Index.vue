<template>
  <div>
    <el-row>
      <el-col>
        <el-button-group style="float: right">
          <el-button type="primary" v-can="{ name: 'policies.general.send-mail' }" v-if="email" @click="handleEmail">
            发送邮件
          </el-button>
          <el-button
            type="primary"
            v-can="{ name: 'policies.general.audit' }"
            v-if="auditable"
            @click="audit.visible = true"
          >
            审核
          </el-button>
          <el-button type="primary" v-can="{ name: 'policies.download' }" v-if="downloadable" @click="download">
            下载保单
          </el-button>
          <el-button
            type="primary"
            v-can="{ name: 'policies.general.send-back' }"
            v-if="sendbackable"
            @click="sendback.visible = true"
          >
            退回
          </el-button>
        </el-button-group>
      </el-col>
    </el-row>

    <el-row v-if="showAlert">
      <el-col>
        <el-alert
          show-icon
          :title="alertTitle"
          :description="alertDescription"
          type="warning"
          :closable="false"
        ></el-alert>
      </el-col>
    </el-row>

    <el-dialog title="退回" width="520px" :visible.sync="sendback.visible">
      <el-form ref="sendbackForm" :model="sendback.form" :rules="sendback.rules" label-suffix=":" label-width="100px">
        <el-form-item prop="sendback_reason" label="理由">
          <el-input v-model="sendback.form.sendback_reason" placeholder="请输入理由"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSendBackPolicySubmit()">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog title="审核产品" width="520px" :visible.sync="audit.visible">
      <el-form ref="auditForm" :model="audit.form" :rules="audit.rules" label-suffix=":" label-width="100px">
        <el-form-item prop="policy_no" label="保单号">
          <el-input v-model="audit.form.policy_no" placeholder="请输入保单号"></el-input>
        </el-form-item>
        <el-form-item prop="policy_file" label="保单文件">
          <upload-file v-model="audit.form.policy_file" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleAuditPolicySubmit()">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { generalPolicyAudit, generalPolicySendBack, buildDownloadHref } from '@/apis/policy'
import UploadFile from '../../globals/UploadFile/UploadFile.vue'

export default {
  components: { UploadFile },
  name: 'PolicyGeneralOperationBar',
  props: {
    model: {
      type: Object,
      default: () => {}
    },
    email: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    auditable() {
      return this.model.status === 2
    },
    downloadable() {
      return this.model.status === 5
    },
    sendbackable() {
      return [1, 2].includes(this.model.status)
    },
    showAlert() {
      return [6, 10].includes(this.model?.status)
    },
    alertTitle() {
      return `该保单${this.model?.status === 6 ? '已退保' : '已退回'}`
    },
    alertDescription() {
      return `${this.model?.status === 6 ? '退保' : '退回'}原因: ${
        this.model?.status === 6 ? this.model?.surrender_reason : this.model?.sendback_reason
      }`
    }
  },
  data() {
    return {
      audit: {
        visible: false,
        form: {
          policy_no: '',
          policy_file: ''
        },
        rules: {
          policy_no: [{ required: true, message: '请输入保单号', trigger: 'blur' }],
          policy_file: [{ required: true, message: '请上传保单文件', trigger: ['blur', 'change'] }]
        }
      },
      sendback: {
        visible: false,
        form: {
          sendback_reason: ''
        },
        rules: {
          sendback_reason: [{ required: true, message: '请输入退回原因', trigger: 'blur' }]
        }
      }
    }
  },
  methods: {
    handleEmail() {
      this.$emit('send-email')
    },
    download() {
      const link = buildDownloadHref(this.model.id)

      window.open(link, '_blank')
    },
    handleAuditPolicySubmit() {
      this.$refs.auditForm.validate((valid) => {
        if (valid) {
          generalPolicyAudit(this.model.id, this.audit.form).then(() => {
            this.$message.success('审核成功')
            this.audit.visible = false

            this.$emit('operated', 'audited', this.model)
          })
        }
      })
    },
    handleSendBackPolicySubmit() {
      this.$refs.sendbackForm.validate((valid) => {
        if (valid) {
          generalPolicySendBack(this.model.id, this.sendback.form).then(() => {
            this.$message.success('退回成功')

            this.sendback.visible = false

            this.$emit('operated', 'sendback', this.model)
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0px !important;
}
</style>
