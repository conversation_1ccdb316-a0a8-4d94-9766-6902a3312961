<template>
  <div class="p-extra-large-b">
    <el-card class="table-wrap" shadow="never">
      <el-button type="primary" style="margin-bottom: 15px" @click="importDialog.visible = true">批量录入</el-button>
      <el-dialog title="批量导入" width="520px" :visible.sync="importDialog.visible">
        <el-form ref="importForm" :model="importDialog.form" :rules="importRules" label-suffix=":" label-width="120px">
          <el-form-item prop="file" label="批量导入文件">
            <el-link type="primary" :underline="false" @click="downloadImportTemplateExcel()"> (点击下载模板) </el-link>
            <upload-file v-model="importDialog.form.file" />
          </el-form-item>
          <el-form-item>
            <el-button icon="fas fa-times" @click="importDialog.visible = false">取消</el-button>
            <el-button type="primary" icon="fas fa-check" @click="importSubmit()">提交</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
      <el-form ref="form" :model="form" :rules="rules" label-suffix=":" label-width="100px" label-position="top">
        <section>
          <h3>基本信息</h3>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item prop="company_id" label="保险公司">
                <el-select
                  v-model="form.company_id"
                  placeholder="请选择保险公司"
                  clearable
                  filterable
                  style="width: 100%"
                >
                  <el-option v-for="com in companies" :key="com.id" :value="com.id" :label="com.name" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="company_branch_id" label="出单公司">
                <el-select
                  v-model="form.company_branch_id"
                  placeholder="请选择出单公司"
                  clearable
                  filterable
                  style="width: 100%"
                >
                  <el-option v-for="com in companyBranches" :key="com.id" :value="com.id" :label="com.name" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="channel_id" label="投保渠道">
                <el-select
                  v-model="form.channel_id"
                  placeholder="请选择投保渠道"
                  clearable
                  filterable
                  style="width: 100%"
                >
                  <el-option v-for="com in channels" :key="com.id" :value="com.id" :label="com.name" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="业务员" prop="salesman_id">
                <el-select placeholder="请选择业务员" v-model="form.salesman_id" class="w-100">
                  <el-option v-for="s in sales" :key="s.id" :label="s.name" :value="s.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="policy_no" label="保单号">
                <el-input v-model="form.policy_no" placeholder="请输入保单号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="policy_file" label="保单文件">
                <upload-file v-model="form.policy_file"></upload-file>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="policyholder" label="投保人">
                <el-input v-model="form.policyholder" placeholder="请输入投保人" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="insured" label="被保人">
                <el-input v-model="form.insured" placeholder="请输入被保人" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="start_at" label="保单起始日期">
                <el-date-picker v-model="form.start_at" class="w-100" type="date" placeholder="请选择保单起始日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="end_at" label="保单终止日期">
                <el-date-picker
                  :picker-options="pickerOptions"
                  v-model="form.end_at"
                  class="w-100"
                  type="date"
                  placeholder="请选择保单终止日期"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="subject" label="标的名称">
                <el-input v-model="form.subject" placeholder="请输入标的名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="insure_date" label="投保时间">
                <el-date-picker v-model="form.insure_date" class="w-100" type="date" placeholder="请选择投保时间">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="coverage" label="保险金额">
                <el-input v-model="form.coverage" placeholder="请输入保险金额" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="coverage_currency_id" label="保额币种">
                <el-select v-model="form.coverage_currency_id" placeholder="请选择币种" class="w-100">
                  <el-option
                    v-for="currency in coverageCurrencies"
                    :value="currency.id"
                    :key="currency.id"
                    :label="currency.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="产品来源" prop="product_from">
                <el-select placeholder="请选择产品来源" v-model="form.product_from" class="w-100">
                  <el-option
                    v-for="productFrom in productFroms"
                    :key="productFrom.value"
                    :label="productFrom.label"
                    :value="productFrom.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </section>
        <section>
          <h3 v-if="offlineFields != []">
            其他信息 <span style="color: #ff7f4c; font-size: 12px">(以下都为必填项)</span>
          </h3>
          <el-row :gutter="48">
            <el-col :span="12" v-for="fields in offlineFields" :key="fields.name">
              <el-form-item :prop="fields.name" :label="fields.title">
                <div slot="label" v-if="fields.file">
                  {{ fields.title }}
                  <span><a :href="fields.file" target="_blank">模板</a></span>
                </div>
                <el-input
                  v-model="form[fields.name]"
                  v-if="fields.type == 'text'"
                  :placeholder="'请输入' + fields.title"
                />
                <el-input
                  v-if="fields.type == 'textarea'"
                  v-model="form[fields.name]"
                  type="textarea"
                  :rows="3"
                  :placeholder="'请输入' + fields.title"
                />
                <el-date-picker
                  v-if="fields.type == 'date'"
                  v-model="form[fields.name]"
                  class="w-100"
                  type="date"
                  :placeholder="'请选择' + fields.title + '时间'"
                >
                </el-date-picker>
                <el-select
                  v-if="fields.type == 'select'"
                  v-model="form[fields.name]"
                  :placeholder="'请输入' + fields.title"
                  clearable
                  filterable
                  style="width: 100%"
                >
                  <el-option v-for="value in fields.options" :key="value" :value="value" :label="value" />
                </el-select>
                <template v-if="fields.type === 'file' || fields.type === '_file'">
                  <upload-file
                    v-model="form[fields.name]"
                    accept=".jpeg,.png,.pdf,.jpg,.zip"
                    :limitSize="2"
                  ></upload-file>

                  <el-link
                    v-if="typeof form[fields.name] === 'string' && form[fields.name] && fields.type === '_file'"
                    :href="form[fields.name]"
                    target="_blank"
                  >
                    点击查看
                  </el-link>
                  <span class="text-danger"> .jpeg, .png, .pdf, .jpg, .zip 文件，大小不能超过 2M</span>
                </template>
              </el-form-item>
            </el-col>
          </el-row>
        </section>
        <section>
          <h3>费用信息</h3>
          <el-row :gutter="48">
            <div v-if="form?.status != 5">
              <el-col :span="12">
                <el-form-item prop="premium" label="保费">
                  <el-input v-model="form.premium" placeholder="请填写保费" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="poundage_rate" label="经纪费比例%">
                  <el-input v-model="form.poundage_rate" type="number" placeholder="请填写经纪费比例" />
                </el-form-item>
              </el-col>
              <!-- <el-col :span="12">
                <el-form-item prop="premium_pay_type" label="保费支付情况">
                  <el-select
                    v-model="form.premium_pay_type"
                    placeholder="请选择保费支付情况"
                    clearable
                    filterable
                    style="width: 100%"
                  >
                    <el-option
                      v-for="payType in premiumPayTypes"
                      :key="payType.value"
                      :value="payType.value"
                      :label="payType.label"
                    />
                  </el-select>
                </el-form-item>
              </el-col> -->
              <el-col :span="12">
                <el-form-item prop="settlement_type" label="结算方式">
                  <el-select
                    v-model="form.settlement_type"
                    placeholder="请选择结算方式"
                    clearable
                    filterable
                    style="width: 100%"
                  >
                    <el-option
                      v-for="settlementType in settlementTypes"
                      :key="settlementType.value"
                      :value="settlementType.value"
                      :label="settlementType.label"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <!-- <div v-if="form.premium_pay_type === 1">
                <el-col :span="12">
                  <el-form-item label="保费支付类型" prop="type">
                    <el-select placeholder="请选择保费支付类型" v-model="form.payee_type" class="w-100">
                      <el-option
                        v-for="payeeType in payeeTypes"
                        :key="payeeType.value"
                        :label="payeeType.label"
                        :value="payeeType.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="memo_file" label="付款水单文件">
                    <upload-file v-model="form.memo_file"></upload-file>
                  </el-form-item>
                </el-col>
              </div> -->
              <el-col :span="12">
                <el-form-item label="业务类型" prop="type">
                  <el-select placeholder="请选择业务类型" v-model="form.business_type" class="w-100">
                    <el-option
                      v-for="businessType in businessTypes"
                      :key="businessType.value"
                      :label="businessType.label"
                      :value="businessType.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <div v-if="form.business_type == 1">
                <el-col :span="12">
                  <el-form-item label="投保用户" prop="type">
                    <el-select placeholder="请选择投保用户" v-model="form.user_id" class="w-100" filterable>
                      <el-option v-for="user in users" :key="user.id" :label="user.name" :value="user.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </div>
              <div v-if="form.business_type == 2">
                <el-col :span="12">
                  <el-form-item label="代理商" prop="type">
                    <el-select placeholder="请选择代理商" v-model="form.user_id" class="w-100" filterable>
                      <el-option v-for="user in users" :key="user.id" :label="user.name" :value="user.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="commission_rate" label="佣金比例">
                    <el-input v-model="form.commission_rate" type="number" placeholder="请填写佣金比例" />
                  </el-form-item>
                </el-col>
              </div>
              <div v-if="form.business_type == 3">
                <el-col :span="12">
                  <el-form-item label="业务来源" prop="type">
                    <el-autocomplete
                      class="w-100"
                      v-model="form.business_from"
                      :fetch-suggestions="fetchBusinessFroms"
                      placeholder="请输入内容"
                      :trigger-on-focus="false"
                      @blur="blurBusinessFrom"
                      @select="handleSelectBusinessFrom"
                    ></el-autocomplete>
                  </el-form-item>
                </el-col>
              </div>
            </div>
            <el-col :span="24">
              <el-form-item>
                <el-button @click="$refs.form.resetFields()">清 空</el-button>
                <el-button type="primary" @click="handleSubmit">确 定</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </section>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { getCompaniesDict } from '@/apis/company'
import { findPlatformByName } from '@/apis/platform'
import { getChannels } from '@/apis/channel'
import { getOfflineCategory } from '@/apis/product'
import { createOfflinePolicy, offlineImportTemplate, importOfflinePolicy } from '@/apis/policy'
import { getSales } from '@/apis/admin'
import { getUsersOrAgents } from '@/apis/user'
import { getPayees } from '@/apis/finance'
import { getLatestCurrencyExchange } from '@/apis/currency'
import { Loading } from 'element-ui'
import dayjs from 'dayjs'

export default {
  name: 'OfflinePolicyCreateForm',
  props: {
    model: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      companies: [],
      channels: [],
      fields: [],
      sales: [],
      users: [],
      payees: [],
      coverageCurrencies: [],
      businessFroms: [],
      importDialog: {
        visible: false,
        form: {
          file: ''
        }
      },
      importRules: {
        file: [{ required: true, message: '请上传批量导入文件', trigger: ['blur', 'change'] }]
      },
      form: {
        category_id: this.$route.params.categoryId,
        company_id: '',
        company_branch_id: '',
        channel_id: '',
        salesman_id: '',
        policy_no: '',
        policy_file: '',
        start_at: '',
        end_at: '',
        policyholder: '',
        insured: '',
        subject: '',
        insure_date: '',
        coverage: '',
        coverage_currency_id: '',
        product_from: '',
        premium: '',
        // premium_pay_type: '',
        settlement_type: '',
        business_type: '',
        business_from: '',
        user_id: '',
        agent_id: '',
        commission_rate: '',
        payee_type: '',
        memo_file: '',
        poundage_rate: ''
      },
      rules: {
        company_id: [{ required: true, message: '请选择保险公司', trigger: ['blur', 'change'] }],
        company_branch_id: [{ required: true, message: '请选择出单公司', trigger: ['blur', 'change'] }],
        channel_id: [{ required: true, message: '请选择投保渠道', trigger: ['blur', 'change'] }],
        salesman_id: [{ required: true, message: '请选择业务员', trigger: ['blur', 'change'] }],
        policy_no: [{ required: true, message: '请填写保单号', trigger: ['blur', 'change'] }],
        policy_file: [{ required: false, message: '请选择保单文件', trigger: ['blur', 'change'] }],
        start_at: [{ required: true, message: '请选择保单起始时间', trigger: ['blur', 'change'] }],
        end_at: [{ required: true, message: '请选择保单终止时间', trigger: ['blur', 'change'] }],
        policyholder: [{ required: true, message: '请填写投保人', trigger: ['blur', 'change'] }],
        insured: [{ required: true, message: '请填写被保人', trigger: ['blur', 'change'] }],
        subject: [{ required: true, message: '请填写标的名称', trigger: ['blur', 'change'] }],
        insure_date: [{ required: true, message: '请选择投保时间', trigger: ['blur', 'change'] }],
        coverage: [{ required: true, message: '请填写保险金额', trigger: ['blur', 'change'] }],
        coverage_currency_id: [{ required: true, message: '请选择保额币种', trigger: ['blur', 'change'] }],
        product_from: [{ required: true, message: '请选择产品来源', trigger: ['blur', 'change'] }],
        premium: [{ required: true, message: '请填写保费', trigger: ['blur', 'change'] }],
        // premium_pay_type: [{ required: true, message: '请选择保费支付情况', trigger: ['blur', 'change'] }],
        settlement_type: [{ required: true, message: '请选择结算方式', trigger: ['blur', 'change'] }],
        business_type: [{ required: true, message: '请选择业务类型', trigger: ['blur', 'change'] }],
        user_id: [
          {
            required: true,
            message: this?.form?.business_type == 1 ? '请选择会员' : '请选择代理商',
            trigger: ['blur', 'change']
          }
        ],
        business_from: [
          {
            required: this?.form?.business_type == 3,
            message: '请填写业务来源',
            trigger: ['blur', 'change']
          }
        ],
        agent_id: [{ required: false, message: '请选择代理商', trigger: ['blur', 'change'] }],
        commission_rate: [{ required: false, message: '请填写佣金比例', trigger: ['blur', 'change'] }],
        payee_type: [{ required: false, message: '请选择保费支付类型', trigger: ['blur', 'change'] }],
        memo_file: [{ required: false, message: '请上传付款水单文件', trigger: ['blur', 'change'] }],
        poundage_rate: [{ required: true, message: '请填写经纪费比例', trigger: ['blur', 'change'] }]
      },
      pickerOptions: {
        disabledDate: (time) => {
          if (this.form.start_at.getTime() > time.getTime()) {
            return true
          }
        }
      }
    }
  },
  computed: {
    offlineFields() {
      return this.fields || []
    },
    companyBranches() {
      const com = this.companies.find((e) => e.id == this.form.company_id)

      return com?.branches || []
    },
    premiumPayTypes() {
      return [
        {
          label: '保费未付',
          value: 2
        }
      ]
    },
    settlementTypes() {
      return [
        {
          label: '含税保费',
          value: 1
        },
        {
          label: '不含税保费',
          value: 2
        }
      ]
    },
    payeeTypes() {
      return [
        {
          label: '客户支付保险公司',
          value: 1
        },
        {
          label: '客户支付平台',
          value: 2
        }
      ]
    },
    businessTypes() {
      return [
        {
          label: '会员业务',
          value: 1
        },
        {
          label: '代理业务',
          value: 2
        },
        {
          label: '非自有业务',
          value: 3
        }
      ]
    },
    productFroms() {
      return [
        {
          label: '自有产品',
          value: 1
        },
        {
          label: '非自有产品',
          value: 2
        }
      ]
    }
  },
  watch: {
    model: {
      immediate: true,
      deep: true,
      handler(value) {
        if (Object.keys(value).length > 0) {
          this.form = Object.assign({}, value)
        }
      }
    },
    // 'form.premium_pay_type'(payType) {
    //   if (payType === 2) {
    //     this.form.payee_type = ''
    //     this.form.memo_file = ''
    //   }
    // },
    'form.payee_type'(payeeType) {
      if (payeeType != '') {
        getPayees(payeeType).then((r) => {
          this.payees = r.data
        })
      }
    },
    'form.business_type'(businessType) {
      getUsersOrAgents({ is_agent: businessType === 2 ? 1 : 0, salesman_id: this.form.salesman_id }).then((r) => {
        this.users = r.data
      })
    },
    'form.salesman_id'(salesmanId) {
      this.form.user_id = ''
      getUsersOrAgents({ is_agent: this.form.business_type === 2 ? 1 : 0, salesman_id: this.form.salesman_id }).then(
        (r) => {
          this.users = r.data
        }
      )
    }
  },
  created() {
    getCompaniesDict({
      is_enabled: 1
    }).then((r) => (this.companies = r.data))
    this.fetchCurrencies()
    getChannels({
      is_enabled: 1
    }).then((r) => (this.channels = r.data))
    getSales().then((r) => {
      this.sales = r.data
    })
    this.fetchFields()
  },
  methods: {
    fetchFields() {
      getOfflineCategory(this.$route.params.categoryId).then((r) => {
        this.fields = r.data.fields
      })
    },
    handleSubmit() {
      this.$refs.form.validate((valid, errors) => {
        if (valid) {
          Object.keys(this.form).forEach((key) => {
            if (typeof this.form[key] === 'object' && dayjs(this.form[key]).isValid()) {
              this.form[key] = dayjs(this.form[key]).format('YYYY-MM-DD')
            }
          })
          const data = Object.assign({}, this.form)
          const loading = Loading.service()
          createOfflinePolicy(data)
            .then(() => {
              this.$message.success('保存成功')

              this.$router.push({
                name: 'OfflinePolicies'
              })
            })
            .finally(() => loading.close())
        } else {
          window.alert_validate_errors(errors)
        }
      })
    },
    downloadImportTemplateExcel() {
      const link = offlineImportTemplate(this.$route.params.categoryId)
      window.open(link, '_blank')
    },
    importSubmit() {
      const loading = Loading.service()
      this.$refs.importForm.validate((valid) => {
        if (valid) {
          importOfflinePolicy(this.$route.params.categoryId, this.importDialog.form)
            .then(() => {
              this.$message.success('数据已提交至导入队列,导入结果请查看登录账号邮箱')
              this.$router.push({
                name: 'OfflinePolicies'
              })
            })
            .finally(() => {
              loading.close()
            })
        }
      })
    },
    fetchCurrencies() {
      getLatestCurrencyExchange().then((r) => {
        this.coverageCurrencies = r.data
      })
    },
    fetchBusinessFroms(value, cb) {
      findPlatformByName(value).then((r) => {
        const data = r.data.map((item) => {
          item.value = item.name
          return item
        })
        this.businessFroms = data

        cb(data)
      })
    },
    blurBusinessFrom() {
      if (!this.businessFroms.length > 0) {
        this.form.business_from = ''
      } else if (this.businessFroms[0].value != this.form.business_from) {
        this.form.business_from = ''
      }
    },
    handleSelectBusinessFrom(name) {
      console.log(name)
    }
  }
}
</script>
