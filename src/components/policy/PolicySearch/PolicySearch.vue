<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-08 15:20:51
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-11 09:52:20
-->
<template>
  <el-card class="policy-search" shadow="never">
    <div slot="header">
      <el-row :gutter="10">
        <el-col :span="6">
          <el-date-picker
            ref="createdAtSelect"
            v-model="form.submitted_at"
            type="datetime"
            clearable
            placeholder="投保时间"
          ></el-date-picker>
        </el-col>
        <el-col :span="6">
          <el-select v-model="form.company_id" placeholder="保险公司" clearable>
            <el-option v-for="com in companies" :label="com.name" :key="com.id" :value="com.id"></el-option>
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select v-model="form.company_branch_id" placeholder="出单公司" clearable>
            <el-option v-for="com in companyBranches" :label="com.name" :key="com.id" :value="com.id"></el-option>
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select v-model="form.status" placeholder="状态" clearable>
            <el-option v-for="s in statuses" :label="s.name" :value="s.key" :key="s.key"></el-option>
          </el-select>
        </el-col>
      </el-row>
    </div>
    <el-button icon="fas fa-filter">高级搜索</el-button>
    <i class="flex-fill m-mini-l"></i>
    <el-button icon="fas fa-eraser" @click="handleResetSearch">清除查询 </el-button>
    <el-button icon="el-icon-search" @click="handleSearch" type="primary">查询</el-button>
  </el-card>
</template>

<script>
import { getCompanies } from '@/apis/company'

export default {
  name: 'PolicySearch',
  data() {
    return {
      form: {
        created_at: '',
        company_id: '',
        company_branch_id: '',
        status: ''
      },
      statuses: [
        { key: 0, name: '暂存单' },
        { key: 1, name: '已提交' },
        { key: 2, name: '审核中' },
        { key: 3, name: '已支付' },
        { key: 4, name: '审核完成' },
        { key: 5, name: '已出单' },
        { key: 6, name: '已退保' },
        { key: 7, name: '已作废' },
        { key: 8, name: '批改中' },
        { key: 9, name: '取消中' },
        { key: 10, name: '已退回' },
        { key: 11, name: '待确认' },
        { key: 12, name: '待支付' }
      ],
      companies: [],
      companyBranches: []
    }
  },
  watch: {
    'form.company_id'() {
      this.companyBranches = this.companies.find((e) => (e.id = this.form.company_id))?.branches
    }
  },
  created() {
    getCompanies().then((r) => {
      this.companies = r.data

      r.data.forEach((e) => (this.companyBranches = this.companyBranches.concat(...e.branches)))
    })
  },
  methods: {
    handleSearch() {
      this.$emit('search', Object.assign({}, this.form))
    },
    handleResetSearch() {
      this.form = Object.assign(
        {},
        {
          created_at: '',
          company_id: '',
          company_branch_id: '',
          status: ''
        }
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.policy-search {
  /deep/.el-select,
  /deep/.el-date-editor {
    width: 100% !important;
  }
  /deep/.el-card__body {
    display: flex;
  }
}
</style>
