<template>
  <div>
    <el-row>
      <el-col>
        <el-button
          style="float: right; margin-left: 5px"
          type="primary"
          @click="callbacks.visible = true"
          v-if="model.is_owned"
          icon="fa fa-flag"
        >
          录单反馈
        </el-button>
        <el-button
          v-if="downloadable"
          v-can="{ name: 'policies.group.download' }"
          style="float: right; margin-left: 5px"
          type="primary"
          @click="download"
          icon="fa fa-download"
        >
          下载保单
        </el-button>
        <el-button-group style="float: right">
          <el-button
            type="primary"
            v-can="{ name: 'policies.group.transactions.index' }"
            icon="fa fa-money-check-alt"
            @click="$router.push({ name: 'GroupTransaction', params: { policyGroupId: policyGroupId } })"
          >
            支付列表
          </el-button>
          <el-button
            type="primary"
            v-can="{ name: 'policies.group.employees.index' }"
            icon="fa fa-users"
            @click="$router.push({ name: 'GroupEmployee', params: { policyGroupId: policyGroupId } })"
          >
            人员列表
          </el-button>
        </el-button-group>
        <el-button
          style="float: right; margin-right: 5px"
          type="primary"
          v-can="{ name: 'policies.group.audit' }"
          @click="audit.visible = true"
          icon="fa fa-check-square"
          v-if="auditable && model.is_owned"
        >
          审核
        </el-button>
        <el-button
          style="float: right; margin-right: 5px"
          type="danger"
          v-can="{ name: 'policies.group.sendBack' }"
          v-if="refundable && model.is_owned"
          @click="sendback.visible = true"
          icon="fa fa-minus-circle"
        >
          退回
        </el-button>
        <el-button
          style="float: right; margin-right: 5px"
          type="danger"
          v-if="surrenderable"
          v-can="{ name: 'policies.group.surrender' }"
          @click="surrender.visible = true && model.is_owned"
          icon="fa fa-minus-circle"
        >
          退保
        </el-button>
        <el-button
          style="float: right; margin-right: 5px"
          type="primary"
          v-if="[1, 2].includes(model.status) && model.is_owned"
          v-can="{ name: 'policies.group.sendMail' }"
          @click="sendInsureEmail"
          icon="fa fa-envelope"
          :disabled="mailSent"
        >
          {{ mailSent ? '邮件发送中...' : '发送邮件' }}
        </el-button>
      </el-col>
    </el-row>

    <div class="info-box">
      <el-alert
        center
        v-if="[1, 2].includes(model.status)"
        title="保单审批中"
        description="请点击右上方【发送邮件】确认投保已通知到产品报备邮箱"
        type="warning"
        effect="dark"
        show-icon
        :closable="false"
      >
      </el-alert>
      <el-alert
        center
        v-if="model.status === 4 && model.is_owned"
        title="保单审批中"
        description="请点击右上方【审核】确认保单生效"
        type="warning"
        effect="dark"
        show-icon
        :closable="false"
      >
      </el-alert>
      <el-alert
        center
        v-if="model.status === 6"
        title="保单已退保"
        description=" "
        type="warning"
        effect="dark"
        show-icon
        :closable="false"
      >
      </el-alert>
      <el-alert
        center
        v-if="model.status === 8 && model.is_owned"
        title="保单批改中"
        description="请进入【人员列表】中的【人员批单】进行审批"
        type="warning"
        effect="dark"
        show-icon
        :closable="false"
      >
      </el-alert>
      <el-alert
        center
        v-if="model.status === 9"
        title="保单退保申请中"
        description="保单退保申请中，等待接口回调处理"
        type="warning"
        effect="dark"
        show-icon
        :closable="false"
      >
      </el-alert>
      <el-alert
        center
        v-if="model.status === 10"
        title="保单已退回"
        :description="this.policyGroup.sendback_reason"
        type="warning"
        effect="dark"
        show-icon
        :closable="false"
      >
      </el-alert>
    </div>

    <el-dialog title="审核保单" width="520px" :visible.sync="audit.visible">
      <el-form ref="auditForm" :model="audit.form" :rules="audit.rules" label-suffix=":" label-width="100px">
        <template v-if="productPlatform === 'API_GROUP_ZY'">
          <el-form-item label="保单号">
            <span>{{ policyGroup.policy_no }}</span>
          </el-form-item>
          <el-form-item label="保单文件" v-if="policyGroup.policy_no">
            <el-link @click="download">查看</el-link>
          </el-form-item>
          <el-alert :closable="false"> 该保单为系统自动录单处理，若未出现保单号请稍后查看 </el-alert>
        </template>
        <template v-else>
          <el-form-item prop="policy_no" label="保单号">
            <el-input v-model="audit.form.policy_no" placeholder="请输入保单号"></el-input>
          </el-form-item>
          <el-form-item prop="policy_file" label="保单文件">
            <upload-file v-model="audit.form.policy_file" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleAuditPolicySubmit()">提交</el-button>
          </el-form-item>
        </template>
      </el-form>
    </el-dialog>

    <el-dialog title="退回" width="520px" :visible.sync="sendback.visible">
      <el-form ref="sendbackForm" :model="sendback.form" :rules="sendback.rules" label-suffix=":" label-width="100px">
        <el-form-item prop="sendback_reason" label="理由">
          <el-input v-model="sendback.form.sendback_reason" placeholder="请输入理由"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSendBackPolicySubmit()">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog title="退保" :visible.sync="surrender.visible" width="600px">
      <template
        v-if="
          policyGroup.product?.additional?.payment_type === 2 &&
          policyGroup.product?.additional?.third_platform === 'API_GROUP_ZY'
        "
      >
        <el-alert
          title="温馨提示"
          :description="policyGroup.payment_method === 1 ? '请输入原付款人信息' : '请输入投保人或被保人收款信息'"
          show-icon
          type="warning"
          :closable="false"
        ></el-alert>
        <el-form ref="refundForm" :model="surrender.form" labelPosition="left" :rules="surrenderRules">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="收款人" prop="accountName">
                <el-input v-model="surrender.form.accountName" placeholder="请输入收款人名称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="银行卡号" prop="accountcode">
                <el-input v-model="surrender.form.accountcode" placeholder="请输入银行卡号"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="收款银行" prop="bankcode">
                <el-select v-model="surrender.form.bankcode" placeholder="请选择收款银行" class="w-100" filterable>
                  <el-option v-for="(bank, bankCode) in banks" :key="bankCode" :value="bankCode" :label="bank" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开户地址" prop="bankAddress">
                <div class="d-flex w-100">
                  <el-cascader
                    filterable
                    v-model="surrender.form.bankAddress"
                    :style="{ width: '500px' }"
                    :options="regions"
                    :props="{
                      expandTrigger: 'hover',
                      filterable: true,
                      value: 'code',
                      label: 'value',
                      children: 'city'
                    }"
                  ></el-cascader>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="开户行名称" prop="customBankName">
                <el-input v-model="surrender.form.customBankName" placeholder="请输入开户行"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="银联行号" prop="customBankCode">
                <el-input v-model="surrender.form.customBankCode" placeholder="请输入银联行号"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="付款备注" prop="compensateRemark">
            <el-input v-model="surrender.form.compensateRemark" placeholder="请输入付款备注"></el-input>
          </el-form-item>
          <el-form-item label="退保原因" prop="refundDesc">
            <el-input v-model="surrender.form.refundDesc" placeholder="请输入退保原因"></el-input>
          </el-form-item>
        </el-form>
      </template>
      <template v-else>
        <span>是否发起保单退保申请？</span>
      </template>

      <span slot="footer" class="dialog-footer">
        <el-button @click="surrender.visible = false">取 消</el-button>
        <el-button type="primary" @click="handlePolicySurrender">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog
      v-if="policyGroup.policy_group"
      title="录单反馈"
      width="750px"
      :visible.sync="callbacks.visible"
      :lockScroll="false"
    >
      <el-tabs v-model="activeTab">
        <el-tab-pane label="投保接口" name="insure">
          <el-alert title="投保接口返回" :closable="false" class="m-mini-b">
            <pre>{{ insureMessage }}</pre>
          </el-alert>
          <el-alert title="录单回调信息" :closable="false" class="m-mini-b">
            <pre>{{ insureCallbackMessage }}</pre>
          </el-alert>
        </el-tab-pane>
        <el-tab-pane label="批单接口" name="modify">
          <el-alert title="批单接口返回" :closable="false" class="m-mini-b">
            <pre>{{ modifyMessage }}</pre>
          </el-alert>
          <el-alert title="录单回调信息" :closable="false" class="m-mini-b">
            <pre>{{ modifyCallbackMessage }}</pre>
          </el-alert>
        </el-tab-pane>
        <el-tab-pane label="退保接口" name="cancel">
          <el-alert title="退保接口返回" :closable="false">
            <pre>{{ cancelMessage }}</pre>
          </el-alert>
          <el-alert title="录单回调信息" :closable="false" class="m-mini-b">
            <pre>{{ cancelCallbackMessage }}</pre>
          </el-alert>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
</template>

<script>
import { groupPolicyAudit, sendGroupInsuredEmail, sendGroupPolicyBack, groupPolicySurrender } from '@/apis/policy'
import UploadFile from '../../globals/UploadFile/UploadFile'
import { tokenKey } from '@/config'
import { Loading } from 'element-ui'
import regions from '@/utils/areadata'
import banks from './banks'

export default {
  name: 'PolicyGroupOperationBar',
  components: { UploadFile },
  props: {
    model: {
      type: Object,
      default: () => {}
    },
    policyGroup: {
      type: Object,
      default: () => {}
    },
    productPlatform: {
      type: String,
      default: () => ''
    }
  },
  computed: {
    auditable() {
      return [4, 5].includes(this.model.status)
    },
    downloadable() {
      return this.model.status === 5 || this.model.policy_no
    },
    refundable() {
      return (
        [1, 2, 4].includes(this.model.status) ||
        (this.productPlatform === 'API_GROUP_ZY' && ![5, 7, 6, 9, 10].includes(this.model.status))
      )
    },
    surrenderable() {
      // 中意产品，已出单才允许退保。
      return this.productPlatform === 'API_GROUP_ZY' && [5].includes(this.model.status)
    },
    policyGroupId() {
      return this.policyGroup.policy_group.id
    },
    insureMessage() {
      return this.policyGroup?.policy_group?.extra_info?.insure ?? '~~'
    },
    insureCallbackMessage() {
      return this.policyGroup?.policy_group?.extra_info?.callbacks?.insure ?? '~~'
    },
    modifyMessage() {
      return this.policyGroup?.policy_group?.extra_info?.modify ?? '~~'
    },
    modifyCallbackMessage() {
      return this.policyGroup?.policy_group?.extra_info?.callbacks?.modify ?? '~~'
    },
    cancelMessage() {
      return this.policyGroup?.policy_group?.extra_info?.cancel ?? '~~'
    },
    cancelCallbackMessage() {
      return this.policyGroup?.policy_group?.extra_info?.callbacks?.cancel ?? '~~'
    }
  },
  data() {
    return {
      banks,
      regions,
      mailSent: false,
      activeTab: 'insure',
      audit: {
        visible: false,
        form: {
          policy_no: '',
          policy_file: '',
          product_type: '',
          ins_rate: 0,
          user_rate: 0
        },
        rules: {
          policy_no: [{ required: true, message: '请输入保单号', trigger: 'blur' }],
          policy_file: [{ required: true, message: '请上传保单文件', trigger: ['blur', 'change'] }]
        }
      },
      sendback: {
        visible: false,
        form: {
          sendback_reason: ''
        },
        rules: {
          sendback_reason: [{ required: true, message: '请输入退回原因', trigger: 'blur' }]
        }
      },
      surrender: {
        visible: false,
        form: {
          accountName: '',
          accountcode: '',
          bankcode: '',
          bankname: '',
          customBankCode: '',
          customBankName: '',
          bankAddress: '',
          bankProvince: '',
          bankCity: '',
          unionBank: '',
          compensateRemark: '保单退保',
          recareaCode: '',
          reccityName: '',
          refundDesc: ''
        }
      },
      surrenderRules: {
        accountName: [{ required: true, message: '请输入收款账户开户名', trigger: 'blur' }],
        accountcode: [{ required: true, message: '请输入银行卡号', trigger: 'blur' }],
        bankcode: [{ required: true, message: '请选择收款银行', trigger: ['blur', 'change'] }],
        customBankCode: [{ required: true, message: '请输入银行联行号', trigger: 'blur' }],
        customBankName: [{ required: true, message: '请输入开户行名称', trigger: 'blur' }],
        compensateRemark: [{ required: true, message: '请输入付款备注', trigger: 'blur' }],
        bankAddress: [{ required: true, message: '请选择开户行地址', trigger: ['blur', 'change'] }],
        refundDesc: [{ required: true, message: '请输入退保原因', trigger: 'blur' }]
      },
      callbacks: {
        visible: false
      }
    }
  },
  watch: {
    'surrender.form.accountcode'(value) {
      value = value.replace(' ', '')
      if (value != this.surrender.form.accountcode) {
        this.surrender.form.accountcode = value
      }
    },
    'surrender.form.bankcode'() {
      this.surrender.form.bankname = this.banks[this.surrender.form.bankcode]
    },
    'surrender.form.customBankCode'(value) {
      this.surrender.form.unionBank = value
    },
    'surrender.form.bankAddress'(value) {
      this.surrender.form.bankProvince = value[1].slice(0, 2)

      const specialCity = ['110000', '310000', '120000', '500000']
      this.surrender.form.bankProvince = value[1].slice(0, 2)
      if (specialCity.includes(value[0])) {
        this.surrender.form.bankCity = value[0].slice(0, 4)
        this.surrender.form.recareaCode = this.surrender.form.bankCity
      } else {
        this.surrender.form.bankCity = value[1].slice(0, 4)
        this.surrender.form.recareaCode = this.surrender.form.bankCity
      }

      const province = this.regions.find((item) => item.code === value[0])
      const city = province?.city?.find((item) => item.code === value[1])

      this.surrender.form.reccityName = city?.value
    }
  },
  methods: {
    download() {
      let baseUrl = process.env.VUE_APP_BASE_API
      if (!process.env.VUE_APP_BASE_API.startsWith('http')) {
        baseUrl = `${window.location.origin}${baseUrl}`
      }

      let link = baseUrl + `policies/group/${this.model.id}/download?token=` + window.localStorage.getItem(tokenKey)

      window.open(link, '_blank')
    },
    handleAuditPolicySubmit() {
      this.$refs.auditForm.validate((valid) => {
        if (valid) {
          const loading = Loading.service()
          groupPolicyAudit(this.policyGroupId, this.audit.form)
            .then(() => {
              loading.close()
              this.$message.success('审核成功')
              this.audit.visible = false
              this.$router.push({ name: 'PoliciesGroup' })
              // this.$emit('operated', this.model)
            })
            .finally(() => {
              loading.close()
            })
        }
      })
    },
    sendInsureEmail() {
      this.mailSent = true
      let infoEmailText = ((emails) => {
        emails = String(emails)
          .split(';')
          .filter((e) => {
            return e.length !== 0
          })
        let _t = ''
        for (let email of emails) {
          _t += `<li><span class="text-blue">${email}</span></li>`
        }
        return `<ul>${_t}</ul>`
      })(this.model?.policy_group?.product?.additional?.email)
      const info = `将发送邮件到 <b>${this.model?.policy_group?.product?.name}</b> 的报备邮箱。<span class="text-info">下列邮箱会收到邮件</span><br> ${infoEmailText}`
      this.$confirm(info, '发送邮件', {
        confirmButtonText: '继续',
        cancelButtonText: '取消',
        type: 'success',
        closeOnClickModal: false,
        dangerouslyUseHTMLString: true
      })
        .then(() => {
          const loading = Loading.service()
          sendGroupInsuredEmail(this.policyGroupId)
            .then((r) => {
              this.$message.info(r.data.message)

              this.$emit('operated', this.model)
            })
            .finally(() => {
              this.mailSent = false
              setTimeout(() => {
                loading.close()
              }, 300)
            })
        })
        .finally(() => {
          this.mailSent = false
        })
    },
    handleSendBackPolicySubmit() {
      this.$refs.sendbackForm.validate((valid) => {
        if (valid) {
          const loading = Loading.service()
          sendGroupPolicyBack(this.model.policy_group.id, this.sendback.form)
            .then(() => {
              loading.close()
              this.$message.success('操作成功')

              this.sendback.visible = false
              this.$emit('operated', this.model)
            })
            .finally(() => {
              loading.close()
            })
        }
      })
    },
    handlePolicySurrender() {
      const send = () => {
        const loading = Loading.service()
        groupPolicySurrender(this.model.policy_group.id, this.surrender.form)
          .then(() => {
            loading.close()
            this.$message.success('操作成功')

            this.surrender.visible = false
            this.$emit('operated', this.model)
          })
          .finally(() => {
            loading.close()
          })
      }

      if (this.policyGroup.payment_method === 1) {
        this.$refs.refundForm.validate((valid) => {
          if (valid) {
            send()
          }
        })
      } else {
        send()
      }
    },
    optional(value, option) {
      return value ? value : option
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0px !important;
}

.info-box {
  padding-top: 12px;
}
</style>
