<template>
  <div>
    <el-row>
      <el-col class="btns">
        <template v-if="model.is_owned">
          <el-button type="primary" v-can="{ name: 'policies.audit' }" v-if="email" @click="handleEmail">
            发送邮件
          </el-button>
          <template v-if="isAssociable">
            <el-popconfirm title="确认领取保单吗？" v-can="{ name: 'policies.audit' }" @confirm="associateAuditor">
              <el-button icon="fas fa-link" slot="reference" type="primary">领取</el-button>
            </el-popconfirm>
          </template>
          <template v-else>
            <el-button
              icon="fas fa-check-double"
              type="primary"
              v-can="{ name: 'policies.audit' }"
              v-if="isOperable"
              @click="audit.visible = true"
            >
              审核
            </el-button>
          </template>

          <el-popconfirm
            title="确认重提保单吗？"
            v-can="{ name: 'policies.resubmit' }"
            v-if="sendbackable && isOperable"
            @confirm="handleResubmit"
          >
            <el-button icon="fas fa-sync" slot="reference" type="primary"> 重新提交 </el-button>
          </el-popconfirm>
          <el-button
            type="primary"
            icon="fas fa-backspace"
            v-can="{ name: 'policies.sendback' }"
            v-if="sendbackable && isOperable"
            @click="sendback.visible = true"
          >
            退回
          </el-button>
          <el-button
            type="primary"
            icon="fas fa-backspace"
            v-can="{ name: 'policies.sendback' }"
            v-if="sendbackable && isOperable"
            @click="sendBackSupplementInfo.visible = true"
          >
            退回(补充资料)
          </el-button>
          <el-button
            type="primary"
            icon="fas fa-clipboard-list"
            v-can="{ name: 'policies.export-cpic-word' }"
            v-if="isOperable && canExportCpicWord"
            @click="exportCpicDocx"
          >
            太保投保单
          </el-button>

          <el-button
            type="primary"
            icon="fas fa-backspace"
            v-can="{ name: 'policies.sendback' }"
            v-if="model.payment_method === 2 && model.status === 12"
            @click="sendBackUnPaidPolicy"
          >
            未支付退回
          </el-button>
        </template>
        <el-button
          icon="fas fa-download"
          type="primary"
          v-can="{ name: 'policies.download' }"
          v-if="downloadable"
          @click="download"
        >
          下载
        </el-button>
      </el-col>
    </el-row>

    <el-alert
      v-if="!model?.policy_no && !!model?.apply_no"
      type="warning"
      class="m-extra-large-t"
      :title="model?.callback_message"
      :closable="false"
      show-icon
    />

    <el-row v-if="model?.tickets?.length > 0">
      <el-col>
        <el-alert
          v-for="(ticket, idx) in model?.tickets"
          show-icon
          :title="
            `批改记录 [${ticket.status_text}] - 提交于: ${ticket.submitted_at}` +
            (ticket?.status === 2 ? ` 完成时间: ${ticket.done_at}` : '')
          "
          :key="idx"
          :type="ticket.status === 2 ? 'success' : 'warning'"
          :closable="false"
          class="m-extra-large-t"
        >
          <template>
            <div v-if="ticket.reason"><b>退回理由：</b> {{ ticket.reason }}</div>
            <div v-html="ticket.content"></div>
          </template>
        </el-alert>
      </el-col>
    </el-row>

    <el-row v-if="model?.papers?.length > 0">
      <el-col>
        <el-alert
          v-for="(paper, idx) in model?.papers"
          show-icon
          :title="paper.status | paperStatusTitle"
          :type="paper.status | paperStatusStyle"
          :key="idx"
          :closable="false"
          class="m-extra-large-t"
        >
          <template v-if="paper.status === 1">
            客户于 {{ paper.created_at }} 申请的纸质保单正在处理中请耐心等待，如有疑问请联系客服
          </template>
          <template v-if="paper.status === 2">
            <div v-if="paper.type === 3">
              客户于 {{ paper.created_at }} 申请的纸质保单已打印完成，请联系客服获取自取信息
            </div>
            <div v-else>
              客户于 {{ paper.created_at }} 申请的纸质保单已通过 <b>{{ paper.express_company }}</b> 快递配送，快递单号：
              <b>{{ paper.express_no }}</b>
            </div>
          </template>
          <template v-if="paper.status === 3">
            客户于 {{ paper.created_at }} 申请的纸质保单因提交批改已被取消，如有疑问请联系客服
            <div>
              退回理由: <b>{{ paper.reason }}</b>
            </div>
          </template>
        </el-alert>
      </el-col>
    </el-row>

    <el-row class="m-extra-large-t" v-if="model?.remark">
      <el-col>
        <el-alert show-icon title="备注" type="warning" effect="dark" :closable="false">
          <template>
            <div v-html="model?.remark"></div>
          </template>
        </el-alert>
      </el-col>
    </el-row>

    <el-row v-if="showAlert" class="m-extra-large-t">
      <el-col>
        <el-alert show-icon :title="alertTitle" type="warning" :closable="false">
          <template>
            <div v-html="alertDescription"></div>
          </template>
        </el-alert>
      </el-col>
    </el-row>

    <el-dialog title="退回" width="520px" :visible.sync="sendback.visible">
      <el-form ref="sendbackForm" :model="sendback.form" :rules="sendback.rules" label-suffix=":" label-width="100px">
        <el-form-item prop="sendback_reason" label="理由">
          <el-input v-model="sendback.form.sendback_reason" placeholder="请输入理由"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSendBackPolicySubmit()">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog title="退回(补充资料)" width="520px" :visible.sync="sendBackSupplementInfo.visible">
      <el-form
        ref="sendBackSupplementInfoForm"
        :model="sendBackSupplementInfo.form"
        :rules="sendBackSupplementInfo.rules"
        label-suffix=":"
        label-width="100px"
      >
        <el-form-item prop="sendback_reason" label="理由">
          <el-input v-model="sendBackSupplementInfo.form.sendback_reason" placeholder="请输入理由"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSendBackSupplementInfoPolicySubmit()">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog title="保单审核" width="900px" :visible.sync="audit.visible">
      <el-form ref="auditForm" :model="audit.form" :rules="auditRules" label-suffix=":" label-width="120px">
        <template v-if="isManaulInsure && model?.product?.is_automatable && !model?.policy_no">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item prop="is_automatically" label="自动录入">
                <el-radio-group v-model="audit.form.is_automatically">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="option" label="录单配置" v-if="audit.form.is_automatically">
                <el-select v-model="audit.form.option" placeholder="请选择录单配置" class="w-100">
                  <el-option
                    v-for="item in model?.product?.options"
                    :key="item"
                    :label="item"
                    :value="item"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
        <template v-if="audit.form.is_automatically">
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item prop="special" label="特别约定">
                <el-input
                  type="textarea"
                  :rows="3"
                  v-model="audit.form.special"
                  placeholder="请输入特别约定"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item prop="deductible" label="免赔">
                <el-input type="textarea" :rows="3" v-model="audit.form.deductible" placeholder="请输入免赔" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24" v-if="model?.company?.identifier === 'PINGAN'">
            <el-col :span="24">
              <el-form-item prop="custom_deductible" label="自定义免赔">
                <el-input
                  type="textarea"
                  :rows="3"
                  v-model="audit.form.custom_deductible"
                  placeholder="请输入自定义免赔，请按格式填写 免赔率损失金额比例|免赔率保险金额比例|免赔额|免赔额币种"
                />
                <div style="font-size: 12px; color: #666">
                  请输入自定义免赔，请按格式填写: 免赔率损失金额比例|免赔率保险金额比例|免赔额|免赔额币种
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
        <template v-if="!audit.form.is_automatically">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item prop="policy_no" label="保单号">
                <el-input v-model="audit.form.policy_no" placeholder="请输入保单号"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="policy_file" label="保单文件">
                <upload-file v-model="audit.form.policy_file" />
              </el-form-item>
            </el-col>
          </el-row>
        </template>
        <template v-if="isManaulInsure">
          <el-alert
            v-if="model.is_premium_sync"
            show-icon
            title="当前为保费同步保单，请录入相同成本费率、平台费率、代理费率、用户费率"
            type="warning"
            :closable="false"
            class="m-extra-large-b"
          />
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item prop="rate" label="成本费率">
                <el-input type="number" :min="1" :max="100" v-model="audit.form.rate" placeholder="请输入成本费率">
                  <template slot="append">‱</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="premium" label="成本保费">
                <el-input type="number" :min="1" :max="100" v-model="audit.form.premium" placeholder="成本保费">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="service_charge" label="经纪费比例">
                <el-input
                  type="number"
                  :min="1"
                  :max="100"
                  v-model="audit.form.service_charge"
                  placeholder="请输入经纪费比例"
                >
                  <template slot="append">%&nbsp;</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <template v-if="isCrossPlatform">
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item prop="platform_rate" label="平台费率" required>
                  <el-input
                    type="number"
                    :min="1"
                    :max="100"
                    v-model="audit.form.platform_rate"
                    placeholder="请输入平台费率"
                  >
                    <template slot="append">‱</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item prop="platform_premium" label="平台成本保费">
                  <el-input
                    type="number"
                    :min="1"
                    :max="100"
                    v-model="audit.form.platform_premium"
                    placeholder="平台成本保费"
                  >
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item prop="platform_commission_rate" label="平台佣金比例">
                  <el-input
                    type="number"
                    :min="1"
                    :max="100"
                    v-model="audit.form.platform_commission_rate"
                    placeholder="请输入平台佣金比例"
                  >
                    <template slot="append">%&nbsp;</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
          <template v-if="showAgentRateFormItem">
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item prop="agent_rate" label="代理费率" required>
                  <el-input
                    type="number"
                    :min="1"
                    :max="100"
                    v-model="audit.form.agent_rate"
                    placeholder="请输入代理费率"
                  >
                    <template slot="append">‱</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item prop="agent_premium" label="代理保费" required>
                  <el-input type="number" :min="1" :max="100" v-model="audit.form.agent_premium" placeholder="代理保费">
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item prop="agent_commission_rate" label="代理佣金比例">
                  <el-input
                    type="number"
                    :min="1"
                    :max="100"
                    v-model="audit.form.agent_commission_rate"
                    placeholder="请输入代理佣金比例"
                  >
                    <template slot="append">%&nbsp;</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
          <template v-if="showUserRateFormItem">
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item prop="user_rate" label="用户费率">
                  <el-input
                    type="number"
                    :min="1"
                    :max="100"
                    v-model="audit.form.user_rate"
                    placeholder="请输入用户费率"
                  >
                    <template slot="append">‱</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item prop="user_premium" label="用户保费" required>
                  <el-input type="number" :min="1" :max="100" v-model="audit.form.user_premium" placeholder="用户保费">
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
        </template>
        <el-form-item>
          <el-button type="primary" @click="handleAuditPolicySubmit()" icon="fas fa-check">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-card shadow="never" class="m-extra-large-t">
      <define-table :cols="logsCols" :data="model?.logs" />
    </el-card>
  </div>
</template>

<script>
import {
  auditPolicy,
  associatePolicyAuditor,
  resubmitPolicy,
  buildDownloadHref,
  sendBackPolicy,
  sendBackUnPaidPolicy,
  sendBackSupplementInfoPolicy,
  exportCpicWord
} from '@/apis/policy'
import { Loading } from 'element-ui'
import { mapGetters } from 'vuex'

export default {
  name: 'PolicyOperationBar',
  props: {
    model: {
      type: Object,
      default: () => {}
    },
    email: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapGetters('auth', ['admin']),
    isAssociable() {
      if (this.model.auditor_id === -1 && [1, 2, 11, 15].includes(this.model.status)) {
        return true
      }

      return false
    },
    isOperable() {
      if (this.model.status === 5) {
        return true
      }

      return this.model.auditor_id !== -1
    },
    downloadable() {
      return [5, 8].includes(this.model.status) && this.model.downloadable
    },
    canExportCpicWord() {
      return [1, 2].includes(this.model.status) && this.model.company.identifier === 'CPIC'
    },
    sendbackable() {
      return [1, 2, 11, 15].includes(this.model.status)
    },
    isManaulInsure() {
      return this.model?.detail?.subject?.identifier === 'MANUAL'
    },
    showAlert() {
      return [6, 10].includes(this.model?.status)
    },
    alertTitle() {
      const statuses = {
        6: '已退保',
        10: '已退回',
        13: '已退回(补充资料)'
      }
      return `该保单${statuses[this.model?.status]}`
    },
    alertDescription() {
      const statuses = {
        6: {
          title: '退保原因:',
          content: this.model?.surrender_reason
        },
        10: {
          title: '退回原因:',
          content: this.model?.sendback_reason
        },
        13: {
          title: '退回原因:',
          content: this.model?.sendback_reason
        }
      }
      return `${statuses[this.model?.status].title} ${statuses[this.model?.status].content}`
    },
    showAgentRateFormItem() {
      // 如果是代理下面的用户或者代理投保
      return this.model?.user?.agent_id !== -1 || this.model?.user?.is_agent
    },
    showUserRateFormItem() {
      // 如果是代理下面的用户或者是平台直属客户
      return this.model?.user?.agent_id !== -1 || (!this.model?.user?.is_agent && this.model?.user?.agent_id === -1)
    },
    isCrossPlatform() {
      return this.model?.is_cross_platform_product
    },
    auditRules() {
      let rules = {
        is_automatically: [{ required: true, message: '请选择是否自动出单', trigger: 'change' }]
      }

      if (!this.audit.form.is_automatically) {
        rules = Object.assign({}, rules, {
          policy_no: [{ required: true, message: '请输入保单号', trigger: 'blur' }],
          policy_file: [{ required: true, message: '请上传保单文件', trigger: ['blur', 'change'] }]
        })
      } else {
        rules = Object.assign({}, rules, {
          option: [{ required: true, message: '请选择录单配置', trigger: 'blur' }]
        })
      }

      if (this.isManaulInsure) {
        rules = Object.assign({}, rules, {
          company_branch_id: [{ required: true, message: '请选择出单公司', trigger: 'change' }],
          rate: [{ required: true, message: '请输入成本费率', trigger: 'blur' }],
          premium: [{ required: true, message: '请输入成本保费', trigger: 'blur' }],
          service_charge: [{ required: true, message: '请输入经纪费比例', trigger: 'blur' }]
        })

        if (this.showAgentRateFormItem) {
          rules.agent_rate = [{ required: true, message: '请输入代理费率', trigger: 'blur' }]
          rules.agent_premium = [{ required: true, message: '请输入用户保费', trigger: 'blur' }]
          rules.agent_commission_rate = [{ required: true, message: '请输入代理佣金比例', trigger: 'blur' }]
        }

        if (this.showUserRateFormItem) {
          rules.user_rate = [{ required: true, message: '请输入用户费率', trigger: 'blur' }]
          rules.user_premium = [{ required: true, message: '请输入用户保费', trigger: 'blur' }]
        }

        if (this.isCrossPlatform) {
          rules.platform_rate = [{ required: true, message: '请输入平台费率', trigger: 'blur' }]
          rules.platform_premium = [{ required: true, message: '请输入平台保费', trigger: 'blur' }]
          rules.platform_commission_rate = [{ required: true, message: '请输入平台佣金比例', trigger: 'blur' }]
        }
      }

      return rules
    }
  },
  filters: {
    paperStatusTitle(status) {
      const statuses = {
        1: '纸质保单申请处理中',
        2: '纸质保单已发出',
        3: '纸质保单申请已退回'
      }

      return statuses[status] || '未知状态'
    },
    paperStatusStyle(status) {
      const styles = {
        1: 'warning',
        2: 'success',
        3: 'error'
      }

      return styles[status] || 'info'
    }
  },
  data() {
    return {
      logsCols: [
        { prop: 'created_at', label: '操作时间', width: 180 },
        { prop: 'operator', label: '操作人', width: 120 },
        { prop: 'content', label: '操作内容' }
      ],
      audit: {
        company_branches: [],
        visible: false,
        form: {
          is_automatically: 0,
          option: '',
          policy_no: '',
          policy_file: '',
          product_type: '',
          rate: '',
          premium: '',
          platform_rate: '',
          platform_premium: '',
          platform_commission_rate: '',
          agent_rate: '',
          agent_premium: '',
          agent_commission_rate: '',
          user_rate: '',
          user_premium: '',
          service_charge: '',
          special: '',
          deductible: '',
          custom_deductible: ''
        }
      },
      sendback: {
        visible: false,
        form: {
          sendback_reason: ''
        },
        rules: {
          sendback_reason: [{ required: true, message: '请输入退回原因', trigger: 'blur' }]
        }
      },
      sendBackSupplementInfo: {
        visible: false,
        form: {
          sendback_reason: ''
        },
        rules: {
          sendback_reason: [{ required: true, message: '请输入退回原因', trigger: 'blur' }]
        }
      },
      sendBackUnPaid: false
    }
  },
  watch: {
    'audit.form.rate'(value) {
      if (value === this.model.cost_rate) {
        return
      }

      const premium = parseFloat(
        (((this.model?.coverage * 100 * value) / 10000) *
          ([2, 7].includes(this.model?.source_type) ? parseFloat(this.model?.detail?.coverage_currency?.rate, 5) : 1)) /
          100
      ).toFixed(2)

      this.audit.form.premium = premium < this.model?.minimum_premium ? this.model?.minimum_premium : premium

      if (this.model?.is_premium_sync) {
        this.audit.form.platform_rate = value
        this.audit.form.agent_rate = value
        this.audit.form.user_rate = value
      }
    },
    'audit.form.platform_rate'(value) {
      if (value === this.model.platform_rate) {
        return
      }
      const premium = parseFloat(
        (((this.model?.coverage * 100 * value) / 10000) *
          ([2, 7].includes(this.model?.source_type) ? parseFloat(this.model?.detail?.coverage_currency?.rate, 5) : 1)) /
          100
      ).toFixed(2)

      this.audit.form.platform_premium =
        premium < this.model?.platform_minimum_premium ? this.model?.platform_minimum_premium : premium

      if (this.model?.is_premium_sync) {
        this.audit.form.rate = value
        this.audit.form.agent_rate = value
        this.audit.form.user_rate = value
      }
    },
    'audit.form.agent_rate'(value) {
      if (value === this.model.agent_rate) {
        return
      }
      const premium = parseFloat(
        (((this.model?.coverage * 100 * value) / 10000) *
          ([2, 7].includes(this.model?.source_type) ? parseFloat(this.model?.detail?.coverage_currency?.rate, 5) : 1)) /
          100
      ).toFixed(2)

      this.audit.form.agent_premium =
        premium < this.model?.agent_minimum_premium ? this.model?.agent_minimum_premium : premium

      if (this.model?.is_premium_sync) {
        this.audit.form.rate = value
        this.audit.form.platform_rate = value
        this.audit.form.user_rate = value
      }
    },
    'audit.form.user_rate'(value) {
      if (value === this.model.actual_user_rate) {
        return
      }
      const premium = parseFloat(
        (((this.model?.coverage * 100 * value) / 10000) *
          ([2, 7].includes(this.model?.source_type) ? parseFloat(this.model?.detail?.coverage_currency?.rate, 5) : 1)) /
          100
      ).toFixed(2)

      this.audit.form.user_premium =
        premium < this.model?.user_minimum_premium ? this.model?.user_minimum_premium : premium

      // 保费同步
      if (this.model?.is_premium_sync) {
        this.audit.form.rate = value
        this.audit.form.platform_rate = value
        this.audit.form.agent_rate = value
      }
    },
    // 保费同步
    'audit.form.user_premium'(value) {
      if (this.model?.is_premium_sync) {
        this.audit.form.premium = value
        this.audit.form.platform_premium = value
        this.audit.form.agent_premium = value
      }
    },
    model: {
      deep: true,
      immediate: true,
      handler() {
        const mergeData = {
          policy_no: this.model?.policy_no,
          rate: this.model?.cost_rate,
          premium: this.model?.cost_premium,
          service_charge: this.model?.service_charge,
          special: this.model?.detail?.special,
          deductible: this.model?.detail?.deductible
        }

        if (this.showAgentRateFormItem) {
          mergeData.agent_rate = this.model?.agent_rate || ''
          mergeData.agent_premium = this.model?.agent_premium || ''
          mergeData.agent_commission_rate = this.model?.agent_commission_rate || ''
        }

        if (this.showUserRateFormItem) {
          mergeData.user_rate = this.model?.actual_user_rate || ''
          mergeData.user_premium = this.model?.user_premium || ''
        }

        if (this.isCrossPlatform) {
          mergeData.platform_rate = this.model?.platform_rate || ''
          mergeData.platform_premium = this.model?.platform_premium || ''
          mergeData.platform_commission_rate = this.model?.platform_commission_rate || ''
        } else {
          mergeData.platform_rate = this.model?.cost_rate
        }

        this.audit.form = Object.assign({}, this.audit.form, mergeData)
      }
    }
  },
  methods: {
    associateAuditor() {
      associatePolicyAuditor(this.model.id).then(() => {
        this.$message.success('关联成功')

        window.location.reload()
      })
    },
    handleEmail() {
      this.$emit('send-email')
    },
    download() {
      const link = buildDownloadHref(this.model.id)

      window.open(link, '_blank')
    },
    exportCpicDocx() {
      const link = exportCpicWord(this.model.id)

      window.open(link, '_blank')
    },
    handleAuditPolicySubmit() {
      this.$refs.auditForm.validate((valid) => {
        if (valid) {
          const loading = Loading.service()
          this.audit.form.product_type = this.model.detail.subject.identifier
          if (!this.isCrossPlatform) {
            this.audit.form.platform_rate = this.audit.form.rate
            this.audit.form.platform_premium = this.audit.form.premium
          }

          auditPolicy(this.model.id, this.audit.form)
            .then(() => {
              this.$message.success('审核成功')
              this.audit.visible = false

              this.$emit('operated', 'audited', this.model)

              this.$refs.auditForm.resetFields()
            })
            .finally(() => loading.close())
        }
      })
    },
    handleSendBackPolicySubmit() {
      const loading = Loading.service()
      this.$refs.sendbackForm.validate((valid) => {
        if (valid) {
          const action = this.sendBackUnPaid
            ? sendBackUnPaidPolicy(this.model.id, this.sendback.form)
            : sendBackPolicy(this.model.id, this.sendback.form)
          action
            .then(() => {
              this.sendback.visible = false

              this.$message.success('退回成功')

              this.$emit('operated', 'sendback', this.model)
            })
            .finally(() => loading.close())
        }
      })
    },
    handleSendBackSupplementInfoPolicySubmit() {
      const loading = Loading.service()
      this.$refs.sendBackSupplementInfoForm.validate((valid) => {
        if (valid) {
          sendBackSupplementInfoPolicy(this.model.id, this.sendBackSupplementInfo.form)
            .then(() => {
              this.$message.success('退回成功')

              this.sendBackSupplementInfo.visible = false

              this.$emit('operated', 'sendback', this.model)
            })
            .finally(() => loading.close())
        }
      })
    },
    handleResubmit() {
      resubmitPolicy(this.model.id).then(() => {
        this.$message.success('重新提交成功')

        this.$emit('operated', 'resubmit', this.model)
      })
    },
    sendBackUnPaidPolicy() {
      this.sendBackUnPaid = true
      this.sendback.visible = true
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0px !important;
}

.btns {
  text-align: right;

  /deep/ .el-button {
    margin-left: 10px;
  }
}
</style>
