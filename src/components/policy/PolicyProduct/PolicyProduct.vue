<!--
 * @Descripttion:
 * @Author: Mr. <PERSON><PERSON>
 * @Date: 2021-01-11 11:06:11
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-11 14:40:02
-->
<template>
  <el-form class="policy-prodect bg-white" label-position="top">
    <el-tabs type="border-card" v-model="type">
      <el-tab-pane name="first" label="普通火货物">
        <el-form-item required label="请选择主险">
          <el-radio-group v-model="value1">
            <el-radio border :label="1">基本险</el-radio>
            <el-radio border :label="2">基本险</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item required label="请选择附加险">
          <el-radio-group v-model="value2">
            <el-radio border :label="1">仓至仓，盗抢险</el-radio>
            <el-radio border :label="2">放弃追偿</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item required label="请选择保险公司">
          <el-radio-group v-model="value3">
            <el-radio border :label="1">中国人保</el-radio>
            <el-radio border :label="2">中国平安</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-tab-pane>
      <el-tab-pane name="second" label="果蔬鲜活">果蔬鲜活</el-tab-pane>
      <el-tab-pane name="third" label="易碎货物">易碎货物</el-tab-pane>
    </el-tabs>
    <div class="selected-content">
      <el-alert type="warning">
        <el-row>
          <el-col :span="12">
            <div class="field">
              <label>费率</label>
              <span>0.02%</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field">
              <label>最低保费</label>
              <span>10.00</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field">
              <label>最高保额</label>
              <span>1000000.00</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="field">
              <label>禁用区域</label>
              <span>北京、上海、成都、武汉</span>
            </div>
          </el-col>
          <el-col :span="24">
            <div class="field">
              <label>免赔</label>
              <span>olorem eum magni eos aperiam quia</span>
            </div>
          </el-col>
          <el-col :span="24">
            <div class="field">
              <label>特别约定</label>
              <span>
                ut aspernatur corporis harum nihil quis provident sequi mollitia nobis aliquid molestiae perspiciatis et
                ea nemo ab reprehenderit accusantium quas voluptate dolores velit et doloremque molestiae
              </span>
            </div>
          </el-col>
        </el-row>
      </el-alert>
    </div>
  </el-form>
</template>

<script>
export default {
  name: 'PolicyProduct',
  data() {
    return {
      type: 'first',
      value1: 1,
      value2: 1,
      value3: 1
    }
  }
}
</script>

<style lang="scss" scoped>
.policy-prodect {
  border: 1px solid #ebeef5;
  /deep/ .el-tabs {
    background-color: transparent;
    box-shadow: none;
    border: none;
    .el-radio-group {
      .el-radio.is-bordered {
        margin-right: 0;
        padding: 8px 10px 0 10px;
        .el-radio__input {
          display: none;
        }
        .el-radio__label {
          padding-left: 0;
        }
      }
    }
  }
  .selected-content {
    padding: 0 15px 15px;
    .field {
      @extend .d-flex;
      font-size: 14px;
      margin-bottom: $app-size-mini;
      label {
        width: 75px;
        min-width: 75px;
        font-weight: bold;
      }
      span {
        @extend .flex-fill;
      }
    }
  }
}
</style>
