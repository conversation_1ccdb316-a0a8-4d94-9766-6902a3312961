<template>
  <div>
    <el-dialog
      title="历史信息"
      width="520px"
      :visible.sync="visible"
      :before-close="handleBeforeClose"
      destroy-on-close
    >
      <el-form label-suffix=":" label-width="100px">
        <el-form-item prop="policy_no" label="姓名">
          {{ history.name }}
        </el-form-item>
        <el-form-item prop="policy_no" label="身份证号">
          {{ history.idcard_no }}
        </el-form-item>
        <el-form-item prop="policy_no" label="手机号码">
          {{ history.mobile }}
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'PolicyGroupEmployeeHistory',
  props: {
    visible: {
      type: Boolean,
      default: () => false
    },
    history: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    handleBeforeClose() {
      this.$emit('update:visible', false)
    }
  }
}
</script>
