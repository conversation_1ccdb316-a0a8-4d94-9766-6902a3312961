<!--
 * @Descripttion: 
 * @version: 
 * @Author: yanb
 * @Date: 2022-01-05 10:14:55
 * @LastEditors: yanb
 * @LastEditTime: 2022-03-21 16:21:44
-->
<template>
  <el-dialog title="销账申请" :visible.sync="visible" :before-close="handleClose" width="20%" center>
    <el-row>
      <el-col :span="8"><div class="grid-content bg-purple">水单数:</div></el-col>
      <el-col :span="16"
        ><div class="grid-content bg-purple-light">{{ previewData.memoCount }}</div></el-col
      >
    </el-row>
    <el-row class="preview">
      <el-col :span="8"><div class="grid-content bg-purple">水单金额:</div></el-col>
      <el-col :span="16"
        ><div class="grid-content bg-purple-light">{{ previewData.memoAmount }}</div></el-col
      >
    </el-row>
    <el-row class="preview">
      <el-col :span="8"><div class="grid-content bg-purple">保单数:</div></el-col>
      <el-col :span="16"
        ><div class="grid-content bg-purple-light">{{ previewData.policyCount }}</div></el-col
      >
    </el-row>
    <el-row class="preview">
      <el-col :span="8"><div class="grid-content bg-purple">保单金额:</div></el-col>
      <el-col :span="16"
        ><div class="grid-content bg-purple-light">{{ previewData.policyAmount }}</div></el-col
      >
    </el-row>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'SettlementPreview',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    previewData: {
      type: Object,
      default: () => {}
    }
  },
  created() {},
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
    },
    handleSubmit() {
      this.$emit('submit', Object.assign({}, this.prepareData()))

      this.handleClose()
    },
    prepareData() {
      var data = []
      data['memo_ids'] = this.previewData.memoIds
      data['receivable_ids'] = this.previewData.receivableIds
      return data
    }
  },
  watch: {}
}
</script>
<style scoped>
.preview {
  margin-top: 10px;
}
</style>
>
