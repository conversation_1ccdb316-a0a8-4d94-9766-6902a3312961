<template>
  <el-select v-model="subjectId" placeholder="请选择标的" class="w-100">
    <el-option v-for="s in subjects" :key="s.id" :label="s.name" :value="s.id"></el-option>
  </el-select>
</template>

<script>
import { getSubjects } from '@/apis/subject'

export default {
  name: 'SelectorSubject',
  props: {
    value: {
      type: [Number, String],
      default: ''
    }
  },
  data() {
    return {
      subjectId: '',
      subjects: []
    }
  },
  watch: {
    value(value) {
      if (value !== this.subjectId) {
        this.subjectId = value
      }
    },
    subjectId(value) {
      if (value !== this.value) {
        this.$emit('input', value)
      }
    }
  },
  created() {
    getSubjects().then((r) => {
      this.subjects = r.data
    })
  }
}
</script>
