<!--
 * @Descripttion: 
 * @version: 
 * @Author: yanb
 * @Date: 2021-06-25 16:17:44
 * @LastEditors: yanb
 * @LastEditTime: 2021-10-21 15:17:26
-->
<template>
  <el-select v-model="type" placeholder="请选择险种" class="w-100" clearable>
    <el-option v-for="type in types" :key="type.value" :value="type.value" :label="type.label"></el-option>
  </el-select>
</template>

<script>
export default {
  name: 'SelectorInsuranceType',
  props: {
    value: {
      type: [Number, String],
      default: ''
    }
  },
  watch: {
    value(value) {
      this.type = value
    },
    type(value) {
      if (value !== this.value) {
        this.$emit('input', value)
      }
    }
  },
  data() {
    return {
      type: '',
      types: [
        { value: 1, label: '国内货运险' },
        { value: 2, label: '国际货运险' },
        { value: 3, label: '单车责任险' },
        { value: 4, label: '其他险种' },
        { value: 5, label: '雇主责任险' }
      ]
    }
  }
}
</script>
