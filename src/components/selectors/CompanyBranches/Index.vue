<template>
  <el-select v-model="branchId" placeholder="请选择出单公司" class="w-100">
    <el-option v-for="b in branches" :key="b.id" :value="b.id" :label="b.name"></el-option>
  </el-select>
</template>

<script>
import { getCompaniesDict } from '@/apis/company'

export default {
  name: 'SelectorCompanyBranches',
  props: {
    value: {
      type: [Number, String],
      default: ''
    }
  },
  data() {
    return {
      companies: [],
      branchId: ''
    }
  },
  watch: {
    value(value) {
      this.branchId = value
    },
    branchId(value) {
      if (value !== this.value) {
        this.$emit('input', value)
      }
    }
  },
  computed: {
    branches() {
      const branches = []
      this.companies.forEach((com) => {
        branches.push(...com.branches)
      })

      return branches
    }
  },
  created() {
    getCompaniesDict().then((r) => {
      this.companies = r.data
    })
  }
}
</script>
