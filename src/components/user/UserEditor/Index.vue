<template>
  <el-dialog
    :title="currentDialogTitle"
    :visible.sync="visible"
    width="700px"
    :before-close="handleBeforeClose"
    destroy-on-close
  >
    <el-form ref="form" label-suffix=":" label-position="top" :model="form" :rules="rules" label-width="150px">
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="账户类型" prop="type">
            <el-radio v-model="form.type" :label="1">个人用户</el-radio>
            <el-radio v-model="form.type" :label="2">企业用户</el-radio>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="业务员" prop="salesman_id">
            <el-select placeholder="请选择业务员" v-model="form.salesman_id" class="w-100">
              <el-option v-for="s in sales" :key="s.id" :label="s.name" :value="s.id" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item :label="form.type === 1 ? '姓名' : '企业名'" prop="name">
            <el-input
              v-model.trim="form.name"
              :placeholder="`请输入${form.type === 1 ? '联系人名称' : '企业名称'}`"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model.trim="form.email" placeholder="请输入邮箱"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="通知邮箱" prop="cc_email">
            <el-input
              type="textarea"
              v-model="form.cc_email"
              :rows="2"
              placeholder="投保通知邮箱，填写后投保相关通知会抄送到填写的邮箱地址，一行一个"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="手机号" prop="phone_number">
            <el-input v-model.trim="form.phone_number" placeholder="请输入手机号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="地址" prop="address">
            <el-input v-model.trim="form.address" placeholder="请输入地址"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item prop="idcard_no" :label="form.type === 1 ? '身份证号' : '统一社会信用代码'">
            <el-input
              v-model="form.idcard_no"
              :placeholder="`请输入${form.type === 1 ? '身份证号' : '统一社会信用代码'}`"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="用户名" prop="username">
            <el-input v-model.trim="form.username" maxlength="32" placeholder="请输入用户名长度为 5 ～ 16位"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="密码" prop="password">
            <el-input
              v-model.trim="form.password"
              type="password"
              maxlength="32"
              placeholder="请输入密码"
              show-password
              auto-complete="new-password"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="确认密码" prop="password_confirmation">
            <el-input
              v-model.trim="form.password_confirmation"
              type="password"
              maxlength="32"
              placeholder="请确认密码"
              auto-complete="new-password"
              show-password
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="是否是代理商">
            <el-switch
              active-text="是"
              inactive-text="否"
              :active-value="1"
              :inactive-value="0"
              v-model="form.is_agent"
            ></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否启用">
            <el-switch
              active-text="是"
              inactive-text="否"
              :active-value="1"
              :inactive-value="0"
              v-model="form.is_enabled"
            ></el-switch>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="充值类型">
            <el-switch
              active-text="虚拟充值"
              inactive-text="真实充值"
              :active-value="1"
              :inactive-value="0"
              v-model="form.charge_type"
            ></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否使用投保预设信息">
            <el-switch
              active-text="是"
              inactive-text="否"
              :active-value="1"
              :inactive-value="0"
              v-model="form.is_use_insure_preset_data"
            ></el-switch>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item prop="idcard_no_file">
            <template slot="label">
              <span>{{ idcardNoFileTitle }}</span>
              <span class="text-danger" v-if="form?.idcard_no_file && form?.idcard_no_file?.length > 5">
                <a :href="form?.idcard_no_file" target="_blank"
                  ><small> 下载{{ idcardNoFileTitle }}</small></a
                >
              </span>
            </template>
            <upload-file v-model="form.idcard_no_file"></upload-file>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="attachment">
            <template slot="label">
              <span>其他附件</span>
              <span class="text-danger" v-if="form?.attachment && form?.attachment?.length > 5">
                <a :href="form?.attachment" target="_blank"><small> 下载其他附件</small></a>
              </span>
            </template>
            <upload-file v-model="form.attachment"></upload-file>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="是否启用在线支付">
            <el-switch
              active-text="是"
              inactive-text="否"
              :active-value="1"
              :inactive-value="0"
              v-model="form.is_online_payment"
            ></el-switch>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleBeforeClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getSales } from '@/apis/admin'

export default {
  name: 'UserEditor',
  props: {
    model: {
      type: [Object, MouseEvent],
      default: () => {}
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        type: 1,
        name: '',
        email: '',
        cc_email: '',
        phone_number: '',
        address: '',
        idcard_no: '',
        idcard_no_file: '',
        attachment: '',
        username: '',
        password: '',
        password_confirmation: '',
        is_agent: 0,
        is_enabled: 1,
        is_online_payment: 0,
        charge_type: 0,
        is_use_insure_preset_data: 0,
        salesman_id: ''
      },
      rules: {
        type: [{ required: true, message: '请选择账户类型', trigger: ['blur', 'change'] }],
        salesman_id: [{ required: true, message: '请选择业务员', trigger: ['blur', 'change'] }],
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        email: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
        ],
        phone_number: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
        address: [{ required: false }],
        idcard_no: [{ required: true, message: '证件号长度错误', trigger: 'blur' }],
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 4, max: 32, message: '用户名长度在 5 到 32 位之间', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 5, max: 32, message: '密码长度在 6 到 32 位之间', trigger: 'blur' }
        ],
        password_confirmation: [
          { required: true, message: '请重复输入密码', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value !== this.form.password) {
                callback(new Error('两次输入的密码不一致'))
              }

              callback()
            },
            trigger: 'blur'
          }
        ]
      },
      sales: []
    }
  },
  computed: {
    currentDialogTitle() {
      return this.form && this.form.id ? '修改用户' : '添加用户'
    },
    idcardNoFileTitle() {
      return this.form.type === 1 ? '身份证' : '营业执照'
    }
  },
  created() {
    getSales().then((r) => {
      this.sales = r.data
    })
  },
  methods: {
    handleBeforeClose() {
      this.$emit('update:visible', false)
      this.$refs.form.resetFields()
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('submit', this.form)
        }
      })
    }
  },
  watch: {
    model: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val && Object.keys(val).length > 0) {
          if (val?.id !== undefined) {
            delete this.rules.password
            delete this.rules.password_confirmation
          }
          this.form = Object.assign({}, val)
        } else {
          this.form = {
            type: 1,
            name: '',
            email: '',
            phone_number: '',
            address: '',
            idcard_no: '',
            idcard_no_file: '',
            attachment: '',
            username: '',
            password: '',
            password_confirmation: '',
            is_agent: 0,
            charge_type: 0,
            is_enabled: 1,
            is_online_payment: 0,
            is_use_insure_preset_data: 0
          }
        }
      }
    }
  }
}
</script>
