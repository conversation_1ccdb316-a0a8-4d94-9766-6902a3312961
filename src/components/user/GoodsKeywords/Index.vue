<template>
  <div>
    <el-dialog title="货物关键字" :visible.sync="visible" :before-close="handleBeforeClose" destroy-on-close>
      <el-alert
        title="货物关键字用于匹配货物名称，匹配成功提醒用户附加免赔和特别约定"
        type="warning"
        show-icon
        :closable="false"
      />

      <el-button type="primary" style="margin-top: 1rem" @click="showForm = true" icon="fas fa-plus">新增</el-button>

      <define-table class="m-extra-large-t" :data="data" :cols="cols" />
    </el-dialog>

    <el-dialog
      :title="form?.id ? '编辑关键字' : '添加关键字'"
      :visible.sync="showForm"
      :beforeClose="handleCloseForm"
      width="80%"
      append-to-body
      destroy-on-close
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="关键字" prop="keyword">
          <el-input v-model="form.keyword" placeholder="请输入关键字" />
        </el-form-item>
        <el-form-item label="执行操作" prop="action">
          <el-radio-group v-model="form.action">
            <el-radio v-for="item in actions" :label="item.value" :key="item.value">{{ item.label }}</el-radio>
          </el-radio-group>
        </el-form-item>

        <template v-if="form.action === 1">
          <el-divider>免赔特约</el-divider>

          <el-button type="primary" @click="addNewDataItem" icon="fas fa-plus">新增</el-button>

          <div class="m-extra-large-t d-flex flex-column" style="gap: 1rem">
            <div v-for="(item, index) in form.data" :key="index" class="d-flex" style="gap: 1rem">
              <el-select
                v-model="item.company_branch_id"
                placeholder="出单公司"
                style="flex-basis: 15%"
                filterable
                multiple
              >
                <el-option v-for="company in companies" :key="company.id" :label="company.name" :value="company.id" />
              </el-select>
              <el-select v-model="item.product_type" placeholder="产品类型" style="flex-basis: 15%" filterable>
                <el-option label="国内货运险" :value="1" />
                <el-option label="国际货运险" :value="2" />
              </el-select>
              <el-input type="textarea" autosize style="flex-basis: 30%" placeholder="免赔" v-model="item.deductible" />
              <el-input
                type="textarea"
                autosize
                style="flex-basis: 30%"
                placeholder="特别约定"
                v-model="item.special_agreement"
              />
              <el-button type="danger" icon="fas fa-trash-alt" style="flex: 1" @click="form.data.splice(index, 1)">
                删除
              </el-button>
            </div>
          </div>
        </template>
      </el-form>

      <template #footer>
        <el-button type="primary" @click="handleCloseForm">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  fetchUserGoodsKeywords,
  storeUserGoodsKeywords,
  updateUserGoodsKeywords,
  deleteUserGoodsKeywords
} from '@/apis/user'
import { getCompaniesDict } from '@/apis/company'

const ACTION_WITH_DEDUCTIBLE_AND_SPECIAL = 1
const ACTION_MANUAL_REVIEW = 2

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    user: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      showForm: false,
      companies: [],
      form: {
        action: '',
        keyword: '',
        data: []
      },
      actions: [
        { label: '提醒附加', value: ACTION_WITH_DEDUCTIBLE_AND_SPECIAL },
        { label: '转人工审核', value: ACTION_MANUAL_REVIEW }
      ],
      rules: {
        keyword: [{ required: true, message: '请输入关键字', trigger: 'blur' }],
        action: [{ required: true, message: '请选择执行操作', trigger: 'blur' }]
      },
      data: [],
      cols: [
        { label: '关键字', prop: 'keyword' },
        {
          label: '执行操作',
          prop: 'action',
          width: 100,
          scopedSlots: {
            default: (scoped) => {
              return scoped.row.action === ACTION_WITH_DEDUCTIBLE_AND_SPECIAL ? (
                <el-tag type="success">提醒附加</el-tag>
              ) : (
                <el-tag type="danger">转人工审核</el-tag>
              )
            }
          }
        },
        { label: '添加时间', prop: 'added_at', width: 150 },
        {
          label: '操作',
          prop: 'action',
          width: 50,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    onClick={() => {
                      this.form = { ...scoped.row }
                      this.showForm = true
                    }}
                    type="text"
                    size="small"
                    icon="fas fa-edit"
                  />
                  <el-button
                    onClick={() => this.handleDelete(scoped.row)}
                    type="text"
                    size="small"
                    icon="fas fa-trash-alt"
                  />
                </div>
              )
            }
          }
        }
      ]
    }
  },
  computed: {
    formData() {
      return this.data.find((item) => item.id === this.form.id)
    }
  },
  watch: {
    user() {
      this.fetch()
    }
  },
  created() {
    this.fetch()
    this.fetchCompanies()
  },
  methods: {
    addNewDataItem() {
      if (!this.form.data) this.form.data = []

      this.form.data.push({
        company_branch_id: [],
        product_type: '',
        deductible: '',
        special_agreement: ''
      })
    },
    async fetch() {
      if (!this.user) return
      const { data } = await fetchUserGoodsKeywords(this.user)
      this.data = data
    },
    async fetchCompanies() {
      const { data } = await getCompaniesDict()
      this.companies = data.map((item) => item.branches).flat()
    },
    async handleSubmit() {
      try {
        this.$refs.form.validate(async (valid) => {
          if (!valid) return
          if (this.form.action === ACTION_WITH_DEDUCTIBLE_AND_SPECIAL && !this.validateFormData()) {
            return
          }
          if (this.form.action === ACTION_MANUAL_REVIEW) this.form.data = []

          this.form.id
            ? await updateUserGoodsKeywords(this.user, this.form.id, this.form)
            : await storeUserGoodsKeywords(this.user, this.form)

          this.handleCloseForm()
          await this.fetch()
        })
      } catch (e) {
        console.log(e)
      }
    },
    validateFormData() {
      const companyIds = this.form.data.map((item) => item.company_branch_id)
      const uniqueCompanyIds = new Set(companyIds)
      // 重复配置了出单公司
      if (companyIds.length !== uniqueCompanyIds.size) {
        return false
      }
      return true
    },
    async handleDelete(data) {
      try {
        await this.$confirm('确定删除该关键字吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await deleteUserGoodsKeywords(this.user, data.id)
        await this.fetch()
      } catch (e) {
        console.log(e)
      }
    },
    handleBeforeClose() {
      this.$emit('update:visible', false)
    },
    handleCloseForm() {
      this.showForm = false
      this.$refs.form.resetFields()
      this.form = {
        action: '',
        keyword: '',
        data: []
      }
    }
  }
}
</script>
