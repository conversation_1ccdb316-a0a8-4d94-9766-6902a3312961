<template>
  <div class="m-extra-large-b">
    <search-panel
      @command="handleSearch"
      @change="(data) => (searchData = data)"
      :custom="searchFields"
      :exportable="false"
    />

    <el-card shadow="never" class="m-extra-large-t justify-content-start">
      <div class="d-flex justify-content-start">
        <el-button type="primary" v-can="{ name: 'users.products.sync' }" @click="assignUserProductAll">
          统一配置
        </el-button>
      </div>
      <define-table
        ref="productTable"
        :data="products"
        :cols="cols"
        :paging.sync="paging"
        :paging-events="pagingEvents"
        :events="tableEvents"
        :rowKey="getRowKeys"
      />
    </el-card>

    <el-dialog
      :visible.sync="rateForm.visible"
      :title="`配置产品 - ${is_multiple ? '统一配置' : rateForm.model.name}`"
      width="520px"
      :before-close="handleBeforeClose"
      destroy-on-close
    >
      <el-form ref="rateForm" :model="rateForm.form" :rules="rateForm.rules" label-suffix=":" label-width="12  0px">
        <el-form-item v-if="hasRateForm" prop="user_rate" :label="this.user.role == 'AGENT' ? '代理费率' : '用户费率'">
          <el-input v-model="rateForm.form.user_rate" placeholder="请输入用户费率 1.00">
            <template slot="append">‱</template>
          </el-input>
        </el-form-item>
        <el-form-item v-if="hasRateForm" prop="minimum_premium" label="最小保费">
          <el-input v-model="rateForm.form.minimum_premium" placeholder="请输入最小保费 1.00">
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
        <el-form-item prop="agent_commission_rate" label="代理佣金比例" v-if="user.role === 'AGENT'">
          <el-input v-model="rateForm.form.agent_commission_rate" placeholder="请输入代理佣金比例 1.00">
            <template slot="append">%</template>
          </el-input>
        </el-form-item>
        <el-form-item prop="is_enabled" label="是否启用">
          <el-switch
            active-text="是"
            inactive-text="否"
            :active-value="1"
            :inactive-value="0"
            v-model="rateForm.form.is_enabled"
          ></el-switch>
        </el-form-item>
        <el-form-item v-if="hasRateForm" prop="is_allowed_invoice" label="是否允许开票">
          <el-switch
            active-text="是"
            inactive-text="否"
            :active-value="1"
            :inactive-value="0"
            v-model="rateForm.form.is_allowed_invoice"
          ></el-switch>
        </el-form-item>
        <el-form-item v-if="hasRateForm" prop="is_premium_sync" label="保费同步">
          <el-switch
            active-text="是"
            inactive-text="否"
            :active-value="1"
            :inactive-value="0"
            v-model="rateForm.form.is_premium_sync"
          ></el-switch>
        </el-form-item>

        <el-form-item>
          <el-button @click="handleBeforeClose">取 消</el-button>
          <el-button type="primary" @click="handleSubmitMultiple" v-if="is_multiple === true">确 定</el-button>
          <el-button type="primary" @click="handleSubmit" v-else>确 定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { getSubjects } from '@/apis/subject'
import { getPlatformProductCompanies } from '@/apis/platform_product'

export default {
  name: 'UserProducts',
  props: {
    name: {
      type: String,
      default: ''
    },
    hasRateForm: {
      type: Boolean,
      default: true
    },
    user: {
      type: Object,
      default: () => {}
    },
    data: {
      type: Array,
      default: () => []
    },
    paging: {
      type: Object,
      default: () => {
        return {
          page: 1,
          currentPage: 1,
          pageSize: 15,
          layout: 'prev, pager, next, jumper, total',
          total: 0
        }
      }
    },
    products: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    cols() {
      let cols = [
        { align: 'center', type: 'selection', reserveSelection: true },
        {
          hideNonCargo: true,
          label: '产品代码',
          prop: 'code',
          width: 100,
          fixed: 'left'
        },
        {
          label: '产品名称',
          prop: 'name',
          width: 180,
          fixed: 'left'
        },
        {
          label: '产品来源',
          width: 100,
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.is_own) {
                return <span class="text-info">自营产品</span>
              } else {
                return <span class="text-primary">非自营产品</span>
              }
            }
          }
        },
        {
          label: '出单公司',
          width: 100,
          prop: 'company_branch.name'
        },
        {
          label: '平台费率(‱)',
          hideNonCargo: true,
          prop: 'platform_rate'
        },
        {
          label: '平台最低保费',
          hideNonCargo: true,
          prop: 'platform_minimum_premium'
        },
        {
          label: '平台佣金比例(%)',
          prop: 'platform_commission_rate'
        },
        {
          label: this.user.role == 'AGENT' ? '代理费率(‱)' : '会员费率(‱)',
          hideNonCargo: true,
          scopedSlots: {
            default: (scoped) => {
              if (this.hasRateForm) {
                return (
                  <span class="text-primary">
                    {this.data.find((e) => e.product_id === scoped.row.product_id)?.user_rate || '-'}
                  </span>
                )
              }

              return '-'
            }
          }
        },
        {
          label: this.user.role == 'AGENT' ? '代理最低保费' : '会员最低保费',
          hideNonCargo: true,
          scopedSlots: {
            default: (scoped) => {
              if (this.hasRateForm) {
                return (
                  <span class="text-primary">
                    {this.data.find((e) => e.product_id === scoped.row.product_id)?.minimum_premium || '-'}
                  </span>
                )
              }

              return '-'
            }
          }
        },
        {
          label: '代理佣金比例(%)',
          hideNonAgent: true,
          scopedSlots: {
            default: (scoped) => {
              return (
                <span class="text-primary">
                  {this.data.find((e) => e.product_id === scoped.row.product_id)?.agent_commission_rate || '-'}
                </span>
              )
            }
          }
        },
        {
          label: '是否可用',
          fixed: 'right',
          width: 80,
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              const row = this.data.find((e) => e.product_id === scoped.row.product_id) || { product_id: 0 }

              return (
                <el-switch
                  value={row?.is_enabled}
                  active-value={1}
                  inactive-value={0}
                  v-can={{ name: 'users.products.sync' }}
                  // disabled={this.user.is_create_to_agent}
                  onChange={() => {
                    if (row?.is_enabled === 1) {
                      row.is_enabled = 0
                    } else {
                      row.is_enabled = 1
                    }

                    this.$emit('update-status', row.product_id, row.is_enabled)
                  }}
                />
              )
            }
          }
        },
        {
          label: '配置',
          fixed: 'right',
          width: 80,
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <el-button
                  type="text"
                  size="small"
                  onClick={() => this.handleConfigProduct(scoped.row)}
                  v-can={{ name: 'users.products.sync' }}
                  // disabled={this.user.is_create_to_agent}
                >
                  配置费率
                </el-button>
              )
            }
          }
        }
      ]

      if (this.user.role != 'AGENT') {
        cols = cols.filter((e) => e.hideNonAgent === undefined || e.hideNonAgent === false)
      }
      if (!this.hasRateForm) {
        cols = cols.filter((e) => e.hideNonCargo === undefined || e.hideNonCargo === false)
      }

      return cols
    }
  },
  data() {
    return {
      is_multiple: false,
      selectAll: false,
      excepts: [],
      searchFields: [
        {
          type: 'select',
          valKey: 'source',
          hintText: '产品来源',
          options: [
            { value: 1, label: '自营产品' },
            { value: 2, label: '非自营产品' }
          ]
        },
        {
          type: 'select',
          valKey: 'company_id',
          hintText: '保险公司',
          onChangeReset: 'company_branch_id',
          options: []
        },
        {
          type: 'select',
          valKey: 'company_branch_id',
          hintText: '出单公司',
          options: []
        },
        {
          type: 'select',
          valKey: 'subject_id',
          hintText: '标的',
          options: []
        },
        {
          type: 'select',
          valKey: 'has_configured',
          hintText: '是否配置',
          options: [
            { value: 0, label: '未配置' },
            { value: 1, label: '已配置' }
          ]
        }
      ],
      searchData: {},
      rawCompanies: [],
      rateForm: {
        visible: false,
        model: {},
        form: {
          is_cargo_product: this.hasRateForm,
          rate: '',
          user_rate: '',
          agent_commission_rate: '',
          minimum_premium: '',
          is_allowed_invoice: 0,
          is_premium_sync: 0,
          is_enabled: 1
        },
        rules: {
          user_rate: [{ required: true, message: '用户费率必填', trigger: 'blur' }],
          agent_commission_rate: [{ required: false }],
          minimum_premium: [{ required: true, message: '最低保费必填', trigger: 'blur' }],
          is_allowed_invoice: [{ required: true, message: '请选择是否允许开票', trigger: 'blur' }],
          is_premium_sync: [{ required: true, message: '请选择保费是否同步', trigger: 'blur' }],
          is_enabled: [{ required: true, message: '请选择是否启用', trigger: 'blur' }]
        }
      },
      pagingEvents: {
        currentChange: (page) => {
          this.$emit('query-submit', page, Object.assign({}, this.searchData))
        }
      },
      tableEvents: {
        'selection-change': (val) => {
          this.multipleSelection = val
        },
        'select-all': (val) => {
          this.selectAll = !this.selectAll
          if (!this.selectAll) {
            this.$refs.productTable.$refs.dataTable.clearSelection()
          }
        },
        select: (val, row) => {
          if (val.indexOf(row) === -1) {
            this.excepts.push(row)
          } else {
            this.excepts.pop(row)
          }
        }
      },
      multipleSelection: []
    }
  },
  watch: {
    searchData: {
      immediate: true,
      deep: true,
      handler(value, old) {
        if (old?.company_id !== value?.company_id) {
          this.loadCompanyBranches()
        }
      }
    },
    products() {
      if (this.selectAll) {
        this.selectCurrentPageProducts()
      }
    }
  },
  mounted() {
    getPlatformProductCompanies().then((r) => {
      this.rawCompanies = r.data

      this.loadCompanies()
      this.loadCompanyBranches()
    })

    getSubjects().then((r) => {
      this.assignSelectOptions(
        'subject_id',
        r.data.map((e) => ({ value: e.id, label: e.name }))
      )
    })
  },
  methods: {
    getRowKeys(row) {
      return row.id
    },
    loadCompanies() {
      const options = this.rawCompanies.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('company_id', options)
    },
    loadCompanyBranches() {
      let options = []
      if (this.searchData?.company_id !== '' && this.searchData?.company_id !== undefined) {
        const company = this.rawCompanies.find((e) => e.id === this.searchData?.company_id)
        if (company) {
          options = company.branches.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      } else {
        this.rawCompanies.forEach((e) => {
          e?.branches.forEach((e) => {
            options.push({
              label: e.name,
              value: e.id
            })
          })
        })
      }

      this.assignSelectOptions('company_branch_id', options)
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    },
    handleFilterStatus(status) {
      this.getTableList({ status })
    },
    handleSearch(cmd, data) {
      this.searchData = data
      this.$emit('query-submit', 1, Object.assign({}, this.searchData))
    },
    handleBeforeClose() {
      this.rateForm.visible = false
      this.rateForm.model = {}
      this.rateForm.form = {}
    },
    handleSubmit() {
      this.$refs.rateForm.validate((valid) => {
        if (valid) {
          this.$emit('submit', { ...this.rateForm.form, product_id: this.rateForm.model.product_id })

          this.handleBeforeClose()
        }
      })
    },
    handleConfigProduct(row) {
      this.is_multiple = false
      this.rateForm.visible = true
      this.rateForm.model = row

      const rowData = this.data.find((e) => e.product_id === row.product_id) || {}
      this.rateForm.form = {
        is_cargo_product: this.hasRateForm,
        user_rate: rowData?.user_rate || 0,
        agent_commission_rate: rowData?.agent_commission_rate || 0,
        minimum_premium: rowData?.minimum_premium || 0,
        is_allowed_invoice: rowData?.is_allowed_invoice || 0,
        is_premium_sync: rowData?.is_premium_sync || 0,
        is_enabled: rowData?.is_enabled || 0
      }
    },
    assignUserProductAll() {
      this.is_multiple = true
      if (!this.multipleSelection.length) {
        this.$message({
          type: 'warning',
          message: '请至少选择一条数据'
        })
        return false
      }
      this.rateForm.visible = true
    },
    selectCurrentPageProducts() {
      this.products.forEach((row) => {
        if (this.excepts.find((e) => e.product_id === row.product_id) === undefined) {
          this.$refs.productTable.$refs.dataTable.toggleRowSelection(row, true)
        }
      })
    },
    handleSubmitMultiple() {
      this.$refs.rateForm.validate((valid) => {
        this.rateForm.form.select_all = this.selectAll
        this.rateForm.form.is_cargo_product = this.hasRateForm
        this.rateForm.form.filter = this.searchData
        if (this.selectAll) {
          this.rateForm.form.except_ids = this.excepts.map((row) => {
            return row.product_id
          })
        } else {
          this.rateForm.form.ids = this.multipleSelection.map((row) => {
            return row.product_id
          })
        }
        if (this.user.role !== 'AGENT') {
          this.rateForm.form.agent_commission_rate = 0
        }
        if (valid) {
          this.$refs.productTable.$refs.dataTable.clearSelection()

          this.is_multiple = false

          this.selectAll = false

          this.multipleSelection = []

          this.excepts = []

          this.$emit('multiple-submit', { ...this.rateForm.form })

          this.handleBeforeClose()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0px !important;
}
</style>
