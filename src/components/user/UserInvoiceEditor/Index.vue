<template>
  <el-dialog
    :title="currentDialogTitle"
    :visible.sync="visible"
    width="700px"
    :before-close="handleBeforeClose"
    destroy-on-close
  >
    <el-form ref="form" label-suffix=":" label-position="top" :model="form" :rules="rules" label-width="150px">
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="发票抬头" prop="company_name">
            <el-input v-model.trim="form.company_name" placeholder="请输入发票抬头"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="纳税人识别号" prop="tax_no">
            <el-input v-model.trim="form.tax_no" placeholder="请输入纳税人识别号"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="开户行" prop="bank_name">
            <el-input v-model.trim="form.bank_name" placeholder="请输入开户行"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开户行账号" prop="bankcard_no">
            <el-input v-model.trim="form.bankcard_no" placeholder="请输入开户行账号"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="注册地址" prop="registered_addr">
            <el-input v-model.trim="form.registered_addr" placeholder="请输入注册地址"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="注册电话" prop="registered_phone_number">
            <el-input v-model.trim="form.registered_phone_number" placeholder="请输入注册电话"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="收件人" prop="recipient">
            <el-input v-model.trim="form.recipient" placeholder="请输入收件人"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="收件人电话" prop="recipient_phone_number">
            <el-input v-model.trim="form.recipient_phone_number" placeholder="请输入收件人电话"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="配送地址" prop="delivery_address">
            <el-input v-model.trim="form.delivery_address" placeholder="请输入配送地址"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleBeforeClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'UserInvoiceEditor',
  props: {
    model: {
      type: [Object, MouseEvent],
      default: () => {}
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        company_name: '',
        tax_no: '',
        bank_name: '',
        bankcard_no: '',
        registered_addr: '',
        registered_phone_number: '',
        recipient: '',
        recipient_phone_number: '',
        delivery_address: ''
      },
      rules: {
        company_name: [{ required: true, message: '请输入发票抬头', trigger: 'blur' }],
        tax_no: [{ required: true, message: '请输入纳税人识别号', trigger: 'blur' }]
      }
    }
  },
  computed: {
    currentDialogTitle() {
      return '发票信息设置'
    }
  },
  methods: {
    handleBeforeClose() {
      this.$emit('update:visible', false)
      this.$refs.form.resetFields()
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('submit', this.form)

          this.handleBeforeClose()
        }
      })
    }
  },
  watch: {
    model: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val && Object.keys(val).length > 0) {
          this.form = Object.assign({}, val)
        } else {
          this.form = {
            company_name: '',
            tax_no: '',
            bank_name: '',
            bankcard_no: '',
            registered_addr: '',
            registered_phone_number: '',
            recipient: '',
            recipient_phone_number: '',
            delivery_address: ''
          }
        }
      }
    }
  }
}
</script>
