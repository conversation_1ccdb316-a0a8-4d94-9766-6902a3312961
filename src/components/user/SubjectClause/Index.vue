<template>
  <div>
    <el-dialog
      title="免赔和特别约定"
      :visible.sync="visible"
      :before-close="handleBeforeClose"
      destroy-on-close
      width="80%"
    >
      <el-alert title="用户选择标的后提醒附加免赔和特别约定" type="warning" show-icon :closable="false" />

      <el-button type="primary" style="margin-top: 20px" @click="showForm = true" icon="fas fa-plus">新增</el-button>

      <define-table class="m-extra-large-t" :data="data" :cols="cols" />
    </el-dialog>

    <el-dialog
      :title="form?.id ? '编辑记录' : '添加记录'"
      :visible.sync="showForm"
      :beforeClose="handleCloseForm"
      width="600px"
      append-to-body
      destroy-on-close
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="标的" prop="subject_id">
          <el-select v-model="form.subject_id" placeholder="请选择标的" class="w-100">
            <el-option v-for="item in subjects" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="出单公司" prop="company_branch_id">
          <el-select v-model="form.company_branch_id" placeholder="请选择出单公司" class="w-100" filterable multiple>
            <el-option v-for="item in companies" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="产品类型" prop="product_type">
          <el-select v-model="form.product_type" placeholder="请选择产品类型" class="w-100">
            <el-option label="国内货运险" :value="1" />
            <el-option label="国际货运险" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="免赔" prop="deductible">
          <el-input
            type="textarea"
            autosize
            v-model="form.deductible"
            placeholder="请输入自定义免赔，请按格式填写: 免赔率损失金额比例|免赔率保险金额比例|免赔额|免赔额币种|内容"
          />
          <div style="font-size: 12px; color: #666">
            请输入自定义免赔，请按格式填写: 免赔率损失金额比例|免赔率保险金额比例|免赔额|免赔额币种|内容
          </div>
        </el-form-item>
        <el-form-item label="特别约定" prop="special_agreement">
          <el-input type="textarea" autosize v-model="form.special_agreement" placeholder="请输入特别约定" />
        </el-form-item>
        <el-form-item label="去除港到港条款" prop="without_port_to_port_clause">
          <el-switch v-model="form.without_port_to_port_clause" :active-value="1" :inactive-value="0" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="handleCloseForm">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { getUserSubjectClauses, storeUserSubjectClauses, deleteUserSubjectClauses } from '@/apis/user'
import { getSubjects } from '@/apis/subject'
import { getCompaniesDict } from '@/apis/company'

export default {
  name: 'SubjectClause',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    user: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      data: [],
      cols: [
        { prop: 'subject.name', label: '标的', width: 100 },
        {
          label: '出单公司',
          width: 300,
          scopedSlots: {
            default: (scoped) => {
              const branches = []
              for (const branch of scoped.row.company_branches) {
                branches.push(branch.name)
              }
              return branches.join(',')
            }
          }
        },
        {
          prop: 'product_type',
          label: '产品类型',
          width: 100,
          scopedSlots: { default: (scoped) => (scoped.row.product_type === 1 ? '国内货运险' : '国际货运险') }
        },
        { prop: 'deductible', label: '免赔' },
        { prop: 'special_agreement', label: '特别约定' },
        {
          label: '去除港到港条款',
          width: 120,
          scopedSlots: {
            default: (scoped) => {
              return scoped.row.without_port_to_port_clause ? '是' : '否'
            }
          }
        },
        {
          label: '操作',
          prop: 'action',
          width: 50,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button onClick={() => this.handleEdit(scoped.row)} type="text" size="small" icon="fas fa-edit" />
                  <el-button
                    onClick={() => this.handleDelete(scoped.row)}
                    type="text"
                    size="small"
                    icon="fas fa-trash-alt"
                  />
                </div>
              )
            }
          }
        }
      ],
      form: {
        subject_id: null,
        company_branch_id: [],
        product_type: null,
        deductible: '',
        special_agreement: '',
        without_port_to_port_clause: 0
      },
      rules: {
        subject_id: [{ required: true, message: '请选择标的', trigger: 'blur' }],
        company_branch_id: [{ required: true, message: '请选择保险公司', trigger: 'blur' }],
        product_type: [{ required: true, message: '请选择产品类型', trigger: 'blur' }]
        // deductible: [{ required: true, message: '请输入免赔', trigger: 'blur' }],
        // special_agreement: [{ required: true, message: '请输入特别约定', trigger: 'blur' }]
      },
      subjects: [],
      companies: [],
      showForm: false
    }
  },
  watch: {
    user() {
      this.fetch()
    }
  },
  created() {
    this.fetch()
    this.fetchSubjects()
    this.fetchCompanies()
  },
  methods: {
    async fetch() {
      if (!this.user) return
      const { data } = await getUserSubjectClauses(this.user)
      this.data = data
    },
    async fetchSubjects() {
      const { data } = await getSubjects()
      this.subjects = data
    },
    async fetchCompanies() {
      const { data } = await getCompaniesDict()
      this.companies = data.map((item) => item.branches).flat()
    },
    handleBeforeClose() {
      this.handleCloseForm()
      this.$emit('update:visible', false)
    },
    handleCloseForm() {
      this.showForm = false
      this.$refs.form?.resetFields()
      this.form = {
        subject_id: null,
        company_branch_id: [],
        product_type: null,
        deductible: '',
        special_agreement: '',
        without_port_to_port_clause: 0
      }
    },
    async handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          await storeUserSubjectClauses(this.user, this.form)
          this.handleCloseForm()
          this.$message.success('保存成功')
          await this.fetch()
        } else {
          return false
        }
      })
    },
    handleEdit(data) {
      data.company_branch_id = data.company_branches.map((item) => item.id)
      this.form = { ...data }
      this.showForm = true
    },
    async handleDelete(data) {
      try {
        await this.$confirm('确定删除该记录吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await deleteUserSubjectClauses(this.user, data.id)
        this.$message.success('删除成功')
        await this.fetch()
      } catch (e) {
        console.log(e)
      }
    }
  }
}
</script>
