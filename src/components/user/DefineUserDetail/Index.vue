<!--
 * @Descripttion: 
 * @version: 
 * @Author: yanb
 * @Date: 2021-04-08 15:38:58
 * @LastEditors: yanb
 * @LastEditTime: 2021-04-16 09:43:06
-->
<template>
  <el-card v-if="data && data.title" class="define-user-details" :header="data.title" shadow="never">
    <el-form label-width="120px" label-position="left">
      <el-row v-for="(row, index) in data.data" :key="`row_${index}`">
        <el-col :span="24">
          <h6 class="clearfix">{{ row.title }}</h6>
        </el-col>
        <el-col v-for="(label, i) in row.groups" :key="`row_${index}_${i}`" :span="label.row ? 24 : 12">
          <el-form-item
            :label="label.label"
            :class="{
              row: label.row,
              empty: !label.value
            }"
          >
            <template v-if="label.isLink">
              <!-- 如果是链接 -->
              <el-link type="primary" v-if="label.target && label.target === '_blank'" :href="label.to" target="_blank">
                <!-- 如果是链接 并且是 target="_blank" -->
                {{ label.value }}
              </el-link>
              <el-link type="primary" v-else :href="label.to">
                {{ label.value }}
              </el-link>
            </template>
            <template v-else>
              {{ label.value }}
            </template>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
  <center v-else>
    <el-link :underline="false" type="danger">详情数据格式错误</el-link>
  </center>
</template>

<script>
export default {
  name: 'DefineUserDetail',
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  }
}
</script>

<style lang="scss" scoped>
$base-space: 12px;
$base-height: 45px;
.define-user-details {
  font-size: 14px;
  /deep/ .el-card__header {
    font-weight: bolder;
    border-bottom: none;
    padding: $base-space;
  }
  /deep/ .el-card__body {
    padding: 0;
    h6 {
      font-size: 16px;
      font-weight: 500;
      padding: $base-space;
      margin: 0;
      height: $base-height;
      box-sizing: border-box;
    }
    .el-col {
      border-top: 1px solid #dee2e6;
    }
    .el-form-item {
      margin-bottom: 0;
      .el-form-item__label,
      .el-form-item__content {
        height: $base-height;
        display: flex;
        align-items: center;
      }
      .el-form-item__label {
        padding: $base-space;
        background-color: #f8f9fa !important;
      }
      .el-form-item__content {
        padding: 0 $base-space;
      }

      &.row:not(.empty) {
        display: flex;
        align-items: center;
        background-color: #f8f9fa !important;
        .el-form-item__label {
          min-width: 120px !important;
          max-width: 120px !important;
          float: initial;
        }
        .el-form-item__content {
          height: auto;
          flex: 1;
          padding: 0.75rem;
          line-height: 26px;
          margin-left: 0 !important;
          background-color: #fff !important;
        }
      }
    }
  }
}
</style>
