<template>
  <el-dialog
    :title="currentDialogTitle"
    :visible.sync="visible"
    width="700px"
    :before-close="handleBeforeClose"
    destroy-on-close
  >
    <el-form ref="form" label-suffix=":" label-position="top" :model="form" label-width="150px">
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="投保人名称" prop="policyholder">
            <el-input v-model.trim="form.policyholder" placeholder="请输入投保人名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="被保人名称" prop="insured">
            <el-input v-model.trim="form.insured" placeholder="请输入被保人名称"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="投保人类型">
            <el-select v-model.trim="form.policyholder_type" placeholder="请选择投保人类型" style="width: 100%">
              <el-option label="团体用户" :value="0"></el-option>
              <el-option label="个人用户" :value="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="被保人类型">
            <el-select v-model.trim="form.insured_type" placeholder="请选择被保人类型" style="width: 100%">
              <el-option label="团体用户" :value="0"></el-option>
              <el-option label="个人用户" :value="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="投保人证件号" prop="policyholder_idcard_no">
            <el-input v-model.trim="form.policyholder_idcard_no" placeholder="请输入投保人证件号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="被保人证件号" prop="insured_idcard_no">
            <el-input v-model.trim="form.insured_idcard_no" placeholder="请输入被保人证件号"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="投保人电话" prop="policyholder_phone_number">
            <el-input v-model.trim="form.policyholder_phone_number" placeholder="请输入投保人电话"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="被保人电话" prop="insured_phone_number">
            <el-input v-model.trim="form.insured_phone_number" placeholder="请输入被保人电话"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="投保人地址" prop="policyholder_address">
            <el-input v-model.trim="form.policyholder_address" placeholder="请输入投保人地址"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="被保人地址" prop="insured_address">
            <el-input v-model.trim="form.insured_address" placeholder="请输入被保人地址"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item prop="policyholder_idcard_issue_date" label="投保人证件有效起始时间">
            <el-date-picker
              v-model="form.policyholder_idcard_issue_date"
              class="w-100"
              type="date"
              placeholder="请选择投保人证件有效起始时间"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="insured_idcard_issue_date" label="被保人证件有效起始时间">
            <el-date-picker
              v-model="form.insured_idcard_issue_date"
              class="w-100"
              type="date"
              placeholder="请选择被保人证件有效起始时间"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item prop="policyholder_idcard_valid_till" label="投保人证件有效结束时间">
            <el-date-picker
              v-model="form.policyholder_idcard_valid_till"
              class="w-100"
              type="date"
              placeholder="请选择投保人证件有效结束时间"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="insured_idcard_valid_till" label="被保人证件有效结束时间">
            <el-date-picker
              v-model="form.insured_idcard_valid_till"
              class="w-100"
              type="date"
              placeholder="请选择被保人证件有效结束时间"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleBeforeClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import dayjs from 'dayjs'

export default {
  name: 'UserInvoiceEditor',
  props: {
    model: {
      type: [Object, MouseEvent],
      default: () => {}
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        policyholder: '',
        policyholder_type: '',
        policyholder_idcard_no: '',
        policyholder_phone_number: '',
        policyholder_address: '',
        policyholder_idcard_issue_date: '',
        policyholder_idcard_valid_till: '',
        insured: '',
        insured_type: '',
        insured_idcard_no: '',
        insured_phone_number: '',
        insured_address: '',
        insured_idcard_issue_date: '',
        insured_idcard_valid_till: ''
      }
    }
  },
  computed: {
    currentDialogTitle() {
      return '投保预设信息设置'
    }
  },
  methods: {
    handleBeforeClose() {
      this.$emit('update:visible', false)
      this.$refs.form.resetFields()
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('submit', this.marshalData(this.form))

          this.handleBeforeClose()
        }
      })
    },
    marshalData(data) {
      if (Object.keys(data).length <= 0) {
        return data
      }
      if (data.policyholder_idcard_issue_date) {
        data.policyholder_idcard_issue_date = dayjs(data.policyholder_idcard_issue_date).format('YYYY-MM-DD')
      }
      if (data.policyholder_idcard_valid_till) {
        data.policyholder_idcard_valid_till = dayjs(data.policyholder_idcard_valid_till).format('YYYY-MM-DD')
      }
      if (data.insured_idcard_issue_date) {
        data.insured_idcard_issue_date = dayjs(data.insured_idcard_issue_date).format('YYYY-MM-DD')
      }
      if (data.insured_idcard_valid_till) {
        data.insured_idcard_valid_till = dayjs(data.insured_idcard_valid_till).format('YYYY-MM-DD')
      }
      return data
    }
  },
  watch: {
    model: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val && Object.keys(val).length > 0) {
          this.form = Object.assign({}, val)
        } else {
          this.form = {
            policyholder: '',
            policyholder_type: '',
            policyholder_idcard_no: '',
            policyholder_phone_number: '',
            policyholder_address: '',
            policyholder_idcard_issue_date: '',
            policyholder_idcard_valid_till: '',
            insured: '',
            insured_type: '',
            insured_idcard_no: '',
            insured_phone_number: '',
            insured_address: '',
            insured_idcard_issue_date: '',
            insured_idcard_valid_till: ''
          }
        }
      }
    }
  }
}
</script>
