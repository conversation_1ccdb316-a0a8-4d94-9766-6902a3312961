<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-09 20:34:18
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-10 01:25:36
-->
<template>
  <el-card class="form-block" shadow="never" :class="{ center }">
    <div v-if="$slots.title" slot="header">
      <slot name="title"></slot>
    </div>
    <div v-else class="title" slot="header">{{ title }}</div>
    <slot></slot>
  </el-card>
</template>

<script>
export default {
  name: 'FormBlock',
  props: {
    title: {
      type: String,
      default: undefined
    },
    center: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style lang="scss">
.form-block {
  padding: $app-size-extra-small * 2;
  .el-card__header {
    padding: 0 0 $app-size-extra-small * 2;
    border: none;
    .title {
      padding-left: $app-size-mini;
      border-left: 3px solid $app-color-primary;
    }
  }
  .el-card__body {
    padding: 0;
  }
  &.center .el-card__header,
  &.center .el-card__body {
    text-align: center;
  }
}
</style>
