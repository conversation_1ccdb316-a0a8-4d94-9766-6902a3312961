<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2020-10-19 12:30:41
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2020-10-19 12:36:02
-->

<template>
  <el-container class="simple-container">
    <el-main class="simple-container__mian">
      <slot></slot>
    </el-main>
  </el-container>
</template>

<script>
export default {
  name: 'SimpleContainer',
  props: {
    title: {
      required: false
    }
  }
}
</script>

<style lang="scss" scoped>
.simple-container {
  // min-height: 100vh;
  #{&}__header {
    margin-bottom: 24px;
    padding: 0 !important;
    display: flex;
    justify-content: space-between;
    .title {
      display: flex;
      flex-direction: column;
      .title-text {
        color: #212529;
        font-size: 14px;
      }
      .title-badge {
        display: block;
        width: 25px;
        height: 5px;
        border-radius: 3px;
        background: #177eec;
        margin-top: 10px;
      }
    }
    .actions {
      display: flex;
    }
  }
  #{&}__mian {
    padding: 0 !important;
  }
}
</style>
