<!--
 * @Descripttion: 保单详情
 * @Author: Mr. zhu
 * @Date: 2020-10-22 16:06:44
 * @LastEditors: yanb
 * @LastEditTime: 2024-05-22 10:22:46
-->
<template>
  <el-card shadow="never" v-if="data && data.title" class="define-details">
    <el-form :label-width="data.label_width || '120px'" label-position="left">
      <div v-for="(row, index) in data.data" :key="`row_${index}`" class="group">
        <h6 class="clearfix title p-mini-l">
          <label>{{ row.title }}</label>
          <i class="flex-fill"></i>
          <template v-if="row.actions || row.editable">
            <div v-for="(action, _i) in row.actions" :key="`action_${_i}`">
              <el-button
                v-if="!action?.hide"
                :icon="action.icon"
                :type="action.type || 'text'"
                :disabed="action.disabled || false"
                @click="action?.handler()"
                class="m-mini-l"
                size="mini"
              >
                {{ action.text }}
              </el-button>
            </div>
          </template>
        </h6>
        <section class="data-content">
          <div v-for="(rows, i) in marshalRows(row.groups)" :key="`row_${index}_${i}`" class="data-row">
            <div
              v-for="(data, key) in rows"
              :key="`sub_row_${key}`"
              class="data-col"
              :class="{
                'data-full-col': rows.length === 1,
                hidden: rows.filter((r) => !r?.hide).length === 0
              }"
            >
              <template v-if="!(data?.hide || false)">
                <div
                  class="data-label-text"
                  :style="data.labelWidth ? `min-width: ${data.labelWidth}; max-width: ${data.labelWidth}` : ''"
                  v-html="data.label"
                ></div>
                <div class="data-label-content">
                  <div class="content">
                    <template v-if="data.isLink">
                      <!-- 如果是链接 -->
                      <el-link
                        type="primary"
                        v-if="data.target && data.target === '_blank'"
                        @click="data?.handler(row) || (() => {})"
                        :href="data.to"
                        target="_blank"
                      >
                        <!-- 如果是链接 并且是 target="_blank" -->
                        {{ data.value }}
                      </el-link>
                      <el-link type="primary" v-else :href="data.to" @click="data?.handler(row) || (() => {})">
                        {{ data.value }}
                      </el-link>
                    </template>

                    <template v-else>
                      <template v-if="data?.type === 'popover'">
                        <el-popover
                          placement="top-start"
                          :width="data?.width || '350px'"
                          :trigger="data?.trigger || 'hover'"
                          :popper-class="data?.popperClass || ''"
                        >
                          <jsx-render :render="data?.render || (() => {})"></jsx-render>
                          <template slot="reference">
                            <el-button
                              :icon="data?.icon || 'el-icon-eye'"
                              :type="data?.btnType || 'text'"
                              :size="data?.btnSize || 'mini'"
                            >
                              {{ data?.btnText || '查看' }}
                            </el-button>
                          </template>
                        </el-popover>
                      </template>
                      <pre v-else-if="!data?.editable && data?.withTag" v-html="data.value"></pre>
                      <div v-else-if="!data?.editable" v-html="data.value"></div>
                      <template v-else>
                        <template v-if="(data?.type || 'text') == 'date'">
                          <el-date-picker
                            v-model="form[data.key]"
                            type="date"
                            placeholder="选择日期"
                            format="yyyy-MM-dd"
                            :picker-options="data?.pickerOptions || {}"
                            style="width: 100% !important"
                          >
                          </el-date-picker>
                        </template>
                        <template v-else-if="data?.type === 'datetime'">
                          <el-date-picker
                            v-model="form[data.key]"
                            type="datetime"
                            placeholder="选择日期"
                            format="yyyy-MM-dd HH:mm:ss"
                            :picker-options="data?.pickerOptions || {}"
                            style="width: 100% !important"
                          >
                          </el-date-picker>
                        </template>
                        <template v-else-if="data?.type === 'select'">
                          <el-select :placeholder="data.label" v-model="form[data.key]" class="w-100">
                            <el-option
                              v-for="(item, index) in data?.options || []"
                              :key="`option_${index}`"
                              :label="item.label"
                              :value="item.value"
                            ></el-option>
                          </el-select>
                        </template>
                        <template v-else-if="data?.type === 'radio'">
                          <el-radio-group v-model="form[data.key]">
                            <el-radio
                              v-for="(item, index) in data?.options || []"
                              :key="`radio_${index}`"
                              :label="item.value"
                            >
                              {{ item.label }}
                            </el-radio>
                          </el-radio-group>
                        </template>
                        <template v-else-if="data?.type === 'cascader'">
                          <el-cascader
                            class="w-100"
                            v-model="form[data.key]"
                            :options="data?.options || []"
                            :props="
                              data?.props || {
                                expandTrigger: 'hover',
                                filterable: true,
                                value: 'value',
                                label: 'value',
                                children: 'children'
                              }
                            "
                          ></el-cascader>
                        </template>
                        <template v-else>
                          <el-input
                            :placeholder="data.label"
                            :type="data?.type || 'text'"
                            :rows="3"
                            :cols="5"
                            autosize
                            v-model="form[data.key]"
                          />
                        </template>
                      </template>
                    </template>
                  </div>

                  <el-button-group class="actions" v-if="data.actions">
                    <el-button
                      v-for="action in data.actions"
                      :key="action.text"
                      :type="action.type"
                      size="mini"
                      :icon="action.icon"
                      @click="action.handler(row)"
                    >
                      {{ action.text }}
                    </el-button>
                  </el-button-group>
                </div>
              </template>
            </div>
          </div>
        </section>
      </div>
    </el-form>
  </el-card>
  <center v-else>
    <el-link :underline="false" type="danger">详情数据格式错误</el-link>
  </center>
</template>

<script>
const jsxRender = {
  props: {
    render: {
      type: Function,
      default: () => {}
    }
  },
  render(h) {
    return <div>{this.$props.render(h)}</div>
  }
}

export default {
  name: 'DefineDetails',
  components: {
    jsxRender
  },
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      form: {}
    }
  },
  created() {
    this.initForm()
  },
  watch: {
    data() {
      this.initForm()
    },
    form: {
      deep: true,
      immediate: true,
      handler(value) {
        this.$emit('changed', value)
      }
    }
  },
  methods: {
    initForm() {
      this.data?.data?.forEach((row) => {
        row.groups?.forEach((data) => {
          data.editable &&
            this.$set(this.form, data.key || data.label, data.rawValue === undefined ? data.value : data.rawValue)
        })
      })
    },
    marshalRows(rows) {
      const dstRows = []
      for (let index = 0; index < rows?.length; index++) {
        const row = rows[index]
        if (row.hide) {
          continue
        }

        if (row.row) {
          dstRows.push([row])
        } else {
          if (rows[index + 1] !== undefined) {
            dstRows.push([row, rows[index + 1]])
          } else {
            dstRows.push([row])
          }
          index++
        }
      }
      return dstRows
    }
  }
}
</script>

<style>
pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>

<style lang="scss" scoped>
$__base-space: 12px;
$__base-border: 1px solid #dee2e6;
$__base-height: 45px;
$__title-height: 60px;
$__base-title-width: 150px;

.define-details {
  font-size: 14px;

  border: none;
  /deep/ .el-card__header {
    font-weight: bolder;
    border-bottom: none;
    padding: $__base-space;
  }
  /deep/ .el-card__body {
    padding: 0;
    .title {
      font-size: 16px;
      font-weight: 500;
      margin: 0;
      height: $__title-height;
      box-sizing: border-box;
      position: relative;
      display: flex;
      align-items: center;
      &::before {
        content: '';
        display: block;
        height: 15px;
        width: 3px;
        position: absolute;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
        background-color: $app-color-primary;
      }
    }
  }

  .data-content {
    width: 100%;

    .data-row {
      display: grid;
      grid-template-columns: 50% 50%;

      .data-col {
        display: flex;
        border-left: $__base-border;
        border-top: $__base-border;
        border-right: $__base-border;
        min-height: $__base-height;

        .data-label-text,
        .data-label-content {
          display: flex;
          align-items: center;
          padding: $__base-space;
          overflow: hidden;
        }

        .data-label-content {
          flex: 1;
          flex-shrink: 1;
          line-height: 1.5;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .content {
            flex: 1;
            overflow: hidden;
            word-wrap: break-word;
          }
        }

        .data-label-text {
          min-width: $__base-title-width;
          border-right: $__base-border;
        }

        &:nth-child(2) {
          border-left: none;
        }
      }
      .data-full-col {
        grid-column: 1 / 4;
      }

      &:last-child {
        border-bottom: $__base-border;
      }
    }
  }

  .hidden {
    display: none !important;
  }
}
</style>
