<template>
  <div id="editor" ref="editor"></div>
</template>

<script>
import Editor from 'wangeditor'

export default {
  name: 'Editor',
  props: {
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      editor: null,
      disabledUpdateEditorContent: false
    }
  },
  watch: {
    value(value) {
      if (this.disabledUpdateEditorContent) {
        return
      }

      this.editor?.txt?.html(value)
      if (value) {
        this.disabledUpdateEditorContent = true
      }
    }
  },
  mounted() {
    this.editor = new Editor(this.$refs.editor)

    this.editor.config.onchange = (value) => this.$emit('input', value)

    this.editor.create()

    if (this.value) {
      this.editor.txt.html(this.value)
    }
  },
  beforeDestroy() {
    if (this.editor !== null) {
      this.editor.destroy()
      this.editor = null
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .w-e-toolbar {
  z-index: 1 !important;
}

/deep/ .w-e-text-container {
  z-index: 0 !important;
}
</style>
