/*
 * @Descripttion: mock数据
 * @Author: Mr. <PERSON><PERSON>
 * @Date: 2020-10-22 16:22:07
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-08 14:32:34
 */
const mock = {
  title: '保单详情 - 已提交',
  data: [
    {
      title: '系统信息',
      groups: [
        { label: '保单号', value: 'CGO2020102210149575' },
        { label: '出单时间', value: '' },
        { label: '投保单号', value: '' },
        { label: '流水号', value: 'CGO2020102210149575' },
        { label: '保额(元)', value: '2500.00' },
        { label: '保费(元)', value: '15.00' },
        { label: '投保用户', value: '好伙伴' },
        { label: '投保时间', value: '2020-10-22 13:54:22' },
        { label: '第三方标识号', value: 'YD2010220020', row: true }
      ]
    },
    {
      title: '产品信息',
      actions: [
        {
          // ’‘ default  primary / success / warning / danger / info / text
          type: '',
          // 文本
          text: '修改',
          // https://element.eleme.io/#/zh-CN/component/icon
          icon: 'el-icon-edit-outline'
        }
      ],
      groups: [
        { label: '标的', value: '人保' },
        { label: '保险公司', value: '' },
        { label: '保险产品', value: 'PI0001' },
        {
          label: '产品代码',
          value: '400001000224',
          isLink: true,
          target: '_blank',
          to:
            'http://test.51baoya.com/uploads/policy/cargo_intl/backdate_file/ttcPoCJ2X9v5IIr446skxVJJwEWulrUXeg3GDyHv.pdf'
        }
      ]
    },
    {
      title: '基本信息',
      groups: [
        { label: '投保人', value: '李清' },
        {
          label: '地址',
          value: '云南红河弥勒弥勒市髯翁西路中龙时代中心项目A6栋601号办公楼'
        },
        { label: '被保人', value: '李清' },
        {
          label: '地址',
          value: '云南红河弥勒弥勒市髯翁西路中龙时代中心项目A6栋601号办公楼'
        },
        { label: '投保人电话', value: '13011165000' },
        { label: '被保人电话', value: '13011165000' },
        {
          label: '第三方标识号',
          value: 'YD2010220020',
          row: true
        }
      ]
    },
    {
      title: '货物信息',
      groups: [
        { label: '货物类别', value: '其他' },
        { label: '装载方式', value: '集装箱' },
        { label: '运输方式', value: '公路运输' },
        { label: '包装', value: '箱' },
        { label: '货物名称', value: '新希望货品' },
        { label: '包装件数', value: '11吨' }
      ]
    },
    {
      title: '运输信息',
      groups: [
        { label: '运单号', value: 'YD2010220020' },
        { label: '发票号', value: '' },
        { label: '车牌号', value: '川ACC871' },
        { label: '起运日期', value: '2020-10-22' },
        { label: '起运地', value: '四川省-成都市-锦江区' },
        { label: '目的地', value: '山西省-太原市-万柏林区' },
        { label: '中转地', value: '', row: true }
      ]
    },
    {
      title: '保险内容',
      groups: [
        { label: '主条款', value: '基本险', row: true },
        {
          label: '免赔',
          value: '每次事故绝对免赔额为 2000元人民币，或损失金额的 10 %，以高者为准。',
          row: true
        },
        {
          label: '特别约定',
          value: '裸装货物：本保单不负责货物的锈损和标的受到刮擦导致的损失；',
          row: true
        }
      ]
    },
    {
      title: '其他',
      groups: [{ label: '备注', value: '', row: true }]
    }
  ]
}
export default mock
