/*
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2020-10-19 12:30:41
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-10 16:31:09
 */

// 分页器默认参数
export const pagingConfig = {
  attrs: {
    small: false,
    background: false,
    pageSize: 10,
    total: 0,
    pageCount: undefined,
    pagerCount: undefined,
    currentPage: 1,
    layout: 'total, sizes, prev, pager, next, jumper',
    pageSizes: [10, 20, 30, 40, 50, 100],
    popperClass: '',
    prevText: undefined,
    nextText: undefined,
    disabled: false,
    hideOnSinglePage: undefined,
    // 对齐方式 'left', 'center', 'right'
    align: 'left'
  },
  events: {
    sizeChange: (size) => {},
    currentChange: (currentPage) => {},
    prevClick: (currentPage) => {},
    nextClick: (currentPage) => {}
  }
}

/**
 * 合并分页参数
 * @param { Object } props 传入的配置参数
 * @param { 'attrs' | 'events' } type 类型 attrs: '合并属性', events: 合并事件
 */
export function mergePagingArguments(props, type) {
  let obj = {}
  for (const key in pagingConfig[type]) {
    obj[key] = props.hasOwnProperty(key) ? props[key] : pagingConfig[type][key]
  }
  return obj
}
