# Define Table

## 安装所需依赖(vue 2.5+ jsx 依赖 默认支持)

```bash
npm install @vue/babel-preset-jsx @vue/babel-helper-vue-jsx-merge-props --save-dev
# or
cnpm install @vue/babel-preset-jsx @vue/babel-helper-vue-jsx-merge-props --save-dev
```

## 使用

```javascript
import DefineTable from 'path/DefineTable'
```

```html
<DefineTable :data="" :col="" {...} />
```

## Attributes 属性

#### data

- 类型：`Array`
- 用途： 数据列表

  > [注意]如果 data 存在 则优先级高于下面 attrs 中的 data

- example

```html
<DefineTable :data="tabelData" />
```

```javascript
export default {
  name: 'App',
  components: {
    DefineTable
  },
  data() {
    return {
      tableData: [
        {
          date: '2020-03-27',
          name: 'Mr. zhu',
          address: '四川省成都市青羊区人民中路一段16号'
        },
        {
          date: '2020-03-27',
          name: 'Mr. zhu',
          address: '四川省成都市青羊区人民中路一段16号'
        },
        {
          date: '2020-03-27',
          name: 'Mr. zhu',
          address: '四川省成都市青羊区人民中路一段16号'
        },
        {
          date: '2020-03-27',
          name: 'Mr. zhu1',
          address: '四川省成都市青羊区人民中路一段16号'
        }
      ]
    }
  }
}
```

#### cols

- 类型：`Array`
- 用途：列配置

  > https://element.eleme.cn/#/zh-CN/component/table#table-column-attributes
  >
  > [注意] Scoped Slot 通过 scopedSlots 属性指定
  >
  > 支持上述链接中的所有属性

- example

```html
<DefineTable :cols="cols" />
```

```javascript
export default {
  name: 'App',
  components: {
    DefineTable
  },
  data() {
    return {
      cols: [
        { align: 'center', type: 'index' },
        { align: 'center', type: 'selection' },
        { prop: 'date', label: '日期', width: '180' },
        { prop: 'name', label: '姓名', width: '180' },
        { prop: 'address', label: '地址' },
        // JSX 插槽
        {
          label: '插槽',
          scopedSlots: {
            default: scoped => {
              return (
                <div>
                  <label>{scoped.row.name}</label>
                  <br />
                  <small>{scoped.row.date}</small>
                </div>
              )
            }
          }
        },
        {
          label: '操作',
          align: 'center',
          width: '140',
          scopedSlots: {
            default: scoped => {
              return (
                <el-button
                  onClick={() => this.handlerEditor(scoped.row)}
                  type="primary"
                  plain
                  size="small"
                  icon="el-icon-edit"
                >
                  Editor
                </el-button>
              )
            }
          }
        }
      ]
    }
  },
  methods: {
    handlerEditor(row) {
      console.log(row)
    }
  }
}
```

#### attrs

- 类型：`Object`
- 用途： Table Attributes

  > https://element.eleme.cn/#/zh-CN/component/table#table-attributes
  >
  > 支持上述链接中的所有属性
  >
  > [注意] 如果上面 data 存在 则优先级高于 arrts 中的 data，会被上面 data 覆盖

- example

```html
<DefineTable :attrs="tableAttrs" />
```

```javascript
export default {
  name: 'App',
  components: {
    DefineTable
  },
  data() {
    return {
      tableAttrs: {
        stripe: true,
        border: false,
        'show-header': true,
        data: [
          {
            date: '2020-03-27',
            name: 'Mr. zhu',
            address: '四川省成都市青羊区人民中路一段16号'
          },
          {
            date: '2020-03-27',
            name: 'Mr. zhu',
            address: '四川省成都市青羊区人民中路一段16号'
          },
          {
            date: '2020-03-27',
            name: 'Mr. zhu',
            address: '四川省成都市青羊区人民中路一段16号'
          },
          {
            date: '2020-03-27',
            name: 'Mr. zhu',
            address: '四川省成都市青羊区人民中路一段16号'
          }
        ]
      }
    }
  }
}
```

#### events

- 类型：`Object`
- 用途：table events 事件

  > https://element.eleme.cn/#/zh-CN/component/table#table-events
  >
  > 支持上述链接中的所有属性
  >
  > 参数同上述链接中的参数相同

- example

```html
<DefineTable :events="tableEvents" />
```

```javascript
export default {
  name: 'App',
  components: {
    DefineTable
  },
  data() {
    return {
      tableEvents: {
        'row-click': (row, column, event) => {
          console.log(row)
          console.log(column)
          console.log(event)
        }
      }
    }
  }
}
```

#### paging

- 类型：`Object`
- 用途：分页器参数 不传则不显示

> https://element.eleme.cn/#/zh-CN/component/pagination#attributes
>
> 支持上面链接中的所有参数，带中划线参数转为驼峰命名 page-count => pageCount
>
> 除了上面参数之外添加新的参数：[align] 对齐方式 'left', 'center', 'right'

- example

```html
<DefineTable :paging="pagingAttrs" />
```

```javascript
export default {
  name: 'App',
  components: {
    DefineTable
  },
  data() {
    return {
      pagingAttrs: {
        currentPage: 1,
        pageSizes: [10, 20, 30, 40, 50],
        pageSize: 0,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 4
      }
    }
  }
}
```

#### pagingEvents

- 类型：`Object`
- 用途：分页器事件

> ttps://element.eleme.cn/#/zh-CN/component/pagination#events
>
> 支持上面链接中的所有事件，带中划线事件名转为驼峰命名 current-change => currentChange

- example

```html
<DefineTable :pagingEvents="pagingEvents" />
```

```javascript
export default {
  name: 'App',
  components: {
    DefineTable
  },
  data() {
    return {
      pagingEvents: {
        sizeChange: () => {},
        currentChange: () => {}
      }
    }
  }
}
```
