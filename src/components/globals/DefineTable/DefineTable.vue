<!--
 * @Descripttion:
 * @Author: Mr. <PERSON>hu
 * @Date: 2020-10-19 12:30:41
 * @LastEditors: yanb
 * @LastEditTime: 2022-03-15 14:59:35
-->
<script>
import { mergePagingArguments } from './utils'
export default {
  name: 'DefineTable',
  props: {
    // 自动高度
    autoHeight: {
      type: Boolean,
      default: false
    },
    /**
     * table 数据列表
     * [注意] 如果data 存在 则优先级高于下面 attrs 中的 data
     * example:
     * # component:
     *  <DefineTable :data="tabelData" />
     * # data
     *  data() {
     *    return {
     *       tabelData: [
     *              {
     *                  date: "2016-05-02",
     *                  name: "王小虎",
     *                  address: "上海市普陀区金沙江路 1518 弄"
     *              },
     *              {
     *                  date: "2016-05-04",
     *                  name: "王小虎",
     *                  address: "上海市普陀区金沙江路 1517 弄"
     *              }
     *          ]
     *      },
     *  }
     */
    data: Array,
    /**
     * table Attributes 属性
     * https://element.eleme.cn/#/zh-CN/component/table#table-attributes
     * 支持上述链接中的所有属性
     * [注意] 如果上面data 存在 则优先级高于arrts中的data，会被上面data覆盖
     * example:
     * # component:
     *  <DefineTable :attrs="tableAttrs" />
     * # data
     *  data() {
     *    return {
     *       tableAttrs: {
     *          stripe: true,
     *          border: false,
     *          "show-header": true,
     *          data: [
     *              {
     *                  date: "2020-03-27",
     *                  name: "老村长",
     *                  address: "四川省成都市青羊区人民中路一段16号"
     *              },
     *              {
     *                  date: "2020-03-27",
     *                  name: "老村长",
     *                  address: "四川省成都市青羊区人民中路一段16号"
     *              }
     *          ]
     *      },
     *  }
     */
    attrs: Object,
    /**
     * table events 事件
     * https://element.eleme.cn/#/zh-CN/component/table#table-events
     * 支持上述链接中的所有属性
     * 参数同上述链接中的参数相同
     * example:
     * # component:
     *  <DefineTable :events="tableEvents" />
     * # data
     *  data() {
     *    return {
     *       tableEvents: {
     *           "row-click": (row, column, event) => {
     *               console.log(row);
     *               console.log(column);
     *               console.log(event);
     *           }
     *       }
     *  }
     */
    events: Object,
    /**
     * table 列配置
     * [注意] Scoped Slot 通过 scopedSlots 属性指定
     * https://element.eleme.cn/#/zh-CN/component/table#table-column-attributes
     * 支持上述链接的所有参数
     * example:
     * # component:
     *  <DefineTable :cols="cols" />
     * # data
     *  data() {
     *    return {
     *       cols: [
     *          { prop: "date", label: "日期", width: "180" },
     *          { prop: "name", label: "姓名", width: "180" },
     *          { prop: "address", label: "地址" },
     *          {
     *              label: "操作",
     *              scopedSlots: {
     *                  default: scoped => {
     *                      return (
     *                          <el-button
     *                              onClick={() => this.handlerEditor(scoped.row)}
     *                              type="primary"
     *                              plain
     *                              size="small"
     *                              icon="el-icon-edit"
     *                          >
     *                              Editor
     *                          </el-button>
     *                      );
     *                  }
     *              }
     *          }
     *        ]
     *     },
     *  },
     *  methods: {
     *      handlerEditor(row) {
     *          console.log(row)
     *      }
     * }
     */
    rowKey: Function,
    cols: {
      type: Array,
      default: () => []
    },
    lazy: {
      type: Boolean,
      default: false
    },
    /**
     * 分页器参数 不传则不显示
     * https://element.eleme.cn/#/zh-CN/component/pagination#attributes
     * 支持上面链接中的所有参数，带中划线参数转为驼峰命名page-count  => pageCount
     * 除了上面参数之外添加新的参数：[align] 对齐方式 'left', 'center', 'right'
     */
    paging: {
      type: Object,
      default: () => {}
    },
    /**
     * 分页器事件
     * https://element.eleme.cn/#/zh-CN/component/pagination#events
     * 支持上面链接中的所有事件，带中划线事件名转为驼峰命名 current-change  => currentChange
     * 注意下面三个事件全部在 绑定在 currentChange
     * 'current-change': this.localPagingEvents.currentChange,
     * 'prev-click': this.localPagingEvents.currentChange,
     * 'next-click': this.localPagingEvents.currentChange
     *
     * Exapmle:
     * pagingEvents: {
     *    sizeChange: (pageSize) => {
     *     console.log(pageSize)
     *   },
     *   currentChange: (page) => {
     *     console.log(page)
     *   }
     * }
     *
     */
    pagingEvents: {
      type: Object,
      default: () => {}
    }
  },
  mounted() {
    if (this.autoHeight) {
      this.getAutoHeight()
      const _vm = this
      window.onresize = function () {
        _vm.getAutoHeight()
      }
    }
  },
  computed: {
    localPaging() {
      if (this.paging) {
        return mergePagingArguments(this.paging, 'attrs')
      }
      return undefined
    },
    localPagingEvents() {
      if (this.pagingEvents) {
        return mergePagingArguments(this.pagingEvents, 'events')
      }
      return {}
    }
  },
  data() {
    return {
      height: '200px'
    }
  },
  methods: {
    // 这个方法用来动态设置 height
    getAutoHeight() {
      try {
        let el = document.querySelector('#auto-height')
        if (!el) return
        let elParent = el.parentNode,
          pt = this.getStyle(elParent, 'paddingTop'),
          pb = this.getStyle(elParent, 'paddingBottom')
        // 一定要使用 nextTick 来改变height 不然不会起作用
        this.$nextTick(() => {
          this.height = elParent.clientHeight - (pt + pb) + 'px'
        })
      } catch (e) {
        console.log(e)
      }
    },
    // 获取样式 我们需要减掉 padding-top， padding-bottom的值
    getStyle(obj, attr) {
      // 兼容IE浏览器
      let result = obj.currentStyle
        ? obj.currentStyle[attr].replace('px', '')
        : document.defaultView.getComputedStyle(obj, null)[attr].replace('px', '')
      return Number(result)
    }
  },
  render() {
    // 直属 data 优先级高于 attrs中的data的原因
    // 如果存在直属data 和attrs 中的data同时存在 => 删除attrs中的data
    if (this.data && this.attrs && this.attrs.data) {
      delete this.attrs.data
    }
    return (
      <div class="mo_table" style={{ overflowY: this.autoHeight ? 'hidden' : 'auto' }}>
        {/* table */}
        <div class="mo_table_wrap" style={{ overflowY: this.autoHeight ? 'hidden' : 'auto' }}>
          <el-table
            ref="dataTable"
            id={this.autoHeight ? 'auto-height' : undefined}
            data={this.data}
            row-key={this.rowKey}
            {...{ attrs: this.attrs }}
            {...{ on: this.events }}
            height={this.autoHeight ? this.height : undefined}
            // ref={this.attrs.ref || null}
          >
            {/* table 列 */}
            {this.cols.map((col, index) => {
              // 结构出插槽
              const { scopedSlots } = col
              return (
                <el-table-column
                  show-overflow-tooltip
                  key={index}
                  {...{ attrs: col }}
                  label={col.label}
                  scopedSlots={scopedSlots}
                />
              )
            })}
          </el-table>
        </div>
        {/* 分页器 不传入 paging prop 则不显示 */}
        {this.localPaging ? (
          <el-pagination
            {...{ attrs: this.localPaging }}
            {...{
              on: {
                ...this.localPagingEvents,
                'size-change': this.localPagingEvents.sizeChange,
                'current-change': this.localPagingEvents.currentChange,
                'prev-click': this.localPagingEvents.currentChange,
                'next-click': this.localPagingEvents.currentChange
              }
            }}
          />
        ) : null}
      </div>
    )
  }
}
</script>
<style scope>
.mo_table {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.mo_table > .mo_table_wrap {
  flex: 1;
}
.mo_table > .el-pagination,
.mo_table > .mo_table_wrap {
  margin-bottom: 1rem;
}
</style>
