<template>
  <el-dialog :visible.sync="visible" title="上传附件" :before-close="handleClose" destroy-on-close width="600px">
    <div class="w-100">
      <div class="__ua-choose-box">
        <el-cascader
          class="w-100"
          v-model="fileCategory"
          :options="fileCategories"
          filterable
          placeholder="请选择文件类别"
          popper-class="__cascader-full-height"
          :props="{
            expandTrigger: 'hover',
            filterable: true,
            value: 'value',
            label: 'value'
          }"
        ></el-cascader>
        <el-upload
          action=""
          :auto-upload="false"
          :on-change="handleChange"
          :show-file-list="false"
          multiple
          :file-list="fileList"
          accept=".jpeg, .png, .pdf, .jpg, .doc, .docx, .xlsx, .xls, .zip, .rar, .mp4, .mp3, .wav, .txt, .ppt, .pptx, .avi, .mov, .mkv"
        >
          <el-button
            type="primary"
            icon="fas fa-plus"
            class="m-mini-l"
            slot="trigger"
            :disabled="fileCategory.length == 0"
          >
            添加附件
          </el-button>
        </el-upload>
      </div>
      <div slot="tip" class="el-upload__tip">单个文件大小不超过 4MB</div>
    </div>
    <div class="w-100 m-mini-t">
      <ul class="__ua-file-list">
        <li v-for="(f, idx) in fileList" :key="idx" class="__ua-file-item">
          <span>{{ f.categoryName }} - {{ f.name }}</span>
          <el-button type="plain" icon="fas fa-times" size="mini" @click="handleRemove(idx)">删除</el-button>
        </li>
      </ul>
    </div>
    <div slot="footer" class="dialog-footer m-mini-t">
      <div class="d-flex __ua-actions">
        <el-button
          type="primary"
          @click="handleUpload"
          :loading="uploading"
          :disabled="fileList.length == 0"
          icon="fas fa-upload"
        >
          {{ caseId ? '全部上传' : '确认添加' }}
        </el-button>
        <el-button type="danger" @click="handleRemoveAll" :disabled="fileList.length == 0" icon="fas fa-trash">
          全部删除
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import * as claimApi from '@/apis/claim'

export default {
  props: {
    caseId: {
      type: Number,
      required: false
    },
    visible: {
      type: Boolean,
      default: false
    },
    maxSize: {
      type: Number,
      default: 8
    }
  },
  data() {
    return {
      uploading: false,
      fileList: [],
      fileCategory: [],
      rawFileCategories: {
        承保类: ['保单', '批单', '保险合同', '倒签保函', '其他'],
        通用单证: ['索赔申请书', '营业执照', '身份证', '其他'],
        运输单证: [
          '提单',
          '运输合同/运单',
          '车辆道路运输证',
          '装车清单',
          '运输经营许可证',
          '驾驶证',
          '行驶证',
          '运输工具证明',
          '监装报告',
          '设备交接单EIR',
          '签收交接单',
          '其他'
        ],
        货物单证: ['形式发票', '报关单', '购货合同', '装箱单', '其他'],
        现场照片视频类: [
          '运输工具（全貌、船舱、车厢的密封情况、受损状况、船名、车牌号）',
          '集装箱（全貌、集装箱号、内外状况、受损细节）',
          '货物（全貌、包装、运输标志，防震动、防倾、温控标签，绑扎和积载、铭牌，受损细节）',
          '标的货物（全貌、包装、运输标志，防震动、防倾、温控标签，绑扎和积载、铭牌，受损细节）',
          '发货现场（装箱情况、标的物状态）',
          '其它'
        ],
        定责类: ['交通责任认定书', '火灾证明', '破损单', '理货报告', '承运人出具的事故说明', '其他'],
        定损类: ['报损清单', '检验报告', '鉴定报告', '其他'],
        支付类: ['保险赔付协议', '报价单', '发票、收据', '维修明细', '支付授权书', '其他'],
        第三方材料: ['公估报告', '检验报告', '费用账单', '发票', '其他'],
        追偿类: ['向责任方索赔函', '其他'],
        残值类: ['残值照片', '残值清单', '残值报价', '残值回收支付单证', '其他'],
        风控类: ['风险调查', '风险报告', '其他']
      }
    }
  },
  computed: {
    fileCategories() {
      const fileTypes = []
      Object.keys(this.rawFileCategories).forEach((key) => {
        const option = {
          label: key,
          value: key,
          children: []
        }
        this.rawFileCategories[key].forEach((item) => {
          option.children.push({
            label: item,
            value: item
          })
        })

        fileTypes.push(option)
      })

      return fileTypes
    }
  },
  methods: {
    handleRemove(fileIndex) {
      this.fileList.splice(fileIndex, 1)
    },
    handleChange(file) {
      if (file.size / 1024 / 1024 > this.maxSize) {
        this.$message.error(`文件大小不能超过 ${this.maxSize} MB`)
        return
      }

      if (this.fileCategory.length === 0) {
        this.$message.error('请先选择文件类别')
        return
      }

      const fileType = this.fileCategory.join('/')
      file.category = {
        primary: this.fileCategory[0],
        secondary: this.fileCategory[1]
      }
      file.categoryName = fileType
      this.fileList.push(file)
    },
    async handleRemoveAll() {
      try {
        await this.$confirm('确认删除所有附件吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        this.fileList = []
        this.fileCategory = []
      } catch (error) {
        //
      }
    },
    async handleUpload() {
      if (this.fileList.length === 0) {
        this.$message.error('请先添加附件')
        return
      }

      if (this.caseId) {
        this.uploading = true
        for (const f of this.fileList) {
          try {
            await claimApi.uploadAttachment(this.caseId, {
              primary_category: f.category.primary,
              secondary_category: f.category.secondary,
              name: f.name,
              file: f.raw
            })
          } catch (e) {
            this.uploading = false
          }
        }

        this.uploading = false
        this.$message({
          type: 'success',
          message: '上传成功'
        })
      }

      this.$emit('succeed', this.fileList)
      this.handleClose()
    },
    handleClose() {
      this.fileList = []
      this.fileCategory = []
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style lang="scss">
.__cascader-full-height .el-cascader-menu__wrap {
  height: auto !important;
}
</style>

<style scoped lang="scss">
.__ua-choose-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.__ua-file-list {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  margin: 0;
  padding: 0;
  list-style: none;

  .__ua-file-item {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &:not(:last-child) {
      margin-bottom: 10px;
    }
  }
}

.__ua-actions {
  justify-content: center;
}
</style>
