<!--
 * @Descripttion:
 * @version:
 * @Author: yanb
 * @Date: 2023-09-05 09:58:31
 * @LastEditors: yanb
 * @LastEditTime: 2023-12-08 15:45:08
-->
<template>
  <div class="p-extra-large-x p-extra-large-b m-extra-large-t bg-white flex-fill o-hidden o-y-auto w-100">
    <div class="w-100 text-center">
      <h3>赔案理算书</h3>
    </div>
    <table class="table">
      <tbody>
        <tr>
          <td class="title" colspan="8">案件基本信息</td>
        </tr>
        <tr>
          <td>被保险人</td>
          <td>{{ data?.insured }}</td>
          <td>保险公司</td>
          <td>{{ data?.company_branch?.name }}</td>
          <td>保单号</td>
          <td>{{ data?.policy_no }}</td>
          <td>险种</td>
          <td>{{ data?.policy_type_text }}</td>
        </tr>
        <tr>
          <td>投保人</td>
          <td>{{ data?.policyholder }}</td>
          <td>理赔编号</td>
          <td>{{ data?.case_no }}</td>
          <td>保司报案号</td>
          <td>{{ data?.external_case_no }}</td>
          <td>保险金额({{ data?.policy_coverage_currency?.name }})</td>
          <td>{{ data?.policy_coverage }}</td>
        </tr>
        <tr>
          <td>报案人</td>
          <td>{{ data?.claimant }}</td>
          <td>报案人电话</td>
          <td>{{ data?.claimant_phone_number }}</td>
          <td>报案人邮箱</td>
          <td>{{ data?.claimant_email }}</td>
          <td>重复报案提示</td>
          <td>
            本保单下报案次数: {{ data?.report_num }} <br />
            <pre>{{ data?.claim_records?.join('\r\n') }}</pre>
          </td>
        </tr>
        <tr>
          <td>业务员</td>
          <td>{{ data?.salesman?.name }}</td>
          <td>报案时间</td>
          <td>{{ data?.created_at }}</td>
          <td>保险公司理赔员</td>
          <td>{{ data?.external_adjuster }}</td>
          <td>保司理赔员手机号</td>
          <td>{{ data?.external_adjuster_phone_number }}</td>
        </tr>
        <tr>
          <td class="title" colspan="8">案件处理</td>
        </tr>
        <tr>
          <td>标的</td>
          <td colspan="7">{{ data?.subject }}</td>
        </tr>
        <tr>
          <td>出险时间</td>
          <td colspan="7">{{ data?.date_of_loss }}</td>
        </tr>
        <tr>
          <td>出险地点</td>
          <td colspan="7">
            {{ Array.isArray(data?.loss_location) ? data?.loss_location.join('/') : data?.loss_location }}
          </td>
        </tr>
        <tr>
          <td>出险原因</td>
          <td colspan="7">{{ Array.isArray(data?.loss_reason) ? data?.loss_reason.join('/') : data?.loss_reason }}</td>
        </tr>
        <tr>
          <td>事故经过</td>
          <td colspan="7">{{ data?.loss_detail }}</td>
        </tr>
        <template v-if="isCargoCase">
          <tr>
            <td>买方</td>
            <td colspan="7">{{ data?.buyer }}</td>
          </tr>
          <tr>
            <td>卖方</td>
            <td colspan="7">{{ data?.seller }}</td>
          </tr>
          <tr>
            <td>货物类别</td>
            <td colspan="7">{{ data?.goods_type?.name }}</td>
          </tr>
          <tr>
            <td>包装方式</td>
            <td colspan="7">{{ data?.packaging_method_text }}</td>
          </tr>
          <tr>
            <td>装载方式</td>
            <td colspan="7">{{ data?.loading_method_text }}</td>
          </tr>
          <tr>
            <td>装箱公司</td>
            <td colspan="7">{{ data?.packing_company }}</td>
          </tr>
          <tr>
            <td>承运人</td>
            <td colspan="7">{{ data?.carrier }}</td>
          </tr>
          <tr>
            <td>运输公司</td>
            <td colspan="7">{{ data?.transportation_company }}</td>
          </tr>
          <tr>
            <td>搬运公司</td>
            <td colspan="7">{{ data?.moving_company }}</td>
          </tr>
          <tr>
            <td>贸易类型</td>
            <td colspan="7">{{ data?.trade_type_text }}</td>
          </tr>
          <tr>
            <td>起运地</td>
            <td colspan="7">{{ data?.departure }}</td>
          </tr>
          <tr>
            <td>目的地</td>
            <td colspan="7">{{ data?.destination }}</td>
          </tr>
          <tr>
            <td>运输方式</td>
            <td colspan="7">{{ data?.transport_method?.name }}</td>
          </tr>
          <tr>
            <td>运输工具</td>
            <td colspan="7">{{ data?.transport_no }}</td>
          </tr>
          <tr>
            <td>起运日期</td>
            <td colspan="7">{{ data?.shipping_date }}</td>
          </tr>
          <tr>
            <td>发票金额({{ data?.invoice_amount_currency?.name }})</td>
            <td colspan="7">{{ data?.invoice_amount }}</td>
          </tr>
          <tr>
            <td>发票号</td>
            <td colspan="7">{{ data?.invoice_no }}</td>
          </tr>
          <tr>
            <td>提/运单号</td>
            <td colspan="7">{{ data?.waybill_no }}</td>
          </tr>
        </template>
        <tr>
          <td>主险</td>
          <td colspan="7">{{ data?.main_clause }}</td>
        </tr>
        <tr>
          <td>附加险</td>
          <td colspan="7">{{ data?.additional_clause }}</td>
        </tr>
        <tr>
          <td>免赔约定</td>
          <td colspan="7">{{ data?.special_clause }}</td>
        </tr>
        <tr>
          <td>保险责任成立</td>
          <td colspan="7">{{ data?.is_established === 1 ? '是' : '否' }}</td>
        </tr>
        <tr>
          <td class="title" colspan="8">理算报告</td>
        </tr>
        <tr>
          <td colspan="8">
            <pre>{{ data?.claims_assessment_report }}</pre>
          </td>
        </tr>
        <tr>
          <td class="title" colspan="8">结案信息</td>
        </tr>
        <tr>
          <td>结案金额(CNY)</td>
          <td colspan="3">{{ claimSettleAmount }}</td>
          <td>结案日期</td>
          <td colspan="3">{{ data?.settlement_date }}</td>
        </tr>
        <tr>
          <td>结案赔款({{ data?.settlement_payment_amount_currency?.name }})</td>
          <td colspan="3">{{ data?.settlement_payment_amount }}</td>
          <td>结案费用({{ data?.settlement_costs_currency?.name }})</td>
          <td colspan="3">{{ data?.settlement_costs }}</td>
        </tr>
        <tr>
          <td>理算人</td>
          <td colspan="2">{{ data?.operator?.name }}</td>
          <td>审核人</td>
          <td></td>
          <td>打印时间</td>
          <td colspan="2">{{ now }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
import dayjs from 'dayjs'
export default {
  name: 'ReportPdf',
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      currencies: {}
    }
  },
  computed: {
    isCargoCase() {
      return [1, 2, 7].includes(this.data?.policy_type)
    },
    claimSettleAmount() {
      return Number(
        Number(this.data?.settlement_payment_amount * this.data?.settlement_payment_amount_currency?.rate) +
          Number(this.data?.settlement_costs * this.data?.settlement_costs_currency?.rate)
      ).toFixed(2)
    },
    now() {
      return dayjs().format('YYYY-MM-DD H:mm:ss')
    }
  }
}
</script>
<style lang="scss" scoped>
h3 {
  text-align: center;
}
.table {
  border-collapse: collapse;
  width: 100%;
}
.table td {
  border: 1px solid #333;
  padding: 5px;
}
.title {
  font-weight: bold;
  background: #aaa;
}
</style>
