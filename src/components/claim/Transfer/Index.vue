<!--
 * @Descripttion: 
 * @version: 
 * @Author: yanb
 * @Date: 2023-08-09 14:33:27
 * @LastEditors: yanb
 * @LastEditTime: 2023-08-10 16:20:19
-->
<template>
  <el-dialog :visible.sync="visible" title="案件转移" :before-close="handleClose" destroy-on-close width="520px">
    <el-select class="w-100" v-model="adjusterId" placeholder="请选择接收方">
      <el-option v-for="op in adjusters" :key="op.id" :value="op.id" :label="op.name"></el-option>
    </el-select>

    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleTransfer" :loading="loading" icon="fas fa-check">确认转移</el-button>
      <el-button type="default" @click="handleClose" icon="fas fa-times">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import * as claimApi from '@/apis/claim'
import * as adminApi from '@/apis/admin'

export default {
  props: {
    caseId: {
      type: Number,
      required: true
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      adjusterId: null,
      loading: false,
      adjusters: []
    }
  },
  async created() {
    const data = await adminApi.getClaimsAdjusters()
    this.adjusters = data.data
  },
  methods: {
    async handleTransfer() {
      try {
        this.loading = true
        await claimApi.transferCase(this.caseId, {
          new_operator_id: this.adjusterId
        })

        this.$message.success('转移成功')
        this.$emit('succeed', this.caseId, this.adjusterId)
        this.handleClose()
      } catch {
        this.loading = false
        this.$message.error('转移失败')
      } finally {
        this.loading = false
      }
    },
    handleClose() {
      this.$emit('update:visible', false)
    }
  }
}
</script>
