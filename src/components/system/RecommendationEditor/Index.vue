<template>
  <el-dialog title="推荐设置" :visible.sync="visible" width="800px" :before-close="handleClose" destroy-on-close>
    <el-form ref="form" :model="form" :rules="rules" label-suffix=":">
      <el-form-item prop="title" label="标题">
        <el-input v-model="form.title" placeholder="请输入标题"></el-input>
      </el-form-item>
      <el-form-item prop="starting_price" label="推荐价格">
        <el-input v-model="form.starting_price" placeholder="请输入推荐价格"></el-input>
      </el-form-item>
      <el-form-item prop="tags" label="标签">
        <template>
          <small class="text-warning">请输入英文逗号隔开</small>
        </template>
        <el-input v-model="form.tags" placeholder="请输入标签"></el-input>
      </el-form-item>
      <el-form-item prop="product_type" label="推荐产品类型">
        <el-select v-model="form.product_type" placeholder="请选择产品类型" class="w-100">
          <el-option v-for="(name, k) in types" :key="k" :value="k" :label="name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="image" label="封面图">
        <el-upload
          :on-change="handleCoverChange"
          action=""
          :file-list="cover"
          :show-file-list="false"
          :auto-upload="false"
        >
          <el-button size="small" type="primary" icon="fas fa-upload">点击上传</el-button>
          <p class="cover-preview">
            <img v-if="coverPreviewURL" :src="coverPreviewURL" alt="Image" />
            <img v-if="form.image && typeof form.image === 'string'" :src="form.image" alt="Image" />
          </p>
        </el-upload>
      </el-form-item>
      <el-form-item prop="content" label="推荐内容">
        <br />
        <editor v-model="form.content" class="w-100" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSubmit()">提交</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
export default {
  name: 'RecommendationEditor',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      types: {
        1: '国内货运险',
        2: '国际货运险',
        3: '单车责任险',
        4: '其他险种',
        5: '雇主责任险',
        6: '线下录入保单',
        7: '跨境电商险'
      },
      cover: [],
      coverPreviewURL: '',
      form: {
        product_type: '',
        title: '',
        starting_price: '',
        tags: '',
        image: '',
        content: ''
      },
      rules: {
        title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
        product_type: [{ required: true, message: '请选择产品类型', trigger: ['blur', 'change'] }],
        image: [{ required: true, message: '请上传封面图', trigger: ['blur', 'change'] }],
        content: [{ required: true, message: '推荐内容', trigger: 'blur' }]
      }
    }
  },
  watch: {
    data(value) {
      if (value.title) {
        this.form = Object.assign({}, value)
      } else {
        this.form = {
          product_type: '',
          title: '',
          starting_price: '',
          tags: '',
          image: '',
          content: ''
        }
      }
    }
  },
  methods: {
    handleCoverChange(file) {
      this.cover = [file]
      this.coverPreviewURL = URL.createObjectURL(file.raw)
      this.form.image = file.raw
    },
    handleClose() {
      this.$emit('update:visible', false)
      this.$refs.form.resetFields()
      this.cover = []
      this.coverPreviewURL = ''
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('submit', this.form)

          this.handleClose()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0px !important;
}

/deep/ .el-upload {
  .el-button {
    float: left;
    margin-bottom: 10px;
  }
}

.cover-preview img {
  max-width: 64px;
}
</style>
