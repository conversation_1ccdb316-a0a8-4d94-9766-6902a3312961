<template>
  <div>
    <el-dialog
      title="余额流水"
      :visible.sync="visible"
      width="980px"
      :before-close="handleBeforeClose"
      destroy-on-close
    >
      <search-panel
        @command="handleSearchPanel"
        @change="(data) => (searchData = data)"
        :custom="searchFields"
        size="small"
        :exportable="false"
      />

      <el-header class="d-flex align-items-center p-none-x">
        <el-button icon="el-icon-circle-plus" type="primary" @click="chargeDialog.visible = true"> 充值 </el-button>
      </el-header>

      <el-card shadow="never">
        <define-table :data="data" :cols="cols" :paging="paging" :paging-events="pagingEvents"></define-table>
      </el-card>
    </el-dialog>
    <el-dialog
      append-to-body
      title="充值"
      :visible.sync="chargeDialog.visible"
      width="520px"
      :before-close="() => (chargeDialog.visible = false)"
      destory-on-close
    >
      <el-form ref="form" :model="form" :rules="rules" label-suffix=":" label-width="100">
        <el-form-item prop="amount" label="充值金额">
          <el-input v-model="form.amount" placeholder="请输入金额" />
        </el-form-item>
        <el-form-item prop="proof" label="凭据">
          <el-upload
            :on-change="handleProofChange"
            action=""
            :file-list="proof"
            :show-file-list="false"
            :auto-upload="false"
          >
            <el-button size="small" type="primary">点击上传</el-button>
            <p class="proof-preview">
              <img v-if="proofPreviewURL" :src="proofPreviewURL" alt="Proof" />
            </p>
          </el-upload>
        </el-form-item>
        <el-form-item prop="remark" label="请输入备注">
          <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入金额" />
        </el-form-item>
        <el-form-item>
          <el-form-item>
            <el-button @click="chargeDialog.visible = false">取 消</el-button>
            <el-button type="primary" @click="handleSubmit">确 定</el-button>
          </el-form-item>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'PlatformTransaction',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    visible: {
      type: Boolean,
      default: false
    },
    paging: {
      type: Object,
      default: () => {
        return {
          currentPage: 1,
          pageSize: 15,
          layout: ' prev, pager, next, jumper, total',
          total: 0
        }
      }
    }
  },
  data() {
    return {
      chargeDialog: {
        visible: false
      },
      pagingEvents: {
        currentChange: (page) => {
          this.$emit('fetch-data', this.searchData, page)
        }
      },
      searchFields: [
        {
          type: 'select',
          valKey: 'type',
          hintText: '类型',
          options: [
            { label: '充值', value: 1 },
            { label: '扣费', value: 2 },
            { label: '退款', value: 3 }
          ]
        },
        {
          type: 'input',
          valKey: 'policy_no',
          hintText: '保单号'
        },
        {
          type: 'daterange',
          valKey: 'created_at_range'
        }
      ],
      searchData: {},
      cols: [
        {
          label: '类型',
          prop: 'type',
          width: 50,
          scopedSlots: {
            default: (scoped) => {
              console.log(scoped.row.type)
              switch (parseInt(scoped.row.type, 10)) {
                case 1:
                  return <span class="text-primary">充值</span>
                case 2:
                  return <span class="text-blue">扣费</span>
                case 3:
                  return <span class="text-danger">退款</span>
                default:
                  break
              }
            }
          }
        },
        {
          label: '操作人',
          prop: 'operator',
          width: 80
        },
        {
          label: '交易保单号',
          width: 180,
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.policy.policy_no) {
                return scoped.row.policy.policy_no
              }

              return '-'
            }
          }
        },
        {
          label: '交易时间',
          prop: 'created_at'
        },
        {
          label: '金额',
          width: 80,
          prop: 'amount'
        },
        {
          label: '余额',
          width: 80,
          prop: 'balance'
        },
        {
          label: '凭据',
          prop: 'proof',
          width: 80,
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              if (scoped.row.proof) {
                return (
                  <el-link href={scoped.row.proof} target="_blank">
                    查看
                  </el-link>
                )
              }

              return '-'
            }
          }
        },
        {
          label: '备注',
          prop: 'remark'
        }
      ],
      proof: [],
      proofPreviewURL: '',
      form: {
        amount: '',
        proof: '',
        remark: ''
      },
      rules: {
        amount: [{ required: true, message: '请输入充值金额', trigger: 'blur' }],
        proof: [{ required: true, message: '请上传充值凭证', trigger: ['blur', 'change'] }]
      }
    }
  },
  methods: {
    handleSearchPanel(command, data) {
      this.searchData = data
      if (command == 'submit') {
        this.$emit('fetch-data', this.searchData, 1)
      }
    },
    handleProofChange(file) {
      this.proof = [file]
      this.proofPreviewURL = URL.createObjectURL(file.raw)
      this.form.proof = file.raw
    },
    handleBeforeClose() {
      this.$emit('update:visible', false)
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.chargeDialog.visible = false
          this.$emit('update:visible', false)
          this.$emit('submit', this.form)

          this.$refs.form.resetFields()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0px !important;
}

/deep/ .el-upload {
  .el-button {
    float: left;
    margin-bottom: 10px;
  }
}

.proof-preview img {
  max-width: 64px;
}
</style>
