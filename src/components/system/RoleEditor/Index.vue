<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-21 16:48:22
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-22 10:03:18
-->
<template>
  <el-dialog
    :title="currentDialogTitle"
    :visible.sync="visible"
    width="520px"
    :before-close="handleBeforeClose"
    destroy-on-close
  >
    <el-form ref="form" label-suffix=":" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="角色标识" prop="name">
        <el-input v-model.trim="form.name" placeholder="请输入角色标识 如: sales"></el-input>
      </el-form-item>
      <el-form-item label="角色名称" prop="display_name">
        <el-input v-model.trim="form.display_name" placeholder="请输入角色名称"></el-input>
      </el-form-item>
      <template v-if="isSuperAdmin">
        <el-form-item label="平台角色">
          <el-switch
            active-text="是"
            inactive-text="否"
            :active-value="1"
            :inactive-value="0"
            v-model="form.is_platform"
          ></el-switch>
        </el-form-item>
        <el-form-item label="平台默认角色">
          <el-switch
            active-text="是"
            inactive-text="否"
            :active-value="1"
            :inactive-value="0"
            v-model="form.is_platform_default"
          ></el-switch>
        </el-form-item>
      </template>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleBeforeClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  name: 'RoleEditor',
  props: {
    model: {
      type: [Object, MouseEvent],
      default: () => {}
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        name: '',
        display_name: '',
        is_platform: 0,
        is_platform_default: 0
      },
      rules: {
        name: [{ required: true, message: '角色标识必填', trigger: 'blur' }],
        display_name: [{ required: true, message: '角色显示名称', trigger: 'blur' }]
      }
    }
  },
  computed: {
    ...mapGetters('auth', ['roleName']),
    currentDialogTitle() {
      return this.form && this.form.id ? '修改角色' : '添加角色'
    },
    isSuperAdmin() {
      return this.roleName === 'super-admin'
    }
  },
  methods: {
    handleBeforeClose() {
      this.$emit('update:visible', false)
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('submit', this.form)

          this.$emit('update:visible', false)
        }
      })
    }
  },
  watch: {
    model: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val) {
          this.form = Object.assign({}, val)
        } else {
          this.form = Object.assign(
            {},
            {
              name: '',
              display_name: '',
              is_platform: 0,
              is_platform_default: 0
            }
          )
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
