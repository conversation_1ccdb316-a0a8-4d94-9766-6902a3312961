<template>
  <div class="p-extra-large-x p-extra-large-b w-100 o-hidden-x">
    <el-card class="table-wrap" shadow="never">
      <el-form ref="form" :model="form" :rules="rules" label-position="top">
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item label="平台名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入平台名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="默认管理员" prop="admin_id">
              <el-select v-model="form.admin_id" placeholder="请选择平台管理员" filterable style="width: 100%">
                <el-option v-for="admin in admins" :key="admin.id" :value="admin.id" :label="admin.name" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item label="域名" prop="domain">
              <el-input v-model="form.domain" placeholder="请输入绑定域名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="站点标题" prop="title">
              <el-input v-model="form.title" placeholder="请输入站点标题"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="平台标语" prop="slogan">
              <el-input type="textarea" :rows="2" v-model="form.slogan" placeholder="请输入站点标语"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="平台描述" prop="description">
              <el-input type="textarea" :rows="2" v-model="form.description" placeholder="请输入平台描述"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item label="站点图标" prop="favicon">
              <el-upload
                :on-change="handleIconChange"
                action=""
                :file-list="favicon"
                :show-file-list="false"
                :auto-upload="false"
              >
                <el-button size="small" type="primary">点击上传</el-button>
                <p class="icon-preview">
                  <img v-if="faviconPreviewURL" :src="faviconPreviewURL" alt="ICON" />
                </p>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="LOGO" prop="logo">
              <el-upload
                :on-change="handleLogoChange"
                action=""
                :file-list="logo"
                :show-file-list="false"
                :auto-upload="false"
              >
                <el-button size="small" type="primary">点击上传</el-button>
                <p class="logo-preview">
                  <img v-if="logoPreviewURL" :src="logoPreviewURL" alt="Logo" />
                </p>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item label="白色 LOGO" prop="logo">
              <el-upload
                :on-change="handleLogoWhiteChange"
                action=""
                :file-list="logoWhite"
                :show-file-list="false"
                :auto-upload="false"
              >
                <el-button size="small" type="primary">点击上传</el-button>
                <p class="logo-preview">
                  <img v-if="logoWhitePreviewURL" :src="logoWhitePreviewURL" alt="Logo White" />
                </p>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="背景图" prop="bg_image">
              <el-upload
                :on-change="handleBgImageChange"
                action=""
                :file-list="bgImage"
                :show-file-list="false"
                :auto-upload="false"
              >
                <el-button size="small" type="primary">点击上传</el-button>
                <p class="bg-image-preview">
                  <img v-if="bgImagePreviewURL" :src="bgImagePreviewURL" alt="Logo" />
                </p>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item label="主色调" prop="primary_color">
              <el-input v-model="form.primary_color" placeholder="请输入主色调"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="副色调" prop="secondary_color">
              <el-input v-model="form.secondary_color" placeholder="请输入副色调"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="自定义样式" prop="styles">
              <el-input type="textarea" :rows="5" v-model="form.styles" placeholder="请输入 CSS 样式"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="自定义底部" prop="custom_footer">
              <el-input
                type="textarea"
                :rows="5"
                v-model="form.custom_footer"
                placeholder="测试|链接地址 一行一条"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12" v-for="(name, key) in configs" :key="key">
            <el-form-item :label="name">
              <el-input v-model="form.configs[key]" type="textarea" :rows="1" autosize :placeholder="name"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item label="标的绑定" prop="subject_ids">
              <el-select
                v-model="form.subject_ids"
                multiple
                placeholder="请选择平台可使用标的"
                filterable
                style="width: 100%"
              >
                <el-option v-for="subject in subjects" :key="subject.id" :value="subject.id" :label="subject.name" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="48">
          <el-col :span="24">
            <el-form-item label="功能列表" prop="feature_ids">
              <el-tree
                ref="featuresTree"
                :data="features"
                :props="{ label: 'name', children: 'children' }"
                :default-expanded-keys="form.feature_ids"
                :default-checked-keys="form.feature_ids"
                node-key="id"
                show-checkbox
                :check-strictly="true"
                :accordion="true"
              ></el-tree>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item>
              <el-button type="primary" icon="fas fa-check" @click="handleSubmit()">提交</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import * as featureApi from '@/apis/feature'
import { getAssociableAdmins } from '@/apis/admin'
import { getSubjects } from '@/apis/subject'
import { mapGetters } from 'vuex'
import { Loading } from 'element-ui'
import { array2Tree } from '@/utils'

export default {
  name: 'PlatformsForm',
  props: {
    platform: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      admins: [],
      features: [],
      subjects: [],
      favicon: [],
      faviconPreviewURL: '',
      logo: [],
      logoPreviewURL: '',
      logoWhite: [],
      logoWhitePreviewURL: '',
      bgImage: [],
      bgImagePreviewURL: '',
      configs: {
        official_website: '官网地址',
        registration_email_recipients: '新用户注册提醒邮箱',
        sms_sign_name: '短信签名',
        claim_new_cases_report_email_recipients: '理赔新案件报备邮箱',
        smtp_host: 'SMTP 服务器地址',
        smtp_port: 'STMP 端口',
        smtp_encryption: 'STMP 加密方式(ssl/tls)',
        smtp_username: 'STMP 用户名',
        smtp_password: 'STMP 密码',
        smtp_from: 'STMP 发件人邮箱',
        smtp_from_name: 'STMP 发件人名称',
        email_signature: '邮件签名',
        online_payment_is_enabled: '在线支付是否启用(0:禁用 1:启用)',
        online_payment_url: '在线支付URL',
        online_payment_token: '在线支付TOKEN'
      },
      form: {
        name: '',
        admin_id: '',
        domain: '',
        title: '',
        description: '',
        slogan: '',
        favicon: '',
        logo: '',
        logo_white: '',
        bg_image: '',
        primary_color: '',
        secondary_color: '',
        styles: '',
        custom_footer: '',
        feature_ids: [],
        subject_ids: [],
        configs: {
          official_website: '',
          registration_email_recipients: '',
          sms_sign_name: '',
          claim_new_cases_report_email_recipients: '',
          smtp_host: '',
          smtp_port: 465,
          smtp_encryption: 'ssl',
          smtp_username: '',
          smtp_password: '',
          smtp_from: '',
          smtp_from_name: '',
          email_signature: '',
          online_payment_is_enabled: 0,
          online_payment_url: '',
          online_payment_token: ''
        }
      },
      rules: {
        name: [{ required: true, message: '请输入平台名称', trigger: 'blur' }],
        admin_id: [{ required: true, type: 'number', message: '请选择平台管理员', trigger: ['blur', 'change'] }],
        domain: [{ required: true, message: '请输入绑定域名', trigger: 'blur' }],
        title: [{ required: true, message: '请输入平台站点标题', trigger: 'blur' }],
        slogan: [{ required: true, message: '请输入平台标语', trigger: 'blur' }],
        description: [{ required: true, message: '请输入平台描述', trigger: 'blur' }],
        favicon: [{ required: true, message: '请上传站点图标', trigger: ['blur', 'change'] }],
        logo: [{ required: true, message: '请上传站点 LOGO', trigger: ['blur', 'change'] }],
        logo_white: [{ required: true, message: '请上传站点 LOGO', trigger: ['blur', 'change'] }],
        bg_image: [{ required: true, message: '请上传背景图', trigger: ['blur', 'change'] }],
        primary_color: [{ required: true, message: '请输入主色调', trigger: 'blur' }],
        secondary_color: [{ required: true, message: '请输入副色调', trigger: 'blur' }]
      }
    }
  },
  watch: {
    platform: {
      immediate: true,
      deep: true,
      handler(value) {
        if (value) {
          if (!value.configs || value?.configs?.length === 0) {
            value.configs = {}
          }
          this.form = Object.assign({}, value)

          this.faviconPreviewURL = this.form.favicon
          this.logoPreviewURL = this.form.logo
          this.logoWhitePreviewURL = this.form.logo_white
          this.bgImagePreviewURL = this.form.bg_image
        }
      }
    }
  },
  computed: {
    ...mapGetters('auth', ['admin'])
  },
  created() {
    getAssociableAdmins({
      platform_id: this.$route.params.id !== undefined ? this.$route.params.id : -1
    }).then((r) => {
      this.admins = r.data
    })
    this.fetchFeatures()
    this.fetchSubjects()
  },
  methods: {
    async fetchFeatures() {
      const loading = Loading.service()
      try {
        this.features = array2Tree((await featureApi.fetchFeatures()) || [], 'id', 'parent_id', -1, 'name')
      } finally {
        loading.close()
      }
    },
    async fetchSubjects() {
      const loading = Loading.service()
      try {
        const _subjects = await getSubjects()
        this.subjects = _subjects.data.map((e) => ({ id: e.id, name: e.name }))
      } finally {
        loading.close()
      }
    },
    handleIconChange(file) {
      this.favicon = [file]
      this.faviconPreviewURL = URL.createObjectURL(file.raw)
      this.form.favicon = file.raw
    },
    handleLogoChange(file) {
      this.logo = [file]
      this.logoPreviewURL = URL.createObjectURL(file.raw)
      this.form.logo = file.raw
    },
    handleLogoWhiteChange(file) {
      this.logoWhite = [file]
      this.logoWhitePreviewURL = URL.createObjectURL(file.raw)
      this.form.logo_white = file.raw
    },
    handleBgImageChange(file) {
      this.bgImage = [file]
      this.bgImagePreviewURL = URL.createObjectURL(file.raw)
      this.form.bg_image = file.raw
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const data = Object.assign({}, this.form)
          data.configs = JSON.stringify(data.configs)
          data.feature_ids = this.$refs.featuresTree.getCheckedNodes(false, true).map((node) => node.id)

          this.$emit('submit', data)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.platforms-form {
  /deep/ .el-upload {
    .el-button {
      float: left;
      margin-bottom: 10px;
    }
  }
}
.icon-preview img {
  max-width: 64px;
}

.logo-preview img {
  max-height: 100px;
}

.bg-image-preview img {
  max-height: 300px;
}
</style>
