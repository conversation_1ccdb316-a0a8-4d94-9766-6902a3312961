<template>
  <div>
    <basic-search v-once @search="handleSearch"></basic-search>
    <el-card shadow="never" class="m-extra-large-t justify-content-start">
      <div class="d-flex justify-content-start">
        <el-button type="primary" @click="assignProductMultiple">统一配置</el-button>
      </div>
      <define-table
        ref="productTable"
        :style="{ marginTop: '20px' }"
        :data="products"
        :cols.sync="cols"
        :paging.sync="paging"
        :paging-events="pagingEvents"
        :events="tableEvents"
        :rowKey="getRowKeys"
      />
    </el-card>

    <el-dialog
      :visible.sync="rateForm.visible"
      :title="title"
      width="520px"
      :before-close="handleBeforeClose"
      destroy-on-close
    >
      <el-form ref="rateForm" :model="rateForm.form" :rules="rateForm.rules" label-suffix=":" label-width="120px">
        <el-form-item v-if="isCargoProduct" prop="platform_rate" label="平台费率">
          <el-input v-model="rateForm.form.platform_rate" placeholder="请输入平台费率 1.00">
            <template slot="append">‱</template>
          </el-input>
        </el-form-item>
        <el-form-item v-if="isCargoProduct" prop="platform_minimum_premium" label="平台最低保费">
          <el-input v-model="rateForm.form.platform_minimum_premium" placeholder="请输入平台最低保费 1.00">
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
        <el-form-item v-if="isCargoProduct" prop="agent_rate" label="代理费率">
          <el-input v-model="rateForm.form.agent_rate" placeholder="请输入代理费率 1.00">
            <template slot="append">‱</template>
          </el-input>
        </el-form-item>
        <el-form-item v-if="isCargoProduct" prop="agent_minimum_premium" label="代理最低保费">
          <el-input v-model="rateForm.form.agent_minimum_premium" placeholder="请输入代理最低保费 1.00">
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
        <el-form-item v-if="isCargoProduct" prop="user_rate" label="用户默认费率">
          <el-input v-model="rateForm.form.user_rate" placeholder="请输入用户默认费率 1.00">
            <template slot="append">‱</template>
          </el-input>
        </el-form-item>
        <el-form-item v-if="isCargoProduct" prop="minimum_premium" label="用户最低保费">
          <el-input v-model="rateForm.form.minimum_premium" placeholder="请输入用户最低保费 1.00">
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
        <el-form-item prop="platform_commission_rate" label="平台佣金比例">
          <el-input v-model="rateForm.form.platform_commission_rate" placeholder="请输入平台佣金比例 1.00">
            <template slot="append">%</template>
          </el-input>
        </el-form-item>
        <el-form-item prop="agent_commission_rate" label="代理佣金比例">
          <el-input v-model="rateForm.form.agent_commission_rate" placeholder="请输入代理佣金比例 1.00">
            <template slot="append">%</template>
          </el-input>
        </el-form-item>
        <el-form-item prop="is_enabled" label="是否启用">
          <el-switch
            active-text="是"
            inactive-text="否"
            :active-value="1"
            :inactive-value="0"
            v-model="rateForm.form.is_enabled"
          ></el-switch>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="handleBeforeClose" icon="fas fa-times">取 消</el-button>
        <el-button type="primary" @click="handleSubmitMultiple" v-if="is_multiple === true">确 定</el-button>
        <el-button type="primary" @click="handleSubmit" icon="fas fa-check" v-else>确 定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import ProductSearch from '@/components/product/ProductSearch'

export default {
  name: 'PlatformProducts',
  components: {
    'basic-search': ProductSearch
  },
  props: {
    name: {
      type: String,
      default: ''
    },
    isCargoProduct: {
      type: Boolean,
      default: true
    },
    data: {
      type: Array,
      default: () => []
    },
    paging: {
      type: Object,
      default: () => {
        return {
          page: 1,
          currentPage: 1,
          pageSize: 15,
          layout: 'prev, pager, next, jumper, total',
          total: 0
        }
      }
    },
    products: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    cols() {
      const cols = [
        { align: 'center', type: 'selection', reserveSelection: true },
        {
          hideNonCargo: true,
          label: '产品代码',
          prop: 'code',
          width: 100,
          fixed: 'left'
        },
        {
          label: '产品名称',
          prop: 'name',
          width: 180,
          fixed: 'left'
        },
        {
          label: '平台',
          width: 120,
          prop: 'platform.name'
        },
        {
          label: '出单公司',
          width: 100,
          prop: 'company_branch.name'
        },
        {
          label: '成本费率(‱)',
          hideNonCargo: true,
          prop: 'additional.rate'
        },
        {
          label: '最低保费',
          hideNonCargo: true,
          prop: 'additional.minimum_premium'
        },
        {
          label: '经纪费比例(%)',
          prop: 'additional.service_charge'
        },
        {
          label: '平台费率(‱)',
          hideNonCargo: true,
          scopedSlots: {
            default: (scoped) => {
              return this.data.find((e) => e.product_id === scoped.row.id)?.platform_rate || '-'
            }
          }
        },
        {
          label: '平台最低保费',
          hideNonCargo: true,
          scopedSlots: {
            default: (scoped) => {
              return this.data.find((e) => e.product_id === scoped.row.id)?.platform_minimum_premium || '-'
            }
          }
        },
        {
          label: '平台佣金比例(%)',
          scopedSlots: {
            default: (scoped) => {
              return this.data.find((e) => e.product_id === scoped.row.id)?.platform_commission_rate || '-'
            }
          }
        },
        {
          label: '代理费率(‱)',
          hideNonCargo: true,
          scopedSlots: {
            default: (scoped) => {
              return this.data.find((e) => e.product_id === scoped.row.id)?.agent_rate || '-'
            }
          }
        },
        {
          label: '代理最小保费',
          hideNonCargo: true,
          scopedSlots: {
            default: (scoped) => {
              return this.data.find((e) => e.product_id === scoped.row.id)?.agent_minimum_premium || '-'
            }
          }
        },
        {
          label: '代理佣金比例(%)',
          scopedSlots: {
            default: (scoped) => {
              return this.data.find((e) => e.product_id === scoped.row.id)?.agent_commission_rate || '-'
            }
          }
        },
        {
          label: '用户默认费率(‱)',
          hideNonCargo: true,
          scopedSlots: {
            default: (scoped) => {
              return this.data.find((e) => e.product_id === scoped.row.id)?.user_rate || '-'
            }
          }
        },
        {
          label: '用户最小保费',
          hideNonCargo: true,
          scopedSlots: {
            default: (scoped) => {
              return this.data.find((e) => e.product_id === scoped.row.id)?.minimum_premium || '-'
            }
          }
        },
        {
          label: '是否可用',
          fixed: 'right',
          width: 80,
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              const row = this.data.find((e) => e.product_id === scoped.row.id) || {}

              return (
                <el-switch
                  value={row?.is_enabled}
                  active-value={1}
                  inactive-value={0}
                  disabled={row.product_id === undefined}
                  onChange={() => {
                    if (row?.is_enabled === 1) {
                      row.is_enabled = 0
                    } else {
                      row.is_enabled = 1
                    }

                    this.$emit('update-status', row.product_id, row.is_enabled)
                  }}
                />
              )
            }
          }
        },
        {
          label: '配置',
          fixed: 'right',
          width: 80,
          align: 'center',
          scopedSlots: {
            default: (scoped) => {
              return (
                <el-button
                  type="text"
                  size="small"
                  onClick={() => this.handleConfigProduct(scoped.row)}
                  v-can={{ name: 'platforms.products.sync' }}
                >
                  配置费率
                </el-button>
              )
            }
          }
        }
      ]

      if (!this.isCargoProduct) {
        return cols.filter((e) => e.hideNonCargo === undefined || e.hideNonCargo === false)
      }

      return cols
    },
    title() {
      return `配置产品 - ${this.rateForm.model.name}`
    }
  },
  data() {
    return {
      is_multiple: false,
      selectAll: false,
      excepts: [],
      rateForm: {
        visible: false,
        model: {},
        form: {
          is_cargo_product: this.isCargoProduct,
          product_id: '',
          platform_rate: 0.0,
          platform_minimum_premium: 0,
          agent_rate: 0.0,
          agent_minimum_premium: 0.0,
          user_rate: 0.0,
          platform_commission_rate: 0.0,
          agent_commission_rate: 0.0,
          minimum_premium: 0.0,
          is_enabled: 1
        },
        rules: {
          platform_rate: [{ required: true, message: '平台费率必填', trigger: 'blur' }],
          platform_minimum_premium: [{ required: true, message: '平台最低保费必填', trigger: 'blur' }],
          agent_rate: [{ required: true, message: '代理费率必填', trigger: 'blur' }],
          agent_minimum_premium: [{ required: true, message: '代理最低保费必填', trigger: 'blur' }],
          user_rate: [{ required: true, message: '用户费率必填', trigger: 'blur' }],
          platform_commission_rate: [{ required: true, message: '平台佣金比例必填', trigger: 'blur' }],
          agent_commission_rate: [{ required: true, message: '代理佣金比例必填', trigger: 'blur' }],
          minimum_premium: [{ required: true, message: '用户最低保费必填', trigger: 'blur' }],
          is_enabled: [{ required: true, message: '请选择是否启用', trigger: 'blur' }]
        }
      },
      queries: {},
      pagingEvents: {
        currentChange: (page) => {
          this.$emit('query-submit', page, this.queries)
        }
      },
      multipleSelection: [],
      tableEvents: {
        'selection-change': (val) => {
          this.multipleSelection = val
        },
        'select-all': (val) => {
          this.selectAll = !this.selectAll
          if (!this.selectAll) {
            this.$refs.productTable.$refs.dataTable.clearSelection()
          }
        },
        select: (val, row) => {
          if (val.indexOf(row) === -1) {
            this.excepts.push(row)
          } else {
            this.excepts.pop(row)
          }
        }
      }
    }
  },
  watch: {
    products() {
      console.log(this.selectAll)
      if (this.selectAll) {
        this.selectCurrentPageProducts()
      }
    }
  },
  methods: {
    getRowKeys(row) {
      return row.id
    },
    handleSearch(queries) {
      this.queries = queries

      this.$emit('query-submit', 1, queries)
    },
    handleBeforeClose() {
      this.rateForm.visible = false
      this.rateForm.model = {}
      this.rateForm.form = {}
    },
    handleSubmit() {
      this.$refs.rateForm.validate((valid) => {
        if (valid) {
          this.$emit('submit', this.rateForm.form)

          this.handleBeforeClose()
        }
      })
    },
    handleConfigProduct(row) {
      this.rateForm.visible = true
      this.rateForm.model = row

      const rowData =
        this.data.find((e) => e.product_id === row.id && e.platform_id === parseInt(this.$route.params.id, 10)) || {}
      const isOwn = parseInt(this.$route.params.id, 10) === parseInt(row.platform.id, 10)

      this.rateForm.form = {
        is_cargo_product: this.isCargoProduct,
        product_id: row.id,
        platform_rate: isOwn && !(rowData?.platform_rate || '') ? row.additional.rate : rowData?.platform_rate || '',
        platform_minimum_premium:
          isOwn && !(rowData?.platform_minimum_premium || '')
            ? row.additional.minimum_premium
            : rowData?.platform_minimum_premium || '',
        agent_rate: rowData?.agent_rate || '',
        agent_minimum_premium: rowData?.agent_minimum_premium || '',
        user_rate: rowData?.user_rate || '',
        platform_commission_rate:
          isOwn && !(rowData?.platform_commission_rate || '')
            ? row.additional.service_charge
            : rowData?.platform_commission_rate || '',
        agent_commission_rate: rowData?.agent_commission_rate || '',
        minimum_premium: rowData?.minimum_premium || '',
        is_enabled: rowData?.is_enabled || 0
      }
    },
    assignProductMultiple() {
      this.is_multiple = true
      if (!this.multipleSelection.length) {
        this.$message({
          type: 'warning',
          message: '请至少选择一条数据'
        })
        return false
      }
      this.rateForm.visible = true
    },
    selectCurrentPageProducts() {
      this.products.forEach((row) => {
        if (this.excepts.find((e) => e.product_id === row.product_id) === undefined) {
          this.$refs.productTable.$refs.dataTable.toggleRowSelection(row, true)
        }
      })
    },
    handleSubmitMultiple() {
      this.$refs.rateForm.validate((valid) => {
        this.rateForm.form.select_all = this.selectAll
        this.rateForm.form.is_cargo_product = this.isCargoProduct
        this.rateForm.form.filter = this.queries
        if (this.selectAll) {
          this.rateForm.form.except_ids = this.excepts.map((row) => {
            return row.id
          })
        } else {
          this.rateForm.form.ids = this.multipleSelection.map((row) => {
            return row.id
          })
        }
        if (valid) {
          this.$refs.productTable.$refs.dataTable.clearSelection()

          this.is_multiple = false

          this.selectAll = false

          this.multipleSelection = []

          this.excepts = []

          this.$emit('multiple-submit', { ...this.rateForm.form })

          this.handleBeforeClose()
        }
      })
    }
  }
}
</script>
