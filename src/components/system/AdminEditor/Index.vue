<template>
  <el-dialog
    :title="currentDialogTitle"
    :visible.sync="visible"
    width="520px"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form ref="form" label-suffix=":" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="角色" prop="role_id">
        <el-select v-model="form.role_id" placeholder="请选择角色" style="width: 100%">
          <el-option v-for="role in roles" :value="role.id" :label="role.display_name" :key="role.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="用户名称" prop="name">
        <el-input v-model.trim="form.name" placeholder="请输入用户名称"></el-input>
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input v-model.trim="form.email" placeholder="请输入邮箱"></el-input>
      </el-form-item>
      <el-form-item label="手机号" prop="phone_number">
        <el-input v-model.trim="form.phone_number" maxlength="11" placeholder="请输入手机号"></el-input>
      </el-form-item>
      <el-form-item label="用户名" prop="username">
        <el-input v-model.trim="form.username" maxlength="32" placeholder="请输入用户名"></el-input>
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input
          v-model.trim="form.password"
          type="password"
          maxlength="32"
          placeholder="请输入密码"
          show-password
          auto-complete="new-password"
        ></el-input>
      </el-form-item>
      <el-form-item label="确认密码" prop="password_confirmation">
        <el-input
          v-model.trim="form.password_confirmation"
          type="password"
          maxlength="32"
          placeholder="请确认密码"
          auto-complete="new-password"
        ></el-input>
      </el-form-item>
      <el-form-item label="SMTP 用户名">
        <el-input v-model.trim="form.smtp_username" placeholder="请输入 SMTP 用户名"></el-input>
      </el-form-item>
      <el-form-item label="SMTP 密码">
        <el-input
          show-password
          type="password"
          v-model.trim="form.smtp_password"
          placeholder="请输入 SMTP 密码"
        ></el-input>
      </el-form-item>
      <el-form-item label="是否启用">
        <el-switch
          active-text="是"
          inactive-text="否"
          :active-value="1"
          :inactive-value="0"
          v-model="form.is_enabled"
        ></el-switch>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  name: 'AdminEditor',
  props: {
    model: {
      type: Object,
      default: () => {}
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        role_id: '',
        name: '',
        email: '',
        smtp_username: '',
        smtp_password: '',
        phone_number: '',
        username: '',
        password: '',
        password_confirmation: '',
        is_enabled: 1
      },
      rules: {
        role_id: [{ required: true, message: '请选择角色', trigger: 'change' }],
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        email: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
        ],
        phone_number: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { min: 11, max: 11, message: '手机号格式不正确', trigger: 'blur' }
        ],
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 5, max: 32, message: '用户名长度在 5 到 32 位之间', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 5, max: 32, message: '密码长度在 5 到 32 位之间', trigger: 'blur' }
        ],
        password_confirmation: [
          { required: true, message: '请重复输入密码', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value !== this.form.password) {
                callback(new Error('两次输入的密码不一致'))
              }

              callback()
            },
            trigger: 'blur'
          }
        ]
      }
    }
  },
  computed: {
    ...mapGetters('app', ['roles']),
    currentDialogTitle() {
      return this.form && this.form.id ? '修改用户' : '添加用户'
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)

      this.$refs.form.resetFields()

      this.form = {
        role_id: '',
        name: '',
        email: '',
        smtp_username: '',
        smtp_password: '',
        phone_number: '',
        username: '',
        password: '',
        password_confirmation: '',
        is_enabled: 1
      }
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          console.log(this.form)
          this.$emit('submit', Object.assign({}, this.form))

          this.handleClose()
        }
      })
    }
  },
  watch: {
    model: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val && Object.keys(val).length > 0) {
          if (val?.id !== undefined) {
            delete this.rules.password
            delete this.rules.password_confirmation
          }

          this.form = Object.assign({}, val)
        } else {
          this.form = {
            role_id: '',
            name: '',
            email: '',
            smtp_username: '',
            smtp_password: '',
            phone_number: '',
            username: '',
            password: '',
            password_confirmation: '',
            is_enabled: 1
          }
        }
      }
    }
  }
}
</script>
