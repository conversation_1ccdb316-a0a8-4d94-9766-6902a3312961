<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-21 16:48:22
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-22 10:28:19
-->
<template>
  <el-dialog
    :title="currentDialogTitle"
    :visible.sync="visible"
    width="600px"
    :before-close="handleBeforeClose"
    destroy-on-close
    :key="key"
  >
    <el-tree
      :data="permissions"
      show-checkbox
      ref="rolePermissionsTree"
      node-key="name"
      default-expand-all
      :default-checked-keys="checkedPermissions"
      :props="defaultProps"
    >
    </el-tree>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleSetAllExpand('rolePermissionsTree', !isAllExpand)">
        全部{{ isAllExpand ? '收起' : '展开' }}
      </el-button>
      <el-button @click="handleCheckedAll('rolePermissionsTree')"> 全部{{ isAllChecked ? '取消' : '选择' }} </el-button>
      <el-button @click="handleBeforeClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'RoleSecurity',
  props: {
    roleId: {
      type: [String, Number],
      default: ''
    },
    roleName: {
      type: String,
      default: ''
    },
    checked: {
      type: Array,
      default: () => []
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      key: -1,
      isAllExpand: true,
      isAllChecked: false,
      defaultProps: {
        children: 'children',
        label: 'display_name'
      }
    }
  },
  computed: {
    ...mapGetters('app', ['permissions']),
    currentDialogTitle() {
      return `分配${this.roleName}角色权限`
    },
    checkedPermissions() {
      const checked = []
      this.checked.forEach((e) => {
        // 只要最底层的选中就 OK
        if (!this.checked.find((f) => f.parent_id === e.id)) {
          checked.push(e.name)
        }
      })

      return checked
    }
  },
  methods: {
    handleBeforeClose() {
      this.$emit('update:visible', false)
      this.$emit('update:checked', [])
      this.key = new Date().getTime()
    },
    handleSubmit() {
      const checked = this.$refs.rolePermissionsTree.getCheckedKeys()
      this.$refs.rolePermissionsTree.getHalfCheckedKeys().forEach((e) => {
        checked.push(e)
      })

      this.$emit('submit', this.roleId, checked)
      this.$emit('update:visible', false)
      this.handleBeforeClose()
    },
    //设置全部展开和折叠。state参数为bool值
    handleSetAllExpand(refName, state) {
      this.isAllExpand = !this.isAllExpand
      var nodes = this.$refs[refName].store.nodesMap
      for (const i in nodes) {
        nodes[i].expanded = state
      }
    },
    //设置全选和反选
    handleCheckedAll(refName) {
      this.isAllChecked = !this.isAllChecked
      this.isAllChecked ? this.$refs[refName].setCheckedNodes(this.permissions) : this.$refs[refName].setCheckedKeys([])
    }
  }
}
</script>

<style lang="scss" scoped></style>
