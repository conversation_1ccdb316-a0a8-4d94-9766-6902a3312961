<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-21 16:48:22
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-21 17:54:11
-->
<template>
  <el-dialog
    :title="currentDialogTitle"
    :visible.sync="visible"
    width="520px"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form ref="form" label-suffix=":" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="上级名称">
        <el-input readonly :value="currentParentName"></el-input>
      </el-form-item>
      <el-form-item label="权限标识" prop="name">
        <el-input v-model.trim="form.name" placeholder="请输入权限标识"></el-input>
      </el-form-item>
      <el-form-item label="显示名称" prop="display_name">
        <el-input v-model.trim="form.display_name" placeholder="请输入显示名称"></el-input>
      </el-form-item>
      <el-form-item label="菜单名称" prop="menu_name">
        <el-input v-model.trim="form.menu_name" placeholder="请输入菜单名称"></el-input>
      </el-form-item>
      <el-form-item label="路由名称" prop="route_name">
        <el-input v-model.trim="form.route_name" placeholder="请输入路由名称"></el-input>
      </el-form-item>
      <el-form-item label="排序" prop="index">
        <el-input v-model.trim="form.index" placeholder="请输入序号，越小越靠前"></el-input>
      </el-form-item>
      <el-form-item label="适用平台">
        <el-select v-model="form.platform_ids" class="w-100" placeholder="请选择平台" multiple>
          <el-option v-for="p in platforms" :key="p.id" :value="p.id" :label="p.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="平台通用" prop="is_universal">
        <el-switch
          active-text="是"
          inactive-text="否"
          :active-value="1"
          :inactive-value="0"
          v-model="form.is_universal"
        ></el-switch>
      </el-form-item>
      <el-form-item label="菜单/按钮" prop="is_menu">
        <el-radio-group v-model="form.is_menu">
          <el-radio :label="1">菜单</el-radio>
          <el-radio :label="0">功能</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="图标">
        <el-input v-model.trim="form.icon"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import * as platformApi from '@/apis/platform'

export default {
  name: 'PermissionsEditor',
  props: {
    model: {
      type: Object,
      default: () => {}
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      platforms: [],
      form: {
        platform_ids: [],
        name: '',
        menu_name: '',
        display_name: '',
        route_name: '',
        icon: '',
        is_universal: 0,
        is_menu: 1,
        index: -1
      },
      rules: {
        name: [
          {
            required: true,
            message: '权限标识必填',
            trigger: 'blur'
          }
        ],
        display_name: [
          {
            required: true,
            message: '显示名称必填',
            trigger: 'blur'
          }
        ],
        is_universal: [
          {
            required: true,
            message: '请选择是否是通用菜单',
            trigger: ['blur', 'change']
          }
        ],
        is_menu: [
          {
            required: true,
            message: '请选择是否是菜单',
            trigger: ['blur', 'change']
          }
        ]
        // route_name: [
        //   {
        //     required: true,
        //     message: '前端路由名称必填',
        //     trigger: 'blur'
        //   }
        // ]
      }
    }
  },
  computed: {
    currentDialogTitle() {
      return this.form && this.form.id ? '修改权限' : '添加权限'
    },
    currentParentName() {
      return this.form && this.form.parent_name ? this.form.parent_name : '顶级菜单'
    }
  },
  created() {
    this.fetchPlatforms()
  },
  methods: {
    async fetchPlatforms() {
      const { data } = await platformApi.getPlatforms()
      this.platforms = data
    },
    handleClose() {
      this.$emit('update:visible', false)
      this.$refs.form.resetFields()

      this.form = {
        platform_ids: [],
        name: '',
        menu_name: '',
        display_name: '',
        route_name: '',
        icon: '',
        is_universal: 0,
        is_menu: 1,
        index: -1
      }
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          let data = Object.assign({}, this.form)
          if (data.parent_id === -1) {
            delete data.parent_id
          }
          this.$emit('submit', data)
          this.handleClose()
        }
      })
    }
  },
  watch: {
    model: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val !== null && Object.keys(val).length > 0) {
          this.form = Object.assign({}, val)
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
