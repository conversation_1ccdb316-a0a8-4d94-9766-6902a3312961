<template>
  <div class="chart-box" :class="{ 'chart-box-one': oneColumn }">
    <bar-chart-card
      v-if="sheets.includes('summary')"
      title="整体保费及赔付情况"
      :without-table="true"
      :chart-height="oneColumn ? 100 : 200"
      :chart-data="{
        labels: ['累计保费(元)', '已决金额(元)', '未决金额(元)', '简单赔付率(%)'],
        datasets: [
          { label: '整体保费及赔付情况', data: data?.summary?.for_chart?.slice(0, 3) || [], yAxisID: 'left' },
          {
            label: '简单赔付率(%)',
            data: [
              { x: 0, y: 0, r: 0 },
              { x: 0, y: 0, r: 0 },
              { x: 0, y: 0, r: 0 },
              { x: 0, y: data?.summary?.simple_claim_rate || 0, r: 6 }
            ],
            yAxisID: 'right',
            type: 'bubble'
          }
        ]
      }"
      :chart-options="{
        barPercentage: 0.5,
        scales: {
          left: { type: 'linear', display: true, position: 'left', grid: { drawOnChartArea: false } },
          right: { type: 'linear', display: true, position: 'right' }
        }
      }"
    >
      <template v-if="showDescription" #description>
        <div class="text-left">
          累计保费金额为{{ data?.summary?.premium }}元， 已决金额{{ data?.summary?.finished_claim_amount }}元，
          未决金额{{ data?.summary?.pending_claim_amount }}元，简单赔付率为{{ data?.summary?.simple_claim_rate }}%。
        </div>
      </template>
      <template #default>
        <el-descriptions direction="vertical" :column="4" border>
          <el-descriptions-item label="累计保费（元）">
            {{ data?.summary?.premium?.toFixed(2) }}
          </el-descriptions-item>
          <el-descriptions-item label="已决金额（元）">
            {{ data?.summary?.finished_claim_amount?.toFixed(2) }}
          </el-descriptions-item>
          <el-descriptions-item label="未决金额（元）">
            {{ data?.summary?.pending_claim_amount?.toFixed(2) }}
          </el-descriptions-item>
          <el-descriptions-item label="简单赔付率（%）">
            {{ data?.summary?.simple_claim_rate?.toFixed(2) }}%
          </el-descriptions-item>
        </el-descriptions>
      </template>
    </bar-chart-card>

    <detail-card
      title="承保数据"
      v-if="sheets.includes('policy_summary')"
      :data="[
        { label: '有效客户数', value: data?.policy_summary?.customer_count },
        { label: '客户年化增长率', value: data?.policy_summary?.customer_increase_rate + '%' },
        { label: '月均保费0.5万元以上客户数', value: data?.policy_summary?.monthly_premium_over_five_thousand },
        { label: '年均保费万元以上客户数', value: data?.policy_summary?.yearly_premium_avg_over_ten_thousand },
        {
          label: '年均保费10万元以上客户数',
          value: data?.policy_summary?.yearly_premium_avg_over_one_hundred_thousand
        },
        { label: '投保人（数）', value: data?.policy_summary?.policyholder_count },
        { label: '被保险人（数）', value: data?.policy_summary?.insured_count },
        { label: '保险公司（数）', value: data?.policy_summary?.company_count },
        { label: '出单公司（数）', value: data?.policy_summary?.company_branch_count },
        { label: '累计保额（万元）', value: ((data?.policy_summary?.total_coverage || 0) / 10000).toFixed(2) },
        { label: '累计保费（元）', value: data?.policy_summary?.total_premium?.toFixed(2) },
        { label: '保费年化增长率', value: data?.policy_summary?.yearly_premium_increase_rate?.toFixed(2) + '%' },
        { label: '费率均值', value: data?.policy_summary?.avg_rate?.toFixed(2) + '‱' },
        { label: '费率均值年化增长率', value: data?.policy_summary?.yearly_rate_increase_rate?.toFixed(2) + '%' },
        { label: '投保单数', value: data?.policy_summary?.count },
        { label: '投保单年化增长率', value: data?.policy_summary?.yearly_policy_increase_rate?.toFixed(2) + '%' },
        {
          label: '自助出单单数',
          value: data?.policy_summary?.automated_policy_count + `(${data?.policy_summary?.automated_policy_percent}%)`
        },
        {
          label: '人工出单单数',
          value:
            data?.policy_summary?.count -
            data?.policy_summary?.automated_policy_count +
            `(${(100 - data?.policy_summary?.automated_policy_percent).toFixed(2)}%)`
        }
      ]"
    />

    <detail-card
      v-if="sheets.includes('claim_summary')"
      title="理赔数据"
      :data="[
        { label: '出险报案（件）', value: data?.claim_summary?.total_cases },
        { label: '未决案件（件）', value: data?.claim_summary?.pending_cases },
        { label: '已决案件（件）', value: data?.claim_summary?.finished_cases },
        { label: '出险率', value: data?.claim_summary?.rate + '‰' },
        { label: '出险率相对值', value: data?.claim_summary?.rate_relative_value?.toFixed(2) },
        {
          label: '结案率',
          value: ((data?.claim_summary?.finished_cases / data?.claim_summary?.total_cases || 0) * 100).toFixed(2) + '%'
        },
        { label: '5万元以上理赔案件（件）', value: data?.claim_summary?.amount_over_five_hundred_thousand },
        { label: '万元以下理赔案件（件）', value: data?.claim_summary?.amount_less_ten_thousand },
        { label: '已决金额（元）', value: data?.claim_summary?.finished_amount?.toFixed(2) },
        { label: '结案赔款（元）', value: data?.claim_summary?.finished_payment_amount?.toFixed(2) },
        { label: '结案费用（元）', value: data?.claim_summary?.finished_fee?.toFixed(2) },
        { label: '未决金额（元）', value: data?.claim_summary?.pending_amount?.toFixed(2) },
        { label: '未决赔款（元）', value: data?.claim_summary?.pending_settlement_amount?.toFixed(2) },
        { label: '未决费用（元）', value: data?.claim_summary?.pending_lodging_fee?.toFixed(2) },
        { label: '简单赔付率', value: data?.claim_summary?.simple_rate + '%' },
        {
          label: '赔付率相对值',
          value: data?.claim_summary?.simple_rate && data?.claim_summary?.claim_rate_relative_value?.toFixed(2)
        },
        { label: '迟报案（件）', value: data?.claim_summary?.delayed_cases },
        { label: '理赔时长(天）', value: data?.claim_summary?.cost_days?.toFixed(2) }
      ]"
    />

    <bar-chart-card
      v-if="sheets.includes('policy_count_and_premium')"
      title="保费、投保单数按时间分布情况"
      :table-columns="yearTableCols"
      :table-data="premiumCountData"
      :chart-height="oneColumn ? 100 : 200"
      :chart-data="{
        labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        datasets: premiumCountChartDatasets
      }"
      :chart-options="{
        scales: {
          left: { type: 'linear', display: true, position: 'left', grid: { drawOnChartArea: false } },
          right: { type: 'linear', display: true, position: 'right' }
        }
      }"
    >
      <template v-if="showDescription" #description>
        <div class="text-left">
          投保单数合计{{ data?.policy_count_and_premium?.extra?.count }}单， 保费合计{{
            data?.policy_count_and_premium?.extra?.sum
          }}元， 单均保费为{{ data?.policy_count_and_premium?.extra?.avg }}元。保费、投保单按月分布情况，如下
        </div>
      </template>
    </bar-chart-card>

    <line-chart-card
      v-if="sheets.includes('monthly_claim')"
      title="出险报案量按月分布情况"
      :table-columns="yearTableCols"
      :table-data="data?.monthly_claim?.data?.map((e) => ({ year: e.year, ...e.dataset.map((e) => e.count) })) || []"
      :chart-height="oneColumn ? 100 : 200"
      :chart-data="{
        labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        datasets: data?.monthly_claim?.for_chart?.map((item) => ({
          label: `${item.year}年报案量（笔）`,
          data: item.dataset
        }))
      }"
    >
      <template v-if="showDescription" #description>
        <div class="text-left">
          出险报案总数{{ data?.monthly_claim?.extra?.count }}件，其中
          <span v-for="(d, k) in data?.monthly_claim?.for_chart" :key="d.year">
            {{ d.year }}年报案{{ d.dataset.reduce((a, b) => a + b, 0) }}件
            {{ k != data?.monthly_claim?.for_chart.length - 1 ? '、' : '' }}
          </span>
          。出险报案量按月分布情况，如下
        </div>
      </template>
    </line-chart-card>

    <line-chart-card
      v-if="sheets.includes('monthly_finished_claim')"
      title="结案量按月分布情况"
      :table-columns="yearTableCols"
      :table-data="
        data?.monthly_finished_claim?.data?.map((e) => ({ year: e.year, ...e.dataset.map((e) => e.count) })) || []
      "
      :chart-height="oneColumn ? 100 : 200"
      :chart-data="{
        labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        datasets: data?.monthly_finished_claim?.for_chart?.map((item) => ({
          label: `${item.year}结案量（笔）`,
          data: item.dataset
        }))
      }"
    >
      <template v-if="showDescription" #description>
        <div class="text-left">
          结案总数{{ data?.monthly_finished_claim?.extra?.count }}件，其中
          <span v-for="(d, k) in data?.monthly_finished_claim?.for_chart" :key="d.year">
            {{ d.year }}年结案{{ d.dataset.reduce((a, b) => a + b, 0) }}件
            {{ k != data?.monthly_finished_claim?.for_chart.length - 1 ? '、' : '' }}
          </span>
          。结案量按月分布情况，如下
        </div>
      </template>
    </line-chart-card>

    <line-chart-card
      v-if="sheets.includes('monthly_claim_payment_amount')"
      title="赔付金额按月分布情况（元）"
      :table-columns="yearTableCols"
      :table-data="
        data?.monthly_claim_payment_amount?.data?.map((e) => ({
          year: e.year,
          ...e.dataset.map((e) => e.amount)
        })) || []
      "
      :chart-height="oneColumn ? 100 : 200"
      :chart-data="{
        labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        datasets: data?.monthly_claim_payment_amount?.for_chart?.map((item) => ({
          label: `${item.year}赔付金额`,
          data: item.dataset
        }))
      }"
    >
      <template v-if="showDescription" #description>
        <div class="text-left">
          赔付金额总数{{ data?.monthly_claim_payment_amount?.extra?.amount }}元，其中
          <span v-for="(d, k) in data?.monthly_claim_payment_amount?.for_chart" :key="d.year">
            {{ d.year }}年赔付金额{{ Number(d.dataset.reduce((a, b) => a + b, 0)).toFixed(2) }}元
            {{ k != data?.monthly_claim_payment_amount?.for_chart.length - 1 ? '、' : '' }}
          </span>
          。赔付金额按月分布情况，如下
        </div>
      </template>
    </line-chart-card>

    <bar-chart-card
      v-if="sheets.includes('claim_status')"
      title="保司案件进展分布情况（件）"
      :table-columns="[
        { label: '保司', prop: 'name' },
        { label: '已报案', prop: '-1' },
        { label: '已受理', prop: '1' },
        { label: '撤案', prop: '0' },
        { label: '材料收集', prop: '2' },
        { label: '保司审核', prop: '3' },
        { label: '零赔付', prop: '4' },
        { label: '拒赔', prop: '5' },
        { label: '领赔款', prop: '6' },
        { label: '结案', prop: '7' },
        { label: '理赔案件总量', prop: '-99' }
      ]"
      :table-data="data?.claim_status?.data || [] | flattenStatusDataset('count')"
      :chart-height="oneColumn ? 100 : 200"
      :chart-data="{
        labels: [
          '已报案',
          '已受理',
          '撤案',
          '材料收集',
          '保司审核',
          '零赔付',
          '拒赔',
          '领赔款',
          '结案',
          '理赔案件总量'
        ],
        datasets:
          data?.claim_status?.for_chart?.map((e) => ({
            label: e.name,
            data: (() => {
              const tmp = e.dataset[2]
              e.dataset[2] = e.dataset[1]
              e.dataset[1] = tmp
              return e.dataset
            })()
          })) || []
      }"
      :chart-options="{
        scales: {
          x: { stacked: true },
          y: { stacked: true }
        }
      }"
    >
      <template v-if="showDescription" #description>
        <div class="text-left">
          在统计口径下涉及赔案合计{{ data?.claim_status?.extra?.count }}件， 涉及到出单公司
          {{ data?.claim_status?.data?.length }}家，其中
          <span v-for="(d, k) in data?.claim_status?.extra?.status" :key="d.value">
            {{ d.name }}{{ d.count }}件
            {{ k != data?.claim_status?.extra?.status.length - 1 ? '、' : '' }}
          </span>
          。上述案件进度分布情况，如下
        </div>
      </template>
    </bar-chart-card>

    <bar-chart-card
      v-if="sheets.includes('claim_time_duration')"
      title="理赔时长分布情况(天)"
      :table-columns="[
        { label: '保司/类别', prop: 'name' },
        { label: '受理时长（新案件区停留时长）', prop: '1' },
        { label: '材料收集时长（材料收集环节停留时长）', prop: '2' },
        { label: '保司审核时长（保司审核环节停留时长）', prop: '3' },
        { label: '领赔款时长（领赔款环节停留时长）', prop: '5' },
        { label: '理赔时长', prop: '-99' }
      ]"
      :table-data="data?.claim_time_duration?.data || [] | flattenStatusDataset('days')"
      :chart-height="oneColumn ? 100 : 200"
      :chart-data="{
        labels: ['受理时长', '材料收集时长', '保司审核时长', '领赔款时长', '理赔时长'],
        datasets: data?.claim_time_duration?.for_chart?.map((e) => ({ label: e.name, data: e.dataset })) || []
      }"
      :chart-options="{
        barPercentage: 0.5,
        scales: {
          x: { stacked: true },
          y: { stacked: true }
        }
      }"
    >
      <template v-if="showDescription" #description>
        <div class="text-left">
          在统计口径下涉及已决案件（*指撤/零/拒案件和有赔款结案案件）合计{{
            data?.claim_time_duration?.extra?.count
          }}件， 涉及到出单公司{{ data?.claim_time_duration?.data?.length }}家。其中，
          <span v-for="(d, k) in data?.claim_time_duration?.extra?.finished_claims" :key="k">
            {{ d.company }}已决案件{{ d.count }}件
            {{ k != data?.claim_time_duration?.extra?.finished_claims?.length - 1 ? '、' : '' }}
          </span>
          ，案均结案时长为{{ data?.claim_time_duration?.extra?.avg_days }}天。具体理赔时长分布情况，如下
        </div>
      </template>
    </bar-chart-card>
  </div>
</template>

<script>
import DetailCard from '@/components/data/DetailCard/DetailCard'
import BarChartCard from '@/components/data/BarChartCard/BarChartCard'
import LineChartCard from '@/components/data/LineChartCard/LineChartCard'

export default {
  name: 'DataChartView',
  components: {
    DetailCard,
    BarChartCard,
    LineChartCard
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    showDescription: {
      type: Boolean,
      default: false
    },
    oneColumn: {
      type: Boolean,
      default: false
    },
    sheets: {
      type: Array,
      required: false,
      default: () => [
        'summary',
        'policy_summary',
        'claim_summary',
        'policy_count_and_premium',
        'monthly_claim',
        'monthly_finished_claim',
        'monthly_claim_payment_amount',
        'claim_status',
        'claim_time_duration'
      ]
    }
  },
  data() {
    return {
      chartYear: {}
    }
  },
  computed: {
    yearTableCols() {
      const cols = [{ label: '年度', prop: 'year', width: 50 }]
      for (let month = 1; month <= 12; month++) {
        cols.push({
          label: `${month}月`,
          scopedSlots: {
            default: (scoped) => {
              const data = scoped.row[month - 1]
              return Array.isArray(data) ? (
                <div>
                  <p>{data[0]}</p>
                  <p>{data[1]}</p>
                </div>
              ) : (
                data
              )
            }
          }
        })
      }
      return cols
    },
    premiumCountData() {
      const dataset = []
      for (const item of this.data?.policy_count_and_premium?.data ?? []) {
        dataset.push({
          year: item.year,
          // ...item.dataset.map((data) => `${data.count}(${data.premium}元)`)
          ...item.dataset.map((data) => [`${data.count}单`, `${data.premium}元`])
        })
      }
      return dataset
    },
    premiumCountChartDatasets() {
      const dataset = []
      for (const item of this.data?.policy_count_and_premium?.premium_for_chart ?? []) {
        dataset.push({
          label: `${item.year}年金额（元）`,
          data: item.dataset,
          yAxisID: 'left',
          order: 1
        })
      }

      for (const item of this.data?.policy_count_and_premium?.count_for_chart ?? []) {
        dataset.push({
          type: 'line',
          label: `${item.year}年投保单数（笔）`,
          data: item.dataset,
          yAxisID: 'right',
          order: 0
        })
      }

      return dataset
    }
  },
  filters: {
    flattenStatusDataset(data, key) {
      return (
        data.map((data) => ({
          name: data.name,
          ...data.dataset.reduce((acc, item) => {
            acc[item.status] = item[key]
            return acc
          }, {})
        })) || []
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.chart-box {
  margin-top: 1rem;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;

  /deep/ .el-descriptions-item__cell {
    width: 25%;
  }
}

.chart-box-one {
  grid-template-columns: repeat(1, 1fr);
}

.text-left {
  text-align: left;
}
</style>
