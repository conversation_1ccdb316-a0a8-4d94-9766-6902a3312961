<template>
  <el-card shadow="never">
    <div slot="header">
      <span>{{ title }}</span>
    </div>
    <el-descriptions :column="2" border>
      <el-descriptions-item v-for="(d, idx) in data" :key="idx" :label="d.label">{{ d.value }}</el-descriptions-item>
    </el-descriptions>
  </el-card>
</template>

<script>
export default {
  name: 'DataDetailCard',
  props: {
    title: {
      type: String,
      required: true
    },
    data: {
      type: Array,
      required: true
    }
  }
}
</script>
