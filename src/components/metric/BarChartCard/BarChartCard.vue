<template>
  <el-card shadow="never" :class="{ fullscreen }">
    <div slot="header" class="header">
      <span>{{ title }}</span>
      <i class="el-icon-full-screen" @click="fullscreen = !fullscreen"></i>
    </div>

    <div :class="{ 'm-extra-large-b': $slots.description }">
      <slot name="description"> </slot>
    </div>

    <slot />
    <define-table v-if="!withoutTable" :cols="tableColumns" :data="tableData" />

    <bar-chart
      class="m-extra-large-t"
      :height="chartHeight"
      :data="data"
      :options="{
        ...defaultOptions,
        ...chartOptions
      }"
    />
  </el-card>
</template>

<script>
import { Bar } from 'vue-chartjs'
import {
  Chart as ChartJS,
  Title,
  LineElement,
  BubbleController,
  PointElement,
  Tooltip,
  Legend,
  BarElement,
  CategoryScale,
  Filler,
  LinearScale
} from 'chart.js'

ChartJS.register(
  BubbleController,
  CategoryScale,
  PointElement,
  LineElement,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Filler,
  Legend
)

export default {
  name: 'DataBarChartCard',
  components: {
    BarChart: Bar
  },
  props: {
    title: {
      type: String,
      required: true
    },
    withoutTable: {
      type: Boolean,
      required: false,
      default: false
    },
    tableColumns: {
      type: Array,
      required: false,
      default: () => []
    },
    tableData: {
      type: Array,
      required: false,
      default: () => []
    },
    chartHeight: {
      type: Number,
      required: false,
      default: 200
    },
    chartOptions: {
      type: Object,
      default: () => ({})
    },
    chartData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      fullscreen: false,
      defaultOptions: {
        responsive: true,
        maintainAspectRatio: true
      }
    }
  },
  computed: {
    data() {
      return {
        labels: this.chartData?.labels ?? [],
        datasets:
          this.chartData?.datasets?.map((d) => ({
            ...this.randomColor(),
            ...d
          })) ?? []
      }
    }
  },
  methods: {
    randomColor() {
      const color = `${Math.floor(Math.random() * 255)}, ${Math.floor(Math.random() * 255)}, ${Math.floor(
        Math.random() * 255
      )}`

      return {
        borderWidth: 2,
        borderRadius: 5,
        borderSkipped: false,
        borderColor: `rgb(${color})`,
        backgroundColor: `rgba(${color}, 0.7)`,
        pointStyle: 'circle',
        pointRadius: 5,
        pointHoverRadius: 8
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.fullscreen {
  grid-column: 1 / -1;
}
</style>
