<template>
  <el-card shadow="never" :class="{ fullscreen }">
    <div slot="header" class="header">
      <span>{{ title }}</span>
      <i class="el-icon-full-screen" @click="fullscreen = !fullscreen"></i>
    </div>

    <div :class="{ 'm-extra-large-b': $slots.description }">
      <slot name="description"> </slot>
    </div>
    <slot />

    <define-table :cols="tableColumns" :data="tableData" />

    <line-chart
      class="m-extra-large-t"
      :height="chartHeight"
      :data="data"
      :options="{
        ...defaultOptions,
        ...chartOptions
      }"
    />
  </el-card>
</template>

<script>
import { Line } from 'vue-chartjs'
import {
  Chart as ChartJS,
  Title,
  LineElement,
  PointElement,
  Tooltip,
  Legend,
  CategoryScale,
  Filler,
  LinearScale
} from 'chart.js'

ChartJS.register(CategoryScale, PointElement, LineElement, LinearScale, Title, Tooltip, Filler, <PERSON>)

export default {
  name: 'DataBarChartCard',
  components: {
    LineChart: Line
  },
  props: {
    title: {
      type: String,
      required: true
    },
    tableColumns: {
      type: Array,
      required: true
    },
    tableData: {
      type: Array,
      required: true
    },
    chartHeight: {
      type: Number,
      required: false,
      default: 200
    },
    chartOptions: {
      type: Object,
      default: () => ({})
    },
    chartData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      fullscreen: false,
      defaultOptions: {
        responsive: true,
        maintainAspectRatio: true
      }
    }
  },
  computed: {
    data() {
      return {
        labels: this.chartData?.labels ?? [],
        datasets:
          this.chartData?.datasets?.map((d) => ({
            ...this.randomColor(),
            ...d
          })) ?? []
      }
    }
  },
  methods: {
    randomColor() {
      const color = `${Math.floor(Math.random() * 255)}, ${Math.floor(Math.random() * 255)}, ${Math.floor(
        Math.random() * 255
      )}`

      return {
        borderWidth: 2,
        borderRadius: 5,
        borderSkipped: false,
        borderColor: `rgb(${color})`,
        backgroundColor: `rgba(${color}, 0.7)`,
        pointStyle: 'circle',
        pointRadius: 5,
        pointHoverRadius: 8
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.fullscreen {
  grid-column: 1 / -1;
}
</style>
