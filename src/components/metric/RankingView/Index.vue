<template>
  <div class="data-r-box" :class="{ 'data-r-box-one': oneColumn }">
    <el-card shadow="never" v-if="dataset.by_user">
      <div slot="header">
        <span>按客户数排名</span>
      </div>
      <define-table :cols="tableCols.byUser" :data="dataset.by_user" :attrs="defaultAttrs" />
    </el-card>
    <el-card shadow="never" v-if="dataset.by_premium">
      <div slot="header">
        <span>按保费排名</span>
      </div>
      <define-table :cols="tableCols.byPremium" :data="dataset.by_premium" :attrs="defaultAttrs" />
    </el-card>
    <el-card shadow="never" v-if="dataset.by_policy_count">
      <div slot="header">
        <span>按投保单数排名</span>
      </div>
      <define-table :cols="tableCols.byPolicyCount" :data="dataset.by_policy_count" :attrs="defaultAttrs" />
    </el-card>
    <el-card shadow="never" v-if="dataset.by_rate_avg">
      <div slot="header">
        <span>按费率均值排名</span>
      </div>
      <define-table :cols="tableCols.byRateAvg" :data="dataset.by_rate_avg" :attrs="defaultAttrs" />
    </el-card>
    <el-card shadow="never" v-if="dataset.by_claim_payment_rate">
      <div slot="header">
        <span>按赔付率排名</span>
      </div>
      <define-table :cols="tableCols.byClaimPaymentRate" :data="dataset.by_claim_payment_rate" :attrs="defaultAttrs" />
    </el-card>
    <el-card shadow="never" v-if="dataset.by_claim_rate">
      <div slot="header">
        <span>按出险率排名</span>
      </div>
      <define-table :cols="tableCols.byClaimRate" :data="dataset.by_claim_rate" :attrs="defaultAttrs" />
    </el-card>
    <el-card shadow="never" v-if="dataset.by_claim_payment_amount">
      <div slot="header">
        <span>按赔款排名</span>
      </div>
      <define-table
        :cols="tableCols.byClaimPaymentAmount"
        :data="dataset.by_claim_payment_amount"
        :attrs="defaultAttrs"
      />
    </el-card>
    <el-card shadow="never" v-if="dataset.by_five_ten_thousand_claim_amount_rate">
      <div slot="header">
        <span>5万元以上理赔案件排名</span>
      </div>
      <define-table
        :cols="tableCols.byFiveTenThousandClaimAmountRate"
        :data="dataset.by_five_ten_thousand_claim_amount_rate"
        :attrs="defaultAttrs"
      />
    </el-card>
    <el-card shadow="never" v-if="dataset.by_claim_finished_rate">
      <div slot="header">
        <span>按结案率排名</span>
      </div>
      <define-table
        :cols="tableCols.byClaimFinishedRate"
        :data="dataset.by_claim_finished_rate"
        :attrs="defaultAttrs"
      />
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'RankingView',
  props: {
    dataset: {
      type: Object,
      required: true
    },
    label: {
      type: String,
      required: true
    },
    userLabel: {
      type: String,
      required: true
    },
    oneColumn: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    defaultAttrs() {
      return {
        maxHeight: this.oneColumn ? 9999 : 350
      }
    },
    tableCols() {
      const cols = {
        byUser: [
          { label: this.userLabel, prop: 'name' },
          { label: '客户数', prop: 'users_count' },
          { label: '有效客户数', prop: 'valid_users_count' },
          { label: '年均保费万元以上客户数', prop: 'ten_thousand_premium_count' }
        ],
        byPremium: [
          { label: this.label, prop: 'name' },
          { label: '累计保费(元)', prop: 'premium' },
          { label: '费率均值(‱)', prop: 'rate_avg' },
          { label: '赔付率(%)', prop: 'claim_payment_rate' }
        ],
        byPolicyCount: [
          { label: this.label, prop: 'name' },
          { label: '投保单数', prop: 'policies_count' },
          { label: '出险案件(件)', prop: 'claims_count' },
          { label: '出险率(‰)', prop: 'claim_rate' }
        ],
        byRateAvg: [
          { label: this.label, prop: 'name' },
          { label: '费率均值(‱)', prop: 'rate_avg' },
          { label: '费率均值年化增长率(%)', prop: 'rate_yearly_increase_rate' },
          { label: '赔付率(%)', prop: 'claim_payment_rate' }
        ],
        byClaimPaymentRate: [
          { label: this.label, prop: 'name' },
          { label: '赔付率(%)', prop: 'claim_payment_rate' },
          { label: '出险率(‰)', prop: 'claim_rate' },
          { label: '赔款金额(元)', prop: 'claim_payment_amount' }
        ],
        byClaimRate: [
          { label: this.label, prop: 'name' },
          { label: '出险率(‰)', prop: 'claim_rate' },
          { label: '赔付率(%)', prop: 'claim_payment_rate' },
          { label: '累计保费(元)', prop: 'premium' }
        ],
        byClaimPaymentAmount: [
          { label: this.label, prop: 'name' },
          { label: '已决赔款(元)', prop: 'claim_finished_settlement_payment_amount' },
          { label: '未决赔款(元)', prop: 'claim_pending_settlement_amount' },
          { label: '赔款金额(元)', prop: 'claim_payment_amount' }
        ],
        byFiveTenThousandClaimAmountRate: [
          { label: this.label, prop: 'name' },
          { label: '5万元以上理赔案件数', prop: 'claim_five_ten_thousand_amount_count' },
          { label: '出险案件数', prop: 'claims_count' },
          { label: '占比(%)', prop: 'claim_five_ten_thousand_amount_rate' }
        ],
        byClaimFinishedRate: [
          { label: this.label, prop: 'name' },
          { label: '结案率(%)', prop: 'claim_finished_rate' },
          { label: '出险案件数', prop: 'claims_count' },
          { label: '理赔时长(天)', prop: 'claim_days' }
        ]
      }
      return cols
    }
  }
}
</script>

<style lang="scss" scoped>
.data-r-box {
  margin-top: 20px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.data-r-box-one {
  grid-template-columns: repeat(1, 1fr);
}
</style>
