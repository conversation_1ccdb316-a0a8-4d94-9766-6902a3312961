<template>
  <div>
    <el-row>
      <el-col :span="24">
        <el-select v-model="datasource" placeholder="请选择">
          <el-option :value="1" label="按自有业务" />
          <el-option :value="2" label="按自有产品" />
        </el-select>
      </el-col>
    </el-row>

    <search-panel
      ref="searchPanel"
      class="m-extra-large-t"
      @command="(_, data) => $emit('search', data, searchFields)"
      @change="(data) => (searchData = data)"
      :exportable="false"
      :custom="searchFields"
    ></search-panel>
  </div>
</template>

<script>
import * as platformProductApi from '@/apis/platform_product'
import * as adminApi from '@/apis/admin'
import * as dataApi from '@/apis/data'
import * as subjectApi from '@/apis/subject'
import * as loadingMethodApi from '@/apis/loading_method'
import * as transportMethodApi from '@/apis/transport_method'
import * as packingMethodApi from '@/apis/packing_method'
import * as lossReasons from '@/utils/loss_reason'
import chinaAreadata from '@/utils/areadata.json'
import overseaAreadata from '@/utils/regions'

export default {
  name: 'DataChartSearchPanel',
  data() {
    return {
      datasource: 1,
      lossReasons,
      chinaAreadata,
      overseaAreadata,
      searchFields: [
        {
          type: 'select',
          valKey: 'type',
          hintText: '险种',
          options: [
            { label: '国内货运险', value: 1 },
            { label: '国际货运险', value: 2 },
            { label: '单车责任险', value: 3 },
            { label: '其他险', value: 4 },
            { label: '雇主责任险', value: 5 },
            { label: '线下录入保单', value: 6 },
            { label: '跨境电商险', value: 7 }
          ]
        },
        {
          type: 'daterange',
          valKey: 'submitted_at_range',
          hintText: '投保'
        },
        // {
        //   type: 'daterange',
        //   valKey: 'issued_at_range',
        //   hintText: '出单'
        // },
        {
          type: 'input',
          valKey: 'policyholder',
          hintText: '投保人'
        },
        {
          type: 'input',
          valKey: 'insured',
          hintText: '被保人'
        },
        {
          type: 'input',
          valKey: 'name',
          hintText: '投保用户'
        },
        {
          type: 'input',
          valKey: 'agent_name',
          hintText: '代理用户'
        },
        {
          type: 'select',
          valKey: 'company_id',
          hintText: '保险公司',
          onChangeReset: ['company_branch_id', 'packing_method_id', 'loading_method_id', 'transport_method_id'],
          options: []
        },
        {
          type: 'select',
          valKey: 'company_branch_id',
          hintText: '出单公司',
          isMultiple: true,
          options: []
        },
        {
          type: 'select',
          valKey: 'product_from',
          hintText: '产品来源',
          options: [
            { label: '自有产品', value: 1 },
            { label: '非自有产品', value: 2 }
          ]
        },
        {
          type: 'select',
          valKey: 'business_from',
          hintText: '业务来源',
          options: []
        },
        {
          type: 'select',
          valKey: 'salesman_id',
          hintText: '业务员',
          options: []
        },
        {
          type: 'select',
          valKey: 'departure',
          hintText: '起运地(国/地区) 仅线上国内、国际适用',
          options: []
        },
        {
          type: 'select',
          valKey: 'destination',
          hintText: '目的地(国/地区) 仅线上国内、国际适用',
          options: []
        },
        {
          type: 'select',
          valKey: 'subject_id',
          hintText: '标的类型(仅线上国内、国际适用)',
          options: []
        },
        {
          type: 'select',
          valKey: 'insure_type',
          hintText: '贸易类型(仅线上国内、国际适用)',
          options: [
            { label: '国内运输', value: -1 },
            { label: '出口运输', value: 1 },
            { label: '进口运输', value: 2 },
            { label: '境外运输', value: 3 }
          ]
        },
        {
          type: 'daterange',
          valKey: 'shipping_date_range',
          hintText: '起运'
        },
        {
          type: 'select',
          valKey: 'packing_method_id',
          hintText: '包装方式(请选择出单公司) 仅线上国内、国际适用',
          options: []
        },
        {
          type: 'select',
          valKey: 'transport_method_id',
          hintText: '运输方式(请选择出单公司) 仅线上国内、国际适用',
          options: []
        },
        {
          type: 'select',
          valKey: 'loading_method_id',
          hintText: '装载方式(请选择出单公司) 仅线上国内、国际适用',
          options: []
        },
        {
          type: 'daterange',
          valKey: 'date_of_reporting_range',
          hintText: '报案'
        },
        {
          type: 'daterange',
          valKey: 'date_of_loss_range',
          hintText: '出险'
        },
        {
          type: 'daterange',
          valKey: 'settlement_date_range',
          hintText: '结案'
        },
        {
          type: 'select',
          valKey: 'loading_method',
          hintText: '装载方式(理赔)',
          options: [
            { label: '厢式货车', value: 1 },
            { label: '非厢式货车', value: 2 },
            { label: '集装箱(拼箱)', value: 3 },
            { label: '集装箱(整箱)', value: 4 },
            { label: '非集装箱运输', value: 5 }
          ]
        },
        {
          type: 'select',
          valKey: 'packaging_method',
          hintText: '包装方式(理赔)',
          options: [
            { label: '裸装(无运输包装)', value: 1 },
            { label: '简易包装(包装材料防护力弱，如塑料膜；或不能100%全部包裹住货物的包装)', value: 2 },
            { label: '运输包装完善', value: 3 }
          ]
        },
        // {
        //   type: 'input',
        //   valKey: 'amount',
        //   hintText: '报损金额/结案金额/结案赔款/结案费用/追偿金额/残值金额(理赔)'
        // },
        {
          type: 'select',
          valKey: 'loss_reason',
          hintText: '出险原因(理赔) 请先选择险种',
          options: []
        },
        {
          type: 'select',
          valKey: 'has_risk_warning',
          hintText: '风险提示(理赔)',
          options: [
            { label: '是', value: 1 },
            { label: '否', value: 0 }
          ]
        },
        {
          valKey: 'status',
          type: 'select',
          hintText: '理赔进度(理赔)',
          options: [
            { value: -1, label: '已报案' },
            { value: 0, label: '撤案' },
            { value: 1, label: '已受理' },
            { value: 2, label: '材料收集' },
            { value: 3, label: '保司审核' },
            { value: 4, label: '零赔付' },
            { value: 5, label: '拒赔' },
            { value: 6, label: '领赔款' },
            { value: 7, label: '结案' }
          ]
        },
        {
          type: 'input',
          valKey: 'claimant',
          hintText: '报案人/报案人电话/报案人邮箱(理赔)'
        },
        {
          type: 'select',
          valKey: 'operator_id',
          hintText: '理赔员(理赔)',
          options: []
        },
        {
          type: 'input',
          valKey: 'external_adjuster',
          hintText: '保司理赔员(理赔)'
        },
        {
          type: 'input',
          valKey: 'seller',
          hintText: '卖家(理赔)'
        },
        {
          type: 'input',
          valKey: 'buyer',
          hintText: '买家(理赔)'
        }
      ],
      searchData: {},
      rawCompanies: []
    }
  },
  computed: {
    areadata() {
      const options = this.chinaAreadata.map((province) => {
        const options = []
        province.city.forEach((city) => {
          options.push({
            label: `${province.value}-${city.value}`,
            value: `${province.value}-${city.value}`
          })
        })
        return options
      })

      if (this.searchData?.type !== 1) {
        options.push(
          ...Object.keys(this.overseaAreadata).map((key) => {
            return {
              label: this.overseaAreadata[key],
              value: key
            }
          })
        )
      }

      return options.flat()
    }
  },
  watch: {
    searchData: {
      immediate: true,
      deep: true,
      handler(value, old) {
        if (old?.company_id !== value?.company_id) {
          this.loadCompanyBranches()
          if (value?.company_id) {
            this.fetchCompanyAttributes(value?.company_id)
          }
        }

        if (old?.type !== value?.type && value?.type) {
          this.replaceLossReasons(value?.type)
          this.assignSelectOptions('departure', this.areadata)
          this.assignSelectOptions('destination', this.areadata)
        }
      }
    },
    datasource(value) {
      this.$refs.searchPanel.handlerFunc('reset')
      this.$emit('update:datasource', value)
      // this.$emit('search', this.searchData, this.searchFields)
    }
  },
  async created() {
    await this.fetchAdjusters()
    await this.fetchPlatformProductCompanies()
    await this.fetchSalesmans()
    await this.fetchSubjecs()
    await this.fetchFilterOptions()
  },
  methods: {
    async fetchFilterOptions() {
      const data = await dataApi.fetchFilterOptions()

      this.assignSelectOptions('business_from', data.business_from)
    },
    replaceLossReasons(type) {
      const flattenReasons = (reasons) => {
        reasons = reasons.map((reason) => {
          const reasons = []
          reason.options.forEach((option) => {
            reasons.push({
              label: `${reason.value}/${option.value}`,
              value: `${reason.value}/${option.value}`
            })
          })
          return reasons
        })

        return reasons.flat()
      }

      let reasons = []
      switch (type) {
        case 1:
        case 2:
        case 3:
        case 7:
          reasons = lossReasons.cargoReasons
          break
        case 4:
          reasons = lossReasons.generalReasons
          break
        case 5:
          reasons = lossReasons.groupReasons
          break
        case 6:
          if ([5, 8, 9, 10, 11, 12, 14].includes(this.caseData?.offline_category_id)) {
            reasons = lossReasons.cargoReasons
          } else if ([6].includes(this.caseData?.offline_category_id)) {
            reasons = lossReasons.groupReasons
          } else {
            reasons = lossReasons.generalReasons
          }
          break
        default:
          reasons = []
      }
      this.assignSelectOptions('loss_reason', flattenReasons(reasons))
    },
    async fetchAdjusters() {
      const data = (await adminApi.getClaimsAdjusters()).data
      this.assignSelectOptions(
        'operator_id',
        data.map((e) => {
          return { label: e.name, value: e.id }
        })
      )
    },
    async fetchCompanyAttributes(companyId) {
      await this.fetchLoadingMethods(companyId)
      await this.fetchTransportMethods(companyId)
      await this.fetchPackingMethods(companyId)
    },
    async fetchLoadingMethods(companyId) {
      const data = (await loadingMethodApi.getLoadingMethods(companyId)).data
      this.assignSelectOptions(
        'loading_method_id',
        data.map((e) => {
          return { label: e.name, value: e.id }
        })
      )
    },
    async fetchTransportMethods(companyId) {
      const data = (await transportMethodApi.getTransportMethods(companyId)).data
      this.assignSelectOptions(
        'transport_method_id',
        data.map((e) => {
          return { label: e.name, value: e.id }
        })
      )
    },
    async fetchPackingMethods(companyId) {
      const data = (await packingMethodApi.getPackingMethods(companyId)).data
      this.assignSelectOptions(
        'packing_method_id',
        data.map((e) => {
          return { label: e.name, value: e.id }
        })
      )
    },
    async fetchSubjecs() {
      const data = (await subjectApi.getSubjects()).data
      this.assignSelectOptions(
        'subject_id',
        data.map((e) => {
          return { label: e.name, value: e.id }
        })
      )
    },
    async fetchSalesmans() {
      const data = (await adminApi.getSales()).data
      this.assignSelectOptions(
        'salesman_id',
        data
          .map((e) => {
            return { label: e.name, value: e.id }
          })
          .concat({ label: '自然来源', value: -1 })
      )
    },
    async fetchPlatformProductCompanies() {
      this.rawCompanies = (await platformProductApi.getPlatformProductCompanies()).data

      this.loadCompanies()
      this.loadCompanyBranches()
    },
    loadCompanies() {
      const options = this.rawCompanies.map((e) => {
        return {
          label: e.name,
          value: e.id
        }
      })

      this.assignSelectOptions('company_id', options)
    },
    loadCompanyBranches() {
      let options = []
      if (this.searchData?.company_id !== '' && this.searchData?.company_id !== undefined) {
        const company = this.rawCompanies.find((e) => e.id === this.searchData?.company_id)
        if (company) {
          options = company.branches.map((e) => {
            return {
              label: e.name,
              value: e.id
            }
          })
        }
      } else {
        this.rawCompanies.forEach((e) => {
          e?.branches.forEach((e) => {
            options.push({
              label: e.name,
              value: e.id
            })
          })
        })
      }

      this.assignSelectOptions('company_branch_id', options)
    },
    assignSelectOptions(target, options) {
      const targetIdx = this.searchFields.findIndex((f) => f.valKey === target)

      if (targetIdx !== -1) {
        this.searchFields[targetIdx].options = options
      }
    }
  }
}
</script>
