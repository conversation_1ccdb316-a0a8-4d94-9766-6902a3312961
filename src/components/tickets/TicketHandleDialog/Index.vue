<template>
  <el-dialog title="工单处理" width="900px" :visible.sync="visible" @close="handleClose">
    <el-form ref="handleForm" :model="form" :rules="rules" label-suffix=":" label-width="120px">
      <div v-if="ticketType === 1">
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item prop="endorse_no" label="批单号">
              <el-input v-model="form.endorse_no" placeholder="请输入批单号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="policy_file" label="保单文件">
              <upload-file v-model="form.policy_file" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div style="margin-bottom: 10px" v-else>
        <span v-if="policy.payment_method === 1">当前工单为退保工单,请认真确认保单信息再继续操作</span>
        <span v-else>
          当前保单为<span style="color: red; font-weight: bold">在线支付</span
          >保单,退回或退保时将直接退款,请谨慎处理该工单
        </span>
      </div>

      <!-- 人工审核额外字段 -->
      <template v-if="isManaulInsure">
        <el-alert
          v-if="policy.is_premium_sync"
          show-icon
          title="当前为保费同步保单，请录入相同成本费率、平台费率、代理费率、用户费率"
          type="warning"
          :closable="false"
          class="m-extra-large-b"
        />
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item prop="rate" label="成本费率">
              <el-input type="number" :min="1" :max="100" v-model="form.rate" placeholder="请输入成本费率">
                <template slot="append">‱</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="premium" label="成本保费">
              <el-input type="number" :min="1" :max="100" v-model="form.premium" placeholder="成本保费">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="service_charge" label="经纪费比例">
              <el-input
                type="number"
                :min="1"
                :max="100"
                v-model="form.service_charge"
                placeholder="请输入经纪费比例"
              >
                <template slot="append">%&nbsp;</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <template v-if="isCrossPlatform">
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item prop="platform_rate" label="平台费率" required>
                <el-input
                  type="number"
                  :min="1"
                  :max="100"
                  v-model="form.platform_rate"
                  placeholder="请输入平台费率"
                >
                  <template slot="append">‱</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="platform_premium" label="平台成本保费">
                <el-input
                  type="number"
                  :min="1"
                  :max="100"
                  v-model="form.platform_premium"
                  placeholder="平台成本保费"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="platform_commission_rate" label="平台佣金比例">
                <el-input
                  type="number"
                  :min="1"
                  :max="100"
                  v-model="form.platform_commission_rate"
                  placeholder="请输入平台佣金比例"
                >
                  <template slot="append">%&nbsp;</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
        <template v-if="showAgentRateFormItem">
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item prop="agent_rate" label="代理费率" required>
                <el-input
                  type="number"
                  :min="1"
                  :max="100"
                  v-model="form.agent_rate"
                  placeholder="请输入代理费率"
                >
                  <template slot="append">‱</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="agent_premium" label="代理保费" required>
                <el-input type="number" :min="1" :max="100" v-model="form.agent_premium" placeholder="代理保费">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="agent_commission_rate" label="代理佣金比例">
                <el-input
                  type="number"
                  :min="1"
                  :max="100"
                  v-model="form.agent_commission_rate"
                  placeholder="请输入代理佣金比例"
                >
                  <template slot="append">%&nbsp;</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
        <template v-if="showUserRateFormItem">
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item prop="user_rate" label="用户费率">
                <el-input
                  type="number"
                  :min="1"
                  :max="100"
                  v-model="form.user_rate"
                  placeholder="请输入用户费率"
                >
                  <template slot="append">‱</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="user_premium" label="用户保费" required>
                <el-input type="number" :min="1" :max="100" v-model="form.user_premium" placeholder="用户保费">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
      </template>

      <el-form-item>
        <el-button type="primary" @click="handleSubmit" icon="fas fa-check">提交</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
export default {
  name: 'TicketHandleDialog',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    ticketType: {
      type: Number,
      default: 1
    },
    policy: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      form: {
        endorse_no: '',
        policy_file: '',
        rate: '',
        premium: '',
        platform_rate: '',
        platform_premium: '',
        platform_commission_rate: '',
        agent_rate: '',
        agent_premium: '',
        agent_commission_rate: '',
        user_rate: '',
        user_premium: '',
        service_charge: ''
      },
      rules: {
        endorse_no: [{ required: true, message: '请输入批单号', trigger: 'blur' }],
        policy_file: [{ required: true, message: '请上传保单文件', trigger: ['blur', 'change'] }]
      }
    }
  },
  computed: {
    visible: {
      get() {
        return this.dialogVisible
      },
      set(value) {
        this.$emit('update:dialogVisible', value)
      }
    },
    isManaulInsure() {
      return this.policy?.detail?.subject?.identifier === 'MANUAL' || this.policy?.detail?.subject?.id === 3
    },
    showAgentRateFormItem() {
      // 如果是代理下面的用户或者代理投保
      return this.policy?.user?.agent_id !== -1 || this.policy?.user?.is_agent
    },
    showUserRateFormItem() {
      // 如果是代理下面的用户或者是平台直属客户
      return this.policy?.user?.agent_id !== -1 || (!this.policy?.user?.is_agent && this.policy?.user?.agent_id === -1)
    },
    isCrossPlatform() {
      return this.policy?.is_cross_platform_product
    }
  },
  watch: {
    'form.rate'(value) {
      if (value === this.policy.cost_rate) {
        return
      }

      const premium = parseFloat(
        (((this.policy?.coverage * 100 * value) / 10000) *
          ([2, 7].includes(this.policy?.source_type) ? parseFloat(this.policy?.detail?.coverage_currency?.rate, 5) : 1)) /
          100
      ).toFixed(2)

      this.form.premium = premium < this.policy?.minimum_premium ? this.policy?.minimum_premium : premium

      if (this.policy?.is_premium_sync) {
        this.form.platform_rate = value
        this.form.agent_rate = value
        this.form.user_rate = value
      }
    },
    'form.platform_rate'(value) {
      if (value === this.policy.platform_rate) {
        return
      }
      const premium = parseFloat(
        (((this.policy?.coverage * 100 * value) / 10000) *
          ([2, 7].includes(this.policy?.source_type) ? parseFloat(this.policy?.detail?.coverage_currency?.rate, 5) : 1)) /
          100
      ).toFixed(2)

      this.form.platform_premium =
        premium < this.policy?.platform_minimum_premium ? this.policy?.platform_minimum_premium : premium

      if (this.policy?.is_premium_sync) {
        this.form.rate = value
        this.form.agent_rate = value
        this.form.user_rate = value
      }
    },
    'form.agent_rate'(value) {
      if (value === this.policy.agent_rate) {
        return
      }
      const premium = parseFloat(
        (((this.policy?.coverage * 100 * value) / 10000) *
          ([2, 7].includes(this.policy?.source_type) ? parseFloat(this.policy?.detail?.coverage_currency?.rate, 5) : 1)) /
          100
      ).toFixed(2)

      this.form.agent_premium =
        premium < this.policy?.agent_minimum_premium ? this.policy?.agent_minimum_premium : premium

      if (this.policy?.is_premium_sync) {
        this.form.rate = value
        this.form.platform_rate = value
        this.form.user_rate = value
      }
    },
    'form.user_rate'(value) {
      if (value === this.policy.actual_user_rate) {
        return
      }
      const premium = parseFloat(
        (((this.policy?.coverage * 100 * value) / 10000) *
          ([2, 7].includes(this.policy?.source_type) ? parseFloat(this.policy?.detail?.coverage_currency?.rate, 5) : 1)) /
          100
      ).toFixed(2)

      this.form.user_premium =
        premium < this.policy?.user_minimum_premium ? this.policy?.user_minimum_premium : premium

      // 保费同步
      if (this.policy?.is_premium_sync) {
        this.form.rate = value
        this.form.platform_rate = value
        this.form.agent_rate = value
      }
    },
    // 保费同步
    'form.user_premium'(value) {
      if (this.policy?.is_premium_sync) {
        this.form.premium = value
        this.form.platform_premium = value
        this.form.agent_premium = value
      }
    },
    policy: {
      deep: true,
      immediate: true,
      handler() {
        const mergeData = {
          rate: this.policy?.cost_rate || '',
          premium: this.policy?.cost_premium || '',
          service_charge: this.policy?.service_charge || ''
        }

        if (this.showAgentRateFormItem) {
          mergeData.agent_rate = this.policy?.agent_rate || ''
          mergeData.agent_premium = this.policy?.agent_premium || ''
          mergeData.agent_commission_rate = this.policy?.agent_commission_rate || ''
        }

        if (this.showUserRateFormItem) {
          mergeData.user_rate = this.policy?.actual_user_rate || ''
          mergeData.user_premium = this.policy?.user_premium || ''
        }

        if (this.isCrossPlatform) {
          mergeData.platform_rate = this.policy?.platform_rate || ''
          mergeData.platform_premium = this.policy?.platform_premium || ''
          mergeData.platform_commission_rate = this.policy?.platform_commission_rate || ''
        } else {
          mergeData.platform_rate = this.policy?.cost_rate
        }

        this.form = Object.assign({}, this.form, mergeData)
      }
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.handleForm.validate((valid) => {
        if (valid) {
          this.$emit('submit', this.form)
        }
      })
    },
    handleClose() {
      this.$refs.handleForm.resetFields()
      this.$emit('close')
    },
    resetForm() {
      this.$refs.handleForm.resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0px !important;
}
</style>
