<template>
  <el-dialog :title="filename" :visible.sync="visible" width="700px" :before-close="handleClose" destroy-on-close>
    <el-image v-if="!doesntSupportViewer && image" :src="image" fit="scale-down"></el-image>
    <template>
      <div v-if="doesntSupportViewer" class="viewer__not-supported">
        <p>当前文件暂不支持预览，请下载后查看.</p>
        <el-button type="primary" @click="handleDownload" icon="fas fa-download">下载</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'Viewer',
  props: {
    visible: {
      required: true,
      type: <PERSON><PERSON><PERSON>
    },
    filename: {
      required: true,
      type: String
    },
    href: {
      required: true,
      type: String
    }
  },
  data() {
    return {
      image: '',
      doesntSupportViewer: false
    }
  },
  watch: {
    href() {
      const fileExt = this.href.split('.').pop()
      if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExt)) {
        this.image = this.href
      } else {
        this.image = ''
        this.doesntSupportViewer = true
      }
    }
  },
  methods: {
    handleDownload() {
      window.open(this.href, '_blank')
      this.handleClose()
    },
    handleClose() {
      this.image = ''
      this.doesntSupportViewer = false

      this.$emit('update:visible', false)
    }
  }
}
</script>
