<template>
  <div>
    <el-dialog title="账号配置" :visible.sync="visible" width="800px" :before-close="handleClose" destroy-on-close>
      <el-header class="d-flex align-items-center p-none-x">
        <el-button
          v-can="{ name: 'companies.branches.accounts.create' }"
          icon="el-icon-circle-plus"
          type="primary"
          @click="newAccountDialog"
        >
          添加账号
        </el-button>
      </el-header>
      <define-table :data="data" :cols="cols"></define-table>
    </el-dialog>
    <el-dialog
      :title="currentDialogTitle"
      :visible.sync="formVisible"
      width="520px"
      :before-close="handleCloseForm"
      destroy-on-close
    >
      <el-form ref="form" label-position="top" :model="form" :rules="rules" label-width="100px">
        <el-form-item prop="platform_id" label="所属平台">
          <el-select v-model="form.platform_id" placeholder="请选择所属平台" style="width: 100%">
            <el-option
              v-for="platform in platforms"
              :key="platform.id"
              :label="platform.name"
              :value="platform.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="name" label="账号名称">
          <el-input v-model="form.name" placeholder="请输入账号名称" />
        </el-form-item>
        <el-form-item prop="username" label="用户名">
          <el-input v-model="form.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item prop="password" label="账号密码">
          <el-input v-model="form.password" type="password" show-password placeholder="请输入账号密码" />
        </el-form-item>
        <el-form-item label="报备邮箱(开发配置)">
          <el-input
            type="textarea"
            rows="3"
            v-model="form.config.report_email_recipients"
            placeholder="报备邮箱（由开发人员配置）"
          />
        </el-form-item>
        <el-form-item label="保险公司协议(开发配置)">
          <el-input type="textarea" rows="3" v-model="form.config.agreements" placeholder="保险公司协议(由开发配置)" />
        </el-form-item>
        <el-form-item label="方案(开发配置)">
          <el-input type="textarea" rows="3" v-model="form.config.plans" placeholder="方案(由开发配置)" />
        </el-form-item>
        <el-form-item prop="expires_on" label="协议到期时间">
          <el-date-picker v-model="form.expires_on" type="date" placeholder="请选择协议到期时间" style="width: 100%" />
        </el-form-item>
        <el-form-item label="是否启用" prop="is_enabled">
          <el-switch
            active-text="是"
            inactive-text="否"
            :active-value="1"
            :inactive-value="0"
            v-model="form.is_enabled"
          ></el-switch>
        </el-form-item>
        <el-form-item>
          <el-button @click="handleCloseForm">取 消</el-button>
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { getPlatforms } from '@/apis/platform'
import dayjs from 'dayjs'

export default {
  name: 'CompanyBranchAccount',
  props: {
    data: {
      type: Array,
      default: () => {}
    },
    model: {
      type: Object,
      default: () => {}
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now()
        }
      },
      formVisible: false,
      platforms: [],
      cols: [
        {
          label: '平台',
          prop: 'platform.name'
        },
        {
          label: '名称',
          prop: 'name'
        },
        {
          label: '账号',
          prop: 'username'
        },
        {
          label: '密码',
          prop: 'password'
        },
        {
          label: '是否可用',
          prop: 'is_enabled',
          width: 70,
          scopedSlots: {
            default: (scoped) => {
              return scoped.row.is_enabled ? <span class="text-primary">是</span> : <span class="text-info">否</span>
            }
          }
        },
        {
          label: '操作',
          width: 85,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    v-can={{ name: 'companies.branches.accounts.update' }}
                    size="small"
                    type="text"
                    onClick={() => this.handleUpdate(scoped.row)}
                  >
                    编辑
                  </el-button>
                  <el-popconfirm
                    v-can={{ name: 'companies.branches.accounts.delete' }}
                    style="margin-left: 10px"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    icon="el-icon-info"
                    icon-color="red"
                    title="确定删除吗？"
                    onConfirm={() => this.$emit('delete', scoped.row)}
                  >
                    <el-button size="small" type="text" slot="reference">
                      删除
                    </el-button>
                  </el-popconfirm>
                </div>
              )
            }
          }
        }
      ],
      form: {
        platform_id: '',
        name: '',
        username: '',
        password: '',
        config: {
          report_email_recipients: '',
          agreements: '',
          plans: ''
        },
        expires_on: '',
        is_enabled: '1'
      },
      rules: {
        platform_id: [{ required: true, message: '请选择所属平台', trigger: ['blur', 'change'] }],
        name: [{ required: true, message: '请输入账号名称', trigger: 'blur' }],
        username: [{ required: true, message: '请输入账号', trigger: 'blur' }],
        password: [{ required: true, message: '请输入账号密码啊', trigger: 'blur' }],
        is_enabled: [{ required: true, message: '请选择是否启用', trigger: ['blur', 'change'] }]
      }
    }
  },
  computed: {
    currentDialogTitle() {
      return this.form.id === undefined ? '添加账号' : '编辑账号'
    }
  },
  created() {
    getPlatforms().then((r) => (this.platforms = r.data))
  },
  methods: {
    newAccountDialog() {
      this.formVisible = true
      this.form = {
        platform_id: '',
        name: '',
        username: '',
        password: '',
        config: {
          report_email_recipients: '',
          agreements: '',
          plans: ''
        },
        is_enabled: '1'
      }
    },
    handleClose() {
      this.$emit('update:visible', false)
    },
    handleCloseForm() {
      this.formVisible = false
      this.$refs.form.resetFields()
    },
    handleUpdate(rowData) {
      rowData.expire_on = Date.parse(rowData.expire_on) || Date.now()
      this.form = rowData
      this.formVisible = true
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.formVisible = false

          let data = Object.assign({}, this.form)
          data.expires_on = dayjs(data.expires_on).format('YYYY-MM-DD').toString()

          this.$emit('submit', data)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0px !important;
}
</style>
