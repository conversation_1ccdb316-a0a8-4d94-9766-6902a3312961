<!--
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2021-01-08 15:20:51
 * @LastEditors: yanb
 * @LastEditTime: 2023-02-20 09:34:11
-->
<template>
  <el-card class="policy-search" shadow="never">
    <div slot="header">
      <el-row :gutter="15">
        <el-col :span="6" v-if="showProductCode">
          <el-input v-model="form.code" placeholder="请填写产品代码" clearable />
        </el-col>
        <el-col :span="6">
          <el-select v-model="form.platform_id" placeholder="请选择平台" clearable v-if="showPlatformField">
            <el-option v-for="p in platformsDict" :key="p.id" :label="p.name" :value="p.id"></el-option>
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select v-model="form.company_id" placeholder="请选择保险公司" clearable>
            <el-option v-for="com in companiesDict" :key="com.id" :label="com.name" :value="com.id"></el-option>
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select v-model="form.company_branch_id" placeholder="出单公司" clearable>
            <el-option
              v-for="branch in companyBranches"
              :key="branch.id"
              :label="branch.name"
              :value="branch.id"
            ></el-option>
          </el-select>
        </el-col>
        <el-col :span="6" v-if="showSubjectField">
          <subjects v-model="form.subject_id" />
        </el-col>
        <el-col :span="6">
          <el-select v-model="form.is_enabled" placeholder="请选择是否配置" clearable>
            <el-option :key="1" label="是" :value="1"></el-option>
            <el-option :key="0" label="否" :value="0"></el-option>
          </el-select>
        </el-col>
      </el-row>
    </div>
    <el-button icon="el-icon-search" @click="handleSearch" type="primary">查询</el-button>
    <el-button icon="el-icon-refresh" @click="handleResetSearch">清空</el-button>
  </el-card>
</template>

<script>
import Subjects from '@/components/selectors/Subjects'
import { getCompaniesDict } from '@/apis/company'
import { getPlatformsDict } from '@/apis/platform'
import SearchCache from '@/mixins/SearchCache'

export default {
  name: 'ProductSearch',
  mixins: [SearchCache],
  components: { Subjects },
  props: {
    showSubjectField: {
      type: Boolean,
      default: true
    },
    showPlatformField: {
      type: Boolean,
      default: true
    },
    showProductCode: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      form: {
        platform_id: '',
        company_id: '',
        company_branch_id: '',
        subject_id: ''
      },
      companiesDict: [],
      platformsDict: []
    }
  },
  computed: {
    companyBranches() {
      const com = this.companiesDict.find((e) => e.id === this.form.company_id)

      return com?.branches || []
    }
  },
  created() {
    if (this.hasCached()) {
      this.restoreSearchCache(this.form)
      this.$emit('search', Object.assign({}, this.form))
    }

    getCompaniesDict().then((r) => (this.companiesDict = r.data))
    getPlatformsDict().then((r) => (this.platformsDict = r.data))
  },
  methods: {
    handleSearch() {
      this.setSearchCache(Object.assign({}, this.form))
      this.$emit('search', Object.assign({}, this.form))
    },
    handleResetSearch() {
      this.forgetSearchCache()
      Object.keys(this.form).forEach((k) => (this.form[k] = ''))

      this.$emit('search', Object.assign({}, this.form))
    }
  }
}
</script>

<style lang="scss" scoped>
.policy-search {
  .el-col {
    margin: 5px 0;
    .el-form-item {
      margin: 0;
      display: flex;
      width: 100%;
      /deep/ .el-form-item__content {
        flex: 1;
        overflow: hidden;
      }
    }
  }
  /deep/.el-select,
  /deep/.el-date-editor {
    width: 100% !important;
  }
  /deep/.el-card__body {
    display: flex;
  }
}
</style>
