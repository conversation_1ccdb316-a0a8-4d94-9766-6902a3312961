<template>
  <div class="p-extra-large-b">
    <el-card class="table-wrap" shadow="never">
      <div v-if="$route.params.id">
        <div class="d-flex w-100" slot="header">
          <i class="flex-fill m-mini-r"></i>
          <el-button type="danger" @click="modifyProductStatus" v-can="{ name: 'products.group.update' }">
            {{ form.is_enabled ? '禁用' : '启用' }}
          </el-button>
          <el-button
            type="primary"
            v-can="{ name: 'products.group.plans.index' }"
            @click="$router.push({ name: 'GroupProductPlans', id: $route.params.id })"
          >
            套餐
          </el-button>
          <el-button type="primary" @click="techDialog.visible = true" v-can="{ name: 'products.group.config' }">
            技术配置
          </el-button>
        </div>
      </div>
      <el-form ref="form" :model="form" :rules="rules" label-suffix=":" label-width="100px" label-position="top">
        <section>
          <h3>基本信息</h3>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item prop="name" label="产品名称">
                <el-input v-model="form.name" placeholder="请输入产品名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="platform_id" label="平台">
                <el-select v-model="form.platform_id" placeholder="请选择平台" clearable filterable style="width: 100%">
                  <el-option v-for="p in platforms" :key="p.id" :value="p.id" :label="p.name" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="company_id" label="保险公司">
                <el-select
                  v-model="form.company_id"
                  placeholder="请选择保险公司"
                  clearable
                  filterable
                  style="width: 100%"
                >
                  <el-option v-for="com in companies" :key="com.id" :value="com.id" :label="com.name" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="company_branch_id" label="出单公司">
                <el-select
                  v-model="form.company_branch_id"
                  placeholder="请选择出单公司"
                  clearable
                  filterable
                  style="width: 100%"
                >
                  <el-option v-for="com in companyBranches" :key="com.id" :value="com.id" :label="com.name" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="channel_id" label="投保渠道">
                <el-select
                  v-model="form.channel_id"
                  placeholder="请选择投保渠道"
                  clearable
                  filterable
                  style="width: 100%"
                >
                  <el-option v-for="com in channels" :key="com.id" :value="com.id" :label="com.name" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="email" label="报备邮箱">
                <el-input v-model="form.email" type="textarea" placeholder="报备邮箱 (多个邮箱请使用英文分号隔开)" />
              </el-form-item>
            </el-col>
          </el-row>
        </section>
        <section>
          <h3>保险产品资料</h3>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item label="保险条款" prop="clause_file">
                <el-upload
                  action=""
                  :multiple="false"
                  :limit="1"
                  :auto-upload="false"
                  :on-change="(f) => (form.clause_file = f.raw)"
                >
                  <el-button size="small" type="primary">点击上传</el-button>
                </el-upload>
                <el-link
                  icon="el-icon-view"
                  v-if="model.id && form.clause_file && typeof form.clause_file === 'string'"
                  :href="form.clause_file"
                >
                  点击查看
                </el-link>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="投保资料" prop="insured_file">
                <el-upload
                  action=""
                  :multiple="false"
                  :limit="1"
                  :auto-upload="false"
                  :on-change="(f) => (form.insured_file = f.raw)"
                >
                  <el-button size="small" type="primary">点击上传</el-button>
                </el-upload>
                <el-link
                  icon="el-icon-view"
                  v-if="model.id && form.insured_file && typeof form.insured_file === 'string'"
                  :href="form.insured_file"
                >
                  点击查看
                </el-link>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item label="人员清单导入模版" prop="employee_template_file">
                <el-upload
                  action=""
                  :multiple="false"
                  :limit="1"
                  :auto-upload="false"
                  :on-change="(f) => (form.employee_template_file = f.raw)"
                >
                  <el-button size="small" type="primary">点击上传</el-button>
                </el-upload>
                <el-link
                  icon="el-icon-view"
                  v-if="model.id && form.employee_template_file && typeof form.employee_template_file === 'string'"
                  :href="form.employee_template_file"
                >
                  点击查看
                </el-link>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="职业分类表" prop="job_category_file">
                <el-upload
                  action=""
                  :multiple="false"
                  :limit="1"
                  :auto-upload="false"
                  :on-change="(f) => (form.job_category_file = f.raw)"
                >
                  <el-button size="small" type="primary">点击上传</el-button>
                </el-upload>
                <el-link
                  icon="el-icon-view"
                  v-if="model.id && form.job_category_file && typeof form.job_category_file === 'string'"
                  :href="form.job_category_file"
                >
                  点击查看
                </el-link>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item label="投保人员清单模板" prop="insure_template_file">
                <el-upload
                  action=""
                  :multiple="false"
                  :limit="1"
                  :auto-upload="false"
                  :on-change="(f) => (form.insure_template_file = f.raw)"
                >
                  <el-button size="small" type="primary">点击上传</el-button>
                </el-upload>
                <el-link
                  icon="el-icon-view"
                  v-if="model.id && form.insure_template_file && typeof form.insure_template_file === 'string'"
                  :href="form.insure_template_file"
                >
                  点击查看
                </el-link>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="人员新增模板" prop="add_employee_template_file">
                <el-upload
                  action=""
                  :multiple="false"
                  :limit="1"
                  :auto-upload="false"
                  :on-change="(f) => (form.add_employee_template_file = f.raw)"
                >
                  <el-button size="small" type="primary">点击上传</el-button>
                </el-upload>
                <el-link
                  icon="el-icon-view"
                  v-if="
                    model.id && form.add_employee_template_file && typeof form.add_employee_template_file === 'string'
                  "
                  :href="form.add_employee_template_file"
                >
                  点击查看
                </el-link>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item label="人员替换模板" prop="replace_employee_template_file">
                <el-upload
                  action=""
                  :multiple="false"
                  :limit="1"
                  :auto-upload="false"
                  :on-change="(f) => (form.replace_employee_template_file = f.raw)"
                >
                  <el-button size="small" type="primary">点击上传</el-button>
                </el-upload>
                <el-link
                  icon="el-icon-view"
                  v-if="
                    model.id &&
                    form.replace_employee_template_file &&
                    typeof form.replace_employee_template_file === 'string'
                  "
                  :href="form.replace_employee_template_file"
                >
                  点击查看
                </el-link>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="人员批减模板" prop="remove_employee_template_file">
                <el-upload
                  action=""
                  :multiple="false"
                  :limit="1"
                  :auto-upload="false"
                  :on-change="(f) => (form.remove_employee_template_file = f.raw)"
                >
                  <el-button size="small" type="primary">点击上传</el-button>
                </el-upload>
                <el-link
                  icon="el-icon-view"
                  v-if="
                    model.id &&
                    form.remove_employee_template_file &&
                    typeof form.remove_employee_template_file === 'string'
                  "
                  :href="form.remove_employee_template_file"
                >
                  点击查看
                </el-link>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item prop="insure_template_file">
                <template #label>
                  <span>（线下批单）人员批单申请书</span>
                  <small class="text-danger">如存在多个不同的申请书模板请上传压缩包</small>
                </template>
                <el-upload
                  action=""
                  :multiple="false"
                  :limit="1"
                  :auto-upload="false"
                  :on-change="(f) => (form.revision_template_file = f.raw)"
                >
                  <el-button size="small" type="primary">点击上传</el-button>
                </el-upload>
                <el-link
                  icon="el-icon-view"
                  v-if="model.id && form.revision_template_file && typeof form.revision_template_file === 'string'"
                  :href="form.revision_template_file"
                >
                  点击查看
                </el-link>
              </el-form-item>
            </el-col>
          </el-row>
        </section>
        <section>
          <h3>产品内容</h3>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item label="保险责任">
                <el-input type="textarea" v-model="form.insured_liability" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="保险期限">
                <el-input type="textarea" v-model="form.insurance_period" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item label="承保行业">
                <el-input type="textarea" v-model="form.industry" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="职业类别">
                <el-input type="textarea" v-model="form.job_class" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="48">
            <el-col :span="24">
              <el-form-item label="备注">
                <el-input type="textarea" v-model="form.remark" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="48">
            <el-col :span="24">
              <el-form-item label="责任免除内容">
                <editor v-model="form.description" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="48">
            <el-col :span="24">
              <el-form-item label="投保须知">
                <editor v-model="form.notice" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="48">
            <el-col :span="24">
              <el-form-item label="投保告知">
                <editor v-model="form.inform" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="48">
            <el-col :span="24">
              <el-form-item label="常见问答">
                <editor v-model="form.faq" />
              </el-form-item>
            </el-col>
          </el-row>
        </section>
        <section>
          <h3>财务信息</h3>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item label="保险公司经纪费（%）">
                <el-input type="text" v-model="form.service_charge" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="参考价格（元）">
                <el-input type="text" v-model="form.reference_price" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item label="平台出单费（%）">
                <el-input type="text" v-model="form.platform_service_charge" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="计算保费方式">
                <el-form-item>
                  <el-select v-model="form.charge_method" placeholder="计算保费方式" class="w-100">
                    <el-option :value="2" label="按天计算"></el-option>
                    <el-option :value="3" label="按天计算（1年固定365天）"></el-option>
                    <el-option :value="1" label="按月计算"></el-option>
                  </el-select>
                </el-form-item>
              </el-form-item>
            </el-col>
          </el-row>
        </section>
        <section>
          <h3>邮件信息</h3>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item label="投保附件">
                <el-checkbox-group v-model="insuredConfig">
                  <el-checkbox v-for="item in configList" :label="item.key" :key="item.key">
                    {{ item.label }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="批单附件">
                <el-checkbox-group v-model="modifyConfig">
                  <el-checkbox v-for="item in endorseConfigList" :label="item.key" :key="item.key">{{
                    item.label
                  }}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
          </el-row>
        </section>
        <section>
          <h3>其他信息</h3>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item label="仅限本平台使用">
                <el-radio-group v-model="form.platform_private">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item label="出单后禁止人员变更">
                <el-checkbox-group v-model="limitations">
                  <el-checkbox label="append" key="append">增加</el-checkbox>
                  <el-checkbox label="remove" key="remove">减少</el-checkbox>
                  <el-checkbox label="replace" key="replace">替换</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item label="付款方式">
                <el-radio-group v-model="form.payment_type">
                  <el-radio :label="1">线下支付</el-radio>
                  <el-radio :label="2">在线支付</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item label="参保年龄下限（周岁）">
                <el-input type="text" v-model="form.age_limit_lowest" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="参保年龄上限（周岁）">
                <el-input type="text" v-model="form.age_limit_highest" />
              </el-form-item>
            </el-col>
          </el-row>
        </section>

        <el-form-item>
          <el-button>取 消</el-button>
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </el-form-item>
      </el-form>
      <el-dialog
        append-to-body
        title="技术配置"
        :visible.sync="techDialog.visible"
        width="520px"
        :before-close="() => (techDialog.visible = false)"
        destory-on-close
      >
        <el-form
          ref="techDialogForm"
          :model="techDialog.form"
          :rules="techDialog.rules"
          label-suffix=":"
          label-width="100"
        >
          <el-form-item prop="third_platform" label="投保方式">
            <el-select v-model="techDialog.form.third_platform" placeholder="选择投保方式">
              <el-option v-for="item in techDialog.options" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="third_product_code" label="产品代码">
            <el-input v-model="techDialog.form.third_product_code" placeholder="" />
          </el-form-item>
          <el-form-item prop="parameters" label="接口参数">
            <el-input v-model="techDialog.form.parameters" placeholder="" />
          </el-form-item>
          <el-form-item>
            <el-form-item>
              <el-button @click="techDialog.visible = false">取 消</el-button>
              <el-button type="primary" @click="handleTechModify" v-can="{ name: 'can:products.group.config' }">
                确 定
              </el-button>
            </el-form-item>
          </el-form-item>
        </el-form>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import { getPlatformsDict } from '@/apis/platform'
import { getCompaniesDict } from '@/apis/company'
import { getCompanyBranchAccounts } from '@/apis/company_branch_account'
import { getChannels } from '@/apis/channel'
import { getClauses } from '@/apis/company_clause'
import { createGroupProduct, updateGroupProduct } from '@/apis/groups'

import { Loading } from 'element-ui'
import { updateProductStatus, updateProductTechConfig } from '@/apis/groups'
import { getProduct } from '@/apis/product'

const emailConfigs = [
  { key: 'transaction_file', label: '支付凭证' },
  { key: 'business_license_file', label: '营业执照' },
  { key: 'application_file', label: '投保单文件' },
  { key: 'application_stamp_file', label: '投保单盖章文件' },
  { key: 'staff_list_file', label: '人员清单' },
  { key: 'staff_stamp_list_file', label: '人员清单盖章文件' },
  { key: 'authorization_file', label: '委托书' },
  { key: 'extra_file', label: '其它文件' }
]

const endorseEmailConfigs = [
  { key: 'transaction_file', label: '支付凭证' },
  { key: 'endorse_stamp_file', label: '批单盖章文件' },
  { key: 'staff_list_file', label: '人员清单' },
  { key: 'staff_stamp_list_file', label: '人员清单盖章文件' },
  { key: 'authorization_file', label: '委托书' },
  { key: 'extra_file', label: '其它文件' }
]

export default {
  name: 'GroupProductForm',
  props: {
    model: {
      type: Object,
      default: () => {}
    },
    type: {
      type: Number,
      default: 5
    }
  },
  data() {
    return {
      platforms: [],
      companies: [],
      companyBranchAccounts: [],
      channels: [],
      configList: emailConfigs,
      endorseConfigList: endorseEmailConfigs,
      insuredConfig: [],
      modifyConfig: [],
      limitations: [],
      techDialog: {
        visible: false,
        options: [
          {
            value: 'NONE',
            label: '无'
          },
          {
            value: 'API_GROUP_ZY',
            label: '中意 - 全联雇主'
          },
          {
            value: 'PINGAN',
            label: '平安雇主'
          }
        ],
        form: {
          third_platform: 'NONE',
          third_product_code: '',
          parameters: ''
        },
        rules: {
          third_platform: [{ required: false, message: '投保方式必填', trigger: 'blur' }],
          third_product_code: [{ required: false, message: '', trigger: 'blur' }],
          parameters: [{ required: false, message: '', trigger: 'blur' }]
        }
      },
      form: {
        name: '',
        platform_id: '',
        company_id: '',
        company_branch_id: '',
        company_branch_account_id: 1,
        channel_id: '',
        is_enabled: true,
        job_class: '',
        clause_file: '',
        insured_file: '',
        job_category_file: '',
        charge_method: '',
        reference_price: '',
        service_charge: '',
        platform_service_charge: '',
        employee_template_file: '',
        revision_template_file: '',
        insured_liability: '',
        insurance_period: '',
        insure_template_file: '',
        add_employee_template_file: '',
        replace_employee_template_file: '',
        remove_employee_template_file: '',
        industry: '',
        remark: '',
        description: '',
        notice: '',
        inform: '',
        faq: '',
        third_platform: '',
        third_product_code: '',
        payment_type: 1,
        mail_attachments: {
          insured: [],
          modify: []
        },
        disable_add_staffs: '',
        platform_private: '',
        has_limitation: 1,
        age_limit_lowest: '',
        age_limit_highest: '',
        config: {
          has_limitation: 0,
          age_limit_lowest: '',
          age_limit_highest: ''
        }
      },
      rules: {
        name: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
        platform_id: [{ required: true, message: '请选择运营平台', trigger: ['blur', 'change'] }],
        company_id: [{ required: true, message: '请选择保险公司', trigger: ['blur', 'change'] }],
        company_branch_id: [{ required: true, message: '请选择保险出单公司', trigger: ['blur', 'change'] }],
        channel_id: [{ required: true, message: '请选择投保渠道', trigger: ['blur', 'change'] }],
        email: [{ required: true, message: '请输入报备邮箱', trigger: ['blur', 'change'] }]
      }
    }
  },
  computed: {
    companyBranches() {
      const com = this.companies.find((e) => e.id == this.form.company_id)

      return com?.branches || []
    }
  },
  watch: {
    'form.company_id'() {
      if (this.form.company_id) {
        getClauses(this.form.company_id).then((r) => (this.clauses = r.data))
      }
    },
    'form.company_branch_id'() {
      if (this.form.company_id && this.form.company_branch_id) {
        getCompanyBranchAccounts(this.form.company_id, this.form.company_branch_id).then(
          (r) => (this.companyBranchAccounts = r.data)
        )
      }
    },
    model: {
      immediate: true,
      deep: true,
      handler(value) {
        if (value) {
          this.form = Object.assign({}, value)

          if (value.mail_attachments !== undefined) {
            if (value.mail_attachments.insured instanceof Array) {
              this.insuredConfig = value.mail_attachments.insured
            } else {
              this.insuredConfig = []
            }
            if (value.mail_attachments.modify instanceof Array) {
              this.modifyConfig = value.mail_attachments.modify
            } else {
              this.modifyConfig = []
            }

            if (value.disable_add_staffs instanceof Array) {
              this.limitations = value.disable_add_staffs
            } else {
              this.limitations = []
            }
          }
        }
      }
    }
  },
  created() {
    getPlatformsDict().then((r) => (this.platforms = r.data))
    getCompaniesDict().then((r) => (this.companies = r.data))
    getChannels().then((r) => (this.channels = r.data))

    this.fetchProductTechConfig()
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate((valid, errors) => {
        if (valid) {
          this.form.type = this.type
          this.form.mail_attachments = {
            insured: this.insuredConfig,
            modify: this.modifyConfig
          }
          this.form.mail_attachments = JSON.stringify(this.form.mail_attachments)
          this.form.config = JSON.stringify({
            has_limitation: this.form.has_limitation,
            age_limit_lowest: this.form.age_limit_lowest,
            age_limit_highest: this.form.age_limit_highest
          })
          this.form.disable_add_staffs = JSON.stringify(this.limitations)

          const fileFields = [
            'clause_file',
            'insured_file',
            'job_category_file',
            'employee_template_file',
            'revision_template_file',
            'insure_template_file',
            'add_employee_template_file',
            'replace_employee_template_file',
            'remove_employee_template_file'
          ]

          for (const field of fileFields) {
            if (typeof this.form[field] === 'string') {
              this.form[field] = ''
            }
          }

          const action =
            this.form.id === undefined ? createGroupProduct(this.form) : updateGroupProduct(this.form.id, this.form)

          const loading = Loading.service()
          action
            .then(() => {
              this.$message.success('保存成功')

              if (this.$route.params.id !== undefined) {
                this.$emit('updated', this.model.id)
              } else {
                this.$router.go(-1)
              }
            })
            .finally(() => loading.close())
        } else {
          window.alert_validate_errors(errors)
        }
      })
    },
    modifyProductStatus() {
      let status = 0
      if (this.form.is_enabled) {
        status = 0
      } else {
        status = 1
      }

      const loading = Loading.service()
      updateProductStatus(this.form.id, {
        is_enabled: status
      })
        .then(() => {
          this.$message.success('保存成功')

          this.$router.go(-1)
        })
        .finally(() => loading.close())
    },
    handleTechModify() {
      this.$refs.techDialogForm.validate((valid) => {
        if (valid) {
          const loading = Loading.service()
          updateProductTechConfig(this.form.id, this.techDialog.form)
            .then(() => {
              this.$message.success('修改成功')

              this.fetchProductTechConfig()
            })
            .finally(() => loading.close())
        }
      })
    },
    fetchProductTechConfig() {
      if (this.$route.params.id !== undefined) {
        getProduct(this.$route.params.id).then((r) => {
          this.techDialog.form = {
            third_platform: r.data.third_platform,
            third_product_code: r.data.third_product_code,
            parameters: r.data.config.parameters
          }

          this.techDialog.visible = false
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-upload {
  .el-button {
    margin-right: 10px;
  }
}
</style>
