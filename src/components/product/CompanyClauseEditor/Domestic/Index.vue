<template>
  <el-dialog v-if="visible" :visible.sync="visible" width="500px" :title="title" :before-close="handleClose">
    <el-form ref="form" label-suffix=":" :model="form" :rules="rules" label-width="100px">
      <el-form-item prop="parent_id" label="主条款">
        <el-select v-model="form.parent_id" placeholder="请选择主险条款" filterable style="width: 100%">
          <el-option :value="-1" label="无" />
          <el-option v-for="clause in parentClauses" :key="clause.id" :value="clause.id" :label="clause.name" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.parent_id === -1" prop="is_allow_used" label="旧货可用">
        <el-switch
          inactive-text="否"
          active-text="是"
          :active-value="1"
          :inactive-value="0"
          v-model="form.is_allow_used"
        />
      </el-form-item>
      <el-form-item prop="name" label="条款名称">
        <el-input v-model="form.name" placeholder="请输入条款名称" />
      </el-form-item>
      <el-form-item prop="identifier" label="标识符">
        <el-input v-model="form.identifier" placeholder="请输入标识符" />
      </el-form-item>
      <el-form-item prop="content" label="条款内容">
        <el-input v-model="form.content" type="textarea" :rows="5" placeholder="请输入条款内容" />
      </el-form-item>
      <el-form-item prop="clause_file" label="条款文件">
        <upload-file v-model="form.clause_file" />
        <template v-if="typeof form.clause_file === 'string' && form.clause_file.indexOf('http') !== -1">
          <el-link :href="form.clause_file" target="_blank">查看文件</el-link>
        </template>
      </el-form-item>
      <el-form-item prop="is_enabled" label="是否启用">
        <el-switch
          inactive-text="否"
          active-text="是"
          :active-value="1"
          :inactive-value="0"
          v-model="form.is_enabled"
        ></el-switch>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
export default {
  name: 'DomesticClauseEditor',
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    clauses: {
      type: Array,
      default: () => []
    },
    model: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      form: {
        type: 1,
        parent_id: -1,
        is_allow_used: false,
        identifier: '',
        name: '',
        content: '',
        clause_file: '',
        is_enabled: 1
      },
      rules: {
        type: [{ required: true, message: '请选择条款险种', trigger: ['change', 'blur'] }],
        parent_id: [{ required: true, message: '请选择父条款', trigger: ['change', 'blur'] }],
        is_allow_used: [{ required: true, message: '是否允许旧货可用', trigger: ['change'] }],
        identifier: [{ required: true, message: '请输入条款标识符', trigger: 'blur' }],
        name: [{ required: true, message: '请输入条款名称', trigger: 'blur' }],
        content: [{ required: true, message: '请输入条款内容', trigger: 'blur' }],
        is_enabled: [{ required: true, message: '请选择是否启用', trigger: 'blur' }]
      }
    }
  },
  computed: {
    title() {
      return this.form.id !== undefined ? '编辑条款' : '添加条款'
    },
    parentClauses() {
      return this.clauses.filter((item) => {
        return item.parent_id === -1
      })
    }
  },
  watch: {
    model(value) {
      if (value.id !== undefined) {
        value.is_enabled = parseInt(value.is_enabled, 10)
        this.form.id = value.id
        Object.keys(this.form).forEach((k) => {
          if (value[k] !== undefined) {
            this.$set(this.form, k, value[k])
          }
        })
      }
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          let data = Object.assign({}, this.form)
          if (data.parent_id === -1) delete data.parent_id

          this.$emit('submit', data)

          this.handleClose()
        }
      })
    },
    handleClose() {
      this.form = {
        type: 1,
        parent_id: -1,
        is_allow_used: false,
        identifier: '',
        name: '',
        content: '',
        clause_file: '',
        is_enabled: 1
      }

      this.$refs.form.resetFields()
      this.$emit('update:visible', false)
    }
  }
}
</script>
