<template>
  <el-dialog
    :title="currentDialogTitle"
    :visible.sync="visible"
    width="520px"
    :before-close="handleBeforeClose"
    destroy-on-close
  >
    <el-form ref="form" label-suffix=":" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="标识符" prop="identifier">
        <el-input v-model="form.identifier" placeholder="请输入保险公司标识符" />
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入保险公司名称" />
      </el-form-item>
      <el-form-item label="LOGO" prop="logo">
        <el-upload
          :on-change="handleLogoChange"
          action=""
          :file-list="logo"
          :show-file-list="false"
          :auto-upload="false"
        >
          <el-button size="small" type="primary">点击上传</el-button>
          <p class="logo-preview">
            <img v-if="logoPreviewURL" :src="logoPreviewURL" alt="ICON" />
          </p>
        </el-upload>
      </el-form-item>
      <el-form-item label="是否启用" prop="is_enabled">
        <el-switch
          active-text="是"
          inactive-text="否"
          :active-value="1"
          :inactive-value="0"
          v-model="form.is_enabled"
        ></el-switch>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleBeforeClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
export default {
  name: 'CompanyEditor',
  props: {
    model: {
      type: Object,
      default: () => {}
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      logo: [],
      logoPreviewURL: '',
      form: {
        identifier: '',
        name: '',
        logo: '',
        is_enabled: '1'
      },
      rules: {
        identifier: [{ required: true, message: '请输入保险公司标识符', trigger: 'blur' }],
        name: [{ required: true, message: '请输入保险公司名称', trigger: 'blur' }],
        logo: [{ required: true, message: '请上传保险公司 LOGO', trigger: ['blur', 'change'] }],
        is_enabled: [{ required: true, message: '请选择是否启用', type: 'number', trigger: ['blur', 'change'] }]
      }
    }
  },
  computed: {
    currentDialogTitle() {
      return this.form && this.form.id ? '修改保险公司' : '添加保险公司'
    }
  },
  watch: {
    model: {
      immediate: true,
      deep: true,
      handler(value) {
        if (value) {
          this.form = Object.assign({}, value)
          this.logoPreviewURL = this.form.logo
        }
      }
    }
  },
  methods: {
    handleLogoChange(file) {
      this.logo = [file]
      this.logoPreviewURL = URL.createObjectURL(file.raw)
      this.form.logo = file.raw
    },
    handleBeforeClose() {
      this.$emit('update:visible', false)
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('update:visible', false)
          this.$emit('submit', this.form)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-upload {
  .el-button {
    float: left;
  }
}

.logo-preview img {
  padding-top: 10px;
  max-height: 100px;
}
</style>
