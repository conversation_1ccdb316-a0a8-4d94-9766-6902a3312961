<template>
  <el-dialog
    :title="currentDialogTitle"
    :visible.sync="visible"
    width="520px"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form ref="form" label-suffix=":" :model="form" :rules="rules" label-width="120px">
      <el-form-item prop="name" label="出单公司名称">
        <el-input v-model="form.name" placeholder="请输入出单公司名称" />
      </el-form-item>
      <el-form-item prop="contact" label="联系人">
        <el-input v-model="form.contact" placeholder="请输入联系人" />
      </el-form-item>
      <el-form-item prop="phone_number" label="手机号">
        <el-input v-model="form.phone_number" placeholder="请输入联系电话" />
      </el-form-item>
      <el-form-item prop="email" label="邮箱">
        <el-input v-model="form.email" placeholder="请输入邮箱" />
      </el-form-item>
      <el-form-item prop="bank_name" label="开户行">
        <el-input v-model="form.bank_name" placeholder="请输入开户行" />
      </el-form-item>
      <el-form-item prop="bank_no" label="行号">
        <el-input v-model="form.bank_no" placeholder="请输入行号" />
      </el-form-item>
      <el-form-item prop="bank_card_holder" label="账户名称">
        <el-input v-model="form.bank_card_holder" placeholder="请输入账户名称" />
      </el-form-item>
      <el-form-item prop="bank_card_no" label="账号">
        <el-input v-model="form.bank_card_no" placeholder="请输入账号" />
      </el-form-item>
      <el-form-item prop="invoice_email" label="发票邮箱">
        <el-input v-model="form.invoice_email" placeholder="请输入发票邮箱" />
      </el-form-item>
      <el-form-item prop="settlement_email" label="销账邮箱">
        <el-input v-model="form.settlement_email" placeholder="请输入销账邮箱" />
      </el-form-item>
      <el-form-item prop="inquiry_email" label="询价邮箱">
        <el-input v-model="form.inquiry_email" placeholder="请输入询价邮箱" />
      </el-form-item>
      <el-form-item prop="is_enabled" label="是否启用">
        <el-switch
          active-text="是"
          inactive-text="否"
          :active-value="1"
          :inactive-value="0"
          v-model="form.is_enabled"
        ></el-switch>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
export default {
  name: 'CompanyBranchEditor',
  props: {
    model: {
      type: Object,
      default: () => {}
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        name: '',
        contact: '',
        phone_number: '',
        email: '',
        invoice_email: '',
        settlement_email: '',
        inquiry_email: '',
        bank_name: '',
        bank_no: '',
        bank_card_holder: '',
        bank_card_no: '',
        is_enabled: 1
      },
      rules: {
        name: [{ required: true, message: '请输入出单公司名称', trigger: 'blur' }],
        contact: [{ required: true, message: '请输入出单公司联系人', trigger: 'blur' }],
        phone_number: [
          { required: true, message: '请输入出单公司联系方式', trigger: 'blur' },
          { min: 11, max: 11, message: '手机号长度错误', trigger: 'blur' }
        ],
        email: [{ required: true, type: 'email', message: '请输入出单公司联系邮箱', trigger: 'blur' }],
        bank_name: [{ required: true, message: '请输入出单公司账户名称', trigger: 'blur' }],
        bank_card_holder: [{ required: true, message: '请输入出单公司账号', trigger: 'blur' }],
        bank_card_no: [{ required: true, message: '请输入出单公司银行账号', trigger: 'blur' }],
        invoice_email: [{ required: false, message: '请输入发票邮箱', trigger: 'blur' }],
        settlement_email: [{ required: false, message: '请输入销账邮箱', trigger: 'blur' }],
        is_enabled: [{ required: true, message: '请选择是否启用', trigger: ['change', 'blur'] }]
      }
    }
  },
  computed: {
    currentDialogTitle() {
      return this.form.id === undefined ? '添加出单公司' : '编辑出单公司'
    }
  },
  watch: {
    model: {
      immediate: true,
      deep: true,
      handler(value) {
        if (value) {
          this.form = Object.assign({}, value)
        }
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
      this.$refs.form.resetFields()
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('submit', this.form)

          this.handleClose()
        }
      })
    }
  }
}
</script>
