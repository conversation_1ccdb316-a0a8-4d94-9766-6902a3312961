<template>
  <el-dialog
    :title="currentDialogTitle"
    :visible.sync="visible"
    width="680px"
    :before-close="handleBeforeClose"
    destroy-on-close
  >
    <el-form ref="form" label-suffix=":" :model="form" :rules="rules" label-width="150px">
      <el-form-item label="名称" prop="title">
        <el-input v-model="form.title" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item label="意外身故/残疾" prop="accidental_death">
        <el-input v-model="form.accidental_death" type="textarea" />
      </el-form-item>
      <el-form-item label="附加24小时意外伤害" prop="accidental_death">
        <el-input v-model="form.accidental_injury" type="textarea" />
      </el-form-item>
      <el-form-item label="意外医疗" prop="accidental_medical">
        <el-input v-model="form.accidental_medical" type="textarea" />
      </el-form-item>
      <el-form-item label="误工费" prop="lost_wages">
        <el-input v-model="form.lost_wages" type="textarea" />
      </el-form-item>
      <el-form-item label="意外伤害住院津贴" prop="accidental_allowance">
        <el-input v-model="form.accidental_allowance" type="textarea" />
      </el-form-item>
      <el-form-item label="雇主法律责任" prop="legal_liability">
        <el-input v-model="form.legal_liability" type="textarea" />
      </el-form-item>
      <el-form-item label="累计赔偿限额" prop="total_indemnity">
        <el-input v-model="form.total_indemnity" type="textarea" />
      </el-form-item>
      <el-form-item label="是否启用" prop="is_enabled">
        <el-switch
          active-text="是"
          inactive-text="否"
          :active-value="1"
          :inactive-value="0"
          v-model="form.is_enabled"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="fas fa-times" @click="handleBeforeClose">取 消</el-button>
        <el-button icon="fas fa-check" type="primary" @click="handleSubmit">确 定</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
export default {
  name: 'GroupProductPlanEditor',
  props: {
    model: {
      type: [Object, PointerEvent],
      default: () => {}
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      logoPreviewURL: '',
      form: {
        title: '',
        accidental_death: '',
        accidental_injury: '',
        accidental_medical: '',
        lost_wages: '',
        accidental_allowance: '',
        legal_liability: '',
        total_indemnity: '',
        is_enabled: 1
      },
      rules: {
        title: [{ required: true, message: '请输入套餐名称', trigger: 'blur' }],
        is_enabled: [{ required: true, type: 'number', message: '是否启用必填', trigger: ['blur', 'change'] }]
      }
    }
  },
  computed: {
    currentDialogTitle() {
      return this.form && this.form.id ? '修改套餐' : '添加套餐'
    }
  },
  watch: {
    model: {
      immediate: true,
      deep: true,
      handler(value) {
        if (value) {
          this.form = Object.assign({}, value)
        }
      }
    }
  },
  methods: {
    handleBeforeClose() {
      this.$emit('update:visible', false)
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('update:visible', false)
          this.$emit('submit', this.form)
        }
      })
    }
  }
}
</script>
