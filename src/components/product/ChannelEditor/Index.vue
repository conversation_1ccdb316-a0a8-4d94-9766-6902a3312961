<template>
  <el-dialog
    :title="currentDialogTitle"
    :visible.sync="visible"
    width="520px"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form ref="form" label-suffix=":" :model="form" :rules="rules" label-width="120px">
      <el-form-item prop="name" label="渠道名称">
        <el-input v-model="form.name" placeholder="请输入渠道名称" />
      </el-form-item>
      <el-form-item prop="contact" label="联系人">
        <el-input v-model="form.contact" placeholder="请输入联系人" />
      </el-form-item>
      <el-form-item prop="phone_number" label="手机号">
        <el-input v-model="form.phone_number" placeholder="请输入联系电话" />
      </el-form-item>
      <el-form-item prop="email" label="邮箱">
        <el-input v-model="form.email" placeholder="请输入邮箱" />
      </el-form-item>
      <el-form-item prop="bank_name" label="开户行">
        <el-input v-model="form.bank_name" placeholder="请输入开户行" />
      </el-form-item>
      <el-form-item prop="bank_card_holder" label="户名">
        <el-input v-model="form.bank_card_holder" placeholder="请输入户名" />
      </el-form-item>
      <el-form-item prop="bank_card_no" label="银行卡号">
        <el-input v-model="form.bank_card_no" placeholder="请输入银行卡号" />
      </el-form-item>
      <el-form-item prop="settlement_ratio" label="结算比例">
        <el-input v-model="form.settlement_ratio" placeholder="请输入结算比例">
          <template slot="append">%</template>
        </el-input>
      </el-form-item>
      <!-- <el-form-item prop="commission_rate" label="佣金比例">
        <el-input v-model="form.commission_rate" placeholder="请输入佣金比例" />
      </el-form-item> -->
      <el-form-item prop="remark" label="备注">
        <el-input v-model="form.remark" placeholder="请输入备注" />
      </el-form-item>
      <el-form-item prop="platform_ids" label="平台">
        <el-select class="w-100" v-model="form.platform_ids" multiple placeholder="请选择平台">
          <el-option v-for="item in platforms" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="is_enabled" label="是否启用">
        <el-switch
          active-text="是"
          inactive-text="否"
          :active-value="1"
          :inactive-value="0"
          v-model="form.is_enabled"
        ></el-switch>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
export default {
  name: 'Channel',
  props: {
    model: {
      type: Object,
      default: () => {}
    },
    visible: {
      type: Boolean,
      default: false
    },
    platforms: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      form: {
        name: '',
        contact: '',
        phone_number: '',
        email: '',
        bank_name: '',
        bank_card_holder: '',
        bank_card_no: '',
        settlement_ratio: 0.0,
        // commission_rate: 0,
        remark: '',
        platform_ids: [],
        is_enabled: 1
      },
      rules: {
        name: [{ required: true, message: '请输入渠道名称', trigger: 'blur' }],
        contact: [{ required: true, message: '请输入渠道联系人', trigger: 'blur' }],
        phone_number: [{ required: true, message: '请输入渠道联系方式', trigger: 'blur' }],
        email: [{ required: true, type: 'email', message: '请输入渠道联系邮箱', trigger: 'blur' }],
        bank_name: [{ required: true, message: '请输入渠道开户行', trigger: 'blur' }],
        bank_card_holder: [{ required: true, message: '请输入账户名', trigger: 'blur' }],
        bank_card_no: [{ required: true, message: '请输入账号', trigger: 'blur' }],
        settlement_ratio: [{ required: true, message: '请输入结算比例', trigger: 'blur' }],
        // commission_rate: [{ required: true, message: '请输入佣金比例1', trigger: 'blur' }],
        is_enabled: [{ required: true, message: '请选择是否启用', trigger: ['change', 'blur'] }]
      }
    }
  },
  computed: {
    currentDialogTitle() {
      return this.form.id === undefined ? '添加保险渠道' : '编辑保险渠道'
    }
  },
  watch: {
    model: {
      immediate: true,
      deep: true,
      handler(value) {
        if (value) {
          this.form = Object.assign({}, value)
        }
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
      this.$refs.form.resetFields()
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('submit', this.form)

          this.handleClose()
        }
      })
    }
  }
}
</script>
