<template>
  <div>
    <el-dialog
      title="货物类别"
      :visible.sync="visible"
      width="800px"
      :before-close="handleBeforeClose"
      destroy-on-close
    >
      <el-header class="d-flex align-items-center p-none-x">
        <el-button
          v-can="{ name: 'companies.goods-types.create' }"
          icon="el-icon-circle-plus"
          type="primary"
          @click="formVisible = true"
        >
          添加货物类别
        </el-button>
      </el-header>

      <define-table :attrs="tableAttrs" :cols="cols" :data="treeData" />
    </el-dialog>

    <el-dialog
      :title="formTitle"
      :visible.sync="formVisible"
      :before-close="handleClose"
      destroy-on-close
      width="520px"
    >
      <el-form ref="form" label-suffix=":" :model="form" :rules="rules" label-width="120px">
        <el-form-item prop="parent_id" label="父类别">
          <el-select v-model="form.parent_id" placeholder="请选择父类别" style="width: 100%" filterable>
            <el-option :value="-1" label="无"></el-option>
            <el-option v-for="d in data" :key="d.id" :value="d.id" :label="d.name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="name" label="原始名">
          <el-input v-model="form.name" placeholder="请输入原始名" />
        </el-form-item>
        <el-form-item prop="display_name" label="显示名">
          <el-input v-model="form.display_name" placeholder="请输入显示名" />
        </el-form-item>
        <el-form-item prop="value" label="保险公司对应值">
          <el-input v-model="form.value" placeholder="请输入保险公司对应值" />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import DefineTable from '../../globals/DefineTable/DefineTable.vue'
export default {
  components: { DefineTable },
  name: 'CompanyGoodsTypes',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    model: {
      type: Object,
      default: () => {}
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    formTitle() {
      return this.form.id === undefined ? '添加类别' : '编辑类别'
    },
    treeData() {
      const data = this.data
        .filter((e) => e.parent_id === -1)
        .map((e) => {
          e.is_enabled = e?.is_enabled?.toString()

          e.hasChildren = this.data.find((f) => f.parent_id === e.id) ? true : false
          return e
        })

      return data
    }
  },
  data() {
    return {
      formVisible: false,
      form: {
        parent_id: '',
        name: '',
        display_name: '',
        value: '',
        is_enabled: 1
      },
      rules: {
        parent_id: [{ required: true, message: '请选择父类', trigger: ['blur', 'change'] }],
        name: [{ required: true, message: '请输入原始名', trigger: 'blur' }],
        display_name: [{ required: true, message: '请输入显示名', trigger: 'blur' }]
      },
      tableAttrs: {
        rowKey: 'id',
        border: false,
        lazy: true,
        load: (evt, treeNode, resolve) => {
          resolve(this.data.filter((e) => e.parent_id === evt.id))
        },
        defaultExpandAll: false,
        treeProps: { children: 'children', hasChildren: 'hasChildren' }
      },
      cols: [
        {
          label: '原始名称',
          prop: 'name'
        },
        {
          label: '显示名称',
          prop: 'display_name'
        },
        {
          label: '值',
          prop: 'value'
        },
        {
          label: '操作',
          width: 85,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    v-can={{ name: 'companies.goods-types.update' }}
                    size="small"
                    type="text"
                    onClick={() => this.handleUpdate(scoped.row)}
                  >
                    编辑
                  </el-button>
                  <el-popconfirm
                    v-can={{ name: 'companies.goods-types.delete' }}
                    style="margin-left: 10px"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    icon="el-icon-info"
                    icon-color="red"
                    title="确定删除吗？"
                    onConfirm={() => this.$emit('delete', scoped.row)}
                  >
                    <el-button size="small" type="text" slot="reference">
                      删除
                    </el-button>
                  </el-popconfirm>
                </div>
              )
            }
          }
        }
      ]
    }
  },
  methods: {
    handleBeforeClose() {
      this.$emit('update:visible', false)
    },
    handleClose() {
      this.formVisible = false
      this.$refs.form.resetFields()

      this.form = {
        parent_id: '',
        name: '',
        display_name: '',
        value: '',
        is_enabled: 1
      }
    },
    handleUpdate(rowData) {
      rowData.expire_on = Date.parse(rowData.expire_on) || Date.now()
      this.form = rowData
      this.formVisible = true
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.formVisible = false

          let data = Object.assign({}, this.form)
          if (data.parent_id === '-1' || data.parent_id === -1) {
            delete data.parent_id
          }

          this.$emit('submit', data)

          this.handleClose()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0px !important;
}

/deep/ .el-select-dropdown__item {
  max-width: 360px;
}
</style>
