<template>
  <div>
    <el-dialog :title="attr.title" :visible.sync="visible" width="800px" :before-close="handleClose" destroy-on-close>
      <el-header class="d-flex align-items-center p-none-x">
        <el-button v-can="{ name: attr.create }" icon="el-icon-circle-plus" type="primary" @click="formVisible = true">
          添加{{ attr.title }}
        </el-button>
      </el-header>
      <define-table :data.sync="data" :cols.sync="cols" />
    </el-dialog>

    <el-dialog ref="form" :title="formTitle" :visible.sync="formVisible" destroy-on-close width="520px">
      <el-form ref="form" :model="form" :rules="rules">
        <el-form-item prop="value" label="保险公司值">
          <el-input v-model="form.value" placeholder="请输入保险公司值"></el-input>
        </el-form-item>
        <el-form-item prop="name" label="保险公司名称">
          <el-input v-model="form.name" placeholder="请输入保险公司名称"></el-input>
        </el-form-item>
        <el-form-item prop="loading_method_ids" label="装载方式">
          <el-select class="w-100" v-model="form.loading_method_ids" multiple placeholder="请选择装载方式">
            <el-option v-for="item in loadingMethods" :key="item.id" :label="item.value" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="display_name" label="展示名称">
          <el-input v-model="form.display_name" placeholder="请输入展示名称"></el-input>
        </el-form-item>

        <el-form-item>
          <el-button @click="formVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'CompanyTransportMethods',
  props: {
    attr: {
      type: Object,
      default: () => {
        return {
          title: '',
          create: '',
          update: '',
          delete: ''
        }
      }
    },
    data: {
      type: Array,
      default: () => []
    },
    loadingMethods: {
      type: Array,
      default: () => []
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      cols: [
        {
          label: '保险公司值',
          prop: 'value'
        },
        {
          label: '保险公司名称',
          prop: 'name'
        },
        {
          label: '展示名称',
          prop: 'display_name'
        },
        {
          label: '操作',
          fixed: 'right',
          width: 85,
          scopedSlots: {
            default: (scoped) => {
              return (
                <div>
                  <el-button
                    v-can={{ name: this.attr.update }}
                    size="small"
                    type="text"
                    onClick={() => this.handleUpdate(scoped.row)}
                  >
                    编辑
                  </el-button>
                  <el-popconfirm
                    v-can={{ name: this.attr.delete }}
                    style="margin-left: 10px"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    icon="el-icon-info"
                    icon-color="red"
                    title="确定删除吗？"
                    onConfirm={() => this.$emit('delete', scoped.row)}
                  >
                    <el-button size="small" type="text" slot="reference">
                      删除
                    </el-button>
                  </el-popconfirm>
                </div>
              )
            }
          }
        }
      ],
      formVisible: false,
      form: {
        name: '',
        vlaue: '',
        display_name: '',
        loading_method_ids: []
      },
      rules: {
        name: [{ required: true, message: '保险公司名称必填', trigger: 'blur' }],
        display_name: [{ required: true, message: '展示名称必填', trigger: 'blur' }],
        loading_method_ids: [{ required: true, message: '请选择装载方式', trigger: ['blur', 'change'] }]
      }
    }
  },
  computed: {
    formTitle() {
      return this.form.id === undefined ? '添加' + this.attr.title : '编辑' + this.attr.title
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
    },
    handleUpdate(rowData) {
      this.form = rowData
      this.formVisible = true
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.formVisible = false

          this.$emit('submit', this.form)

          this.form = {
            name: '',
            vlaue: '',
            display_name: ''
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0px !important;
}
</style>
