<template>
  <div class="p-extra-large-b">
    <el-card class="table-wrap" shadow="never">
      <el-form ref="form" :model="form" :rules="rules" label-suffix=":" label-width="100px" label-position="top">
        <section>
          <h3>基本信息</h3>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item prop="name" label="产品名称">
                <el-input v-model="form.name" placeholder="请输入产品名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="insurance_id" label="险类">
                <el-select
                  v-model="form.insurance_id"
                  placeholder="请选择险类"
                  clearable
                  filterable
                  style="width: 100%"
                >
                  <el-option
                    v-for="insurance in insurances"
                    :key="insurance.id"
                    :value="insurance.id"
                    :label="insurance.name"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="category_id" label="险种">
                <el-select v-model="form.category_id" placeholder="请选择险种" clearable filterable style="width: 100%">
                  <el-option v-for="type in types" :key="type.id" :value="type.id" :label="type.name" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="template_id" label="产品模板">
                <el-select v-model="form.template_id" placeholder="请选择险类" clearable filterable style="width: 100%">
                  <el-option
                    v-for="template in templates"
                    :key="template.id"
                    :value="template.id"
                    :label="template.name"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </section>
        <section>
          <h3 v-if="templateFields != ''">
            其他信息 <span style="color: #ff7f4c; font-size: 12px">(以下都为必填项)</span>
          </h3>
          <el-row :gutter="48">
            <el-col :span="12" v-for="fields in templateFields" :key="fields.name">
              <el-form-item :prop="fields.name" :label="fields.title">
                <div slot="label" v-if="fields.file">
                  {{ fields.title }}
                  <span><a :href="fields.file" target="_blank">模板</a></span>
                </div>
                <el-input
                  v-model="form[fields.name]"
                  v-if="fields.fields_type == 'text'"
                  :placeholder="'请输入' + fields.title"
                />
                <el-input
                  v-if="fields.fields_type == 'textarea'"
                  v-model="form[fields.name]"
                  type="textarea"
                  :rows="3"
                  :placeholder="'请输入' + fields.title"
                />
                <el-select
                  v-if="fields.fields_type == 'select'"
                  v-model="form[fields.name]"
                  :placeholder="'请输入' + fields.title"
                  clearable
                  filterable
                  style="width: 100%"
                >
                  <el-option v-for="value in fields.options" :key="value" :value="value" :label="value" />
                </el-select>
                <template v-if="fields.fields_type === 'file' || fields.fields_type === '_file'">
                  <upload-file
                    v-model="form[fields.name]"
                    accept=".jpeg,.png,.pdf,.jpg,.zip"
                    :limitSize="2"
                  ></upload-file>

                  <el-link
                    v-if="typeof form[fields.name] === 'string' && form[fields.name] && fields.fields_type === '_file'"
                    :href="form[fields.name]"
                    target="_blank"
                  >
                    点击查看
                  </el-link>
                  <span class="text-danger"> .jpeg, .png, .pdf, .jpg, .zip 文件，大小不能超过 2M</span>
                </template>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item>
                <el-button @click="$refs.form.resetFields()">清 空</el-button>
                <el-button type="primary" @click="handleSubmit">确 定</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </section>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import {
  getOfflineProductCategories,
  getOfflineProductTemplates,
  createOfflineProduct,
  updateOfflineProduct
} from '@/apis/product'
import { Loading } from 'element-ui'

export default {
  name: 'OfflineProductForm',
  props: {
    model: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      insurances: [],
      templates: [],
      form: {
        name: '',
        platform_id: '',
        insurance_id: '',
        category_id: '',
        template_id: ''
      },
      rules: {
        name: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
        platform_id: [{ required: true, message: '请选择运营平台', trigger: ['blur', 'change'] }],
        insurance_id: [{ required: true, message: '请选择险类', trigger: ['blur', 'change'] }],
        category_id: [{ required: true, message: '请选择险别', trigger: ['blur', 'change'] }],
        template_id: [{ required: true, message: '请选择产品模板', trigger: ['blur', 'change'] }]
      }
    }
  },
  computed: {
    types() {
      const insurance = this.insurances.find((e) => e.id == this.form.insurance_id)
      return insurance?.children || []
    },
    templateFields() {
      const template = this.templates.find((e) => e.id == this.form.template_id)
      template?.product_fields.forEach((value) => {
        if (this.$route.params.id == undefined) {
          this.$set(this.form, value.name, '')
        }
        if (value.fields_type != 'file' && value.fields_type != '_file') {
          this.rules[value.name] = [{ required: true, message: '请输入' + value.title, trigger: ['blur', 'change'] }]
        }
      })
      return template?.product_fields || []
    }
  },
  watch: {
    model: {
      immediate: true,
      deep: true,
      handler(value) {
        if (Object.keys(value).length > 0) {
          this.form = Object.assign({}, value)
        }
      }
    }
  },
  created() {
    getOfflineProductCategories({ is_parent: 1, is_pageable: 0 }).then((r) => (this.insurances = r.data))
    getOfflineProductTemplates({ is_pageable: 0 }).then((r) => (this.templates = r.data))
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate((valid, errors) => {
        if (valid) {
          const data = Object.assign({}, this.form)
          const action =
            this.$route.params.id === undefined ? createOfflineProduct(data) : updateOfflineProduct(this.form.id, data)

          const loading = Loading.service()
          action
            .then(() => {
              this.$message.success('保存成功')

              this.$router.push({
                name: 'OfflineProducts'
              })
            })
            .finally(() => loading.close())
        } else {
          window.alert_validate_errors(errors)
        }
      })
    }
  }
}
</script>
