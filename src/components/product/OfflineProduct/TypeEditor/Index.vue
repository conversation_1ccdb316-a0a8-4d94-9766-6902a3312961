<!--
 * @Descripttion: 
 * @version: 
 * @Author: yanb
 * @Date: 2022-06-01 17:19:00
 * @LastEditors: yanb
 * @LastEditTime: 2022-06-02 10:14:39
-->
<template>
  <el-dialog
    :title="currentDialogTitle"
    :visible.sync="visible"
    width="520px"
    :before-close="handleBeforeClose"
    destroy-on-close
  >
    <el-form ref="form" label-suffix=":" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="险类" prop="parent_id">
        <el-select v-model="form.parent_id" placeholder="请选择险类" class="w-100">
          <el-option v-for="b in insurances" :key="b.id" :value="b.id" :label="b.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入险种名称" />
      </el-form-item>
      <el-form-item label="是否启用" prop="is_enabled">
        <el-switch
          active-text="是"
          inactive-text="否"
          :active-value="1"
          :inactive-value="0"
          v-model="form.is_enabled"
        ></el-switch>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleBeforeClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import { getOfflineProductCategories } from '@/apis/product'

export default {
  name: 'OfflineProductTypeEditor',
  props: {
    model: {
      type: Object,
      default: () => {}
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      insurances: [],
      form: {
        parent_id: -1,
        name: '',
        is_enabled: 1
      },
      rules: {
        parent_id: [{ required: true, message: '请选择险类', trigger: ['blur', 'change'] }],
        name: [{ required: true, message: '请输入险种名称', trigger: 'blur' }],
        is_enabled: [{ required: true, message: '请选择是否启用', type: 'number', trigger: ['blur', 'change'] }]
      }
    }
  },
  computed: {
    currentDialogTitle() {
      return this.form && this.form.id ? '修改险种' : '添加险种'
    }
  },
  created() {
    getOfflineProductCategories({ is_parent: 1, is_pageable: 0 }).then((r) => (this.insurances = r.data))
  },
  watch: {
    model: {
      immediate: true,
      deep: true,
      handler(value) {
        if (value) {
          this.form = Object.assign({}, value)
        }
      }
    }
  },
  methods: {
    handleBeforeClose() {
      this.$emit('update:visible', false)
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('update:visible', false)
          this.$emit('submit', this.form)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-upload {
  .el-button {
    float: left;
  }
}

.logo-preview img {
  padding-top: 10px;
  max-height: 100px;
}
</style>
