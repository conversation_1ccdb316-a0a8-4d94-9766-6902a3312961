<!--
 * @Descripttion: 
 * @version: 
 * @Author: yanb
 * @Date: 2022-06-01 17:19:00
 * @LastEditors: yanb
 * @LastEditTime: 2024-06-13 15:15:52
-->
<template>
  <el-dialog
    :title="currentDialogTitle"
    :visible.sync="visible"
    width="520px"
    :before-close="handleBeforeClose"
    destroy-on-close
  >
    <el-form ref="form" label-suffix=":" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="字段类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择字段类型" class="w-100">
          <el-option label="单行文本" value="text"></el-option>
          <el-option label="多行文本" value="textarea"></el-option>
          <el-option label="时间选择" value="date"></el-option>
          <el-option label="下拉列表" value="select"></el-option>
          <el-option label="文件上传" value="file"></el-option>
          <el-option label="文件上传(带附件)" value="_file"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="名称" prop="title">
        <el-input v-model="form.title" placeholder="请输入字段名称" />
      </el-form-item>
      <el-form-item label="排序" prop="order">
        <el-input v-model="form.order" placeholder="请输入排序序号" type="number" />
      </el-form-item>
      <el-form-item label="下拉选项: " v-if="form.type === 'select'" prop="textarea">
        <el-input v-model="form.options" type="textarea" :rows="4" placeholder="选项与选项请使用 ｜ 隔开"></el-input>
      </el-form-item>
      <el-form-item label="附件：" v-if="form.type === '_file'" prop="file">
        <upload-file v-model="form.file"></upload-file>
        <el-link
          v-if="form.file != ''"
          icon="el-icon-view"
          style="position: absolute; left: 120px; top: 0"
          @click="previewFile(form.file)"
          >点击查看</el-link
        >
      </el-form-item>
      <el-form-item>
        <el-button @click="handleBeforeClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
export default {
  name: 'FieldsEditor',
  props: {
    model: {
      type: Object,
      default: () => {}
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        title: '',
        name: '',
        type: '',
        order: 0,
        options: '',
        file: ''
      },
      rules: {
        title: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
        name: [{ required: true, message: '请输入标识', trigger: 'blur' }],
        type: [{ required: true, message: '请选择字段类型', trigger: ['blur', 'change'] }],
        order: [{ required: false, message: '请输入序号', trigger: 'blur' }]
      }
    }
  },
  computed: {
    currentDialogTitle() {
      return this.form && this.form.id ? '修改字段' : '添加字段'
    }
  },
  watch: {
    model: {
      immediate: true,
      deep: true,
      handler(value) {
        if (Object.keys(value).length > 0) {
          this.form = Object.assign({}, value)
        } else {
          this.form = {
            title: '',
            name: '',
            type: '',
            order: 0,
            options: '',
            file: ''
          }
        }
      }
    }
  },
  methods: {
    handleBeforeClose() {
      this.$emit('update:visible', false)
    },
    handleSubmit() {
      this.form.name = this.form.title
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('update:visible', false)
          this.$emit('submit', this.form)
        }
      })
    },
    previewFile(path) {
      window.open(path)
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-upload {
  .el-button {
    float: left;
  }
}

.logo-preview img {
  padding-top: 10px;
  max-height: 100px;
}
</style>
