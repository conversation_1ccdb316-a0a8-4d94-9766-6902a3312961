<template>
  <el-select v-model="selected" placeholder="请选择国家/地区" class="w-100" multiple filterable>
    <el-option v-for="(name, value) in regions" :key="value" :value="value" :label="name" />
  </el-select>
</template>

<script>
import regions from '@/utils/regions'

export default {
  name: 'CountryRegionSelect',
  props: {
    value: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      regions,
      selected: []
    }
  },
  watch: {
    value(value) {
      this.selected = value
    },
    selected(value) {
      this.$emit('input', value)
    }
  }
}
</script>
