<template>
  <el-dialog :title="title" width="800px" :visible.sync="visible" :before-close="handleBeforeClose" destroy-on-close>
    <el-checkbox v-if="multiple" v-model="checkAll" @change="handleCheckAll">全选</el-checkbox>
    <el-tree
      class="m-mini-t"
      ref="treeData"
      :data="data"
      highlight-current
      show-checkbox
      node-key="id"
      :default-checked-keys="checked"
      @check-change="handleClick"
    >
    </el-tree>

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleBeforeClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'CargoFormDialog',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: ''
    },
    visible: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    },
    checked: {
      type: Array,
      default: () => []
    },
    multiple: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      checkAll: false
    }
  },
  methods: {
    handleCheckAll() {
      if (this.checkAll) {
        this.$refs.treeData.setCheckedNodes(this.data)
      } else {
        this.$refs.treeData.setCheckedKeys([])
      }
    },
    handleBeforeClose() {
      this.$emit('update:visible', false)
    },
    handleClick(data, checked) {
      if (!this.multiple && checked) {
        this.$refs.treeData.setCheckedNodes([data])
      }
    },
    handleSubmit() {
      this.$emit('submit', this.$refs.treeData.getCheckedKeys())

      this.handleBeforeClose()
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-tree-node {
  overflow: auto;
}

/deep/ .el-dialog__body {
  padding: 10px 20px !important;
}
</style>
