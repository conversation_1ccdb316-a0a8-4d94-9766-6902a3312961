<template>
  <div class="p-extra-large-b">
    <el-card class="table-wrap" shadow="never">
      <div class="btn" style="float: right" v-if="$route.params.id && !disabled" v-can="{ name: 'products.update' }">
        <el-button type="primary" icon="fas fa-toggle-on" @click="handleUpdateStatus">
          {{ form.is_enabled ? '禁用' : '启用' }}
        </el-button>
        <el-button type="primary" icon="fas fa-copy" class="m-mini-l" @click="handleCopy"> 复制 </el-button>
      </div>
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-suffix=":"
        label-width="100px"
        label-position="top"
        :disabled="disabled"
      >
        <section>
          <h3>基本信息</h3>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item prop="name" label="产品名称">
                <el-input v-model="form.name" placeholder="请输入产品名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="platform_id" label="平台">
                <el-select v-model="form.platform_id" placeholder="请选择平台" clearable filterable style="width: 100%">
                  <el-option v-for="p in platforms" :key="p.id" :value="p.id" :label="p.name" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="company_id" label="保险公司">
                <el-select
                  v-model="form.company_id"
                  placeholder="请选择保险公司"
                  clearable
                  filterable
                  style="width: 100%"
                >
                  <el-option v-for="com in companies" :key="com.id" :value="com.id" :label="com.name" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="company_branch_id" label="出单公司">
                <el-select
                  v-model="form.company_branch_id"
                  placeholder="请选择出单公司"
                  clearable
                  filterable
                  style="width: 100%"
                >
                  <el-option v-for="com in companyBranches" :key="com.id" :value="com.id" :label="com.name" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="company_branch_account_id" label="账号">
                <el-select
                  v-model="form.company_branch_account_id"
                  placeholder="请选择投保账号"
                  clearable
                  filterable
                  style="width: 100%"
                >
                  <el-option v-for="com in companyBranchAccounts" :key="com.id" :value="com.id" :label="com.name" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="channel_id" label="投保渠道">
                <el-select
                  v-model="form.channel_id"
                  placeholder="请选择投保渠道"
                  clearable
                  filterable
                  style="width: 100%"
                >
                  <el-option v-for="com in channels" :key="com.id" :value="com.id" :label="com.name" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </section>
        <section>
          <h3>产品信息</h3>
          <el-row :gutter="48">
            <el-col :span="12" v-if="type !== types.lbt">
              <el-form-item prop="subject_id" label="标的">
                <el-select v-model="form.subject_id" placeholder="请选择标的" clearable filterable style="width: 100%">
                  <el-option v-for="subject in subjects" :key="subject.id" :value="subject.id" :label="subject.name" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="type === types.intl">
              <el-form-item
                prop="insure_types"
                label="贸易类型"
                :rules="[
                  {
                    required: true,
                    message: '请选择贸易类型',
                    trigger: ['blur', 'change']
                  }
                ]"
              >
                <el-select
                  v-model="form.insure_types"
                  placeholder="请选择贸易类型"
                  clearable
                  filterable
                  multiple
                  style="width: 100%"
                >
                  <el-option
                    v-for="insureType in [
                      { id: 1, name: '出口运输' },
                      { id: 2, name: '进口运输' },
                      { id: 3, name: '境外运输' }
                    ]"
                    :key="insureType.id"
                    :value="insureType.id"
                    :label="insureType.name"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item prop="transport_method_ids" label="运输方式">
                <el-button type="primary" @click="handleShowTransportMethod">选择运输方式</el-button>
                <span class="pl-10">已选择: {{ transportMethodNames }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="goods_type_ids" label="货物类别">
                <el-button type="primary" @click="handleShowGoodsType">选择货物类别</el-button>
                <span class="pl-10">已选择: {{ goodsTypeNames }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item prop="packing_method_ids" label="包装方式">
                <el-button type="primary" @click="handleShowPackingMethod">选择包装方式</el-button>
                <span class="pl-10">已选择: {{ packingMethodNames }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="loading_method_ids" label="装载方式">
                <el-button type="primary" @click="handleShowLoadingMethod">选择装载方式</el-button>
                <span class="pl-10">已选择: {{ loadingMethodNames }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="48">
            <el-col :span="24">
              <el-form-item label="保险条款" required>
                <el-tree
                  ref="treeClauses"
                  :data="treeClauses"
                  :props="{ children: 'children', label: 'name' }"
                  :default-checked-keys="defaultClausesCheckedKeys"
                  node-key="id"
                  show-checkbox
                  :accordion="true"
                  :check-strictly="true"
                  @check-change="handleClauseCheckChange"
                >
                </el-tree>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="48">
            <el-col :span="24">
              <el-form-item prop="disabled_regions" label="禁用区域">
                <template v-if="type !== types.intl">
                  <el-select v-model="form.disabled_regions" class="w-100" multiple>
                    <el-option v-for="d in areadata" :key="d.code" :value="d.value" :label="d.value" />
                  </el-select>
                </template>
                <template v-else>
                  <country-region-select v-model="form.disabled_regions" />
                </template>
              </el-form-item>
            </el-col>
          </el-row>
          <template v-if="type === types.intl">
            <el-row :gutter="48">
              <el-col :span="24">
                <el-form-item prop="war_strike_regions" label="战争罢工国家/地区">
                  <country-region-select v-model="form.war_strike_regions" />
                  <span style="color: #777; font-size: 12px">
                    选择后如客户投保选择列表中的国家及地区将无法选择在条款中配置了战争罢工禁用的条款。
                  </span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="48">
              <el-col :span="24">
                <el-form-item prop="sanctionist" label="制裁国家/地区">
                  <country-region-select v-model="form.sanctionist" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="48">
              <el-col :span="24">
                <el-form-item
                  prop="sanctionist_clause"
                  label="制裁国家/地区条款内容"
                  :required="form.sanctionist?.length > 0"
                  error="请输入制裁国家/地区条款内容"
                >
                  <el-input
                    type="textarea"
                    v-model="form.sanctionist_clause"
                    :autosize="{ minRows: 2, maxRows: 5 }"
                    placeholder="请输入制裁国家/地区条款内容"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="48">
              <el-col :span="24">
                <el-form-item prop="sanctionist" label="保障到港国家/地区">
                  <country-region-select v-model="form.only_port_regions" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="48">
              <el-col :span="24">
                <el-form-item
                  prop="only_port_clause"
                  label="保障到港国家/地区条款内容"
                  :required="form.only_port_regions?.length > 0"
                  error="请输入保障到港国家/地区条款内容"
                >
                  <el-input
                    type="textarea"
                    v-model="form.only_port_clause"
                    :autosize="{ minRows: 2, maxRows: 5 }"
                    placeholder="请输入保障到港国家/地区条款内容"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </template>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item prop="anti_dated_days" label="允许倒签时间">
                <template slot="label">
                  允许倒签时间
                  <span class="m-mini-l text-danger" style="margin-right: 5px"> 根据运输方式配置 </span>
                  <el-radio v-model="form.anti_date_is_in_transports" :label="1">是</el-radio>
                  <el-radio v-model="form.anti_date_is_in_transports" :label="0">否</el-radio>
                </template>
                <el-input v-model="form.anti_dated_days" placeholder="请输入允许倒签时间" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="anti_dated_file" label="倒签保函">
                <el-upload
                  action=""
                  :file-list="antiDatedFile"
                  :on-change="(file) => (form.anti_dated_file = file.raw)"
                  :show-file-list="true"
                  :auto-upload="false"
                >
                  <el-button size="small" type="primary">点击上传</el-button>
                </el-upload>
              </el-form-item>
              <el-link
                v-if="typeof form.anti_dated_file === 'string' && form.anti_dated_file"
                :href="form.anti_dated_file"
                target="_blank"
              >
                点击查看
              </el-link>
            </el-col>
          </el-row>
          <el-row :gutter="48" v-if="form.anti_date_is_in_transports === 1">
            <el-col v-for="transport in transportMethods" :key="transport?.id" :span="12">
              <el-form-item :label="transport?.display_name + '允许倒签时间'">
                <el-input
                  v-model="anti_date_transport_data[transport?.id]"
                  type="number"
                  :placeholder="'请输入' + transport?.display_name + '允许倒签时间'"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </section>

        <section>
          <h3>费用信息</h3>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item prop="service_charge" label="保险公司经纪费">
                <el-input v-model="form.service_charge" placeholder="请输入保险公司经纪费">
                  <template slot="append">%</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="rate" label="费率">
                <el-input v-model="form.rate" placeholder="请输入费率">
                  <template slot="append">‱</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="minimum_premium" label="最低保费">
                <el-input v-model="form.minimum_premium" placeholder="请输入最低保费">
                  <template slot="append">元</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="coverage" :label="coverageTitle">
                <el-input v-model="form.coverage" placeholder="请输入保额">
                  <template slot="append">元</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="platform_service_charge" label="平台出单费">
                <el-input v-model="form.platform_service_charge" placeholder="请输入平台出单费">
                  <template slot="append">元</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </section>

        <section>
          <h3>其他信息</h3>
          <el-row :gutter="48">
            <el-col :span="12">
              <el-form-item prop="deductible" label="免赔">
                <el-input type="textarea" :rows="3" v-model="form.deductible" placeholder="请输入免赔" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="special_agreement" label="特别约定">
                <el-input type="textarea" :rows="3" v-model="form.special_agreement" placeholder="请输入特别约定" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="48">
            <el-col :span="24">
              <el-form-item prop="except_subject" label="除外标的">
                <el-input type="textarea" :rows="3" v-model="form.except_subject" placeholder="请输入除外标的" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="48">
            <el-col :span="24">
              <el-form-item prop="notice" label="投保须知">
                <editor v-model="form.notice" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="48">
            <el-col :span="24">
              <el-form-item prop="inform" label="投保告知">
                <editor v-model="form.inform" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="48">
            <el-col :span="24">
              <el-form-item prop="config" label="其他配置(JSON)">
                <el-input type="textarea" :rows="3" v-model="form.config" placeholder="请输入其他配置" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="48" v-if="!disabled">
            <el-col :span="24">
              <el-form-item>
                <el-button @click="$refs.form.resetFields()">清 空</el-button>
                <el-button type="primary" @click="handleSubmit">确 定</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </section>

        <b-dialog
          title="请选择运输方式"
          :visible.sync="transportMethodDialog.visible"
          :data="transportMethodDialog.data"
          :checked="form.transport_method_ids"
          :multiple="true"
          @submit="(v) => (form.transport_method_ids = v)"
        ></b-dialog>

        <b-dialog
          title="请选择包装方式"
          :visible.sync="packingMethodDialog.visible"
          :data="packingMethodDialog.data"
          :checked="form.packing_method_ids"
          :multiple="true"
          @submit="(v) => (form.packing_method_ids = v)"
        ></b-dialog>

        <b-dialog
          title="请选择货物类别"
          :visible.sync="goodsTypesDialog.visible"
          :data="goodsTypesDialog.data"
          :checked="form.goods_type_ids"
          :multiple="true"
          @submit="(v) => (form.goods_type_ids = v)"
        ></b-dialog>

        <b-dialog
          title="请选择装载方式"
          :visible.sync="loadingMethodDialog.visible"
          :data="loadingMethodDialog.data"
          :checked="form.loading_method_ids"
          :multiple="true"
          @submit="(v) => (form.loading_method_ids = v)"
        ></b-dialog>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { getPlatformsDict } from '@/apis/platform'
import { getCompaniesDict } from '@/apis/company'
import { getCompanyBranchAccounts } from '@/apis/company_branch_account'
import { getChannels } from '@/apis/channel'
import { getSubjects } from '@/apis/subject'
import { getClauses } from '@/apis/company_clause'
import { getTransportMethods } from '@/apis/transport_method'
import { getPackingMethods } from '@/apis/packing_method'
import { getLoadingMethods } from '@/apis/loading_method'
import { getGoodsTypes } from '@/apis/goods_type'
import { createProduct, updateProduct, updateProductEnabled } from '@/apis/product'
import areadata from '@/utils/areadata.json'
import Dialog from './Dialog'
import { array2Tree } from '@/utils'
import { arraysEqual } from '@/utils'
import { Loading } from 'element-ui'
import CountryRegionSelect from './CountryRegionSelect'

const TYPE_DOMESTIC = 1 // 国内
const TYPE_INTL = 2 // 国际
const TYPE_LBT = 3 // 单车

export default {
  name: 'CargoForm',
  components: {
    'b-dialog': Dialog,
    CountryRegionSelect
  },
  props: {
    model: {
      type: Object,
      default: () => {}
    },
    type: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      isCopied: false,
      areadata,
      transportMethodDialog: {
        visible: false,
        data: []
      },
      packingMethodDialog: {
        visible: false,
        data: []
      },
      goodsTypesDialog: {
        visible: false,
        data: []
      },
      loadingMethodDialog: {
        visible: false,
        data: []
      },
      types: {
        domestic: TYPE_DOMESTIC,
        intl: TYPE_INTL,
        lbt: TYPE_LBT
      },
      platforms: [],
      companies: [],
      companyBranchAccounts: [],
      channels: [],
      subjects: [],
      clauses: [],
      antiDatedFile: [],
      form: {
        name: '',
        platform_id: '',
        company_id: '',
        company_branch_id: '',
        company_branch_account_id: '',
        channel_id: '',
        subject_id: '',
        main_clause_ids: [],
        additional_clause_ids: [],
        transport_method_ids: [],
        goods_type_ids: [],
        packing_method_ids: [],
        loading_method_ids: [],
        disabled_regions: [],
        sanctionist: [],
        sanctionist_clause: '',
        only_port_regions: [],
        only_port_clause: '',
        war_strike_regions: [],
        service_charge: '',
        rate: '',
        minimum_premium: '',
        coverage: '',
        platform_service_charge: '',
        deductible: '',
        special_agreement: '',
        except_subject: '',
        insure_types: [],
        notice: '',
        inform: '',
        config: '',
        is_enabled: 0,
        anti_dated_days: '',
        anti_dated_file: '',
        anti_date_is_in_transports: 0
      },
      anti_date_transport_data: [],
      transportMethods: [],
      rules: {
        name: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
        platform_id: [{ required: true, message: '请选择运营平台', trigger: ['blur', 'change'] }],
        company_id: [{ required: true, message: '请选择保险公司', trigger: ['blur', 'change'] }],
        company_branch_id: [{ required: true, message: '请选择保险出单公司', trigger: ['blur', 'change'] }],
        company_branch_account_id: [
          { required: this?.type !== TYPE_LBT, message: '请选择投保账号', trigger: ['blur', 'change'] }
        ],
        channel_id: [{ required: true, message: '请选择投保渠道', trigger: ['blur', 'change'] }],
        subject_id: [{ required: true, message: '请选择标的', trigger: ['blur', 'change'] }],
        main_clause_ids: [{ required: true, message: '请选择主险条款', trigger: ['blur', 'change'] }],
        // additional_clause_ids: [{ required: true, message: '请选择附加险条款', trigger: ['blur', 'change'] }],
        transport_method_ids: [{ required: true, message: '请选择运输方式', trigger: ['blur', 'change'] }],
        goods_type_ids: [{ required: true, message: '请选择货物类别', trigger: ['blur', 'change'] }],
        packing_method_ids: [{ required: true, message: '请选择包装方式', trigger: ['blur', 'change'] }],
        loading_method_ids: [{ required: true, message: '请选择装载方式', trigger: ['blur', 'change'] }],
        service_charge: [{ required: true, message: '请输入保险公司经纪费', trigger: 'blur' }],
        rate: [{ required: true, message: '请输入成本费率', trigger: 'blur' }],
        minimum_premium: [{ required: true, message: '请输入最小保费', trigger: 'blur' }],
        coverage: [{ required: true, message: '请输入保额', trigger: 'blur' }],
        platform_service_charge: [{ required: true, message: '请输入平台出单费', trigger: 'blur' }]
      }
    }
  },
  computed: {
    coverageTitle() {
      return this.types.lbt === this.type ? '保额' : '最高保额'
    },
    disabled() {
      return this.$route.query.from === 'code'
    },
    companyBranches() {
      const com = this.companies.find((e) => e.id == this.form.company_id)

      return com?.branches || []
    },
    treeClauses() {
      return array2Tree(this.clauses, 'id', 'parent_id', -1, 'display_name')
    },
    defaultClausesCheckedKeys() {
      return this.form.main_clause_ids.concat(this.form.additional_clause_ids)
    },
    transportMethodNames() {
      const names = []
      this.transportMethodDialog.data
        .filter((e) => this.form.transport_method_ids?.includes(e.id))
        .forEach((e) => names.push(e.label))

      return names.length < 1 ? '无' : names.length + ' (' + names.join(',') + ')'
    },
    goodsTypeNames() {
      const names = []
      this.goodsTypesDialog.data
        .filter((e) => this.form.goods_type_ids?.includes(e.id))
        .forEach((e) => names.push(e.label))

      return names.length < 1 ? '无' : names.length + ' (' + names.join(',') + ')'
    },
    packingMethodNames() {
      const names = []
      this.packingMethodDialog.data
        .filter((e) => this.form.packing_method_ids?.includes(e.id))
        .forEach((e) => names.push(e.label))

      return names.length < 1 ? '无' : names.length + ' (' + names.join(',') + ')'
    },
    loadingMethodNames() {
      const names = []
      this.loadingMethodDialog.data
        .filter((e) => this.form.loading_method_ids?.includes(e.id))
        .forEach((e) => names.push(e.label))

      return names.length < 1 ? '无' : names.length + ' (' + names.join(',') + ')'
    }
  },
  watch: {
    'form.company_id'(newVal, oldVal) {
      if (this.form.company_id) {
        this.fetchPackingMethods()
        this.fetchLoadingMethods()
        this.fetchGoodsTypes()
        this.fetchTransportMethods()
      }

      if (oldVal !== '' && newVal !== oldVal) {
        this.form.company_branch_id = ''
        this.form.company_branch_account_id = ''
        this.form.packing_method_ids = []
        this.form.loading_method_ids = []
        this.form.goods_type_ids = []
        this.form.transport_method_ids = []

        this.form.main_clause_ids = []
        this.form.additional_clause_ids = []
      }
    },
    'form.company_branch_id'(newVal, oldVal) {
      if (this.form.company_id && this.form.company_branch_id) {
        getCompanyBranchAccounts(this.form.company_id, this.form.company_branch_id).then(
          (r) => (this.companyBranchAccounts = r.data)
        )

        if (oldVal !== '' && newVal !== oldVal) {
          this.form.company_branch_account_id = ''
          this.form.packing_method_ids = []
          this.form.loading_method_ids = []
          this.form.goods_type_ids = []
          this.form.transport_method_ids = []
        }
      }
    },
    'form.transport_method_ids'(value, oldValue) {
      if (!arraysEqual(JSON.parse(JSON.stringify(value)), JSON.parse(JSON.stringify(oldValue)))) {
        this.fetchClauses()
      }
    },
    model: {
      immediate: true,
      deep: true,
      handler(value) {
        if (Object.keys(value).length > 0 && !this.isCopied) {
          this.form = Object.assign({}, value)

          if (this.form.anti_dated_days < 0) {
            this.form.anti_dated_days = ''
            this.form.anti_dated_file = ''
          }

          if (this.form.config) {
            this.form.config = JSON.stringify(this.form.config)
          }
          if (this.form.anti_date_transport_data && Object.keys(this.form.anti_date_transport_data).length > 0) {
            this.anti_date_transport_data = this.form.anti_date_transport_data
          }

          this.form.disabled_regions = value.disabled_regions?.split('|')
          this.form.sanctionist = value.sanctionist?.split('|')
          this.form.only_port_regions = value.only_port_regions?.split('|')
          this.form.war_strike_regions = value.war_strike_regions?.split('|')
        }
      }
    }
  },
  created() {
    getPlatformsDict({
      is_enabled: 1
    }).then((r) => (this.platforms = r.data))
    getCompaniesDict({
      is_enabled: 1
    }).then((r) => (this.companies = r.data))
    getChannels({
      is_enabled: 1
    }).then((r) => (this.channels = r.data))

    if (this.type !== TYPE_LBT) {
      getSubjects().then((r) => (this.subjects = r.data))
    }
  },
  methods: {
    handleClauseCheckChange(data, checked) {
      const ref = this.$refs.treeClauses
      if ([TYPE_DOMESTIC, TYPE_LBT].includes(this.type)) {
        if (data.parent_id === -1) {
          this.form.main_clause_ids.concat(this.form.additional_clause_ids).forEach((id) => ref.setChecked(id, false))

          this.form.main_clause_ids = []
          this.form.additional_clause_ids = []
          if (checked) {
            this.form.main_clause_ids = [data.id]
            ref.setChecked(data.id, true, false)
          }
        }
      } else {
        if (data.parent_id === -1) {
          if (checked) {
            ref.setChecked(data.id, true, false)
            ref.setChecked(data.parent_id, true, false)
            this.form.main_clause_ids.push(data.id)
          } else {
            const idx = this.form.main_clause_ids.findIndex((e) => e === data.id)
            delete this.form.main_clause_ids[idx]

            this.clauses.forEach((e) => {
              if (e.parent_id === data.id) {
                ref.setChecked(e.id, false)
                const additionalIdx = this.form.additional_clause_ids.findIndex((id) => id === e.id)
                delete this.form.additional_clause_ids[additionalIdx]
              }
            })
          }
        }
      }

      if (data.parent_id !== -1) {
        const parentIdx = this.form.main_clause_ids.find((id) => id === data.parent_id)
        if (checked) {
          if (parentIdx === -1 || parentIdx === undefined) {
            ref.setChecked(data.parent_id, true)
            this.form.main_clause_ids.push(data.parent_id)
          }

          ref.setChecked(data.id, true)
          this.form.additional_clause_ids.push(data.id)
        } else {
          delete this.form.additional_clause_ids[this.form.additional_clause_ids.findIndex((id) => id === data.id)]
        }
      }
      this.form.main_clause_ids = this.form.main_clause_ids.filter((e) => e !== undefined)
      this.form.additional_clause_ids = this.form.additional_clause_ids.filter((e) => e !== undefined)
    },
    handleUpdateStatus() {
      if (this.form.id === undefined) {
        return this.$message.error('请先保存后再启用')
      }

      updateProductEnabled(this.form.id).then(() => {
        this.$message.success('操作成功')

        this.form.is_enabled = !this.form.is_enabled
      })
    },
    handleCopy() {
      this.isCopied = true
      delete this.form.id
      this.form.config = ''

      this.$message.success('复制成功')
    },
    handleSubmit() {
      this.form.anti_date_transport_data = JSON.stringify(Object.assign({}, this.anti_date_transport_data))

      this.$refs.form.validate((valid, errors) => {
        if (valid) {
          this.form.type = this.type
          const data = Object.assign({}, this.form)
          data.disabled_regions = data.disabled_regions?.join('|')
          data.sanctionist = data.sanctionist?.join('|')
          data.only_port_regions = data.only_port_regions?.join('|')
          data.war_strike_regions = data.war_strike_regions?.join('|')
          data.main_clause_ids = [...new Set(data.main_clause_ids)]
          data.additional_clause_ids = [...new Set(data.additional_clause_ids)]
          data.is_enabled = data.is_enabled === true || data.is_enabled === 1 || data.is_enabled === '1' ? 1 : 0
          const action = this.form.id === undefined ? createProduct(data) : updateProduct(this.form.id, data)
          const loading = Loading.service()
          action
            .then(() => {
              this.$message.success('保存成功')

              this.$router.go(-1)
            })
            .finally(() => loading.close())
        } else {
          window.alert_validate_errors(errors)
        }
      })
    },
    fetchClauses() {
      let filter = { type: this.type }
      if (this.type === TYPE_INTL) {
        if (this.form.transport_method_ids.length > 0) {
          filter.transport_method_ids = this.form.transport_method_ids

          getClauses(this.form.company_id, filter).then((r) => (this.clauses = r.data))
        } else {
          this.clauses = []
        }
      } else {
        getClauses(this.form.company_id, filter).then((r) => (this.clauses = r.data))
      }
    },
    handleShowPackingMethod() {
      this.packingMethodDialog.visible = true
    },
    fetchPackingMethods() {
      getPackingMethods(this.form.company_id).then((r) => {
        this.packingMethodDialog.data = r.data.map((e) => {
          return {
            id: e.id,
            label: e.name
          }
        })
      })
    },
    handleShowLoadingMethod() {
      this.loadingMethodDialog.visible = true
    },
    fetchLoadingMethods() {
      getLoadingMethods(this.form.company_id).then((r) => {
        this.loadingMethodDialog.data = r.data.map((e) => {
          return {
            id: e.id,
            label: e.name
          }
        })
      })
    },
    handleShowGoodsType() {
      this.goodsTypesDialog.visible = true
    },
    fetchGoodsTypes() {
      getGoodsTypes(this.form.company_id).then((r) => {
        this.goodsTypesDialog.data = array2Tree(
          r.data.map((e) => {
            return {
              id: e.id,
              label: e.display_name,
              parent_id: e.parent_id
            }
          }),
          'id',
          'parent_id',
          -1,
          'label'
        )
      })
    },
    handleShowTransportMethod() {
      this.transportMethodDialog.visible = true
    },
    fetchTransportMethods() {
      getTransportMethods(this.form.company_id).then((r) => {
        this.transportMethodDialog.data = r.data.map((e) => {
          return {
            id: e.id,
            label: e.display_name + '(' + e.name + '-' + e.value + ')'
          }
        })
        this.transportMethods = r.data
      })
    }
  }
}
</script>
