<template>
  <el-dialog :visible.sync="visible" title="技术配置" width="540px" :before-close="handleClose" destroy-on-close>
    <el-form ref="form" label-position="top" :model="form">
      <el-form-item label="协议" v-if="agreements">
        <el-select
          :popper-append-to-body="false"
          popper-class="my-select"
          class="w-100"
          v-model="form.agreement"
          placeholder="请选择保险公司协议"
        >
          <el-option
            v-for="(value, idx) in agreements.split('\n')"
            :key="idx"
            :value="value"
            :label="value"
            :title="value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="方案" v-if="plans">
        <el-select class="w-100" v-model="form.plan" placeholder="请选择方案">
          <el-option v-for="(value, idx) in plans.split('\n')" :key="idx" :value="value" :label="value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="投保方式">
        <el-select class="w-100" v-model="form.policy_mode" placeholder="请选择投保方式">
          <el-option v-for="value in policyModes" :key="value" :value="value" :label="label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="录单方式">
        <el-select class="w-100" v-model="form.is_new" placeholder="请选择录单方式">
          <el-option v-for="mode in insureModes" :key="mode.value" :value="mode.value" :label="mode.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.policy_mode === 'AUTO_PINGAN'" label="录单保费币种选择">
        <el-select class="w-100" v-model="form.premium_currency" placeholder="请进行录单保费币种选择">
          <el-option
            v-for="type in premiumCurrencyTypes"
            :key="type.value"
            :value="type.value"
            :label="type.label"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="录单产品代码">
        <el-input v-model="form.product_code" placeholder="请输入录单产品代码"></el-input>
      </el-form-item>
      <el-form-item label="保单模板">
        <el-input v-model="form.template" placeholder="请输入保单模板名称"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose" icon="fas fa-times">取 消</el-button>
      <el-button type="primary" @click="handleSubmit" icon="fas fa-check">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script lang="js">
export default {
  name: 'DeveloperConfigure',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    agreements: {
      type: String,
      default: () => ''
    },
    plans: {
      type: String,
      default: () => ''
    },
    data: {
      type: Object,
      default: () => {
        return {
          agreement: '',
          plan: '',
          product_code: '',
          is_new: '',
          policy_mode: '',
          template: '',
          premium_currency: '',
        }
      }
    }
  },
  data() {
    return {
      policyModes: [
        'AUTO_PICC', 'AUTO_PINGAN', 'AUTO_CPIC', 'API_SINOSIG', 'API_HUATAI', 'AUTO_TPIC', 'API_DIC', 'EMAIL', 'DIC_EMAIL', 'API_DIC_LBT'
      ],
      insureModes: [
        {
          label:'方案一(旧)',
          value:'0'
        },
        {
          label:'方案二(新)',
          value:'1'
        }
      ],
      premiumCurrencyTypes: [
        {
          label:'按人民币',
          value:'BY_CNY'
        },
        {
          label:'按保额币种',
          value:'BY_AMOUNT'
        }
      ],
      form: {
        agreement: '',
        plan: '',
        product_code: '',
        policy_mode: '',
        is_new: '',
        template: '',
        premium_currency: '',
      },
    }
  },
  watch: {
    data: {
      handler(val) {
        if (Object.keys(val).length > 0) {
          this.form = Object.assign({}, this.form, val)
        }
      },
      deep: true
    }
  },
  methods: {
    handleClose() {
      this.$refs.form.resetFields()

      this.form = {
        agreement: '',
        plan: '',
        product_code: '',
        is_new: '',
        policy_mode: '',
        template:'',
        premium_currency:'',
      }

      this.$emit('update:visible', false)
      this.$emit('close')
    },
    handleSubmit() {
      this.$emit('submit', Object.assign({}, this.form))
      this.handleClose()
    }
  }
}
</script>
<style scoped>
.el-select-dropdown.my-select .el-select-dropdown__item {
  display: inline-block;
  width: 800px;
  overflow: initial;
}
</style>
