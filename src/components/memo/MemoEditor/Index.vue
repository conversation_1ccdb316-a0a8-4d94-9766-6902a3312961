<template>
  <el-dialog
    :title="currentDialogTitle"
    :visible.sync="visible"
    width="700px"
    :before-close="handleBeforeClose"
    destroy-on-close
  >
    <el-form ref="form" label-suffix=":" label-position="top" :model="form" :rules="rules" label-width="150px">
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="水单类型" prop="type">
            <el-select placeholder="请选择水单类型" v-model="form.type" class="w-100">
              <el-option v-for="type in types" :key="type.value" :label="type.label" :value="type.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="金额" prop="amount">
            <el-input type="number" v-model.trim="form.amount" placeholder="请输入金额"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="支付时间" prop="paid_at">
            <el-date-picker
              v-model="form.paid_at"
              class="w-100"
              type="date"
              :picker-options="pickerOptions"
              placeholder="请选择支付时间"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="收款人" prop="payee_id">
            <el-select placeholder="请选择收款人" v-model="form.payee_id" class="w-100" filterable>
              <el-option v-for="payee in payees" :key="payee.value" :label="payee.label" :value="payee.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="支付人" prop="payer">
            <el-input v-model.trim="form.payer" placeholder="请填写支付人"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="proof">
            <template slot="label">
              <span>支付凭证</span>
            </template>
            <upload-file v-model="form.proof"></upload-file>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item prop="remark" label="备注">
            <el-input type="textarea" v-model="form.remark" placeholder="请填写备注"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleBeforeClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getPayees } from '@/apis/finance'
import { getSaleClients } from '@/apis/user'
import { mapGetters } from 'vuex'
import dayjs from 'dayjs'

export default {
  name: 'MemoEditor',
  props: {
    model: {
      type: [Object, MouseEvent],
      default: () => {}
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        type: 1,
        amount: '',
        proof: '',
        paid_at: '',
        payer: '',
        payee_id: '',
        remark: ''
      },
      rules: {
        type: [{ required: true, message: '请选择水单类型', trigger: ['blur', 'change'] }],
        amount: [{ required: true, message: '请输入金额', trigger: 'blur' }],
        proof: [{ required: true, message: '请上传支付凭证', trigger: ['blur', 'change'] }],
        paid_at: [{ required: true, type: 'date', message: '请选择支付时间', trigger: 'change' }],
        payer: [{ required: true, message: '请填写支付人', trigger: ['blur'] }],
        payee_id: [{ required: true, message: '请选择收款人', trigger: ['blur', 'change'] }],
        remark: [{ required: false, message: '请填写备注', trigger: ['blur', 'change'] }]
      },
      types: [
        {
          label: '客户支付保险公司',
          value: 1
        },
        {
          label: '客户支付平台',
          value: 2
        }
      ],
      payees: [],
      payers: [],
      pickerOptions: {
        disabledDate: (time) => {
          if (time.getTime() > dayjs()) {
            return true
          }
        }
      }
    }
  },
  computed: {
    ...mapGetters('auth', ['admin']),
    currentDialogTitle() {
      return this.form && this.form.id ? '水单修改' : '水单录入'
    }
  },
  created() {
    getPayees(this.form.type).then((r) => {
      this.payees = r.data
    })
    getSaleClients(this.admin.id).then((r) => {
      this.payers = r.data
    })
  },
  methods: {
    handleBeforeClose() {
      this.$emit('update:visible', false)
      this.$refs.form.resetFields()
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('submit', Object.assign({}, this.prepareData(this.form)))

          this.handleBeforeClose()
        }
      })
    },
    prepareData(data) {
      console.log(data)
      if (Object.keys(data).length <= 0) {
        return data
      }
      data.paid_at = dayjs(data.paid_at).format('YYYY-MM-DD')
      return data
    }
  },
  watch: {
    model: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val && Object.keys(val).length > 0) {
          val.payee_id = val.payee.id

          this.form = Object.assign({}, val)

          this.form.paid_at = Date.parse(val.paid_at)
        } else {
          this.form = {
            type: 1,
            amount: '',
            proof: '',
            paid_at: '',
            payer: '',
            payee_id: '',
            remark: ''
          }
        }
      }
    },
    'form.type'(type) {
      getPayees(type).then((r) => {
        this.payees = r.data
        // this.form.payee_id = this.payees[0].value
      })
    }
  }
}
</script>
