import Vue from 'vue'

Vue.directive('can', {
  inserted: (el, binding, vnode) => {
    const permissions = vnode.context.$store.getters['app/rolePermissions']
    const roleName = vnode.context.$store.getters['auth/roleName']

    if (roleName !== 'super-admin' && roleName !== 'platform-admin') {
      // if (roleName !== 'super-admin') {
      if (!permissions.some((e) => e.name === binding.value.name)) {
        el.remove()
      }
    }
  }
})
