/*
 * @Descripttion:
 * @Author: Mr. z<PERSON>
 * @Date: 2020-10-19 12:36:37
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-08 16:22:42
 */

import Vue from 'vue'
import Element from 'element-ui'
import '@/styles/element-variables.scss'
import '@fortawesome/fontawesome-free/js/all.js'
import '@/directives'
import 'fix-date'
import '@/utils/global'
import store from '@/store'
import router from '@/router'

Element.Dialog.props.closeOnClickModal.default = false

Vue.use(Element, { size: 'small' })

Vue.use(
  {
    install(Vue, options) {
      Vue.prototype.$open = (params) => {
        const isNewTab = options.store?.getters['auth/admin']?.options?.is_new_tab || false
        if (isNewTab) {
          window.open(options.router.resolve(params).href, '_blank')
        } else {
          options.router.push(params)
        }
      }
    }
  },
  { store, router }
)
