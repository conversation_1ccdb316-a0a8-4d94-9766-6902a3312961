const cargoReasons = [
  {
    value: '自然灾害',
    options: [
      { value: '恶劣气候' },
      { value: '雷电' },
      { value: '海啸' },
      { value: '地震' },
      { value: '洪水' },
      { value: '暴风' },
      { value: '暴雨' },
      { value: '其他' }
    ]
  },
  {
    value: '意外事故',
    options: [
      { value: '运输工具意外' },
      { value: '火灾' },
      { value: '爆炸' },
      { value: '装卸货意外' },
      { value: '震动碰撞挤压' },
      { value: '水湿' },
      { value: '其他' }
    ]
  },
  {
    value: '共同海损',
    options: [{ value: '共同海损牺牲分摊' }, { value: '救助费用分摊' }, { value: '其他' }]
  },
  {
    value: '货物丢失',
    options: [{ value: '货物丢失' }]
  }
]

const generalReasons = [
  {
    value: '自然灾害',
    options: [
      { value: '恶劣气候' },
      { value: '雷电' },
      { value: '海啸' },
      { value: '地震' },
      { value: '洪水' },
      { value: '暴风' },
      { value: '暴雨' },
      { value: '其他' }
    ]
  },
  {
    value: '意外事故',
    options: [
      { value: '交通事故' },
      { value: '火灾' },
      { value: '爆炸' },
      { value: '管道破裂' },
      { value: '盗窃' },
      { value: '震动碰撞挤压' },
      { value: '水湿' },
      { value: '其他' }
    ]
  }
]

const groupReasons = [
  {
    value: '受伤',
    options: [
      { value: '因工作原因受到事故伤害' },
      { value: '因履行工作职责受到暴力等意外伤害' },
      { value: '工作原因受到伤害或者发生事故下落不明' },
      { value: '上下班途中，受到非本人主要责任的交通事故伤害' },
      { value: '其他原因受伤' }
    ]
  },
  {
    value: '死亡',
    options: [{ value: '工作期间突发疾病死亡' }, { value: '在48小时之内经抢救无效死亡' }, { value: '其他原因死亡' }]
  },
  {
    value: '其他',
    options: [{ value: '其他' }]
  }
]

export { cargoReasons, generalReasons, groupReasons }
