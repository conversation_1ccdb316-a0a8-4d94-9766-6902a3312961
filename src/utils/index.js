/*
 * @Descripttion:
 * @Author: Mr. <PERSON><PERSON>
 * @Date: 2020-10-19 12:30:41
 * @LastEditors: Mr. <PERSON>hu
 * @LastEditTime: 2021-01-21 17:27:15
 */

/**
 * 检测传入数据是否是一个字符串
 * @author: Mr. zhu
 * @param {type}
 * @return {Boolean}
 */
export const isStr = (o) => Object.prototype.toString.call(o) === '[object String]'

/**
 * 检测传入数据是否是一个对象
 * @author: Mr. zhu
 * @param { Object } o 需要检测数据
 * @return { Boolean }
 */
export const isObj = (o) => Object.prototype.toString.call(o) === '[object Object]'
export const convertSize = (limit) => {
  let size = ''
  if (limit < 0.1 * 1024) {
    //如果小于0.1KB转化成B
    size = limit.toFixed(2) + 'B'
  } else if (limit < 0.1 * 1024 * 1024) {
    //如果小于0.1MB转化成KB
    size = (limit / 1024).toFixed(2) + 'KB'
  } else if (limit < 0.1 * 1024 * 1024 * 1024) {
    //如果小于0.1GB转化成MB
    size = (limit / (1024 * 1024)).toFixed(2) + 'MB'
  } else {
    //其他转化成GB
    size = (limit / (1024 * 1024 * 1024)).toFixed(2) + 'GB'
  }

  let sizestr = size + ''
  let len = sizestr.indexOf('.')
  let dec = sizestr.substr(len + 1, 2)
  if (dec == '00') {
    //当小数点后为00时 去掉小数部分
    return sizestr.substring(0, len) + sizestr.substr(len + 3, 2)
  }
  return sizestr
}

/** 金额转大写 */
export const digitUppercase = (money) => {
  const fraction = ['角', '分']
  const digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
  const unit = [
    ['元', '万', '亿'],
    ['', '拾', '佰', '仟']
  ]
  const head = money < 0 ? '欠' : ''
  money = Math.abs(money)
  let s = ''
  for (let i = 0; i < fraction.length; i++) {
    s += (digit[Math.floor(money * 10 * Math.pow(10, i)) % 10] + fraction[i]).replace(/零./, '')
  }
  s = s || '整'
  money = Math.floor(money)
  for (let i = 0; i < unit[0].length && money > 0; i++) {
    let p = ''
    for (let j = 0; j < unit[1].length && money > 0; j++) {
      p = digit[money % 10] + unit[1][j] + p
      money = Math.floor(money / 10)
    }
    s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s
  }
  return (
    head +
    s
      .replace(/(零.)*零元/, '元')
      .replace(/(零.)+/g, '零')
      .replace(/^整$/, '零元整')
  )
}
/**
 * Array List 转 Tree Array
 * @param { String } jsonArr 需要转换的Array
 * @param { String } selfIdField 自身id字段名称
 * @param { String } parentIdField 父级id字段名称
 * @param { String } parentld 最顶级父级id
 * @param { String } selfIdFieName 自身名称字段名称
 * @param { String } parentName 父级名称
 */
export const array2Tree = (
  jsonArr = [],
  selfIdField = 'id',
  parentIdField = 'parentId',
  parentld = '',
  selfIdFieName = '',
  parentName = ''
) => {
  let result = []
  for (const val of jsonArr) {
    if (val[parentIdField] === parentld) {
      let obj = {}
      for (const key in val) {
        obj[key] = val[key]
      }
      let children = array2Tree(
        jsonArr,
        selfIdField,
        parentIdField,
        obj[selfIdField],
        selfIdFieName,
        parentName ? obj[parentName] : undefined
      )
      obj.parent_name = selfIdFieName ? obj[selfIdFieName] : undefined
      obj.children = children
      result.push(obj)
    }
  }
  return result
}

export const arraysEqual = (a, b) => {
  a = a.sort()
  b = b.sort()
  if (a === b) return true
  if (a == null || b == null || a.length !== b.length) return false

  for (var i = 0; i < a.length; ++i) {
    if (a[i] !== b[i]) return false
  }

  return true
}
