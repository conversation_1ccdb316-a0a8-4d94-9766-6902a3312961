import axios from 'axios'
import { token<PERSON>ey } from '@/config'
import router from '@/router'
import { Message } from 'element-ui'
import qs from 'qs'
// Full config:  https://github.com/axios/axios#request-config

let config = {
  baseURL: process.env.VUE_APP_BASE_API,
  headers: {
    // 'Content-Type': 'application/json;charset=utf-8'
  },
  timeout: 600000,
  withCredentials: false
}
const instance = axios.create(config)

// 添加请求拦截器
instance.interceptors.request.use(
  (config) => {
    if (window.localStorage.getItem(tokenKey)) {
      config.headers['Authorization'] = 'Bearer ' + window.localStorage.getItem(tokenKey)
    }
    if (['patch', 'put', 'post'].includes(config.method)) {
      if (!(config.data instanceof FormData)) {
        config.headers['Content-Type'] = 'application/json;charset=utf-8'
      }
    }
    return config
  },
  (error) => Promise.reject(error)
)

// 添加响应拦截器
instance.interceptors.response.use(
  (res) => res.data,
  (error) => {
    if (error.response && error.response.status === 401 && router.history.current.name !== 'Login') {
      localStorage.removeItem(tokenKey)
      router.push({ name: 'Login' })
    } else if (error.response && error.response.status === 422) {
      let messages = ''
      const errors = error.response.data.errors || error.response.data.message
      if (Array.isArray(errors)) {
        errors.forEach((e) => {
          messages += e.replace(' ', '') + '<br />'
        })
      } else {
        if (typeof errors === 'string') {
          messages = errors
        } else {
          Object.keys(errors).forEach((e) => {
            if (Array.isArray(errors)) {
              errors[e].forEach((e) => {
                messages += e.replace(' ', '') + '<br />'
              })
            } else {
              messages += errors[e].replace(' ', '') + '<br />'
            }
          })
        }
      }

      Message.error({
        dangerouslyUseHTMLString: true,
        center: true,
        message: messages
      })
    } else {
      const message = error.response.data.message || 'System error'
      if (message.indexOf('Token not provided') !== -1 || message.indexOf('Token has expired') !== -1) {
        Message.error('登录失效')
      } else {
        Message.error(message)
      }
    }

    return Promise.reject(error)
  }
)

export const get = (url, params = {}) => instance.get(url + '?' + qs.stringify(params))

export const post = async (url, params = {}) => instance.post(url, params)

export const postForm = (url, params) =>
  instance.post(url, params, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    transformRequest: [
      (data) => {
        return qs.stringify(data)
      }
    ]
  })

export const del = async (url, params = {}) =>
  instance.delete(url, {
    params
  })

export const put = async (url, params = {}) => instance.put(url, params, config)

export const patch = async (url, params = {}) => instance.patch(url, params)

/**
 * postFormData方法，对应post请求，用来提交文件+数据
 *
 * @param {String} url [请求的url地址]
 * @param {Object} params [请求时携带的参数]
 */
export const postFormData = (url, params = {}) => {
  const formData = new FormData()
  Object.keys(params).forEach((k) => formData.append(k, params[k]))

  return instance.post(url, formData, config)
}

/**
 * postFormData方法，对应post请求，用来提交文件+数据
 *
 * @param {String} url [请求的url地址]
 * @param {Object} params [请求时携带的参数]
 * @param {String} method [请求方式]
 */
export const postFormDataOfArray = (url, params = {}, method = 'POST') => {
  const formData = new FormData()
  appendFormData(formData, params)

  return instance[method.toLowerCase()](url, formData, config)
}

function appendFormData(formData, data, parentKey = '') {
  if (data === null || data === undefined) return

  if (Array.isArray(data)) {
    data.forEach((value) => {
      const key = parentKey + '[]'
      appendFormData(formData, value, key)
    })
  } else if (typeof data === 'object' && !(data instanceof File) && !(data instanceof Blob)) {
    Object.keys(data).forEach((key) => {
      const fullKey = parentKey ? `${parentKey}[${key}]` : key
      appendFormData(formData, data[key], fullKey)
    })
  } else {
    formData.append(parentKey, data)
  }
}
