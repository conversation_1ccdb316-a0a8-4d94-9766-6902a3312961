export default [
  {
    path: 'policies/domestic',
    name: 'PoliciesDomestic',
    component: () => import('@/views/policies/Domestic'),
    meta: {
      titles: [{ label: '保单管理' }, { label: '国内货运险', current: true }]
    }
  },
  {
    path: 'policies/domestic/:id',
    name: 'PoliciesDomesticDetails',
    component: () => import('@/views/policies/DomesticDetails'),
    meta: {
      active: 'PoliciesDomestic',
      titles: [
        { label: '保单管理' },
        { label: '国内货运险', name: 'PoliciesDomestic' },
        { label: '详情', current: true }
      ]
    }
  },
  {
    path: 'policies/intl',
    name: 'PoliciesIntl',
    component: () => import('@/views/policies/Intl'),
    meta: {
      titles: [{ label: '保单管理' }, { label: '国际货运险', current: true }]
    }
  },
  {
    path: 'policies/intl/:id',
    name: 'PoliciesIntlDetails',
    component: () => import('@/views/policies/IntlDetails'),
    meta: {
      active: 'PoliciesIntl',
      titles: [{ label: '保单管理' }, { label: '国际货运险', name: 'PoliciesIntl' }, { label: '详情', current: true }]
    }
  },
  {
    path: 'policies/lbt',
    name: 'PoliciesLbt',
    component: () => import('@/views/policies/Lbt'),
    meta: {
      titles: [{ label: '保单管理' }, { label: '单车责任险', current: true }]
    }
  },
  {
    path: 'policies/lbt/:id',
    name: 'PoliciesLbtDetails',
    component: () => import('@/views/policies/LbtDetails'),
    meta: {
      active: 'PoliciesLbt',
      titles: [{ label: '保单管理' }, { label: '单车责任险', name: 'PoliciesLbt' }, { label: '详情', current: true }]
    }
  },
  {
    path: 'policies/group',
    name: 'PoliciesGroup',
    component: () => import('@/views/policies/Group'),
    meta: {
      titles: [{ label: '保单管理' }, { label: '雇主责任险', current: true }]
    }
  },
  {
    path: 'policies/mixed',
    name: 'PoliciesMixed',
    component: () => import('@/views/policies/Mixed.vue'),
    meta: {
      titles: [{ label: '保单管理' }, { label: '保单综合查询', current: true }]
    }
  },
  {
    path: 'policies/group/:policyGroupId',
    name: 'PoliciesGroupDetails',
    component: () => import('@/views/policies/GroupDetails'),
    meta: {
      active: 'PoliciesGroup',
      titles: [
        { label: '保单管理' },
        { label: '雇主责任险', name: 'PoliciesGroup' },
        { label: '保单详情', current: true }
      ]
    }
  },
  {
    path: 'policies/group/:policyGroupId/staffs',
    name: 'GroupEmployee',
    component: () => import('@/views/policies/GroupEmployee'),
    meta: {
      active: 'PoliciesGroup',
      titles: [
        { label: '保单管理' },
        { label: '雇主责任险', name: 'PoliciesGroup' },
        { label: '保单详情', name: 'PoliciesGroupDetails' },
        { label: '在保人员', current: true }
      ]
    }
  },
  {
    path: 'policies/group/:policyGroupId/endorses',
    name: 'GroupEndorse',
    component: () => import('@/views/policies/GroupEndorse'),
    meta: {
      active: 'PoliciesGroup',
      titles: [
        { label: '保单管理' },
        { label: '雇主责任险', name: 'PoliciesGroup' },
        { label: '保单详情', name: 'PoliciesGroupDetails' },
        { label: '在保人员', name: 'GroupEmployee' },
        { label: '人员批单', current: true }
      ]
    }
  },
  {
    path: 'policies/group/:policyGroupId/endorses/:endorseId/employees',
    name: 'GroupEndorseEmployee',
    component: () => import('@/views/policies/GroupEndorseEmployee'),
    meta: {
      active: 'PoliciesGroup',
      titles: [
        { label: '保单管理' },
        { label: '雇主责任险', name: 'PoliciesGroup' },
        { label: '保单详情', name: 'PoliciesGroupDetails' },
        { label: '在保人员', name: 'GroupEmployee' },
        { label: '人员批单', name: 'GroupEndorse' },
        { label: '批单详情', current: true }
      ]
    }
  },
  {
    path: 'policies/group/:policyGroupId/transactions',
    name: 'GroupTransaction',
    component: () => import('@/views/policies/GroupTransaction'),
    meta: {
      active: 'PoliciesGroup',
      titles: [
        { label: '保单管理' },
        { label: '雇主责任险', name: 'PoliciesGroup' },
        { label: '保单详情', name: 'PoliciesGroupDetails' },
        { label: '支付管理', current: true }
      ]
    }
  },
  {
    path: 'policies/papers',
    name: 'PoliciesPaper',
    component: () => import('@/views/policies/PolicyPaper.vue'),
    meta: {
      titles: [{ label: '保单管理' }, { label: '纸质保单', current: true }]
    }
  },
  {
    path: 'policies/other',
    name: 'PoliciesOther',
    component: () => import('@/views/policies/Other'),
    meta: {
      titles: [{ label: '产品管理' }, { label: '其他险种', current: true }]
    }
  },
  {
    path: 'policies/other/:id',
    name: 'PoliciesOtherDetails',
    component: () => import('@/views/policies/OtherDetails'),
    meta: {
      active: 'PoliciesOther',
      titles: [{ label: '保单管理' }, { label: '其他险种', name: 'PoliciesOther' }, { label: '详情', current: true }]
    }
  },
  {
    title: '历史保单',
    path: 'policies/histories',
    name: 'PoliciesHistory',
    component: () => import('@/views/policies/History.vue'),
    meta: {
      isMenu: true,
      titles: [{ label: '保单管理' }, { label: '历史保单', current: true }]
    }
  },
  {
    title: '历史保单详情',
    path: 'policies/histories/:id',
    name: 'PoliciesHistoryDetails',
    component: () => import('@/views/policies/HistoryDetails.vue'),
    meta: {
      active: 'PoliciesHistory',
      isMenu: false,
      titles: [
        { label: '保单管理' },
        { label: '历史保单', name: 'PoliciesHistory' },
        { label: '保单详情', current: true }
      ]
    }
  },
  {
    path: 'offline-policy/categories',
    name: 'OfflineProductList',
    component: () => import('@/views/policies/offline/OfflineProductList.vue'),
    meta: {
      isMenu: true,
      titles: [{ label: '线下保单录入产品列表' }]
    }
  },
  {
    path: 'offline-policy/create/:categoryId',
    name: 'CreateOfflinePolicy',
    component: () => import('@/views/policies/offline/CreateOfflinePolicy.vue'),
    meta: {
      active: 'OfflineProductList',
      isMenu: true,
      titles: [
        { label: '线下保单录入产品列表', name: 'OfflineProductList' },
        { label: '保单录入', current: true }
      ]
    }
  },
  {
    path: 'offline-policies',
    name: 'OfflinePolicies',
    component: () => import('@/views/policies/offline/OfflinePolicies.vue'),
    meta: {
      isMenu: true,
      titles: [{ label: '保单管理' }, { label: '录入保单列表', current: true }]
    }
  },
  {
    path: 'offline-policies/handle',
    name: 'HandleOfflinePolicies',
    component: () => import('@/views/policies/offline/Handle.vue'),
    meta: {
      active: 'OfflinePolicies',
      isMenu: true,
      titles: [
        { label: '保单管理' },
        { label: '录入保单列表', name: 'OfflinePolicies' },
        { label: '批量处理', current: true }
      ]
    }
  },
  {
    path: 'offline-policies/:id',
    name: 'OfflinePolicyDetails',
    component: () => import('@/views/policies/offline/OfflinePolicyDetails.vue'),
    meta: {
      active: 'OfflinePolicies',
      isMenu: true,
      titles: [
        { label: '保单管理' },
        { label: '录入保单列表', name: 'OfflinePolicies' },
        { label: '保单详情', current: true }
      ]
    }
  },
  {
    path: 'offline-policies/:id/update',
    name: 'UpdateOfflinePolicy',
    component: () => import('@/views/policies/offline/UpdateOfflinePolicy.vue'),
    meta: {
      active: 'OfflinePolicies',
      isMenu: true,
      titles: [
        { label: '保单管理' },
        { label: '保单详情', name: 'OfflinePolicyDetails' },
        { label: '修改保单', current: true }
      ]
    }
  },
  {
    path: 'policies/cbec',
    name: 'PoliciesCbec',
    component: () => import('@/views/policies/Cbec'),
    meta: {
      titles: [{ label: '保单管理' }, { label: '跨境电商货运险', current: true }]
    }
  },
  {
    path: 'policies/cbec/:id',
    name: 'PoliciesCbecDetails',
    component: () => import('@/views/policies/CbecDetails'),
    meta: {
      active: 'PoliciesCbec',
      titles: [
        { label: '保单管理' },
        { label: '跨境电商货运险', name: 'PoliciesCbec' },
        { label: '详情', current: true }
      ]
    }
  }
]
