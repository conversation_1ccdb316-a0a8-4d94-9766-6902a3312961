export default [
  {
    path: 'finance/invoice',
    name: 'Invoice',
    component: () => import('@/views/finance/Invoice'),
    meta: {
      titles: [{ label: '财务管理' }, { label: '发票处理', current: true }]
    }
  },
  {
    path: 'finance/applyInovice',
    name: 'ApplyInvoice',
    component: () => import('@/views/finance/Invoice/ApplyInvoice'),
    meta: {
      active: 'Invoice',
      titles: [{ label: '财务管理' }, { label: '发票管理', name: 'Invoice' }, { label: '申请发票', current: true }]
    }
  },
  {
    path: 'finance/billDetail/:id',
    name: 'BillDetail',
    component: () => import('@/views/finance/Invoice/BillDetail'),
    meta: {
      active: 'Invoice',
      titles: [{ label: '财务管理' }, { label: '发票管理', name: 'Invoice' }, { label: '详情', current: true }]
    }
  },
  {
    path: 'finance/billInfo',
    name: 'BillInfo',
    component: () => import('@/views/finance/Invoice/BillInfo'),
    meta: {
      active: 'Invoice',
      titles: [{ label: '财务管理' }, { label: '发票管理', current: true }]
    }
  },
  {
    path: 'finance/premiumPayment',
    name: 'PremiumPayment',
    component: () => import('@/views/finance/PremiumPayment'),
    meta: {
      active: 'PremiumPayment',
      titles: [{ label: '财务管理' }, { label: '保费应付', current: true }]
    }
  },
  {
    path: 'finance/premiumPayment/handle',
    name: 'HandlePremiumPayment',
    component: () => import('@/views/finance/PremiumPayment/Handle'),
    meta: {
      active: 'PremiumPayment',
      titles: [
        { label: '财务管理' },
        { label: '保费支付', name: 'PremiumPayment' },
        { label: '应付处理', current: true }
      ]
    }
  },
  {
    path: 'finance/premiumPayment/bills',
    name: 'PremiumPaymentBills',
    component: () => import('@/views/finance/PremiumPayment/Bills'),
    meta: {
      active: 'PremiumPayment',
      titles: [
        { label: '财务管理' },
        { label: '保费应付', name: 'PremiumPayment' },
        { label: '支付记录', current: true }
      ]
    }
  },
  {
    path: 'finance/premiumPayment/bills/:id/payments',
    name: 'PremiumBillPayments',
    component: () => import('@/views/finance/PremiumPayment/Payments'),
    meta: {
      active: 'PremiumPayment',
      titles: [
        { label: '财务管理' },
        { label: '保费应付', name: 'PremiumPayment' },
        { label: '支付记录', name: 'PremiumPaymentBills' },
        { label: '处理数据', current: true }
      ]
    }
  },
  {
    path: 'finance/premiumPayment/draft-bills',
    name: 'DraftPremiumPaymentBills',
    component: () => import('@/views/finance/PremiumPayment/Draft'),
    meta: {
      active: 'PremiumPayment',
      titles: [
        { label: '财务管理' },
        { label: '保费应付', name: 'PremiumPayment' },
        { label: '暂存支付记录', current: true }
      ]
    }
  },
  {
    path: 'finance/premiumPayment/draft-bills/:id/payments',
    name: 'DraftPremiumPaymentBillPayments',
    component: () => import('@/views/finance/PremiumPayment/Payments'),
    meta: {
      active: 'PremiumPayment',
      titles: [
        { label: '财务管理' },
        { label: '保费应付', name: 'PremiumPayment' },
        { label: '暂存支付记录', name: 'DraftPremiumPaymentBills' },
        { label: '处理数据', current: true }
      ]
    }
  },
  {
    path: 'finance/premiumPayment/audit',
    name: 'AuditPremiumPaymentBill',
    component: () => import('@/views/finance/PremiumPayment/Audit'),
    meta: {
      active: 'AuditPremiumPaymentBill',
      titles: [{ label: '财务管理' }, { label: '保费应付审核', current: true }]
    }
  },
  {
    path: 'finance/premiumPayment/audit/:id/payments',
    name: 'AuditPremiumPaymentBillPayments',
    component: () => import('@/views/finance/PremiumPayment/Payments'),
    meta: {
      active: 'AuditPremiumPaymentBill',
      titles: [
        { label: '财务管理' },
        { label: '保费应付审核', name: 'AuditPremiumPaymentBill' },
        { label: '处理数据', current: true }
      ]
    }
  },
  {
    path: 'finance/poundagePayment',
    name: 'PoundagePayment',
    component: () => import('@/views/finance/PoundagePayment'),
    meta: {
      titles: [{ label: '财务管理' }, { label: '经纪费结算', current: true }]
    }
  },
  {
    path: 'finance/poundagePayment/handle',
    name: 'HandlePoundagePayment',
    component: () => import('@/views/finance/PoundagePayment/Handle'),
    meta: {
      active: 'PoundagePayment',
      titles: [{ label: '财务管理' }, { label: '经纪费结算', current: true }]
    }
  },
  {
    path: 'finance/poundagePayment/bills',
    name: 'PoundagePaymentBills',
    component: () => import('@/views/finance/PoundagePayment/Bills'),
    meta: {
      active: 'PoundagePayment',
      titles: [{ label: '财务管理' }, { label: '结算记录', current: true }]
    }
  },
  {
    path: 'finance/poundagePayment/bills/:id/payments',
    name: 'PoundageBillPayments',
    component: () => import('@/views/finance/PoundagePayment/Payments'),
    meta: {
      titles: [{ label: '财务管理' }, { label: '结算列表', current: true }]
    }
  },
  {
    path: 'finance/poundagePayment/draft-bills',
    name: 'DraftPoundagePaymentBills',
    component: () => import('@/views/finance/PoundagePayment/Draft'),
    meta: {
      active: 'PoundagePayment',
      titles: [
        { label: '财务管理' },
        { label: '经纪费结算', name: 'PoundagePayment' },
        { label: '暂存支付记录', current: true }
      ]
    }
  },
  {
    path: 'finance/poundagePayment/draft-bills/:id/payments',
    name: 'DraftPoundagePaymentBillPayments',
    component: () => import('@/views/finance/PoundagePayment/Payments'),
    meta: {
      active: 'PoundagePayment',
      titles: [
        { label: '财务管理' },
        { label: '经纪费结算', name: 'PoundagePayment' },
        { label: '暂存支付记录', name: 'DraftPoundagePaymentBills' },
        { label: '处理数据', current: true }
      ]
    }
  },
  {
    path: 'finance/poundagePayment/audit',
    name: 'AuditPoundagePaymentBill',
    component: () => import('@/views/finance/PoundagePayment/Audit'),
    meta: {
      active: 'AuditPoundagePaymentBill',
      titles: [{ label: '财务管理' }, { label: '经纪费结算审核', current: true }]
    }
  },
  {
    path: 'finance/poundagePayment/audit/:id/payments',
    name: 'AuditPoundagePaymentBillPayments',
    component: () => import('@/views/finance/PoundagePayment/Payments'),
    meta: {
      active: 'AuditPoundagePaymentBill',
      titles: [
        { label: '财务管理' },
        { label: '经纪费结算审核', name: 'AuditPoundagePaymentBill' },
        { label: '处理数据', current: true }
      ]
    }
  },
  {
    path: 'finance/commissionPayment',
    name: 'CommissionPayment',
    component: () => import('@/views/finance/CommissionPayment'),
    meta: {
      active: 'CommissionPayment',
      titles: [{ label: '财务管理' }, { label: '佣金应发', current: true }]
    }
  },
  {
    path: 'finance/commissionPayment/handle',
    name: 'HandleCommissionPayment',
    component: () => import('@/views/finance/CommissionPayment/Handle'),
    meta: {
      active: 'CommissionPayment',
      titles: [
        { label: '财务管理' },
        { label: '佣金应发', name: 'CommissionPayment' },
        { label: '应付处理', current: true }
      ]
    }
  },
  {
    path: 'finance/commissionPayment/bills',
    name: 'CommissionPaymentBills',
    component: () => import('@/views/finance/CommissionPayment/Bills'),
    meta: {
      active: 'CommissionPayment',
      titles: [
        { label: '财务管理' },
        { label: '佣金发放', name: 'CommissionPayment' },
        { label: '发放记录', current: true }
      ]
    }
  },
  {
    path: 'finance/commissionPayment/bills/:id/payments',
    name: 'CommissionBillPayments',
    component: () => import('@/views/finance/CommissionPayment/Payments'),
    meta: {
      active: 'CommissionPayment',
      titles: [
        { label: '财务管理' },
        { label: '佣金应付', name: 'CommissionPayment' },
        { label: '支付记录', name: 'CommissionPaymentBills' },
        { label: '处理数据', current: true }
      ]
    }
  },
  {
    path: 'finance/commissionPayment/draft-bills',
    name: 'DraftCommissionPaymentBills',
    component: () => import('@/views/finance/CommissionPayment/Draft'),
    meta: {
      active: 'CommissionPayment',
      titles: [
        { label: '财务管理' },
        { label: '佣金发放', name: 'CommissionPayment' },
        { label: '暂存支付记录', current: true }
      ]
    }
  },
  {
    path: 'finance/commissionPayment/draft-bills/:id/payments',
    name: 'DraftCommissionPaymentBillPayments',
    component: () => import('@/views/finance/CommissionPayment/Payments'),
    meta: {
      active: 'CommissionPayment',
      titles: [
        { label: '财务管理' },
        { label: '佣金发放', name: 'CommissionPayment' },
        { label: '暂存支付记录', name: 'DraftCommissionPaymentBills' },
        { label: '处理数据', current: true }
      ]
    }
  },
  {
    path: 'finance/commissionPayment/audit',
    name: 'AuditCommissionPaymentBill',
    component: () => import('@/views/finance/CommissionPayment/Audit'),
    meta: {
      active: 'AuditCommissionPaymentBill',
      titles: [{ label: '财务管理' }, { label: '佣金发放审核', current: true }]
    }
  },
  {
    path: 'finance/commissionPayment/audit/:id/payments',
    name: 'AuditCommissionPaymentBillPayments',
    component: () => import('@/views/finance/CommissionPayment/Payments'),
    meta: {
      active: 'AuditCommissionPaymentBill',
      titles: [
        { label: '财务管理' },
        { label: '佣金发放审核', name: 'AuditCommissionPaymentBill' },
        { label: '处理数据', current: true }
      ]
    }
  },
  {
    path: 'finance/recharge',
    name: 'Recharge',
    component: () => import('@/views/finance/Recharge'),
    meta: {
      titles: [{ label: '财务管理' }, { label: '充值审核', current: true }]
    }
  },
  {
    path: 'finance/premiumReceivable',
    name: 'PremiumReceivable',
    component: () => import('@/views/finance/PremiumReceivable'),
    meta: {
      active: 'PremiumReceivable',
      titles: [{ label: '财务管理' }, { label: '保费应收', current: true }]
    }
  },
  {
    path: 'finance/premiumReceivable/handle',
    name: 'HandlePremiumReceivable',
    component: () => import('@/views/finance/PremiumReceivable/Handle'),
    meta: {
      active: 'PremiumReceivable',
      titles: [
        { label: '财务管理' },
        { label: '保费应收', name: 'PremiumReceivable' },
        { label: '保费应收处理', current: true }
      ]
    }
  },
  {
    path: 'finance/premiumReceivable/bills',
    name: 'PremiumReceivableBills',
    component: () => import('@/views/finance/PremiumReceivable/Bills'),
    meta: {
      active: 'PremiumReceivable',
      titles: [
        { label: '财务管理' },
        { label: '保费应收', name: 'PremiumReceivable' },
        { label: '收取记录', current: true }
      ]
    }
  },
  {
    path: 'finance/premiumReceivable/bills/:id/receivables',
    name: 'PremiumBillReceivables',
    component: () => import('@/views/finance/PremiumReceivable/Receivables'),
    meta: {
      active: 'PremiumReceivable',
      titles: [
        { label: '财务管理' },
        { label: '保费应收', name: 'PremiumReceivable' },
        { label: '收取记录', name: 'PremiumReceivableBills' },
        { label: '收取数据', current: true }
      ]
    }
  },
  {
    path: 'finance/premiumReceivable/draft-bills',
    name: 'DraftPremiumReceivableBills',
    component: () => import('@/views/finance/PremiumReceivable/Draft'),
    meta: {
      active: 'PremiumReceivable',
      titles: [
        { label: '财务管理' },
        { label: '保费应收', name: 'PremiumReceivable' },
        { label: '暂存记录', current: true }
      ]
    }
  },
  {
    path: 'finance/premiumReceivable/draft-bills/:id/receivables',
    name: 'DraftPremiumBillReceivables',
    component: () => import('@/views/finance/PremiumReceivable/Receivables'),
    meta: {
      active: 'PremiumReceivable',
      titles: [
        { label: '财务管理' },
        { label: '保费应收', name: 'PremiumReceivable' },
        { label: '暂存记录', name: 'DraftPremiumReceivableBills' },
        { label: '收取数据', current: true }
      ]
    }
  },
  {
    path: 'finance/commissionReceivable',
    name: 'CommissionReceivable',
    component: () => import('@/views/finance/CommissionReceivable'),
    meta: {
      active: 'CommissionReceivable',
      titles: [{ label: '财务管理' }, { label: '佣金应收', current: true }]
    }
  },
  {
    path: 'finance/commissionReceivable/handle',
    name: 'HandleCommissionReceivable',
    component: () => import('@/views/finance/CommissionReceivable/Handle'),
    meta: {
      active: 'CommissionReceivable',
      titles: [
        { label: '财务管理' },
        { label: '佣金应收', name: 'CommissionReceivable' },
        { label: '佣金应收处理', current: true }
      ]
    }
  },
  {
    path: 'finance/commissionReceivable/bills',
    name: 'CommissionReceivableBills',
    component: () => import('@/views/finance/CommissionReceivable/Bills'),
    meta: {
      active: 'CommissionReceivable',
      titles: [
        { label: '财务管理' },
        { label: '佣金应收', name: 'CommissionReceivable' },
        { label: '收取记录', current: true }
      ]
    }
  },
  {
    path: 'finance/commissionReceivable/bills/:id/receivables',
    name: 'CommissionBillReceivables',
    component: () => import('@/views/finance/CommissionReceivable/Receivables'),
    meta: {
      active: 'CommissionReceivable',
      titles: [
        { label: '财务管理' },
        { label: '佣金应收', name: 'CommissionReceivable' },
        { label: '收取记录', name: 'CommissionReceivableBills' },
        { label: '收取数据', current: true }
      ]
    }
  },
  {
    path: 'finance/commissionReceivable/draft-bills',
    name: 'DraftCommissionReceivableBills',
    component: () => import('@/views/finance/CommissionReceivable/Draft'),
    meta: {
      active: 'CommissionReceivable',
      titles: [
        { label: '财务管理' },
        { label: '佣金应收', name: 'CommissionReceivable' },
        { label: '暂存记录', current: true }
      ]
    }
  },
  {
    path: 'finance/commissionReceivable/draft-bills/:id/receivables',
    name: 'DraftCommissionBillReceivables',
    component: () => import('@/views/finance/CommissionReceivable/Receivables'),
    meta: {
      active: 'CommissionReceivable',
      titles: [
        { label: '财务管理' },
        { label: '佣金应收', name: 'CommissionReceivable' },
        { label: '暂存记录', name: 'DraftCommissionReceivableBills' },
        { label: '收取数据', current: true }
      ]
    }
  },
  {
    path: 'finance/platformPremium',
    name: 'PlatformPremium',
    component: () => import('@/views/finance/PlatformPremium'),
    meta: {
      titles: [{ label: '财务管理' }, { label: '平台出单费', current: true }]
    }
  },
  {
    path: 'finance/transactions',
    name: 'FinanceTransactions',
    component: () => import('@/views/finance/Transaction'),
    meta: {
      titles: [{ label: '财务管理' }, { label: '交易流水', current: true }]
    }
  },
  {
    path: 'finance/memos',
    name: 'Memo',
    component: () => import('@/views/finance/Memo'),
    meta: {
      titles: [{ label: '财务管理' }, { label: '水单管理', current: true }]
    }
  },
  {
    path: 'finance/applySettlement',
    name: 'ApplySettlement',
    component: () => import('@/views/finance/ApplySettlement'),
    meta: {
      titles: [{ label: '财务管理' }, { label: '销账申请', current: true }]
    }
  },
  {
    path: 'finance/settlement/history',
    name: 'SettlementHistory',
    component: () => import('@/views/finance/Settlement/History'),
    meta: {
      active: 'ApplySettlement',
      titles: [
        { label: '财务管理' },
        { label: '销账申请', name: 'ApplySettlement' },
        { label: '销账记录', current: true }
      ]
    }
  },
  {
    path: 'finance/settlement/history/:id',
    name: 'SettlementHistoryDetail',
    component: () => import('@/views/finance/Settlement/HistoryDetail'),
    meta: {
      titles: [
        { label: '财务管理' },
        { label: '销账申请', name: 'ApplySettlement' },
        { label: '销账记录', name: 'SettlementHistory' },
        { label: '销账详情', current: true }
      ]
    }
  },
  {
    path: 'finance/settlement',
    name: 'Settlement',
    component: () => import('@/views/finance/Settlement'),
    meta: {
      titles: [{ label: '财务管理' }, { label: '销账管理', current: true }]
    }
  },
  {
    path: 'finance/settlement/:id',
    name: 'SettlementDetail',
    component: () => import('@/views/finance/Settlement/Detail'),
    meta: {
      titles: [{ label: '财务管理' }, { label: '销账管理', name: 'Settlement' }, { label: '销账详情', current: true }]
    }
  },
  {
    path: 'finance/mixed',
    name: 'FinanceMixed',
    component: () => import('@/views/finance/FinanceMixed'),
    meta: {
      titles: [{ label: '财务综合查询', current: true }]
    }
  }
]
