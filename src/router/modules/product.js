export default [
  {
    path: 'companies',
    name: 'Companies',
    component: () => import('@/views/product/Companies'),
    meta: {
      titles: [{ label: '产品管理' }, { label: '保险公司', current: true }]
    }
  },
  {
    path: 'curreny',
    name: 'Currency',
    component: () => import('@/views/product/Currency.vue'),
    meta: {
      titles: [{ label: '产品管理' }, { label: '汇率设置', current: true }]
    }
  },
  {
    path: 'curreny/:date?/configure',
    name: 'CurrencyConfigure',
    component: () => import('@/views/product/CurrencyConfigure.vue'),
    meta: {
      active: 'Currency',
      titles: [{ label: '产品管理' }, { label: '汇率设置', name: 'Currency' }, { label: '配置汇率', current: true }]
    }
  },
  {
    path: 'companies/:id',
    name: 'CompanyDetails',
    component: () => import('@/views/product/CompanyDetails'),
    meta: {
      active: 'Companies',
      titles: [{ label: '产品管理' }, { label: '保险公司', name: 'Companies' }, { label: '详情', current: true }]
    }
  },
  {
    path: 'companies/:id/clauses',
    name: 'CompanyClauses',
    component: () => import('@/views/product/CompanyClauses'),
    meta: {
      active: 'Companies',
      titles: [{ label: '产品管理' }, { label: '保险公司', name: 'Companies' }, { label: '保险条约', current: true }]
    }
  },
  {
    path: 'channels',
    name: 'Channels',
    component: () => import('@/views/product/Channels'),
    meta: {
      active: 'Channels',
      titles: [{ label: '产品管理' }, { label: '出单渠道', current: true }]
    }
  },
  // 标的
  {
    path: 'subjects',
    name: 'Subjects',
    component: () => import('@/views/product/subject/Index.vue'),
    meta: {
      titles: [{ label: '产品管理' }, { label: '标的管理', current: true }]
    }
  },
  {
    path: 'subjects/:id/keywords',
    name: 'SubjectKeywords',
    component: () => import('@/views/product/subject/Keywords.vue'),
    meta: {
      active: 'Subjects',
      titles: [{ label: '产品管理' }, { label: '标的管理', name: 'Subjects' }, { label: '关键词管理', current: true }]
    }
  },
  {
    path: 'products/domestic',
    name: 'ProductsDomestic',
    component: () => import('@/views/product/Domestic'),
    meta: {
      titles: [{ label: '产品管理' }, { label: '国内货运险', current: true }]
    }
  },
  {
    path: 'products/domestic/create',
    name: 'ProductsDomesticCreate',
    component: () => import('@/views/product/DomesticForm'),
    meta: {
      active: 'ProductsDomestic',
      titles: [
        { label: '产品管理' },
        { label: '国内货运险', name: 'ProductsDomestic' },
        { label: '添加产品', current: true }
      ]
    }
  },
  {
    path: 'products/domestic/:id',
    name: 'ProductsDomesticUpdate',
    component: () => import('@/views/product/DomesticForm'),
    meta: {
      active: 'ProductsDomestic',
      titles: [
        { label: '产品管理' },
        { label: '国内货运险', name: 'ProductsDomestic' },
        { label: '更新产品', current: true }
      ]
    }
  },
  {
    path: 'products/intl',
    name: 'ProductsIntl',
    component: () => import('@/views/product/Intl'),
    meta: {
      titles: [{ label: '产品管理' }, { label: '国际货运险', current: true }]
    }
  },
  {
    path: 'products/intl/create',
    name: 'ProductsIntlCreate',
    component: () => import('@/views/product/IntlForm'),
    meta: {
      active: 'ProductsIntl',
      titles: [
        { label: '产品管理' },
        { label: '国际货运险', name: 'ProductsIntl' },
        { label: '添加产品', current: true }
      ]
    }
  },
  {
    path: 'products/intl/:id',
    name: 'ProductsIntlUpdate',
    component: () => import('@/views/product/IntlForm'),
    meta: {
      active: 'ProductsIntl',
      titles: [
        { label: '产品管理' },
        { label: '国际货运险', name: 'ProductsIntl' },
        { label: '更新产品', current: true }
      ]
    }
  },
  {
    path: 'products/lbt',
    name: 'ProductsLBT',
    component: () => import('@/views/product/LBT'),
    meta: {
      titles: [{ label: '产品管理' }, { label: '单车责任险', current: true }]
    }
  },
  {
    path: 'products/lbt/create',
    name: 'ProductsLBTCreate',
    component: () => import('@/views/product/LBTForm'),
    meta: {
      active: 'ProductsLBT',
      titles: [
        { label: '产品管理' },
        { label: '单车责任险', name: 'ProductsLBT' },
        { label: '添加产品', current: true }
      ]
    }
  },
  {
    path: 'products/lbt/:id',
    name: 'ProductsLBTUpdate',
    component: () => import('@/views/product/LBTForm'),
    meta: {
      active: 'ProductsLBT',
      titles: [
        { label: '产品管理' },
        { label: '单车责任险', name: 'ProductsLBT' },
        { label: '更新产品', current: true }
      ]
    }
  },
  {
    path: 'otherProducts',
    name: 'OtherProducts',
    component: () => import('@/views/product/otherProducts/OtherProducts.vue'),
    meta: {
      hideBreadcrumb: true,
      titles: [{ label: '产品管理' }, { label: '其他险种', current: true }]
    }
  },
  {
    path: 'otherProducts/edit/:id',
    name: 'EditOtherProduct',
    component: () => import('@/views/product/otherProducts/EditOtherProduct.vue'),
    meta: {
      active: 'OtherProducts',
      hideBreadcrumb: true,
      titles: [{ label: '产品管理' }, { label: '其他险种', name: 'OtherProducts' }, { label: '修改', current: true }]
    }
  },
  {
    path: 'otherProducts/add',
    name: 'AddProduct',
    component: () => import('@/views/product/otherProducts/AddProduct.vue'),
    meta: {
      active: 'OtherProducts',
      hideBreadcrumb: false,
      titles: [
        { label: '产品管理' },
        { label: '其他险种', name: 'OtherProducts' },
        { label: '添加其他险种', current: true }
      ]
    }
  },
  {
    path: 'otherProducts/type',
    name: 'TypeInsurance',
    component: () => import('@/views/product/otherProducts/TypeInsurance.vue'),
    meta: {
      active: 'OtherProducts',
      hideBreadcrumb: true,
      titles: [
        { label: '产品管理' },
        { label: '其他险种', name: 'OtherProducts' },
        { label: '保险分类', current: true }
      ]
    }
  },
  {
    path: 'otherProducts/model',
    name: 'InsuranceModel',
    component: () => import('@/views/product/otherProducts/InsuranceModel.vue'),
    meta: {
      active: 'OtherProducts',
      hideBreadcrumb: true,
      titles: [
        { label: '产品管理' },
        { label: '其他险种', name: 'OtherProducts' },
        { label: '保险模型', current: true }
      ]
    }
  },
  {
    path: 'otherProducts/:id/fields',
    name: 'ModelFields',
    component: () => import('@/views/product/otherProducts/ModelFields.vue'),
    meta: {
      active: 'OtherProducts',
      hideBreadcrumb: true,
      titles: [
        { label: '产品管理' },
        { label: '其他险种', name: 'OtherProducts' },
        { label: '保险模型', name: 'InsuranceModel' },
        { label: '字段管理', current: true }
      ]
    }
  },
  {
    path: 'otherProducts/edit/:id/benefit',
    name: 'Privilege',
    component: () => import('@/views/product/otherProducts/Privilege.vue'),
    meta: {
      active: 'OtherProducts',
      hideBreadcrumb: true,
      titles: [
        { label: '产品管理' },
        { label: '其他险种', name: 'OtherProducts' },
        { label: '修改', name: 'EditOtherProduct' },
        { label: '保障权益', current: true }
      ]
    }
  },
  {
    path: 'otherProducts/edit/:id/aboutFile',
    name: 'AboutFile',
    component: () => import('@/views/product/otherProducts/AboutFile.vue'),
    meta: {
      active: 'OtherProducts',
      hideBreadcrumb: true,
      titles: [
        { label: '产品管理' },
        { label: '其他险种', name: 'OtherProducts' },
        { label: '修改', name: 'EditOtherProduct' },
        { label: '相关资料', current: true }
      ]
    }
  },
  {
    path: 'otherProducts/eidt/:id/notice',
    name: 'Notice',
    component: () => import('@/views/product/otherProducts/Notice.vue'),
    meta: {
      active: 'OtherProducts',
      hideBreadcrumb: false,
      titles: [
        { label: '产品管理' },
        { label: '其他险种', name: 'OtherProducts' },
        { label: '修改', name: 'EditOtherProduct' },
        { label: '投保须知', current: true }
      ]
    }
  },
  {
    path: 'otherProducts/copy/:id',
    name: 'CopyProduct',
    component: () => import('@/views/product/otherProducts/CopyProduct.vue'),
    meta: {
      active: 'OtherProducts',
      hideBreadcrumb: false,
      titles: [{ label: '产品管理' }, { label: '其他险种', name: 'OtherProducts' }, { label: '复制', current: true }]
    }
  },
  {
    path: 'offline-products',
    name: 'OfflineProducts',
    component: () => import('@/views/product/offline/OfflineProducts.vue'),
    meta: {
      active: 'OfflineProducts',
      titles: [{ label: '产品管理' }, { label: '保单录入产品', current: true }]
    }
  },
  {
    path: 'offline-products/create',
    name: 'CreateOfflineProduct',
    component: () => import('@/views/product/offline/OfflineProductForm.vue'),
    meta: {
      active: 'OfflineProducts',
      titles: [
        { label: '产品管理' },
        { label: '保单录入产品', name: 'OfflineProducts' },
        { label: '添加产品', current: true }
      ]
    }
  },
  {
    path: 'offline-products/:id',
    name: 'UpdateOfflineProduct',
    component: () => import('@/views/product/offline/OfflineProductForm.vue'),
    meta: {
      active: 'OfflineProducts',
      titles: [
        { label: '产品管理' },
        { label: '保单录入产品', name: 'OfflineProducts' },
        { label: '修改产品', current: true }
      ]
    }
  },
  {
    path: 'offline-products/insurances',
    name: 'OfflineProductInsurances',
    component: () => import('@/views/product/offline/OfflineProductInsurance.vue'),
    meta: {
      active: 'OfflineProducts',
      titles: [
        { label: '产品管理' },
        { label: '保单录入产品', name: 'OfflineProducts' },
        { label: '险类', current: true }
      ]
    }
  },
  {
    path: 'offline-products/types',
    name: 'OfflineProductType',
    component: () => import('@/views/product/offline/OfflineProductType.vue'),
    meta: {
      active: 'OfflineProducts',
      titles: [
        { label: '产品管理' },
        { label: '保单录入产品', name: 'OfflineProducts' },
        { label: '险种', current: true }
      ]
    }
  },
  {
    path: 'offline-products/template',
    name: 'OfflineProductTemplate',
    component: () => import('@/views/product/offline/OfflineProductTemplate.vue'),
    meta: {
      active: 'OfflineProducts',
      titles: [
        { label: '产品管理' },
        { label: '保单录入产品', name: 'OfflineProducts' },
        { label: '产品模板', current: true }
      ]
    }
  },
  {
    path: 'offline-products/category/:id/fields',
    name: 'OfflineProductFields',
    component: () => import('@/views/product/offline/OfflineProductFields.vue'),
    meta: {
      active: 'OfflineProducts',
      titles: [
        { label: '产品管理' },
        { label: '保单录入产品', name: 'OfflineProducts' },
        { label: '产品字段', current: true }
      ]
    }
  },
  {
    path: 'documents',
    name: 'Documents',
    component: () => import('@/views/product/documents/Index.vue'),
    meta: {
      titles: [{ label: '产品管理' }, { label: '资料中心', current: true }]
    }
  },
  {
    path: 'products/cbec',
    name: 'ProductsCbec',
    component: () => import('@/views/product/Cbec'),
    meta: {
      active: 'ProductsCbec',
      titles: [{ label: '产品管理' }, { label: '跨境电商货运险', current: true }]
    }
  },
  {
    path: 'products/cbec/create',
    name: 'ProductsCbecCreate',
    component: () => import('@/views/product/CbecForm'),
    meta: {
      active: 'ProductsCbec',
      titles: [
        { label: '产品管理' },
        { label: '跨境电商货运险', name: 'ProductsCbec' },
        { label: '添加产品', current: true }
      ]
    }
  },
  {
    path: 'products/cbec/:id',
    name: 'ProductsCbecUpdate',
    component: () => import('@/views/product/CbecForm'),
    meta: {
      active: 'ProductsCbec',
      titles: [
        { label: '产品管理' },
        { label: '跨境电商货运险', name: 'ProductsCbec' },
        { label: '更新产品', current: true }
      ]
    }
  },
  {
    path: 'products/covers',
    name: 'ProductsCover',
    component: () => import('@/views/product/Cover'),
    meta: {
      active: 'ProductsCover',
      titles: [{ label: '产品管理' }, { label: '保障范围与政策', current: true }]
    }
  }
]
