/*
 * @Author: your name
 * @Date: 2021-03-05 09:47:27
 * @LastEditTime: 2021-04-14 15:11:54
 * @LastEditors: yanb
 * @Description: In User Settings Edit
 * @FilePath: \dashboard-fe\src\router\modules\ticket.js
 */
export default [
  {
    path: 'users',
    name: 'Users',
    component: () => import('@/views/users'),
    meta: {
      titles: [{ label: '用户管理', current: true }]
    }
  },
  {
    path: 'users/registrations',
    name: 'UserRegistration',
    component: () => import('@/views/users/Registration'),
    meta: {
      titles: [
        { label: '用户管理', name: 'Users' },
        { label: '注册申请', current: true }
      ]
    }
  },
  {
    path: 'users/:id',
    name: 'User',
    component: () => import('@/views/users/Detail'),
    meta: {
      active: 'Users',
      titles: [
        { label: '用户管理', name: 'Users' },
        { label: '用户详情', current: true }
      ]
    }
  },
  {
    path: 'users/:id/products',
    name: 'UsersProducts',
    component: () => import('@/views/users/Products'),
    meta: {
      active: 'Users',
      titles: [
        { label: '用户管理', name: 'Users' },
        { label: '用户详情', name: 'User' },
        { label: '产品管理', current: true }
      ]
    }
  }
]
