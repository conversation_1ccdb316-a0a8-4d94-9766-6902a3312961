export default [
  {
    path: 'claims/cases',
    name: 'ClaimCases',
    component: () => import('@/views/claim/Cases.vue'),
    meta: {
      titles: [{ label: '理赔管理' }, { label: '综合查询', current: true }]
    }
  },
  {
    path: 'claims/cases/acceptance',
    name: 'ClaimCasesAcceptance',
    component: () => import('@/views/claim/CasesAcceptance.vue'),
    meta: {
      titles: [{ label: '理赔管理' }, { label: '新案件受理', current: true }]
    }
  },
  {
    path: 'claims/workplace',
    name: 'ClaimWorkplace',
    component: () => import('@/views/claim/Workplace.vue'),
    meta: {
      titles: [{ label: '理赔管理' }, { label: '工作区', current: true }]
    }
  },
  {
    path: 'claims/cases/submission',
    name: 'ClaimCaseSubmission',
    component: () => import('@/views/claim/CaseSubmission.vue'),
    meta: {
      titles: [{ label: '理赔管理' }, { label: '系统报案', current: true }]
    }
  },
  {
    path: 'claims/:id',
    name: 'ClaimCasesDetail',
    component: () => import('@/views/claim/CaseDetail.vue'),
    meta: {
      active: 'ClaimWorkplace',
      titles: [{ label: '理赔管理' }, { label: '工作区', name: 'ClaimWorkplace' }, { label: '案件详情', current: true }]
    }
  },
  {
    path: 'claims/:id/readonly',
    name: 'ClaimCasesDetailReadonly',
    component: () => import('@/views/claim/CaseDetail.vue'),
    meta: {
      active: 'ClaimCases',
      titles: [{ label: '理赔管理' }, { label: '综合查询', name: 'ClaimCases' }, { label: '案件详情', current: true }]
    }
  }
]
