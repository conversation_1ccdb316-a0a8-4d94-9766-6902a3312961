/*
 * @Author: your name
 * @Date: 2021-03-05 09:47:27
 * @LastEditTime: 2023-06-26 09:36:12
 * @LastEditors: yanb
 * @Description: In User Settings Edit
 * @FilePath: \dashboard-fe\src\router\modules\inquiry.js
 */
export default [
  {
    path: 'inquiries',
    name: 'InquiryNew',
    component: () => import('@/views/inquiry/Inquiries.vue'),
    meta: {
      titles: [{ label: '询价管理', current: true }]
    }
  },
  {
    path: 'inquiries/create',
    name: 'InquiryCreate',
    component: () => import('@/views/inquiry/InquiryForm.vue'),
    meta: {
      active: 'InquiryNew',
      titles: [
        { label: '询价管理', name: 'InquiryNew' },
        { label: '新增询价单', current: true }
      ]
    }
  },
  {
    path: 'inquiries/edit/:id',
    name: 'InquiryEdit',
    component: () => import('@/views/inquiry/InquiryForm.vue'),
    meta: {
      active: 'InquiryNew',
      titles: [
        { label: '询价管理', name: 'InquiryNew' },
        { label: '编辑询价单', current: true }
      ]
    }
  },
  {
    path: 'inquiries/:id',
    name: 'InquiryDetail',
    component: () => import('@/views/inquiry/InquiryDetail.vue'),
    meta: {
      active: 'InquiryNew',
      titles: [
        { label: '询价管理', name: 'InquiryNew' },
        { label: '询价单', current: true }
      ]
    }
  },

  // 下面是保呀官网的询价管理
  {
    path: 'enquiries/baoya',
    name: 'InquiryBaoya',
    component: () => import('@/views/inquiry/baoya/Inquiries.vue'),
    meta: {
      titles: [{ label: '询价管理(保呀官网)', current: true }]
    }
  },
  {
    path: 'enquiries/baoya/:id',
    name: 'InquiryBaoyaDetail',
    component: () => import('@/views/inquiry/baoya/Inquiry.vue'),
    meta: {
      active: 'InquiryBaoya',
      titles: [
        { label: '询价管理(保呀官网)', name: 'InquiryBaoya' },
        { label: '询价详情', current: true }
      ]
    }
  }
]
