/*
 * @Descripttion:
 * @Author: Mr. z<PERSON>
 * @Date: 2021-01-22 09:28:17
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-22 09:28:35
 */
export default [
  {
    path: 'permissions',
    name: 'Permissions',
    component: () => import('@/views/system/permissions'),
    meta: {
      titles: [{ label: '系统管理' }, { label: '权限管理', current: true }]
    }
  },
  {
    path: 'roles',
    name: 'Roles',
    component: () => import('@/views/system/roles'),
    meta: {
      titles: [{ label: '系统管理' }, { label: '角色管理', current: true }]
    }
  },
  {
    path: 'admins',
    name: 'Admins',
    component: () => import('@/views/system/admins'),
    meta: {
      titles: [{ label: '系统管理' }, { label: '管理员管理', current: true }]
    }
  },
  {
    path: 'admins/:id/transfer',
    name: 'AdminsTransfer',
    component: () => import('@/views/system/admins/Transfer.vue'),
    meta: {
      titles: [{ label: '系统管理' }, { label: '管理员管理', name: 'Admins' }, { label: '离职交接' }]
    }
  },
  {
    path: 'platforms',
    name: 'Platforms',
    component: () => import('@/views/system/platforms'),
    meta: {
      titles: [{ label: '系统管理' }, { label: '平台管理', current: true }]
    }
  },
  {
    path: 'platforms/create',
    name: 'PlatformsCreate',
    component: () => import('@/views/system/platforms/Create'),
    meta: {
      active: 'Platforms',
      titles: [{ label: '系统管理' }, { label: '平台管理', name: 'Platforms' }, { label: '创建平台', current: true }]
    }
  },
  {
    path: 'platforms/:id',
    name: 'PlatformsUpdate',
    component: () => import('@/views/system/platforms/Update'),
    meta: {
      active: 'Platforms',
      titles: [{ label: '系统管理' }, { label: '平台管理', name: 'Platforms' }, { label: '更新平台', current: true }]
    }
  },
  {
    path: 'platforms/:id/products',
    name: 'PlatformsProducts',
    component: () => import('@/views/system/platforms/Products'),
    meta: {
      active: 'Platforms',
      titles: [{ label: '系统管理' }, { label: '平台管理', name: 'Platforms' }, { label: '产品管理', current: true }]
    }
  },
  {
    path: 'recommendations',
    name: 'Recommendations',
    component: () => import('@/views/system/recommendation'),
    meta: {
      active: 'Recommendations',
      titles: [{ label: '系统管理' }, { label: '产品推荐', name: 'Recommendations' }]
    }
  },
  {
    path: 'announcements',
    name: 'Announcements',
    component: () => import('@/views/system/announcement'),
    meta: {
      active: 'Announcements',
      titles: [{ label: '系统管理' }, { label: '系统通知', name: 'Announcements' }]
    }
  },
  {
    path: 'blacklists',
    name: 'Blacklists',
    component: () => import('@/views/system/blacklists'),
    meta: {
      active: 'Blacklists',
      titles: [{ label: '系统管理' }, { label: '黑名单', name: 'Blacklists' }]
    }
  },
  {
    path: 'features',
    name: 'Features',
    component: () => import('@/views/system/features'),
    meta: {
      titles: [{ label: '系统管理' }]
    }
  }
]
