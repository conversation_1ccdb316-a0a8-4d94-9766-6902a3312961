export default [
  {
    path: '/products/group',
    name: 'GroupProducts',
    component: () => import('@/views/product/GroupProducts'),
    meta: {
      active: 'GroupProducts',
      titles: [{ label: '产品管理' }, { label: '雇主责任险', current: true }]
    }
  },
  {
    path: '/products/group/create',
    name: 'GroupProductCreate',
    component: () => import('@/views/product/GroupProduct'),
    meta: {
      active: 'GroupProducts',
      titles: [
        { label: '产品管理' },
        { label: '雇主责任险', name: 'GroupProducts' },
        { label: '添加产品', current: true }
      ]
    }
  },
  {
    path: '/products/group/:id',
    name: 'GroupProductUpdate',
    component: () => import('@/views/product/GroupProduct'),
    meta: {
      active: 'GroupProducts',
      titles: [
        { label: '产品管理' },
        { label: '雇主责任险', name: 'GroupProducts' },
        { label: '产品详情', current: true }
      ]
    }
  },
  {
    path: 'groups/product/:id/plans',
    name: 'GroupProductPlans',
    component: () => import('@/views/product/GroupProductPlans'),
    meta: {
      active: 'GroupProducts',
      titles: [
        { label: '产品管理' },
        { label: '雇主责任险', name: 'GroupProducts' },
        { label: '产品详情', name: 'GroupProductUpdate' },
        { label: '产品套餐', current: true }
      ]
    }
  },
  {
    path: 'groups/product/:id/plans/:planId',
    name: 'GroupProductPlanDetails',
    component: () => import('@/views/product/GroupProductPlanDetails'),
    meta: {
      active: 'GroupProducts',
      titles: [
        { label: '产品管理' },
        { label: '雇主责任险', name: 'GroupProducts' },
        { label: '产品详情', name: 'GroupProductUpdate' },
        { label: '产品套餐', name: 'GroupProductPlans' },
        { label: '套餐详情', current: true }
      ]
    }
  }
]
