/*
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2020-10-19 12:30:41
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-10 17:11:29
 */

import Vue from 'vue'
import VueRouter from 'vue-router'
import routes from './routes'
Vue.use(VueRouter)
import { tokenKey } from '@/config'

// 新窗口打开方法
const router = new VueRouter({
  base: process.env.NODE_ENV === 'production' ? '/dashboard/' : '/',
  mode: 'history',
  routes: routes
})

router.beforeEach((to, from, next) => {
  const meta = to.meta
  if (meta.dynamicTitle) {
    const _temp = meta.titles.find((item) => item.current)
    if (_temp) {
      _temp.label = to.params[meta.dynamicTitle]
    } else {
      meta.titles.push({
        label: to.params[meta.dynamicTitle],
        current: true
      })
    }
  }

  if (to.name !== 'Login' && !localStorage.getItem(tokenKey)) {
    router.push({ name: 'Login' })
  }

  next()
})
export default router
