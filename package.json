{"name": "baoya-v2-fe-dashboard", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "cross-env NODE_OPTIONS=--openssl-legacy-provider vue-cli-service serve --open", "serve": "cross-env NODE_OPTIONS=--openssl-legacy-provider vue-cli-service serve --mode local", "build:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--openssl-legacy-provider vue-cli-service build  --mode production", "build:staging": "cross-env NODE_ENV=production NODE_OPTIONS=--openssl-legacy-provider vue-cli-service build --mode staging", "lint": "vue-cli-service lint"}, "dependencies": {"@fortawesome/fontawesome-free": "^5.15.1", "axios": "^1.6.8", "chart.js": "^4.4.2", "core-js": "^3.36.1", "dayjs": "^1.10.4", "echarts": "^5.6.0", "element-ui": "^2.15.12", "fix-date": "^1.1.6", "html-to-image": "^1.11.11", "regenerator-runtime": "^0.13.9", "style-resources-loader": "^1.4.1", "vue": "^2.7.0", "vue-chartjs": "^5.3.1", "vue-cli-plugin-style-resources-loader": "^0.1.4", "vue-echarts": "^7.0.3", "vue-html2pdf": "^1.8.0", "vue-router": "^3.4.9", "vuex": "^3.6.0", "wangeditor": "^4.6.17"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.10", "@vue/cli-plugin-eslint": "~4.5.10", "@vue/cli-plugin-router": "~4.5.10", "@vue/cli-plugin-vuex": "~4.5.10", "@vue/cli-service": "~4.5.10", "@vue/compiler-dom": "^3.2.41", "@vue/eslint-config-prettier": "^6.0.0", "babel-eslint": "^10.1.0", "cross-env": "^7.0.3", "eslint": "^6.8.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^7.4.1", "node-sass": "^8.0.0", "prettier": "^2.2.1", "qs": "^6.9.4", "sass-loader": "^10.1.0", "vue-template-babel-compiler": "^1.0.6-0", "vue-template-compiler": "^2.6.12"}}