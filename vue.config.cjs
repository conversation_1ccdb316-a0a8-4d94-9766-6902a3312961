/*
 * @Descripttion:
 * @Author: Mr. zhu
 * @Date: 2020-10-19 12:26:38
 * @LastEditors: Mr. zhu
 * @LastEditTime: 2021-01-08 15:56:43
 */
const path = require('path')

const webpackConfig = {
  lintOnSave: false,
  assetsDir: 'static',
  productionSourceMap: false,
  // 开发环境及代理配置
  devServer: {
    port: '9200',
    disableHostCheck: true
  },
  publicPath:
    process.env.NODE_ENV === 'production' ? 'https://yrt-static.oss-cn-hangzhou.aliyuncs.com/baoya-dash-fe' : '/',
  // 全局 scss 引入
  pluginOptions: {
    'style-resources-loader': {
      preProcessor: 'scss',
      patterns: [
        // 需要全局导入的scss路径
        path.resolve(__dirname, './src/styles/variables.scss'),
        path.resolve(__dirname, './src/styles/global.scss'),
        path.resolve(__dirname, './src/styles/spaces.scss')
      ]
    }
  },
  transpileDependencies: ['vue-chartjs', 'chart.js', 'echarts', 'vue-echarts'],
  chainWebpack: (config) => {
    config.module
      .rule('vue')
      .use('vue-loader')
      .tap((options) => {
        options.compiler = require('vue-template-babel-compiler')
        return options
      })
  }
}

module.exports = webpackConfig
