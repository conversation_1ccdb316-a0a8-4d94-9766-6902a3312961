// 测试饼图行为的示例代码
// 这个文件用于验证修复后的饼图显示逻辑

const testData = [
  { name: '车险', value: 1000 },
  { name: '财险', value: 2000 },
  { name: '意外险', value: 500 }
]

// 模拟 CardPie 组件的 convertToPercentages 方法
function convertToPercentages(data) {
  if (!data || !Array.isArray(data) || data.length === 0) {
    return []
  }

  // 计算总值
  const total = data.reduce((sum, item) => {
    const value = parseFloat(item.value) || 0
    return sum + value
  }, 0)

  // 转换为百分比
  if (total === 0) {
    return data.map((item) => ({
      ...item,
      originalValue: item.value,
      value: 0
    }))
  }

  return data.map((item) => {
    const value = parseFloat(item.value) || 0
    return {
      ...item,
      originalValue: item.value,
      value: parseFloat(((value / total) * 100).toFixed(2))
    }
  })
}

// 模拟 tooltip formatter
function tooltipFormatter(params, showPercentage = true, unit = '元') {
  if (showPercentage) {
    // 显示百分比时：显示原始值和百分比占比
    const originalValue = params.data.originalValue || params.data.value
    return `${params.seriesName} <br/>${params.name} : ${originalValue}${unit} (${params.percent}%)`
  } else {
    // 不显示百分比时：显示传递的值和百分比占比
    return `${params.seriesName} <br/>${params.name} : ${params.data.value}${unit} (${params.percent}%)`
  }
}

// 模拟 label formatter
function labelFormatter(params, showPercentage = true, unit = '元') {
  if (showPercentage) {
    // 显示百分比时：显示原始值
    const originalValue = params.data.originalValue || params.data.value
    return `${params.name}\n${originalValue}${unit}`
  } else {
    // 不显示百分比时：显示传递的值
    return `${params.name}\n${params.data.value}${unit}`
  }
}

// 测试用例
console.log('=== 原始数据 ===')
console.log(testData)

console.log('\n=== 转换后的数据 (showPercentage: true) ===')
const convertedData = convertToPercentages(testData)
console.log(convertedData)

console.log('\n=== Tooltip 显示效果 ===')
convertedData.forEach((item, index) => {
  const mockParams = {
    seriesName: '保费',
    name: item.name,
    data: item,
    percent: item.value // 这里 value 已经是百分比
  }
  
  console.log(`showPercentage: true  - ${tooltipFormatter(mockParams, true)}`)
  console.log(`showPercentage: false - ${tooltipFormatter(mockParams, false)}`)
  console.log('---')
})

console.log('\n=== Label 显示效果 ===')
convertedData.forEach((item) => {
  const mockParams = {
    name: item.name,
    data: item
  }
  
  console.log(`showPercentage: true  - ${labelFormatter(mockParams, true)}`)
  console.log(`showPercentage: false - ${labelFormatter(mockParams, false)}`)
  console.log('---')
})

// 预期结果：
// 1. showPercentage: true 时，tooltip 和 label 都显示原始值 (1000元, 2000元, 500元)
// 2. showPercentage: false 时，tooltip 和 label 显示传递的值
// 3. tooltip 始终显示百分比占比
