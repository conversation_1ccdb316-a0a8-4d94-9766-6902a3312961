image: node:14.16.0-alpine

stages:
  - staging
  - production

cache:
  key: ${CI_BUILD_REF_NAME}
  paths:
    - node_modules/

Deploy Staging:
  stage: staging
  before_script:
    - sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
    - apk update && apk add openssh-client bash rsync
    - eval $(ssh-agent -s)
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - echo "$SSH_STAGING_PRIVATE_KEY" | tr -d '\r' > ~/.ssh/id_rsa
    - chmod 700 ~/.ssh/id_rsa
    - ssh-add ~/.ssh/id_rsa
    - ssh-keyscan -H $SSH_STAGING_IP >> ~/.ssh/known_hosts
  script:
    - SASS_BINARY_SITE=https://npmmirror.com/mirrors/node-sass/ npm install --registry https://registry.npmmirror.com
    - npm run build-staging
    - rsync -avz --progress ./dist/* root@$SSH_STAGING_IP:/var/www/new_51baoya/dashboard/web
    - ssh root@${SSH_STAGING_IP} "chown -R www-data:www-data /var/www/new_51baoya/dashboard/web"
    - ssh root@${SSH_STAGING_IP} "docker restart nginx"
  except:
    - master

Deploy Production:
  stage: production
  before_script:
    - sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
    - apk update && apk add openssh-client bash rsync
    - eval $(ssh-agent -s)
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - echo "$SSH_PROD_PRIVATE_KEY" | tr -d '\r' > ~/.ssh/id_rsa
    - chmod 700 ~/.ssh/id_rsa
    - ssh-add ~/.ssh/id_rsa
    - ssh-keyscan -H $SSH_PROD_IP >> ~/.ssh/known_hosts
  script:
    - SASS_BINARY_SITE=https://npmmirror.com/mirrors/node-sass/ npm install --registry https://registry.npmmirror.com
    - npm run build
    - rsync -avz --progress ./dist/* user@$SSH_PROD_IP:/home/<USER>/app/baoya/dashboard/web
    - ssh user@${SSH_PROD_IP} "chown -R user:user /home/<USER>/app/baoya/dashboard/web"
    - ssh user@${SSH_PROD_IP} "docker restart nginx"
  when: manual
  only:
    - master
