<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <link rel="icon" href="">
  <title>加载中</title>

  <style>
    .alert {
      height: 50px;
      line-height: 50px;
      padding: 10px;
      width: 100%;
      text-align: center;
    }

    .alert a {
      text-decoration: none;
    }

    .refresh-button {
      color: #ff7f4c;
      text-decoration: none;
      cursor: pointer;
    }
  </style>
</head>

<body>
  <noscript>
    <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled.
        Please enable it to continue.</strong>
  </noscript>
  <div class="alert" style="display: none" id="browser">
    推荐使用最新版本的
    <a href="https://www.microsoft.com/en-us/edge" target="_blank">
      <img
        src="https://edgestatic.azureedge.net/shared/cms/lrs1c69a1j/section-images/29bfeef37eef4ca3bcf962274c1c7766-png-w265.webp"
        height="22px" alt="Microsoft Edge">
    </a>&nbsp;
    <a href="https://www.google.cn/chrome/" target="_blank">
      <img src="https://www.google.cn/chrome/static/images/chrome-logo.svg" height="20px" alt="Google Chrome">
    </a>&nbsp;
    <a href="https://www.mozilla.org/en-US/firefox/new/">
      <img src="https://www.mozilla.org/media/img/favicons/firefox/browser/favicon-196x196.59e3822720be.png"
        height="20px" alt="Firefox">
    </a>
    以获取最佳体验，如您尚未安装可单击图标直达官网下载。
  </div>
  <div class="el-scrollbar__wrap el-scrollbar__wrap--hidden-default" id="baoya"></div>
  <!-- built files will be auto injected -->

  <script>
    const isChrome = !!window.chrome && (!!window.chrome.webstore || !!window.chrome.runtime) || /Chrome/.test(navigator.userAgent) && /Google Inc/.test(navigator.vendor)
    const isEdgeChromium = isChrome && (navigator.userAgent.indexOf("Edg") != -1)
    const isFirefox = typeof InstallTrigger !== 'undefined'

    if (!isChrome && !isEdgeChromium && !isFirefox) {
      document.querySelector('#browser').style.display = 'block'
    }
  </script>
</body>

</html>
